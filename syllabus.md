# 高通量测序原理与数据分析
## 课程教学大纲

**课程信息**
- 课程名称：高通量测序原理与数据分析
- 课程编码：S0904C213
- 总学时：32学时
- 授课对象：生物信息专业硕士研究生
- 教学形式：理论课(16学时)+实践操作课(16学时)

**课程目标**：
使学生全面掌握高通量测序的基本原理、主流技术平台及数据分析方法，能够独立设计实验方案并执行常见类型的高通量测序数据分析流程。

## 专题设置

### 专题一：高通量测序技术概论
**理论课(2h)**
- 高通量测序技术的发展历史与技术演进
- 第一代、第二代和第三代测序技术原理比较
- 主流测序平台介绍(Illumina, Ion Torrent, PacBio, Oxford Nanopore等)
- 各测序平台的技术特点、优缺点及适用场景
- 测序实验设计与考虑因素

**实践操作课(2h)**
- Linux操作系统基本命令介绍
- 生物信息学分析环境配置与测试
- 常用测序数据格式介绍(FASTQ, SAM/BAM, VCF等)
- 公共数据库资源使用(SRA, GEO, ENCODE等)
- 数据传输与管理实践

### 专题二：测序数据质量控制与预处理
**理论课(2h)**
- 测序错误来源与类型分析
- 测序数据质量评估指标与方法
- 数据过滤与质控策略
- 接头去除和低质量序列过滤原理
- 测序深度与覆盖度的计算及意义

**实践操作课(2h)**
- FastQC工具使用与质量报告解读
- Trimmomatic/Cutadapt等工具进行数据过滤与清洗
- MultiQC进行批量质控结果可视化
- 数据预处理自动化流程搭建
- 质控前后数据质量对比分析

### 专题三：基因组测序(DNA-seq)数据分析
**理论课(2h)**
- 基因组测序实验设计与应用场景
- 参考基因组比对算法原理(BWA, Bowtie2等)
- 变异检测方法学(SNP, Indel, SV检测)
- 变异注释与功能预测原理
- 基因组组装策略与算法

**实践操作课(2h)**
- BWA/Bowtie2进行参考基因组比对实践
- SAMtools进行SAM/BAM文件处理
- GATK/FreeBayes等工具进行变异检测
- ANNOVAR/SnpEff进行变异注释与解读
- IGV等可视化工具使用

### 专题四：转录组测序(RNA-seq)数据分析
**理论课(2h)**
- 转录组测序实验设计与应用场景
- RNA-seq数据分析流程概述
- 转录本比对和拼接(HISAT2, STAR, StringTie)
- 基因表达定量方法(FPKM, TPM, counts)
- 差异表达分析统计原理
- 功能富集分析方法(GO, KEGG)

**实践操作课(2h)**
- HISAT2/STAR进行转录组比对
- StringTie/Cufflinks进行转录本组装与定量
- DESeq2/edgeR进行差异表达分析
- ClusterProfiler进行功能富集分析
- 表达数据可视化(热图、火山图、PCA等)

### 专题五：表观基因组测序数据分析
**理论课(2h)**
- 表观基因组学基础知识
- ChIP-seq实验原理与分析流程
- ATAC-seq技术原理与应用
- 全基因组甲基化测序(WGBS, RRBS)原理
- 表观修饰与基因表达调控关系

**实践操作课(2h)**
- ChIP-seq数据处理与峰检测(MACS2)
- ATAC-seq数据分析与开放染色质区域鉴定
- 甲基化数据分析(Bismark等工具)
- 表观基因组数据可视化
- 多组学数据整合分析实例

### 专题六：单细胞测序技术与数据分析
**理论课(2h)**
- 单细胞测序技术平台与原理(10x Genomics, Drop-seq等)
- 单细胞RNA-seq数据分析流程
- 单细胞数据特有的质控与预处理方法
- 细胞聚类与鉴定策略
- 细胞轨迹分析与拟时序分析
- 单细胞多组学整合技术介绍

**实践操作课(2h)**
- Seurat/Scanpy进行单细胞RNA-seq数据分析
- 单细胞数据质控、标准化与批次效应校正
- 降维分析(PCA, t-SNE, UMAP)与可视化
- 细胞类型注释与标记基因识别
- 细胞通讯与轨迹分析实践

### 专题七：宏基因组测序与数据分析
**理论课(2h)**
- 宏基因组学基本概念与研究问题
- 宏基因组测序实验设计与挑战
- 宏基因组拼接与分箱(binning)策略
- 物种组成分析方法
- 功能注释与代谢通路分析
- 微生物组与宿主互作分析

**实践操作课(2h)**
- 宏基因组数据预处理与质控
- MetaPhlAn/Kraken进行物种分类分析
- MEGAHIT/MetaSPAdes进行宏基因组组装
- HUMAnN进行功能通路分析
- 宏基因组数据可视化与结果解读

### 专题八：高通量测序技术在植物保护中的应用
**理论课(2h)**
- 植物病原体基因组学：真菌、细菌、病毒和线虫
- 高通量测序在植物病害早期诊断与监测中的应用
- 植物-病原体互作的分子机制与全基因组分析
- 农药靶标与抗性机制的基因组学研究
- 植物有益微生物组与生物防治应用
- 环境DNA(eDNA)在入侵物种监测中的应用
- 植物抗病基因资源挖掘与分子标记辅助育种
- 基因编辑技术在植物抗病育种中的应用前景

**实践操作课(2h)**
- 植物病原体基因组组装与注释流程实践
- 病原微生物快速鉴定与系统发育分析
- 植物-病原体互作转录组数据分析
- 抗病/抗性相关基因的挖掘与功能预测
- 田间微生物多样性分析流程
- 综合案例分析：从高通量测序数据解析植物抗病机制

## 考核方式
1. 平时表现(20%)：课堂参与度和实践操作完成情况
2. 实践作业(30%)：每个专题的实践操作报告
3. 期末项目(50%)：独立完成一个高通量测序数据分析项目并撰写技术报告

## 参考资料
1. 《Bioinformatics Data Skills》，Vince Buffalo著
2. 《Computational Genome Analysis: An Introduction》，Richard C. Deonier等著
3. 《RNA-seq Data Analysis: A Practical Approach》，Eija Korpelainen等著
4. 《Plant Pathogen Genomics: Methods and Protocols》，Methods in Molecular Biology系列
5. 相关测序技术和分析工具的官方文档和教程
6. 植物保护与高通量测序相关前沿研究论文集