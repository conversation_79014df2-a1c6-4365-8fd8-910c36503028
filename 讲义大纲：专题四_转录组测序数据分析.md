# 专题四：转录组测序(RNA-seq)数据分析

## 理论课教学大纲 (2小时)

### 教学目标
- 理解转录组测序的实验设计与应用场景
- 掌握RNA-seq数据分析的基本流程
- 了解转录本比对和拼接的算法原理
- 掌握基因表达定量的不同方法及其适用场景
- 理解差异表达分析的统计原理
- 学习功能富集分析的基本方法

![RNA-seq数据分析工作流程概览](专题四_转录组测序(RNA-seq)数据分析/diagrams/rna_seq_workflow.svg)

### 第一部分：转录组测序实验设计与应用场景 (25分钟)
1. 转录组测序基本概念
   - 转录组定义与特点
   - 转录组vs基因组
   - 转录组的动态性与复杂性
   - 转录组研究的意义
   
   ![RNA-seq实验设计](专题四_转录组测序(RNA-seq)数据分析/diagrams/rna_seq_experiment_design.svg)

2. 转录组测序技术类型
   - 全转录组测序(Total RNA-seq)
   - mRNA测序(Poly-A选择)
   - 小RNA测序
   - 长非编码RNA测序
   - 靶向RNA测序
   - 单细胞RNA测序
   - 空间转录组测序

3. 转录组测序实验设计关键因素
   - 研究目标明确化
   - 样本选择与处理
     - 样本数量与统计功效
     - 生物学重复vs技术重复
     - 批次效应控制
   - RNA提取与质量控制
     - RNA完整性评估(RIN值)
     - 污染控制
   - 文库构建策略
     - 链特异性文库
     - 随机引物vs Poly-A选择
     - 去除核糖体RNA
   - 测序深度设计
     - 不同应用的深度要求
     - 饱和分析
     - 成本效益评估

4. 转录组测序主要应用场景
   - 基因表达谱分析
   - 差异表达基因鉴定
   - 可变剪接分析
   - 新转录本发现
   - 融合基因检测
   - RNA编辑位点鉴定
   - 非编码RNA研究
   - 单等位基因表达分析

### 第二部分：RNA-seq数据分析流程概述 (20分钟)
1. RNA-seq数据分析标准流程
   - 数据质控与预处理
   - 序列比对或直接定量
   - 转录本组装(可选)
   - 表达量定量
   - 差异表达分析
   - 功能注释与富集分析
   - 结果可视化与解释

2. 参考基因组依赖vs从头组装策略
   - 参考基因组依赖方法的优缺点
   - 从头组装方法的优缺点
   - 混合策略的应用场景
   - 方法选择建议

3. 计数矩阵vs表达矩阵
   - 原始计数数据特点
   - 标准化表达矩阵
   - 数据变换方法
   - 下游分析的数据选择

4. RNA-seq分析的计算资源需求
   - 存储需求评估
   - 计算需求评估
   - 并行计算策略
   - 云计算资源利用

5. RNA-seq分析的质量控制点
   - 原始数据质控
   - 比对质量评估
   - 表达量分布检查
   - 批次效应检测
   - 异常样本识别

### 第三部分：转录本比对和拼接 (30分钟)
1. RNA-seq比对的特殊挑战
   - 跨越内含子的比对
   - 可变剪接处理
   - 基因融合检测
   - 同源基因区分
   
   ![RNA-seq比对与转录本组装](专题四_转录组测序(RNA-seq)数据分析/diagrams/rna_seq_alignment_assembly.svg)

2. HISAT2算法原理
   - 分层索引结构
   - 剪接位点检测
   - 比对策略与算法
   - 与TopHat2的比较
   - 参数优化策略

3. STAR算法原理
   - 后缀数组索引
   - 二阶段比对策略
   - 非规范剪接检测
   - 嵌合读段检测
   - 计算资源需求

4. 其他RNA-seq比对工具
   - GSNAP
   - BBMap
   - Salmon/Kallisto(伪比对)
   - 工具选择建议

5. 转录本组装原理
   - 参考指导vs从头组装
   - 基于图的组装算法
   - 转录本重建挑战

6. StringTie算法原理
   - 流网络模型
   - 转录本组装策略
   - 表达量估计整合
   - 与Cufflinks的比较

7. 从头转录本组装
   - Trinity工作原理
   - De Bruijn图在转录本组装中的应用
   - 组装后处理与过滤
   - 组装质量评估

### 第四部分：基因表达定量方法 (25分钟)
1. 表达定量的基本概念
   - 读段计数vs覆盖度
   - 基因级别vs转录本级别定量
   - 定量的挑战(多重比对、剪接变体等)
   
   ![表达量定量方法](专题四_转录组测序(RNA-seq)数据分析/diagrams/expression_quantification_methods.svg)

2. 计数方法
   - 唯一比对vs多重比对
   - 交叉计数策略
   - 特征重叠处理
   - HTSeq-count原理
   - featureCounts原理

3. 表达量标准化方法
   - 总读段数标准化(RPM)
   - RPKM/FPKM标准化
     - 计算原理
     - 适用场景
     - 局限性
   - TPM标准化
     - 计算原理
     - 与FPKM的区别
     - 优势与适用场景
   - 相对表达量vs绝对表达量

4. 转录本定量方法
   - 基于比对的方法(Cufflinks, StringTie)
   - 基于k-mer的方法(Salmon, Kallisto)
     - 伪比对原理
     - EM算法在表达定量中的应用
     - 计算效率优势
   - 方法选择建议

5. 表达定量质量评估
   - 技术重复一致性
   - 生物学重复变异性
   - 表达分布检查
   - 批次效应评估

### 第五部分：差异表达分析统计原理 (25分钟)
1. 差异表达分析基本概念
   - 统计假设检验框架
   - 多重检验校正
   - 假阳性率(FDR)控制
   - 统计功效与样本量
   
   ![差异表达分析](专题四_转录组测序(RNA-seq)数据分析/diagrams/differential_expression_analysis.svg)

2. RNA-seq计数数据的统计特性
   - 离散计数数据特点
   - 过度离散现象
   - 低表达基因的挑战
   - 异方差性

3. DESeq2原理
   - 负二项分布模型
   - 离散度估计方法
   - 收缩估计策略
   - 异常值处理
   - 批次效应校正

4. edgeR原理
   - 负二项分布模型
   - 经验贝叶斯框架
   - 精确检验vs似然比检验
   - 标签加权策略

5. limma-voom原理
   - 计数数据转换
   - 线性模型框架
   - 经验贝叶斯调节
   - 复杂实验设计处理

6. 差异表达分析结果解读
   - 火山图解读
   - MA图解读
   - 差异表达基因聚类分析
   - 结果验证策略(qPCR等)

### 第六部分：功能富集分析方法 (15分钟)
1. 功能注释数据库
   - 基因本体论(GO)
     - 结构与组织
     -

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握HISAT2/STAR进行转录组比对的方法
- 学习StringTie/Cufflinks进行转录本组装与定量的流程
- 了解DESeq2/edgeR进行差异表达分析的操作步骤
- 实践ClusterProfiler进行功能富集分析
- 学会使用多种方法进行表达数据可视化

### 第一部分：HISAT2/STAR进行转录组比对 (30分钟)
1. 参考基因组与注释文件准备
   - 参考基因组下载
   - 基因注释文件(GTF/GFF)获取
   - 文件格式检查与处理

2. HISAT2比对实践
   - HISAT2安装与配置
   - 索引构建命令
     - 剪接位点提取
     - 外显子提取
     - 索引构建参数
   - 比对命令详解
     - 单端vs双端测序参数
     - 链特异性参数设置
     - 多线程设置
   - 比对结果处理
   - 实际操作演示

3. STAR比对实践
   - STAR安装与配置
   - 索引构建命令
     - 内存需求估计
     - 索引参数优化
   - 比对命令详解
     - 单端vs双端参数
     - 链特异性设置
     - 输出选项配置
   - 比对结果处理
   - 实际操作演示

4. 比对质量评估
   - 比对率统计
   - 唯一比对vs多重比对比例
   - 剪接位点检测统计
   - 链特异性评估
   - RSeQC工具使用

### 第二部分：StringTie/Cufflinks进行转录本组装与定量 (30分钟)
1. StringTie转录本组装与定量
   - StringTie安装与配置
   - 基本命令格式
   - 参考指导组装
     - 参数设置
     - GTF文件使用
   - 从头组装模式
   - 多样本合并策略
     - stringtie --merge使用
     - 参考转录本整合
   - 表达量估计
     - TPM/FPKM输出
     - 覆盖度文件生成
   - 实际操作演示

2. Cufflinks转录本组装与定量
   - Cufflinks安装与配置
   - 基本命令格式
   - 关键参数设置
   - 转录本组装
   - 表达量估计
   - Cuffmerge使用
   - 实际操作演示

3. 转录本组装质量评估
   - 与参考注释比较
   - 新转录本评估
   - GffCompare工具使用
   - 组装可视化检查

4. 表达矩阵生成
   - StringTie表达矩阵提取
     - prepDE.py脚本使用
   - 表达矩阵格式转换
   - 表达数据初步探索
   - 实际操作演示

### 第三部分：DESeq2/edgeR进行差异表达分析 (30分钟)
1. R环境准备
   - R/RStudio安装
   - Bioconductor配置
   - 必要包安装
   - 工作环境设置

2. DESeq2差异表达分析
   - 数据导入
     - 计数矩阵格式要求
     - 样本信息表格式
   - DESeqDataSet对象创建
   - 数据预处理
     - 低表达基因过滤
     - 数据转换
     - 异常值检测
   - 差异表达分析
     - 设计矩阵构建
     - DESeq函数运行
     - 结果提取与过滤
   - 批次效应校正
   - 结果可视化
     - MA图
     - 火山图
     - 热图
   - 实际操作演示

3. edgeR差异表达分析
   - 数据导入与预处理
   - DGEList对象创建
   - 数据标准化
   - 离散度估计
   - 差异表达分析
     - 精确检验
     - 广义线性模型
   - 结果提取与过滤
   - 结果可视化
   - 实际操作演示

4. 差异表达结果解读
   - 结果表格字段解释
   - 显著性阈值设定
   - 上调/下调基因筛选
   - 结果导出与保存
   - 与DESeq2结果比较

### 第四部分：ClusterProfiler进行功能富集分析 (20分钟)
1. ClusterProfiler安装与配置
   - 包安装
   - 注释数据库准备
   - 物种设置

2. GO富集分析
   - 基因ID转换
   - enrichGO函数使用
   - 参数设置与优化
   - 结果解读
   - 可视化方法
     - 条形图
     - 点图
     - 网络图
   - 实际操作演示

3. KEGG通路富集分析
   - enrichKEGG函数使用
   - 参数设置与优化
   - 结果解读
   - 通路图可视化
   - 实际操作演示

4. 基因集富集分析(GSEA)
   - 排序列表准备
   - GSEA函数使用
   - 结果解读
   - 富集图可视化
   - 实际操作演示

5. 富集结果整合与解读
   - 多种富集结果比较
   - 生物学意义解释
   - 结果导出与报告

### 第五部分：表达数据可视化 (10分钟)
1. 热图绘制
   - pheatmap包使用
   - 数据准备与转换
   - 聚类参数设置
   - 注释信息添加
   - 配色方案选择
   - 实际操作演示

2. 火山图绘制
   - EnhancedVolcano包使用
   - 数据准备
   - 参数设置
   - 标签添加
   - 实际操作演示

3. PCA分析与可视化
   - PCA原理简介
   - prcomp函数使用
   - ggplot2绘图
   - 结果解读
   - 实际操作演示

4. 其他可视化方法
   - 箱线图
   - 小提琴图
   - MA图
   - 基因表达趋势图
   - 实际操作演示

## 课后作业
1. 使用HISAT2或STAR对提供的RNA-seq数据进行比对，并评估比对质量
2. 使用StringTie进行转录本组装与表达量定量，生成表达矩阵
3. 使用DESeq2或edgeR进行差异表达分析，并绘制可视化图表
4. 对差异表达基因进行GO和KEGG富集分析，解释生物学意义
5. 撰写完整的分析报告，包括方法、结果和解释

## 参考资料
1. Kim, D., Langmead, B., & Salzberg, S. L. (2015). HISAT: a fast spliced aligner with low memory requirements. Nature Methods, 12(4), 357-360.
2. Dobin, A., Davis, C. A., Schlesinger, F., Drenkow, J., Zaleski, C., Jha, S., ... & Gingeras, T. R. (2013). STAR: ultrafast universal RNA-seq aligner. Bioinformatics, 29(1), 15-21.
3. Pertea, M., Pertea, G. M., Antonescu, C. M., Chang, T. C., Mendell, J. T., & Salzberg, S. L. (2015). StringTie enables improved reconstruction of a transcriptome from RNA-seq reads. Nature Biotechnology, 33(3), 290-295.
4. Love, M. I., Huber, W., & Anders, S. (2014). Moderated estimation of fold change and dispersion for RNA-seq data with DESeq2. Genome Biology, 15(12), 550.
5. Robinson, M. D., McCarthy, D. J., & Smyth, G. K. (2010). edgeR: a Bioconductor package for differential gene expression analysis of digital gene expression data. Bioinformatics, 26(1), 139-140.
6. Yu, G., Wang, L. G., Han, Y., & He, Q. Y. (2012). clusterProfiler: an R package for comparing biological themes among gene clusters. OMICS: A Journal of Integrative Biology, 16(5), 284-287.
7. Conesa, A., Madrigal, P., Tarazona, S., Gomez-Cabrero, D., Cervera, A., McPherson, A., ... & Mortazavi, A. (2016). A survey of best practices for RNA-seq data analysis. Genome Biology, 17(1), 13.