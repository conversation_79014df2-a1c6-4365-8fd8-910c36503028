# 专题八：高通量测序技术在植物保护中的应用

## 理论课教学大纲 (2小时)

### 教学目标
- 理解植物病原体基因组学研究的基本方法与应用
- 掌握高通量测序在植物病害早期诊断与监测中的应用原理
- 了解植物-病原体互作的分子机制与全基因组分析方法
- 掌握农药靶标与抗性机制的基因组学研究方法
- 理解植物有益微生物组与生物防治的研究策略
- 了解环境DNA在入侵物种监测中的应用
- 掌握植物抗病基因资源挖掘与分子标记辅助育种的基本原理
- 了解基因编辑技术在植物抗病育种中的应用前景

### 第一部分：植物病原体基因组学 (30分钟)
1. 植物病原体类型与特点
   - 真菌病原体
     - 主要类群与分类
     - 基因组特点
     - 致病机制多样性
   - 细菌病原体
     - 主要类群与分类
     - 基因组特点
     - 分泌系统与致病因子
   - 病毒病原体
     - 主要类群与分类
     - 基因组特点
     - 复制与传播机制
   - 线虫病原体
     - 主要类群与分类
     - 基因组特点
     - 寄生策略

2. 植物病原体基因组测序策略
   - 真菌/细菌基因组测序
     - 培养与DNA提取挑战
     - 测序平台选择
     - 组装与注释策略
   - 病毒基因组测序
     - 病毒富集方法
     - 小基因组测序策略
     - 变异体检测
   - 非培养微生物基因组获取
     - 宏基因组方法
     - 单细胞基因组学
     - 基因组拼接与分箱

3. 植物病原体比较基因组学
   - 核心基因组与泛基因组分析
   - 种群结构与进化分析
   - 水平基因转移检测
   - 致病相关基因识别
   - 宿主特异性决定因素

4. 植物病原体功能基因组学
   - 致病因子预测方法
     - 分泌蛋白预测
     - 效应蛋白识别
     - 毒素合成基因簇
   - 转录组分析应用
     - 感染过程基因表达
     - 调控网络重建
   - 蛋白质组学应用
   - 代谢组学应用

### 第二部分：高通量测序在植物病害早期诊断与监测中的应用 (20分钟)
1. 传统植物病害诊断方法的局限性
   - 形态学鉴定局限
   - 血清学方法局限
   - PCR方法局限
   - 新方法需求

2. 基于测序的植物病害诊断方法
   - 靶向测序诊断
     - 特异性引物设计
     - 多重PCR技术
     - 高通量测序检测
   - 宏基因组诊断方法
     - 无偏好性检测
     - 未知病原体发现
     - 多病原体同时检测
   - 宏转录组诊断方法
     - 活跃病原体检测
     - 植物防御反应分析

3. 高通量测序在植物病害监测中的应用
   - 田间监测系统设计
     - 采样策略
     - 样本处理流程
     - 数据分析流程
   - 区域性病害监测网络
     - 数据整合方法
     - 预警系统建立
   - 全球植物病害监测
     - 国际合作框架
     - 数据共享平台

4. 早期诊断与监测的数据分析方法
   - 快速鉴定算法
   - 变异检测与溯源
   - 流行病学分析
   - 风险评估模型

### 第三部分：植物-病原体互作的分子机制与全基因组分析 (25分钟)
1. 植物免疫系统基础
   - PAMP触发的免疫(PTI)
   - 效应蛋白触发的免疫(ETI)
   - 系统获得性抗性(SAR)
   - 激素信号网络

2. 病原体致病策略
   - 效应蛋白功能多样性
   - 毒素与致病因子
   - 免疫抑制机制
   - 病原体适应性进化

3. 植物-病原体互作的组学研究方法
   - 双转录组分析
     - 同时捕获宿主与病原体转录组
     - 数据分离与分析策略
     - 互作网络重建
   - 蛋白质组学方法
     - 互作蛋白鉴定
     - 翻译后修饰分析
   - 代谢组学方法
     - 防御代谢物分析
     - 信号分子检测

4. 全基因组关联分析在互作研究中的应用
   - 植物抗性基因组变异分析
   - 病原体毒力基因组变异分析
   - 基因型-表型关联
   - 共进化模式识别

5. 植物-病原体互作研究案例
   - 水稻-稻瘟病菌互作
   - 小麦-条锈病菌互作
   - 番茄-青枯病菌互作
   - 模式植物互作系统

### 第四部分：农药靶标与抗性机制的基因组学研究 (20分钟)
1. 农药作用机制与靶标
   - 主要农药类别与作用机制
   - 分子靶标多样性
   - 选择性机制
   - 新靶标发现策略

2. 病原体农药抗性机制
   - 靶标位点突变
   - 解毒酶系统
   - 外排转运系统
   - 渗透性改变
   - 代谢途径改变

3. 基因组学在农药靶标研究中的应用
   - 比较基因组识别保守靶标
   - 转录组分析药物响应
   - 功能基因组验证靶标
   - 结构基因组学与药物设计

4. 抗性监测与管理的基因组学方法
   - 抗性标记开发
   - 高通量抗性检测
   - 抗性进化预测
   - 抗性管理策略优化

5. 基于组学的新型农药开发
   - 靶标筛选策略
   - 高通量药效评价
   - 作用机制验证
   - 环境友好型农药设计

### 第五部分：植物有益微生物组与生物防治应用 (20分钟)
1. 植物微生物组概述
   - 根际微生物组
   - 叶际微生物组
   - 内生菌群
   - 微生物组功能多样性

2. 有益微生物的作用机制
   - 直接拮抗作用
   - 竞争作用
   - 诱导系统抗性
   - 促进植物生长
   - 改善环境胁迫耐受性

3. 微生物组研究方法
   - 宏基因组测序应用
   - 宏转录组分析
   - 功能验证方法
   - 微生物互作网络分析

4. 基于微生物组的生物防治策略
   - 单菌株生防制剂
   - 微生物组合制剂
   - 微生物组调控技术
   - 合成微生物组设计

5. 微生物组工程与应用案例
   - 拮抗细菌应用
   - 有益真菌应用
   - 内生菌应用
   - 微生物肥料与生防制剂

### 第六部分：环境DNA在入侵物种监测中的应用 (15分钟)
1. 环境DNA(eDNA)技术原理
   - eDNA来源与特点
   - 采样与提取方法
   - 稳定性与降解动力学
   - 检测限与灵敏度

2. eDNA在植物保护中的应用
   - 入侵病原体早期检测
   - 检疫性有害生物监测
   - 生物多样性评估
   - 生态系统健康监测

3. eDNA数据分析方法
   - 靶向检测方法
     - 特异性引物设计
     - 数字PCR技术
   - 宏条形码方法
     - 通用引物选择
     - 生物信息学分析流程
   - 宏基因组方法
     - 无PCR扩增偏好性
     - 数据分析挑战

4. eDNA监测系统建立
   - 采样网络设计
   - 标准操作流程
   - 数据库建设
   - 预警机制建立

### 第七部分：植物抗病基因资源挖掘与分子标记辅助育种 (15分钟)
1. 植物抗病基因类型与功能
   - NBS-LRR类抗病基因
   - 受体激酶类抗病基因
   - 其他类型抗病基因
   - 抗病基因作用机制

2. 抗病基因资源挖掘方法
   - 图位克隆策略
   - 全基因组关联分析
   - 比较基因组学方法
   - 功能验证技术

3. 分子标记开发与应用
   - 主要分子标记类型
     - SNP标记
     - SSR标记
     - InDel标记
   - 高通量基因分型技术
   - 标记辅助选择策略
   - 基因聚合育种

4. 抗病育种案例分析
   - 水稻抗稻瘟病育种
   - 小麦抗锈病育种
   - 马铃薯抗晚疫病育种
   - 抗病多基因聚合育种

### 第八部分：基因编辑技术在植物抗病育种中的应用前景 (15分钟)
1. 植物基因编辑技术概述
   - CRISPR/Cas系统原理
   - 基因敲除策略
   - 基因替换技术
   - 碱基编辑与点突变
   - 表观基因组编辑

2. 基因编辑在植物抗病育种中的应用
   - 敏感基因敲除
     - 易感基因识别
     - 靶点选择策略
   - 抗病基因改良
     - 抗谱拓宽
     - 持久性增强
   - 免疫系统组件优化
   - 多基因编辑策略

3. 基因编辑技术挑战与解决方案
   - 脱靶效应控制
   - 转化效率提高
   - 无外源DNA编辑
   - 组织特异性表达

4. 基因编辑抗病作物案例
   - 抗白粉病小麦
   - 抗细菌性枯萎病番茄
   - 抗病毒作物
   - 广谱抗病作物设计

5. 基因编辑作物的监管与社会接受度
   - 全球监管框架
   - 风险评估方法
   - 检测技术发展
   - 公众沟通策略

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握植物病原体基因组组装与注释流程
- 学习病原微生物快速鉴定与系统发育分析方法
- 了解植物-病原体互作转录组数据分析流程
- 实践抗病/抗性相关基因的挖掘与功能预测
- 学习田间微生物多样性分析流程
- 综合案例分析：从高通量测序数据解析植物抗病机制

### 第一部分：植物病原体基因组组装与注释流程 (30分钟)
1. 病原体基因组数据预处理
   - 质量控制特殊考虑
   - 宿主DNA去除
   - 污染序列过滤
   - 数据质量评估
   - 实际操作演示

2. 基因组组装
   - SPAdes使用
     - 参数优化
     - 组装运行
     - 结果评估
   - 长读长数据整合(可选)
   - 组装结果评估
     - QUAST使用
     - BUSCO完整性评估
   - 实际操作演示

3. 基因组注释
   - 基因预测
     - Prokka(细菌)
     - AUGUSTUS/MAKER(真菌)
     - 参数优化
   - 功能注释
     - InterProScan使用
     - BLAST注释
     - GO/KEGG注释
   - 特殊功能元件注释
     - 分泌蛋白预测
     - 效应蛋白预测
     - 次级代谢产物基因簇
   - 注释结果可视化
   - 实际操作演示

### 第二部分：病原微生物快速鉴定与系统发育分析 (30分钟)
1. 基于标记基因的快速鉴定
   - 标记基因提取
     - 16S/18S/ITS区域
     - 多基因位点
   - BLAST分析
   - 序列比对
     - MUSCLE/MAFFT使用
     - 比对结果评估
   - 实际操作演示

2. 系统发育分析
   - 系统发育模型选择
     - ModelTest使用
   - 系统发育树构建
     - RAxML/IQ-TREE使用
     - 参数设置
     - 自展支持度分析
   - 系统发育树可视化
     - iTOL使用
     - FigTree使用
   - 实际操作演示

3. 全基因组比较分析
   - 平均核苷酸同一性(ANI)计算
   - 核心基因组分析
     - Roary使用(细菌)
     - GET_HOMOLOGUES使用
   - 全基因组SNP分析
     - Snippy使用
     - SNP树构建
   - 基因组共线性分析
   - 实际操作演示

4. 种群遗传学分析
   - 变异检测与过滤
   - 连锁不平衡分析
   - 群体结构分析
   - 选择压力分析
   - 简单演示

### 第三部分：植物-病原体互作转录组数据分析 (30分钟)
1. 双转录组数据处理策略
   - 数据分离方法
     - 参考基因组映射分离
     - k-mer分类方法
   - 质量控制特殊考虑
   - 比对策略
   - 表达定量方法
   - 实际操作演示

2. 植物响应分析
   - 差异表达分析
     - DESeq2使用
     - 参数优化
   - 功能富集分析
     - GO富集
     - KEGG通路富集
   - 防御相关基因分析
     - PR蛋白
     - 抗病信号通路
   - 实际操作演示

3. 病原体响应分析
   - 病原体基因表达模式
   - 致病因子表达分析
   - 效应蛋白表达动态
   - 实际操作演示

4. 互作网络分析
   - 共表达网络构建
   - 关键调控因子识别
   - 植物-病原体互作预测
   - 简单演示

### 第四部分：抗病/抗性相关基因的挖掘与功能预测 (20分钟)
1. 抗病基因挖掘
   - 基于同源性的抗病基因识别
     - NBS-LRR基因家族分析
     - 保守结构域搜索
   - 候选基因筛选策略
   - 功能验证设计
   - 实际操作演示

2. 抗性相关基因分析
   - 农药靶标基因分析
   - 抗性突变位点识别
   - 抗性机制预测
   - 实际操作演示

3. 基因功能预测
   - 蛋白质结构预测
     - 同源模建
     - 结构评估
   - 蛋白质-蛋白质互作预测
   - 蛋白质-小分子互作预测
   - 简单演示

4. 分子标记开发
   - SNP标记设计
   - KASP引物设计
   - 高通量基因分型策略
   - 简单演示

### 第五部分：田间微生物多样性分析流程 (10分钟)
1. 扩增子数据分析
   - QIIME2使用
     - 数据导入
     - 质量过滤
     - 特征表构建
     - 分类学注释
   - 多样性分析
     - α多样性计算
     - β多样性计算
   - 差异丰度分析
   - 简单演示

2. 宏基因组功能分析
   - 功能注释流程
   - 微生物功能预测
   - 有益功能筛选
   - 简单演示

3. 微生物互作网络
   - 网络构建方法
   - 关键物种识别
   - 网络特性分析
   - 简单演示

### 第六部分：综合案例分析 (10分钟)
1. 案例背景介绍
   - 研究问题设定
   - 数据类型描述
   - 分析目标确定

2. 多组学数据整合分析
   - 基因组-转录组整合
   - 微生物组-转录组整合
   - 表型-基因型关联

3. 植物抗病机制解析
   - 关键发现展示
   - 机制模型构建
   - 验证策略讨论

4. 应用价值与展望
   - 育种应用前景
   - 病害防控策略
   - 未来研究方向

## 课后作业
1. 选择一种植物病原体，完成基因组组装、注释与比较基因组分析
2. 分析植物-病原体互作转录组数据，识别关键防御基因与致病因子
3. 从基因组数据中挖掘抗病/抗性相关基因，并进行功能预测
4. 分析田间微生物多样性数据，评估生物防治潜力
5. 撰写综合分析报告，从多组学角度解析植物抗病机制

## 参考资料
1. Savary, S., Willocquet, L., Pethybridge, S. J., Esker, P., McRoberts, N., & Nelson, A. (2019). The global burden of pathogens and pests on major food crops. Nature Ecology & Evolution, 3(3), 430-439.
2. Jones, J. D., & Dangl, J. L. (2006). The plant immune system. Nature, 444(7117), 323-329.
3. Möller, M., & Stukenbrock, E. H. (2017). Evolution and genome architecture in fungal plant pathogens. Nature Reviews Microbiology, 15(12), 756-771.
4. Mahlein, A. K., Kuska, M. T., Behmann, J., Polder, G., & Walter, A. (2018). Hyperspectral sensors and imaging technologies in phytopathology: state of the art. Annual Review of Phytopathology, 56, 535-558.
5. Borrelli, G. M., Mazzucotelli, E., Marone, D., Crosatti, C., Michelotti, V., Valè, G., & Mastrangelo, A. M. (2018). Regulation and evolution of NLR genes: a close interconnection for plant immunity. International Journal of Molecular Sciences, 19(6), 1662.
6. Dong, O. X., & Ronald, P. C. (2019). Genetic engineering for disease resistance in plants: recent progress and future perspectives. Plant Physiology, 180(1), 26-38.
7. Compant, S., Samad, A., Faist, H., & Sessitsch, A. (2019). A review on the plant microbiome: ecology, functions, and emerging trends in microbial application. Journal of Advanced Research, 19, 29-37.
8. Thomma, B. P., Seidl, M. F., Shi-Kunne, X., Cook, D. E., Bolton, M. D., van Kan, J. A., & Faino, L. (2016). Mind the gap; seven reasons to close fragmented genome assemblies. Fungal Genetics and Biology, 90, 24-30.