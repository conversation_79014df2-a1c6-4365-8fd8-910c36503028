# 专题五：表观基因组测序数据分析

## 理论课教学大纲 (2小时)

### 教学目标
- 理解表观基因组学的基本概念与研究意义
- 掌握ChIP-seq实验原理与分析流程
- 了解ATAC-seq技术原理与应用
- 掌握全基因组甲基化测序原理与分析方法
- 理解表观修饰与基因表达调控的关系

### 第一部分：表观基因组学基础知识 (25分钟)
1. 表观基因组学概述
   - 表观遗传学定义与特点
   - 表观基因组学研究内容
   - 表观修饰的可逆性与动态性
   - 表观基因组学在生物学研究中的意义

2. 主要表观遗传修饰类型
   - DNA甲基化
     - CpG甲基化
     - 非CpG甲基化
     - 5mC, 5hmC, 5fC, 5caC
   - 组蛋白修饰
     - 乙酰化(H3K27ac, H3K9ac等)
     - 甲基化(H3K4me3, H3K27me3, H3K9me3等)
     - 磷酸化、泛素化等其他修饰
   - 染色质开放状态
   - 非编码RNA调控
   - 核小体定位与组蛋白变体

3. 表观修饰与基因调控
   - 启动子区域表观修饰特征
   - 增强子区域表观修饰特征
   - 异染色质与常染色质
   - 表观修饰与转录因子结合
   - 表观修饰与基因表达的关系

4. 表观基因组学研究技术概述
   - 基于芯片的技术(ChIP-chip, MeDIP-chip)
   - 基于测序的技术(ChIP-seq, ATAC-seq, BS-seq等)
   - 单细胞表观基因组学技术
   - 技术优缺点比较

### 第二部分：ChIP-seq实验原理与分析流程 (30分钟)
1. ChIP-seq实验原理
   - 染色质免疫沉淀(ChIP)基本步骤
     - 交联固定
     - 染色质片段化
     - 免疫沉淀
     - 交联逆转
     - 文库构建
   - 抗体选择的关键因素
   - 实验对照设计(Input, IgG等)
   - 技术变体(ChIP-exo, ChIP-nexus等)

2. ChIP-seq实验设计考虑因素
   - 生物学重复设计
   - 测序深度要求
   - 片段大小选择
   - 抗体特异性验证
   - 常见技术挑战与解决方案

3. ChIP-seq数据分析流程
   - 数据质控与预处理
   - 参考基因组比对
     - 比对工具选择(Bowtie2, BWA等)
     - 多重比对处理
     - 重复序列处理
   - 峰检测(Peak calling)
     - 信号富集区域识别
     - 背景模型构建
     - 统计显著性评估
   - 差异结合分析
   - 结合位点注释
   - 结合模式分析
   - 转录因子结合位点预测

4. 主要峰检测算法原理
   - MACS2算法
     - 动态λ背景模型
     - 峰形状建模
     - q值计算
   - HOMER算法
   - SICER/EPIC算法(适用于宽峰)
   - 算法选择建议

5. ChIP-seq数据质量评估
   - 信噪比(SNR)
   - 片段长度分布
   - 重复性评估
   - 峰数量与分布
   - 与已知结合模式比较
   - 富集模式分析

6. ChIP-seq结果解读与下游分析
   - 峰与基因关联
   - 结合位点序列分析
   - 转录因子结合模式识别
   - 与基因表达数据整合
   - 功能富集分析

### 第三部分：ATAC-seq技术原理与应用 (25分钟)
1. ATAC-seq技术原理
   - 转座酶介导的标记技术
   - Tn5转座酶作用机制
   - 开放染色质区域优先标记
   - 与DNase-seq、MNase-seq的比较
   - 技术优势与局限性

2. ATAC-seq实验设计
   - 样本制备关键步骤
   - 细胞数量要求
   - 转座酶浓度优化
   - 测序深度考虑
   - 质控指标

3. ATAC-seq数据分析流程
   - 数据质控特殊考虑
     - 线粒体DNA污染评估
     - 插入片段大小分布分析
   - 参考基因组比对
   - 开放区域检测
     - 峰检测工具(MACS2等)
     - 参数优化
   - 差异开放区域分析
   - 染色质可及性评分

4. ATAC-seq数据特征与质量评估
   - 核小体定位信号
   - 转录起始位点(TSS)富集
   - 插入片段大小周期性
   - 与已知开放区域比较

5. ATAC-seq结果解读与应用
   - 开放染色质区域注释
   - 转录因子足迹分析(Footprinting)
   - 染色质状态预测
   - 与表观修饰数据整合
   - 调控元件识别

### 第四部分：全基因组甲基化测序原理 (25分钟)
1. DNA甲基化基础
   - 甲基化位点类型(CpG, CHG, CHH)
   - CpG岛与CpG岛岸
   - 甲基化在不同基因组区域的分布
   - 甲基化与基因表达调控

2. 甲基化测序技术类型
   - 全基因组亚硫酸氢盐测序(WGBS)
     - 原理与工作流程
     - 覆盖度与分辨率
     - 优势与局限性
   - 简化表示亚硫酸氢盐测序(RRBS)
     - 原理与工作流程
     - 与WGBS的比较
     - 适用场景
   - 甲基化芯片技术
   - 甲基化免疫沉淀测序(MeDIP-seq)
   - 单碱基分辨率技术比较

3. 亚硫酸氢盐转换原理
   - 化学反应机制
   - 非甲基化胞嘧啶vs甲基化胞嘧啶
   - 转换效率评估
   - 技术偏好性与校正

4. 甲基化测序数据分析流程
   - 数据质控特殊考虑
     - 转换效率评估
     - 序列复杂度降低问题
   - 参考基因组比对挑战
     - 三字母比对
     - 专用比对工具(Bismark, BSMAP等)
   - 甲基化水平计算
   - 差异甲基化区域(DMR)检测
     - 统计方法
     - 空间相关性考虑
   - 甲基化模式分析

5. 甲基化数据解读与应用
   - 全基因组甲基化模式分析
   - 基因区域甲基化特征
   - 差异甲基化与疾病关联
   - 与基因表达数据整合
   - 甲基化年龄预测

### 第五部分：表观修饰与基因表达调控关系 (15分钟)
1. 表观修饰组合模式
   - 染色质状态定义
   - 组蛋白修饰组合模式
   - 表观修饰与染色质开放状态的关系
   - 表观修饰与DNA甲基化的相互作用

2. 表观修饰与基因表达的关联分析
   - 启动子区域表观修饰与表达
   - 基因体区域表观修饰与表达
   - 增强子区域表观修饰与表达
   - 表观修饰与转录因子结合的协同作用

3. 多组学数据整合分析方法
   - 相关性分析
   - 聚类分析
   - 网络分析
   - 机器学习方法

4. 表观基因组与疾病研究
   - 癌症表观基因组特征
   - 发育疾病与表观修饰
   - 环境因素对表观基因组的影响
   - 表观治疗靶点识别

5. 表观基因组学前沿研究方向
   - 单细胞表观基因组学
   - 时空表观基因组学
   - 表观编辑技术
   - 计算表观基因组学

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握ChIP-seq数据处理与峰检测的方法
- 学习ATAC-seq数据分析与开放染色质区域鉴定的流程
- 了解甲基化数据分析的基本操作
- 实践表观基因组数据可视化方法
- 学习多组学数据整合分析的基本技术

### 第一部分：ChIP-seq数据处理与峰检测 (40分钟)
1. ChIP-seq数据预处理
   - 数据质控与过滤
   - 比对参数优化
   - BAM文件处理
     - 排序与索引
     - 重复序列标记与去除
     - 质量过滤
   - 比对质量评估
   - 实际操作演示

2. MACS2峰检测
   - MACS2安装与配置
   - 基本命令格式
   - 关键参数设置
     - q值/p值阈值
     - 峰检测模式(narrow vs broad)
     - 片段大小估计
     - 有效基因组大小
   - 不同类型ChIP-seq的参数优化
     - 转录因子ChIP-seq
     - 组蛋白修饰ChIP-seq
   - 输出文件解读
   - 实际操作演示

3. ChIP-seq质量评估
   - phantompeakqualtools使用
   - ChIPQC包使用
   - 富集评分计算
   - 重复性评估
   - 实际操作演示

4. 差异结合分析
   - DiffBind包使用
   - 数据标准化方法
   - 差异分析参数设置
   - 结果可视化
   - 实际操作演示

5. 峰注释与下游分析
   - ChIPseeker包使用
   - 峰与基因关联
   - 功能富集分析
   - 结合位点序列分析
   - 实际操作演示

### 第二部分：ATAC-seq数据分析与开放染色质区域鉴定 (30分钟)
1. ATAC-seq数据预处理
   - 数据质控特殊考虑
   - 线粒体序列过滤
   - 比对参数优化
   - 插入片段大小分析
   - 实际操作演示

2. ATAC-seq峰检测
   - MACS2参数优化
   - 片段大小选择
   - 峰过滤策略
   - 输出文件解读
   - 实际操作演示

3. ATAC-seq数据质量评估
   - TSS富集分析
   - 插入片段大小分布分析
   - 与已知开放区域比较
   - 实际操作演示

4. 差异开放区域分析
   - 计数矩阵生成
   - 差异分析方法
   - 结果可视化
   - 实际操作演示

5. 转录因子足迹分析
   - HINT-ATAC工具使用
   - 足迹信号提取
   - 转录因子结合位点预测
   - 结果解读
   - 简单演示

### 第三部分：甲基化数据分析 (30分钟)
1. Bismark工具使用
   - Bismark安装与配置
   - 参考基因组准备
   - 比对命令详解
   - 甲基化提取
   - 报告解读
   - 实际操作演示

2. 甲基化数据处理与分析
   - 覆盖度过滤
   - 甲基化水平计算
   - 样本间相关性分析
   - 全基因组甲基化模式分析
   - 实际操作演示

3. 差异甲基化分析
   - DSS包使用
   - methylKit包使用
   - DMR检测参数设置
   - 结果过滤与注释
   - 实际操作演示

4. 甲基化数据可视化
   - 甲基化热图
   - 基因区域甲基化分布
   - 差异甲基化区域可视化
   - 实际操作演示

### 第四部分：表观基因组数据可视化 (10分钟)
1. 基因组浏览器可视化
   - IGV加载表观基因组数据
   - 轨道设置与调整
   - 多样本比较视图
   - 截图与导出
   - 实际操作演示

2. deepTools可视化
   - computeMatrix命令
   - plotHeatmap命令
   - plotProfile命令
   - 参数优化
   - 实际操作演示

3. 其他可视化方法
   - EnrichedHeatmap包
   - karyoploteR包
   - 自定义R可视化
   - 实际操作演示

### 第五部分：多组学数据整合分析 (10分钟)
1. 表观修饰与基因表达关联分析
   - 数据准备与格式转换
   - 相关性分析
   - 散点图可视化
   - 实际操作演示

2. 多种表观修饰整合分析
   - 染色质状态预测
   - ChromHMM工具简介
   - 结果解读
   - 简单演示

3. 表观基因组与转录组数据整合
   - 差异表达与差异表观修饰关联
   - 整合可视化方法
   - 功能解读
   - 实际操作演示

4. 多组学数据整合案例分析
   - 研究问题设定
   - 分析策略设计
   - 结果解读
   - 生物学意义探讨

## 课后作业
1. 使用MACS2对提供的ChIP-seq数据进行峰检测，并进行结果注释与可视化
2. 分析ATAC-seq数据，鉴定开放染色质区域并与基因表达数据进行关联分析
3. 使用Bismark处理甲基化测序数据，分析基因组甲基化模式
4. 整合ChIP-seq、ATAC-seq和RNA-seq数据，探索表观修饰与基因表达的关系
5. 撰写完整的分析报告，包括方法、结果和生物学解释

## 参考资料
1. Bailey, T., Krajewski, P., Ladunga, I., Lefebvre, C., Li, Q., Liu, T., ... & Zhang, J. (2013). Practical guidelines for the comprehensive analysis of ChIP-seq data. PLoS Computational Biology, 9(11), e1003326.
2. Buenrostro, J. D., Giresi, P. G., Zaba, L. C., Chang, H. Y., & Greenleaf, W. J. (2013). Transposition of native chromatin for fast and sensitive epigenomic profiling of open chromatin, DNA-binding proteins and nucleosome position. Nature Methods, 10(12), 1213-1218.
3. Krueger, F., & Andrews, S. R. (2011). Bismark: a flexible aligner and methylation caller for Bisulfite-Seq applications. Bioinformatics, 27(11), 1571-1572.
4. Zhang, Y., Liu, T., Meyer, C. A., Eeckhoute, J., Johnson, D. S., Bernstein, B. E., ... & Liu, X. S. (2008). Model-based analysis of ChIP-Seq (MACS). Genome Biology, 9(9), R137.
5. Ramírez, F., Ryan, D. P., Grüning, B., Bhardwaj, V., Kilpert, F., Richter, A. S., ... & Manke, T. (2016). deepTools2: a next generation web server for deep-sequencing data analysis. Nucleic Acids Research, 44(W1), W160-W165.
6. Ernst, J., & Kellis, M. (2012). ChromHMM: automating chromatin-state discovery and characterization. Nature Methods, 9(3), 215-216.
7. Roadmap Epigenomics Consortium. (2015). Integrative analysis of 111 reference human epigenomes. Nature, 518(7539), 317-330.