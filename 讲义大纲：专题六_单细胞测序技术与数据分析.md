# 专题六：单细胞测序技术与数据分析

## 理论课教学大纲 (2小时)

### 教学目标
- 理解单细胞测序技术的基本原理与平台特点
- 掌握单细胞RNA-seq数据分析的基本流程
- 了解单细胞数据特有的质控与预处理方法
- 掌握细胞聚类与鉴定的基本策略
- 理解细胞轨迹分析与拟时序分析的原理
- 了解单细胞多组学整合技术的基本概念

### 第一部分：单细胞测序技术平台与原理 (30分钟)
1. 单细胞测序技术概述
   - 单细胞测序vs常规批量测序
   - 单细胞异质性研究的意义
   - 单细胞测序的应用领域
   - 技术发展历程与里程碑

2. 单细胞分离与捕获技术
   - 流式细胞分选(FACS)
   - 显微操作(Micromanipulation)
   - 微流控技术(Microfluidics)
   - 液滴法(Droplet-based)
   - 微孔板法(Well-based)
   - 技术比较与选择

3. 主要单细胞RNA-seq技术平台
   - 10x Genomics Chromium系统
     - GEM液滴形成原理
     - 细胞条形码与UMI技术
     - 文库构建流程
     - 技术参数与性能
   - Drop-seq/inDrop系统
     - 液滴形成原理
     - 条形码设计
     - 与10x Genomics的比较
   - Smart-seq2
     - 全长转录本捕获
     - 技术优势与局限性
   - BD Rhapsody
   - Parse Biosciences (SPLiT-seq)
   - 其他新兴平台简介

4. 单细胞文库构建关键技术
   - 细胞条形码(Cell Barcode)设计
   - 唯一分子标识符(UMI)原理
   - mRNA捕获策略(Poly-A vs 随机引物)
   - 全长vs 3'端测序
   - 链特异性文库构建
   - 扩增偏好性控制

5. 单细胞测序实验设计考虑因素
   - 细胞数量规划
   - 测序深度设计
   - 细胞活力与质量控制
   - 批次效应控制策略
   - 成本效益评估

### 第二部分：单细胞RNA-seq数据分析流程 (25分钟)
1. 单细胞RNA-seq数据分析概述
   - 分析流程框架
   - 计算挑战与解决策略
   - 主要分析工具与包
   - 分析平台选择(R/Python)

2. 原始数据处理
   - 测序数据质控
   - 条形码识别与校正
   - 序列比对/定量策略
     - 基于比对的方法
     - 伪比对方法(Alevin, Kallisto等)
   - UMI处理与计数去重
   - 表达矩阵生成

3. 主要分析框架介绍
   - Seurat框架(R)
     - 数据结构
     - 主要功能模块
     - 分析流程
   - Scanpy框架(Python)
     - AnnData数据结构
     - 主要功能模块
     - 分析流程
   - 其他分析框架简介

4. 单细胞数据可视化策略
   - 降维可视化
   - 特征基因可视化
   - 细胞类型注释可视化
   - 轨迹分析可视化
   - 交互式可视化工具

5. 单细胞RNA-seq数据分析的计算资源需求
   - 内存需求评估
   - 计算时间估计
   - 并行计算策略
   - 云计算资源利用

### 第三部分：单细胞数据特有的质控与预处理方法 (25分钟)
1. 单细胞数据特点与挑战
   - 高维稀疏矩阵
   - 技术噪声vs生物学变异
   - 批次效应
   - 零膨胀(Zero-inflation)现象
   - 丢失值(Dropout)问题

2. 细胞水平质控
   - 细胞质量评估指标
     - 检测到的基因/UMI数量
     - 线粒体基因比例
     - 核糖体基因比例
     - 细胞周期标记基因
   - 双细胞/多细胞检测
   - 死细胞/凋亡细胞识别
   - 细胞过滤策略
   - 质控参数优化

3. 基因水平质控
   - 基因表达评估指标
   - 低表达基因过滤
   - 高变异基因选择
   - 细胞类型特异性基因识别

4. 数据标准化方法
   - 文库大小标准化
   - LogNormalization
   - SCTransform方法
   - 深度因子标准化
   - 方法比较与选择

5. 技术效应校正
   - 细胞周期效应校正
   - 线粒体/核糖体比例校正
   - 批次效应校正方法
     - 线性回归方法
     - Harmony算法
     - BBKNN算法
     - Seurat整合方法
     - Scanorama
   - 方法比较与选择

6. 特征选择策略
   - 高变异基因识别
   - 主成分分析(PCA)
   - 变异分解方法
   - 特征基因数量确定

### 第四部分：细胞聚类与鉴定策略 (25分钟)
1. 降维方法
   - 主成分分析(PCA)
     - 原理与实现
     - 主成分数量确定
     - 解释方差分析
   - t-SNE算法
     - 原理与参数
     - 优势与局限性
     - 参数优化
   - UMAP算法
     - 原理与参数
     - 与t-SNE比较
     - 参数优化
   - 其他降维方法简介
     - 因子分析
     - 非负矩阵分解(NMF)
     - 自编码器

2. 细胞聚类算法
   - 基于图的聚类方法
     - KNN图构建
     - Louvain算法
     - Leiden算法
     - 分辨率参数优化
   - 层次聚类法
   - K-means聚类
   - 密度峰聚类
   - 聚类稳定性评估
   - 聚类数量确定策略

3. 细胞类型注释方法
   - 标记基因识别
     - 差异表达分析
     - Wilcoxon秩和检验
     - t检验
     - MAST方法
   - 已知标记基因映射
   - 参考数据集映射
     - 单细胞参考图谱
     - 标签转移算法
     - 整合分析策略
   - 自动注释工具
     - SingleR
     - Garnett
     - scType
   - 注释结果验证

4. 细胞亚群分析
   - 亚群重聚类策略
   - 亚群特异性标记基因
   - 罕见细胞类型识别
   - 亚群功能注释

### 第五部分：细胞轨迹分析与拟时序分析 (20分钟)
1. 细胞轨迹分析基本概念
   - 拟时序分析vs真实时间序列
   - 发育轨迹重建原理
   - 分支结构识别
   - 应用场景与局限性

2. 主要轨迹分析算法
   - Monocle系列
     - Monocle 2 (DDRTree)
     - Monocle 3 (UMAP+PQ树)
     - 算法原理与应用
   - Velocity算法
     - RNA速率概念
     - 剪接前RNA vs成熟RNA
     - 动态轨迹预测
   - Slingshot
   - PAGA
   - Wishbone
   - 算法比较与选择

3. 轨迹分析关键步骤
   - 特征基因选择
   - 降维与轨迹推断
   - 伪时间计算
   - 分支点识别
   - 起点/终点确定

4. 基于轨迹的下游分析
   - 时序差异表达分析
   - 基因表达动态模式识别
   - 调控网络推断
   - 发育过程关键调控因子识别
   - 细胞命运决定机制研究

### 第六部分：单细胞多组学整合技术介绍 (15分钟)
1. 单细胞多组学技术概述
   - 多组学整合的意义
   - 技术挑战与解决方案
   - 应用前景

2. 主要单细胞多组学技术
   - 单细胞多组学测序方法
     - CITE-seq/REAP-seq (蛋白质+转录组)
     - scATAC-seq (染色质开放+转录组)
     - scCNV-seq (拷贝数变异+转录组)
     - scBS-seq (甲基化+转录组)
     - G&T-seq (基因组+转录组)
     - SHARE-seq, Paired-seq等多模态技术
   - 空间转录组技术
     - 10x Visium
     - Slide-seq
     - MERFISH
     - 空间分辨率与覆盖度

3. 多组学数据整合分析方法
   - 配对数据分析
   - 非配对数据整合
     - 锚点整合(Seurat)
     - 迁移学习方法
     - 多视图学习
   - 多模态数据降维与可视化
   - 细胞类型一致性分析

4. 单细胞多组学应用案例
   - 免疫细胞功能异质性研究
   - 肿瘤微环境与细胞通讯
   - 发育过程调控机制
   - 疾病机制研究

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握Seurat/Scanpy进行单细胞RNA-seq数据分析的基本流程
- 学习单细胞数据质控、标准化与批次效应校正的方法
- 实践降维分析与可视化技术
- 掌握细胞类型注释与标记基因识别的方法
- 了解细胞通讯与轨迹分析的基本操作

### 第一部分：Seurat/Scanpy进行单细胞RNA-seq数据分析 (30分钟)
1. 分析环境准备
   - R/Python环境配置
   - Seurat/Scanpy安装
   - 依赖包安装
   - 数据导入格式准备

2. 10x Genomics数据处理
   - Cell Ranger结果导入
     - 原始矩阵读取
     - Seurat对象/AnnData对象创建
   - 数据结构探索
   - 初步质量评估
   - 实际操作演示

3. Seurat基本分析流程
   - Seurat对象创建与操作
   - 数据访问与操作
   - 基本可视化功能
   - 分析流程概述
   - 实际操作演示

4. Scanpy基本分析流程(可选)
   - AnnData对象创建与操作
   - 数据访问与操作
   - 基本可视化功能
   - 分析流程概述
   - 简单演示

### 第二部分：单细胞数据质控、标准化与批次效应校正 (40分钟)
1. 细胞质量控制
   - 质量指标计算
     - 基因/UMI计数
     - 线粒体基因比例
     - 核糖体基因比例
   - 质量分布可视化
   - 过滤参数确定
   - 细胞过滤操作
   - 实际操作演示

2. 数据标准化
   - LogNormalization方法
   - SCTransform方法
     - 参数设置
     - 结果评估
   - 标准化效果评估
   - 实际操作演示

3. 特征选择
   - 高变异基因识别
     - 方差稳定变换
     - 变异系数方法
   - 主成分分析
     - PCA运行
     - 主成分评估
     - 主成分数量确定
   - 实际操作演示

4. 批次效应校正
   - 批次效应评估
   - Seurat整合方法
     - CCA锚点识别
     - 数据整合
     - 整合效果评估
   - Harmony方法(可选)
   - 校正前后比较
   - 实际操作演示

### 第三部分：降维分析与可视化 (20分钟)
1. t-SNE实现
   - 参数设置与优化
   - 运行t-SNE
   - 结果可视化
   - 实际操作演示

2. UMAP实现
   - 参数设置与优化
   - 运行UMAP
   - 结果可视化
   - 与t-SNE比较
   - 实际操作演示

3. 聚类分析
   - KNN图构建
   - Louvain/Leiden聚类
   - 分辨率参数优化
   - 聚类结果可视化
   - 聚类稳定性评估
   - 实际操作演示

4. 高级可视化技术
   - 特征基因可视化
     - 热图
     - 小提琴图
     - 特征图
   - 多特征组合可视化
   - 聚类结果注释可视化
   - 实际操作演示

### 第四部分：细胞类型注释与标记基因识别 (20分钟)
1. 差异表达分析
   - Wilcoxon秩和检验
   - 标记基因识别
     - 参数设置
     - 结果过滤
   - 结果可视化
     - 热图
     - 点图
     - 火山图
   - 实际操作演示

2. 已知标记基因映射
   - 标记基因表达可视化
   - 细胞类型评分
   - 注释结果展示
   - 实际操作演示

3. 参考数据集映射
   - SingleR使用(可选)
   - 标签转移方法
   - 注释结果评估
   - 实际操作演示

4. 细胞类型注释结果整合
   - 多种方法结果比较
   - 一致性评估
   - 最终注释确定
   - 注释结果可视化
   - 实际操作演示

### 第五部分：细胞通讯与轨迹分析 (10分钟)
1. 细胞通讯分析
   - CellChat/CellPhoneDB简介
   - 配体-受体数据库
   - 细胞通讯网络构建
   - 结果可视化
   - 简单演示

2. 轨迹分析实践
   - Monocle 3使用
     - 数据准备
     - 轨迹推断
     - 伪时间计算
   - RNA velocity简介
   - 结果可视化
   - 简单演示

3. 基于轨迹的表达动态分析
   - 时序差异表达
   - 基因模块识别
   - 表达模式可视化
   - 简单演示

## 课后作业
1. 使用Seurat/Scanpy分析提供的单细胞RNA-seq数据集，完成质控、标准化、降维和聚类分析
2. 对聚类结果进行细胞类型注释，识别各群体的标记基因，并进行功能富集分析
3. 尝试进行批次效应校正，比较校正前后的聚类结果差异
4. 对特定细胞类型进行亚群分析，并探索其特征基因
5. 撰写完整的分析报告，包括方法、结果和生物学解释

## 参考资料
1. Stuart, T., Butler, A., Hoffman, P., Hafemeister, C., Papalexi, E., Mauck III, W. M., ... & Satija, R. (2019). Comprehensive integration of single-cell data. Cell, 177(7), 1888-1902.
2. Wolf, F. A., Angerer, P., & Theis, F. J. (2018). SCANPY: large-scale single-cell gene expression data analysis. Genome Biology, 19(1), 15.
3. Luecken, M. D., & Theis, F. J. (2019). Current best practices in single‐cell RNA‐seq analysis: a tutorial. Molecular Systems Biology, 15(6), e8746.
4. Trapnell, C., Cacchiarelli, D., Grimsby, J., Pokharel, P., Li, S., Morse, M., ... & Rinn, J. L. (2014). The dynamics and regulators of cell fate decisions are revealed by pseudotemporal ordering of single cells. Nature Biotechnology, 32(4), 381-386.
5. La Manno, G., Soldatov, R., Zeisel, A., Braun, E., Hochgerner, H., Petukhov, V., ... & Kharchenko, P. V. (2018). RNA velocity of single cells. Nature, 560(7719), 494-498.
6. Hao, Y., Hao, S., Andersen-Nissen, E., Mauck III, W. M., Zheng, S., Butler, A., ... & Satija, R. (2021). Integrated analysis of multimodal single-cell data. Cell, 184(13), 3573-3587.
7. Hwang, B., Lee, J. H., & Bang, D. (2018). Single-cell RNA sequencing technologies and bioinformatics pipelines. Experimental & Molecular Medicine, 50(8), 1-14.