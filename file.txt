这是一个测试文件
用于测试文件创建功能

测试内容：
- 第一行测试
- 第二行测试  
- 第三行测试

创建时间：$(date)

# UV vs Conda 用法比较表格

| 功能                | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 安装包              | uv add package-name         | conda install package-name |
| 安装特定版本包       | uv add package-name==1.0.0 | conda install package-name=1.0.0 |
| 从 PyPI 安装       | uv add package-name         | pip install package-name   |
| 从指定源安装        | uv add --index-url URL package-name | conda install -c channel package-name |
| 创建虚拟环境        | uv venv                     | conda create -n env-name   |
| 指定 Python 版本创建环境 | uv venv --python 3.11  | conda create -n env-name python=3.11 |
| 激活环境            | source .venv/bin/activate   | conda activate env-name    |
| 退出环境            | deactivate                  | conda deactivate           |
| 删除环境            | rm -rf .venv                | conda env remove -n env-name |
| 列出环境            | uv venv --list              | conda env list             |
| 列出已安装包        | uv pip list                 | conda list                  |
| 搜索包              | uv search package-name      | conda search package-name  |
| 显示包信息          | uv pip show package-name    | conda info package-name    |
| 更新包              | uv add package-name@latest  | conda update package-name  |
| 更新所有包          | uv sync --upgrade           | conda update --all         |
| 删除包              | uv remove package-name      | conda remove package-name  |
| 清理缓存            | uv cache clean              | conda clean --all          |
| 导出依赖            | uv pip freeze > requirements.txt | conda env export > environment.yml |
| 导出锁文件          | uv lock                     | conda env export --no-builds |
| 安装依赖文件        | uv pip install -r requirements.txt | conda env create -f environment.yml |
| 从锁文件安装        | uv sync                     | conda env update -f environment.yml |
| 运行脚本            | uv run script.py            | python script.py           |
| 运行带依赖的脚本    | uv run --with requests script.py | python script.py (需先安装) |
| 初始化项目          | uv init                     | 手动创建                     |
| 初始化库项目        | uv init --lib               | 手动创建                     |
| 添加开发依赖        | uv add --dev pytest        | conda install pytest       |
| 构建项目            | uv build                    | python setup.py sdist      |
| 发布包              | uv publish                  | twine upload dist/*         |
| 检查依赖树          | uv pip show --files package | conda list --show-channel-urls |
| 版本管理            | uv python install 3.11     | conda install python=3.11  |
| 列出可用 Python 版本 | uv python list            | conda search python        |
| 设置项目 Python     | uv python pin 3.11         | 在环境创建时指定             |

## 高级用法对比

| 高级功能            | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 工作空间管理        | uv workspace                | conda env clone             |
| 多项目依赖          | uv add --workspace          | 手动管理                     |
| 全局安装工具        | uv tool install package     | conda install -g package   |
| 脚本依赖声明        | `# /// script` 注释         | 不支持                       |
| 预编译轮子          | 自动处理                     | conda-forge 提供            |
| 离线安装            | uv sync --offline           | conda install --offline    |
| 配置文件            | pyproject.toml              | .condarc                    |
| 镜像源配置          | uv config set index-url     | conda config --add channels |

## 详细特点对比

### 性能特点

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 安装速度            | 极快 (Rust 实现)            | 较慢 (Python 实现)          |
| 依赖解析速度        | 10-100x 更快                | 传统速度                     |
| 内存使用            | 低内存占用                   | 中等内存占用                 |
| 磁盘缓存            | 高效缓存机制                 | 传统缓存                     |
| 并行下载            | 支持并行下载                 | 顺序下载                     |
| 启动时间            | 毫秒级启动                   | 秒级启动                     |

### 技术架构

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 实现语言            | Rust                        | Python                      |
| 包索引              | PyPI 标准                   | Anaconda/conda-forge       |
| 解析器              | 现代 SAT 求解器             | 传统依赖解析                 |
| 锁文件格式          | uv.lock (精确版本)          | environment.yml            |
| 配置格式            | pyproject.toml              | .condarc, YAML             |
| 插件系统            | 内置功能                     | 扩展插件                     |

### 生态系统支持

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| Python 版本支持    | 3.8+                        | 2.7+ (所有版本)            |
| 操作系统支持        | Windows, macOS, Linux       | Windows, macOS, Linux      |
| 架构支持            | x86_64, ARM64               | x86_64, ARM64, PowerPC     |
| 包源支持            | PyPI, 私有索引              | Anaconda, conda-forge, PyPI |
| 二进制依赖          | 依赖 wheel                  | 原生二进制包管理             |
| 编译器支持          | 自动处理                     | 集成编译器链                 |

### 项目管理特点

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 项目结构            | pyproject.toml 中心化       | 多文件配置                   |
| 依赖分类            | main, dev, optional         | dependencies, channels     |
| 版本锁定            | 自动锁定传递依赖             | 手动或导出锁定               |
| 虚拟环境            | 自动创建管理                 | 手动创建命名                 |
| 构建系统            | 内置 (PEP 517/518)         | 外部工具 (setuptools)      |
| 发布流程            | 一键发布到 PyPI             | 需要额外工具                 |

### 开发体验

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 学习曲线            | 现代化，符合直觉             | 传统，需要学习概念           |
| 错误信息            | 清晰的错误提示               | 详细但冗长                   |
| 文档质量            | 现代化文档                   | 成熟完整文档                 |
| 社区支持            | 快速增长的社区               | 大型成熟社区                 |
| IDE 集成           | VS Code, PyCharm 支持      | 广泛 IDE 支持               |
| 调试工具            | 内置调试信息                 | 丰富的调试选项               |

### 企业级特点

| 特点               | UV                          | Conda                       |
|-------------------|-----------------------------|-----------------------------|
| 稳定性              | 新兴但快速成熟               | 高度稳定成熟                 |
| 向后兼容            | 遵循语义版本                 | 强向后兼容                   |
| 安全性              | 现代安全实践                 | 企业级安全                   |
| 合规性              | 开源 MIT 许可证             | 开源 + 商业许可证选项        |
| 支持服务            | 社区支持                     | 商业支持可用                 |
| 部署规模            | 适合中小型项目               | 适合大规模部署               |

## 主要特点对比

**UV 特点：**
- 更快的包解析和安装速度
- Rust 编写，性能优异  
- Python 专用包管理器
- 现代化的项目管理方式
- 自动虚拟环境管理
- 支持 PEP 标准 (pyproject.toml)
- 内置构建和发布功能
- 单文件脚本依赖管理

**Conda 特点：**
- 支持多种编程语言 (Python, R, Julia 等)
- 优秀的科学计算生态
- 跨平台兼容性好
- 成熟稳定的生态系统
- 二进制包分发
- 系统级包管理
- 强大的环境隔离
- 官方和社区渠道丰富

## 使用场景建议

**推荐使用 UV 的场景：**
- 纯 Python 项目开发
- 需要快速的包管理
- 现代化的项目结构
- CI/CD 环境
- 单文件脚本开发

**推荐使用 Conda 的场景：**
- 科学计算和数据分析
- 需要非 Python 依赖
- 跨语言项目
- 需要稳定的生产环境
- 复杂的系统依赖管理

## 迁移考虑

**从 Conda 迁移到 UV：**
- 评估是否需要非 Python 依赖
- 检查所有包在 PyPI 上的可用性
- 转换 environment.yml 到 pyproject.toml
- 重新测试构建和部署流程

**从 UV 迁移到 Conda：**
- 需要处理系统级依赖时
- 项目需要 R/Julia 等其他语言
- 需要 conda-forge 的特定包
- 企业环境要求稳定性优先 