# 专题三：基因组测序(DNA-seq)数据分析 - 理论课

**课程目标：** 理解基因组测序的基本原理、实验设计考量、核心生物信息学分析流程（比对、变异检测、注释、组装）及其背后的算法思想与应用场景。

---

## 一、 基因组测序实验设计与应用场景

**引言：** 基因组是生命体的蓝图。基因组测序技术，特别是高通量测序（Next-Generation Sequencing, NGS）及其后续发展，使我们能够以前所未有的深度和广度解读这份蓝图。合理的实验设计是获取高质量、有意义数据的基石。

![基因组测序工作流程](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/genome_sequencing_workflow.svg)
*图1：典型的基因组测序与分析工作流程示意图。*

### 1. 基因组测序类型与应用

*   **全基因组测序 (Whole Genome Sequencing, WGS)**
    *   **核心概念：重测序 vs 从头测序**
        *   **重测序（Resequencing）**：针对已有参考基因组（Reference Genome）的物种。将测序产生的短读段（reads）比对回参考基因组，主要目的是**发现样本相对于参考基因组的遗传变异**，如单核苷酸多态性（SNP）、插入缺失（Indel）、结构变异（SV）和拷贝数变异（CNV）。是目前人类遗传学、肿瘤基因组学等领域的主流策略。
        *   **从头测序（*De novo* sequencing）**：针对没有参考基因组或希望构建全新高质量基因组图谱的物种。通过复杂的生物信息学算法，将测序读段拼接（assemble）成连续的基因组序列（contigs and scaffolds）。是构建新物种基因组、解析高度复杂基因组区域（如着丝粒）的基础。
    *   **适用场景与研究问题**
        *   **重测序应用：**
            *   **人类遗传病研究：** 鉴定孟德尔疾病的致病突变，发现复杂疾病的易感位点（GWAS）。
            *   **肿瘤基因组研究：** 识别驱动突变、乘客突变、肿瘤异质性、拷贝数改变、基因融合等。
            *   **药物基因组学：** 发现与药物反应相关的遗传标记。
            *   **群体遗传学与进化：** 研究群体结构、遗传多样性、选择压力、迁徙历史。
            *   **动植物育种：** 关联分析、基因组选择，加速育种进程。
        *   ***De novo* 测序应用：**
            *   **新物种基因组图谱构建：** 为后续功能基因组学、比较基因组学研究奠定基础。
            *   **复杂基因组区域解析：** 如富含重复序列的区域、着丝粒、端粒等。
            *   **比较基因组学：** 研究基因组结构进化、基因家族演化。
            *   **微生物基因组研究：** 快速获得细菌、古菌、病毒等基因组。
    *   **测序深度（Sequencing Depth）要求与考虑因素**
        *   **定义：** 平均每个碱基被测序读段覆盖的次数（例如，30X表示平均覆盖30次）。
        *   **重测序深度考量：**
            *   **人类基因组（二倍体）：** 至少30X可较好地检测杂合SNP；50X以上有助于提高Indel和低频变异检测的敏感性。
            *   **细菌基因组（单倍体）：** 通常50X-100X足以准确检测变异。
            *   **肿瘤基因组（体细胞变异）：** 常需更高深度（如50X-100X甚至更高），以检测低等位基因频率（low Allele Frequency, AF）的体细胞突变，需考虑肿瘤纯度（tumor purity）和异质性（heterogeneity）。
            *   **群体研究：** 可能需要较低深度（如5X-15X）但样本量大，用于群体频率估算和关联分析。
        *   ***De novo* 测序深度考量：**
            *   **细菌基因组：** 通常需要100X以上短读长数据，或结合长读长数据。
            *   **复杂动植物基因组：** 往往需要更高的深度（如50X-100X Illumina数据）**并强烈推荐结合长读长测序技术（PacBio HiFi 或 Oxford Nanopore）** 来跨越重复序列，提升组装连续性（N50）。
        *   **核心考虑因素：** 基因组大小、倍性、杂合度、重复序列含量、研究目标（检测杂合/纯合变异、低频变异、体细胞变异）、预算。

*   **外显子组测序 (Whole Exome Sequencing, WES)**
    *   **捕获技术原理：** 利用设计好的、覆盖基因组外显子区域（约占人类基因组1-2%）的生物素标记探针（probes/baits），与打断后的基因组DNA片段进行液相或固相杂交。通过链霉亲和素（streptavidin）磁珠富集与探针结合的目标DNA片段，然后进行测序。
    *   **与WGS的比较优势：**
        *   **成本效益高：** 显著低于WGS，允许更大规模的样本研究。
        *   **数据量 manageable：** 数据分析存储压力小。
        *   **聚焦编码区：** 直接关注对蛋白质功能影响最大的区域，易于解释。
    *   **适用场景与局限性：**
        *   **适用场景：**
            *   **孟德尔遗传病诊断和研究：** 高效发现编码区致病变异。
            *   **复杂疾病候选基因筛选：** 识别编码区的罕见功能性变异。
            *   **肿瘤研究：** 快速扫描编码区的体细胞突变。
        *   **局限性：**
            *   **无法检测非编码区变异：** 包括内含子、启动子、增强子等调控区域变异。
            *   **对结构变异（SV）和拷贝数变异（CNV）检测能力有限：** 尤其是在捕获区域边界或跨越多个外显子的SV/CNV。
            *   **捕获效率不均一：** 受GC含量、序列复杂度影响，导致部分外显子覆盖度偏低或丢失。
            *   **可能漏掉部分外显子：** 探针设计可能不完美或基因注释更新。

*   **靶向测序 (Targeted Sequencing / Panel Sequencing)**
    *   **设计策略与方法：**
        *   **基于多重PCR扩增（Amplicon-based）：** 使用大量特异性引物对同时扩增多个目标区域。适用于目标区域较小、样本起始量低的场景。
        *   **基于杂交捕获（Hybridization capture-based）：** 类似于WES，但探针设计仅针对特定基因集（panel）或区域。适用于目标区域较大、需要更高均一性的场景。
    *   **应用场景与优势：**
        *   **应用场景：**
            *   **已知致病基因的突变筛查：** 如遗传性肿瘤综合征基因panel。
            *   **肿瘤伴随诊断与监测：** 检测特定药物靶点基因、耐药基因突变（液体活检ctDNA panel）。
            *   **微生物分型与耐药基因检测。**
            *   **特定通路或基因家族研究。**
        *   **优势：**
            *   **成本最低：** 相对于WGS和WES。
            *   **极高测序深度：** 容易达到数百甚至数千X，可检测极低频变异。
            *   **灵敏度高：** 对目标区域内的变异检测能力强。
            *   **分析流程相对简单，周期短。**
    *   **深度与覆盖度要求：**
        *   **深度：** 通常需要非常高，如500X-1000X甚至更高，具体取决于应用（如检测<1% AF的体细胞突变）。
        *   **覆盖度：** 对目标区域的覆盖度要求极高，通常>98%或>99%的靶点达到一定的最低深度（如100X）。

### 2. 基因组测序实验设计关键因素

*   **样本选择与质量控制 (Sample Selection & QC)**
    *   **DNA提取与质量评估：**
        *   **提取方法选择：** 需根据样本类型（血液、组织、唾液、FFPE等）选择，目标是获得高分子量、高纯度的DNA。常见方法包括酚-氯仿抽提、盐析法、硅胶柱纯化法、磁珠法等。FFPE样本需要特殊优化流程。
        *   **质量评估指标：**
            *   **纯度：** OD260/280比值（理想1.8-2.0），OD260/230比值（理想>2.0，指示盐或有机物污染）。
            *   **浓度：** Qubit等荧光染料法定量（比分光光度计更准确，不受RNA污染影响）。
            *   **完整性：** 琼脂糖凝胶电泳（观察主带清晰度、有无降解）；Agilent Bioanalyzer/TapeStation（提供DNA Integrity Number, DIN值）。高完整性对长读长测序和高质量WGS尤为重要。
    *   **样本量要求：**
        *   取决于文库构建方法和测序平台。一般建议：
            *   WGS（标准Illumina PCR-based）：~1 ug
            *   WGS（PCR-free）：通常需要更多，如 >1 ug
            *   WES：~50 ng - 1 ug （取决于捕获试剂盒）
            *   靶向测序（Amplicon）：可低至 1-10 ng
            *   靶向测序（Capture）：~50 ng - 1 ug
            *   长读长测序（PacBio/ONT）：通常需要微克级别的**高分子量**DNA (>1-5 ug HMW DNA)。
    *   **对照样本设计：**
        *   **疾病研究：** 病例 vs 健康对照（需考虑年龄、性别、地域匹配）；家系研究中的亲代、其他受累/未受累成员。
        *   **肿瘤研究：** **肿瘤样本与其配对的正常样本（Matched Normal）**，通常来自同一患者的外周血或癌旁组织，用于区分体系突变（germline）和体细胞突变（somatic）。
        *   **群体研究：** 需要具有代表性的群体样本，或利用大型公共数据库（如1000 Genomes, gnomAD）作为群体对照。
        *   **技术对照：** 考虑设置技术重复（technical replicates）评估流程稳定性，或使用标准品（reference materials）进行质控。

*   **测序平台选择 (Sequencing Platform Selection)**
    *   **短读长 vs 长读长平台：**
        *   **短读长平台（代表：Illumina）：**
            *   **优势：** 极高的单碱基准确率（Q30 > 80-90%），极高的通量，单位碱基成本最低。成熟的生态系统和分析工具。
            *   **劣势：** 读长短（通常50-300bp），难以跨越长重复序列，对复杂SV和基因组组装能力有限，存在GC偏好性。
        *   **长读长平台（代表：PacBio SMRT Sequencing, Oxford Nanopore Technologies - ONT）：**
            *   **优势：** 读长极长（PacBio HiFi: 10-25kb, >99.9%准确率; ONT: 可达Mb级别，准确率持续提升，最新技术接近HiFi）。能有效解决重复序列区域的比对和组装难题，**极大提升SV检测能力和单倍型定相（phasing）能力**，是高质量*de novo*组装的首选。ONT还具有实时测序、便携性等特点。
            *   **劣势：** 单位碱基成本相对较高（但持续下降），通量低于Illumina（但也在快速增长），原始数据准确率（ONT）或特定错误模式需要专门的生物信息学处理。
    *   **准确度 vs 成本 vs 研究目标的权衡：**
        *   **SNP/小Indel检测（重测序）：** Illumina通常是性价比最高的选择。
        *   **高质量*de novo*组装：** PacBio HiFi 或 ONT 是必需的，常结合Illumina进行 polishing 或 hybrid assembly。
        *   **复杂SV检测/定相：** 长读长技术具有明显优势。
        *   **超低频变异检测：** Illumina配合高深度和UMI（Unique Molecular Identifier）技术。
    *   **混合测序策略（Hybrid Sequencing）：**
        *   结合短读长的高精度、低成本和长读长的跨越能力。
        *   常见策略：长读长进行骨架组装（scaffolding），短读长进行碱基纠错（polishing）；或利用长读长辅助短读长进行SV验证和定相。

*   **测序深度设计 (Sequencing Depth Design)**
    *   **不同应用的深度再强调：**
        *   **Germline SNP/Indel (diploid):** 30X+
        *   **Somatic Variant (tumor, low AF):** 100X - 1000X+ (取决于AF和所需敏感性)
        *   **CNV detection (WGS):** 30X-60X (深度均一性更重要)
        *   ***De novo* assembly (complex genome):** 50-100X Illumina + 20-50X Long Read (PacBio HiFi or ONT)
        *   **WES:** 平均深度 >100X, 确保 >95% 目标区域达到 >20X。
        *   **Targeted Panel:** 500X - 10000X+ (取决于应用)
    *   **非均匀覆盖的处理策略 (Addressing Coverage Non-uniformity):**
        *   **实验层面：**
            *   **PCR-free文库构建：** 减少GC偏好性和扩增引入的覆盖不均。
            *   **优化DNA片段化方法。**
            *   **优化杂交捕获探针设计和实验条件（WES/Targeted）。**
        *   **分析层面：**
            *   **GC偏好性校正算法。**
            *   **变异检测算法内部考虑覆盖度因素。**
            *   **对低覆盖区域的变异检测结果持谨慎态度或进行额外验证。**
    *   **成本效益分析：**
        *   深度与成本近乎线性相关。需在统计学功效（power）、检测目标（如低频变异）和预算间找到平衡点。Pilot study有时是必要的。

*   **文库构建策略 (Library Preparation Strategy)**
    *   **插入片段大小选择 (Insert Size Selection):**
        *   标准WGS/WES: 300-500 bp 插入片段适合Illumina。
        *   较大的插入片段（mate-pair libraries, >1kb）：曾用于辅助组装和SV检测，但逐渐被长读长技术取代。
        *   合适的片段大小分布影响比对效率、覆盖均匀性及配对末端信息（pair-end information）的有效利用（如SV检测）。
    *   **PCR扩增 vs PCR-free：**
        *   **PCR扩增：** 是标准流程，需要DNA起始量较低。但可能引入**GC偏好性、扩增偏好性、PCR错误和PCR重复（duplicates）**，影响覆盖均匀性和变异检测准确性，特别是定量应用（如CNV）。
        *   **PCR-free：** 最大程度减少上述偏好性，提供更均匀的覆盖和更准确的变异检测，尤其推荐用于高质量WGS、CNV研究。但通常需要更高的DNA起始量。
    *   **双端 vs 单端测序 (Paired-End vs Single-End Sequencing):**
        *   **单端（Single-End, SE）：** 只测DNA片段的一端。成本稍低，适用于某些特定应用（如计数类应用、某些简单基因组的重测序）。
        *   **双端（Paired-End, PE）：** 测DNA片段的两端。**强烈推荐用于大多数基因组测序应用。**
            *   **优势：** 提高比对准确性（尤其在重复区域）；能够检测插入缺失（Indel）和结构变异（SV）通过分析配对末端的距离和方向是否异常（discordant read pairs）；辅助基因组组装。

### 3. 基因组测序的主要应用场景概述

*   **物种参考基因组构建：** 利用*de novo*组装策略，为新物种或品系提供遗传基础。
*   **种群遗传变异研究：** 通过重测序大量个体，绘制SNP、Indel、SV图谱，进行群体遗传结构分析、全基因组关联研究（GWAS）、选择信号分析等。
*   **进化与系统发育分析：** 基于比较基因组学，重建生命之树，研究基因家族演化、基因组结构变异在进化中的作用。
*   **功能基因组学研究（基础）：** 基因组序列是理解基因功能、调控网络的基础，结合转录组、表观组等多组学数据进行整合分析。
*   **临床诊断与精准医疗：**
    *   **遗传病诊断：** WGS/WES已成为许多单基因病和复杂遗传病的重要诊断工具。
    *   **肿瘤基因组分析：** 指导靶向治疗、免疫治疗选择，评估预后，监测复发。
    *   **药物基因组学：** 预测个体对药物的反应和副作用。
    *   **无创产前检测（NIPT）：** 基于母体血浆中胎儿游离DNA测序。
*   **农业育种与改良：** 开发分子标记，实施全基因组选择，挖掘优良性状基因，加速作物和家畜的遗传改良。
*   **微生物与宏基因组学：** 研究微生物群落结构与功能，病原体溯源与耐药性监测，环境微生物生态学。

**本章小结：** 基因组测序技术提供了强大的工具来探索遗传信息。理解不同测序策略的原理、优缺点、适用场景，并精心设计实验方案（样本、平台、深度、文库），是确保研究成功的关键第一步。

---

## 二、 参考基因组比对算法原理

**引言：** 获得测序读段（reads）后，对于重测序应用，核心步骤是将其精确定位到参考基因组上。这个过程称为比对（Alignment）或映射（Mapping）。比对的准确性和效率直接影响下游分析（如变异检测）的质量。

![比对算法原理](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/alignment_algorithms.svg)
*图2：简化的比对概念图，显示短读段（reads）与参考基因组（reference）的匹配。*

### 1. 序列比对的基本概念

*   **全局比对 vs 局部比对 (Global vs Local Alignment)**
    *   **全局比对（Global Alignment, Needleman-Wunsch算法）：** 尝试对两条序列的**整个长度**进行比对，寻找最佳的整体匹配。适用于比较功能或进化上相关的、长度相近的序列。目标是最大化总比对得分。
    *   **局部比对（Local Alignment, Smith-Waterman算法）：** 在两条序列中寻找并报告**相似度最高的片段（或多个片段）**，而不要求整个序列都参与比对。适用于寻找序列中的保守域、比对长度差异大的序列、或在长序列中寻找短序列的最佳匹配（如NGS读段比对）。目标是找到得分最高的子序列比对。**NGS读段比对本质上是局部比对问题。**

*   **序列相似性度量 (Sequence Similarity Metrics)**
    *   **编辑距离（Edit Distance）：** 将序列A转换为序列B所需的最少单字符编辑操作（插入、删除、替换）次数。
    *   **汉明距离（Hamming Distance）：** 仅适用于**等长**序列，指两个序列在对应位置上字符不同的数量。
    *   **相似度百分比（Percent Identity）：** 比对区域内匹配的碱基数占该区域总长度（包括gap）的百分比。这是衡量相似性最直观的方式之一。

*   **比对评分系统 (Alignment Scoring System)**
    *   定义了比对中不同事件的得分/惩罚值，用于量化比对的好坏。
    *   **匹配得分（Match Score）：** 两个碱基相同时的得分，通常为正值。
    *   **错配惩罚（Mismatch Penalty）：** 两个碱基不同时的惩罚，通常为负值或小于匹配得分。可以设置转换（transition,嘌呤<->嘌呤,嘧啶<->嘧啶）和颠换（transversion,嘌呤<->嘧啶）不同的罚分。
    *   **空位罚分（Gap Penalty）：** 在序列中引入空位（插入或删除）的惩罚，通常为负值。
        *   **线性空位罚分（Linear Gap Penalty）：** `Gap_penalty = d * g`，其中`d`是空位长度，`g`是每个空位的罚分。对长空位惩罚过重。
        *   **仿射空位罚分（Affine Gap Penalty）：** `Gap_penalty = o + e * (d-1)`，其中`o`是打开空位（gap opening）的罚分，`e`是延伸空位（gap extension）的罚分（通常`|o| > |e|`）。更符合生物学实际，因为一个突变事件可能导致较长indel，而不是多个独立的小indel。这是现代比对工具常用的模型。

*   **动态规划算法基础 (Dynamic Programming Basics)**
    *   是解决全局比对（Needleman-Wunsch）和局部比对（Smith-Waterman）最优解的经典方法。
    *   通过构建一个二维矩阵（DP matrix），矩阵中的每个单元格`M[i,j]`存储了序列A的前`i`个字符与序列B的前`j`个字符的最佳比对得分。
    *   得分通过递归关系计算，考虑来自左方（引入gap in B）、上方（引入gap in A）和左上方（匹配/错配）三个方向的可能性。
    *   最终通过回溯（traceback）矩阵找到最佳比对路径。

### 2. 传统比对算法及其局限性

*   **Smith-Waterman算法：**
    *   经典的局部比对算法，保证找到得分最高的局部比对。
    *   时间复杂度为 O(m*n)，空间复杂度也为 O(m*n)，其中m和n是两条序列的长度。

*   **Needleman-Wunsch算法：**
    *   经典的全局比对算法，保证找到最佳的全局比对。
    *   时间复杂度 O(m*n)，空间复杂度 O(m*n)。

*   **计算复杂度挑战：**
    *   对于NGS数据，我们需要将数亿条短读段（m ≈ 100-300 bp）比对到数十亿碱基长度的参考基因组（n ≈ 3 Gbp for human）上。
    *   直接使用 O(m*n) 的算法进行 `N` 次比对（N为读段数），总计算量级约为 O(N*m*n)，这是**完全不可行**的。

*   **大规模数据的算法瓶颈：**
    *   传统DP算法对内存和CPU时间的需求巨大，无法满足高通量测序数据分析的时效性要求。

### 3. 高通量测序数据比对算法创新：索引与启发式

为了克服传统算法的瓶颈，现代NGS比对工具采用了**索引（Indexing）**参考基因组和**启发式（Heuristic）**策略相结合的方法，极大提高了比对速度。

*   **索引策略 (Indexing Strategies) - 预处理参考基因组以实现快速查找：**
    *   **哈希表索引（Hash Table Index / k-mer Index）：**
        *   将参考基因组分割成固定长度（k）的短子串（k-mers）。
        *   构建一个哈希表（或类似数据结构），存储每个k-mer在基因组中出现的位置。
        *   比对时，将读段也分割成k-mers，快速查询这些k-mer在哈希表中的位置，找到潜在的比对候选区域。
        *   **优点：** 概念简单，实现相对容易。
        *   **缺点：** 需要选择合适的k值；内存占用可能较大；对错配和indel不直接支持，需要后续扩展步骤。 (e.g., early tools like MAQ)
    *   **后缀树/数组（Suffix Tree/Array）：**
        *   **后缀树：** 包含参考基因组所有后缀的压缩树状结构。可以在 O(m) 时间内查找一个读段（长度m）是否是基因组的子串。
        *   **后缀数组：** 存储所有后缀按字典序排序后的起始位置。结合辅助数据结构（如LCP array），可以实现类似后缀树的快速查找。
        *   **优点：** 查找速度快，理论基础扎实。
        *   **缺点：** 构建复杂，内存占用巨大（尤其是后缀树），对基因组级别的应用不实用。
    *   **FM-index 与 BWT变换（Burrows-Wheeler Transform based FM-index）：**
        *   **BWT变换：** 一种可逆的数据变换，能将原始文本（基因组）转换成一个具有高局部相似性的字符串，非常有利于压缩。
        *   **FM-index：** 基于BWT变换结果构建的一种**压缩的、自索引**的数据结构。它允许：
            1.  高效地计算一个模式串（读段）在原始文本中出现的次数（`count()`操作）。
            2.  高效地定位这些出现的位置（`locate()`操作）。
        *   **优点：** **极低的内存占用**（接近原始基因组的压缩大小），**极快的查找速度**。是目前主流比对工具（如BWA, Bowtie2）的核心索引技术。

*   **种子与扩展策略（Seed and Extend Strategy）- 启发式比对过程：**
    *   大多数快速比对算法采用此策略，而非全长DP比对。
    *   **1. 播种（Seeding）：** 在读段（read）中选取一个或多个短的子序列（称为种子，seeds），利用索引快速在参考基因组中查找这些种子的**精确匹配**或**允许少量错配**的匹配位置（hits）。种子可以是固定长度的k-mer，或最大精确匹配（Maximal Exact Matches, MEMs）。
    *   **2. 扩展（Extension）：** 从找到的种子匹配位置（anchors）出发，向两端延伸比对，通常使用更耗时但更精确的算法（如Smith-Waterman或其变种，Banded SW）来评估该候选区域的完整比对得分，并处理种子区域之外的错配和indel。
    *   **3. 打分与选择：** 比较所有候选区域的比对得分，选择最佳（或得分最高的几个）比对结果。

*   **启发式算法优化 (Heuristic Algorithm Optimization):**
    *   **Banding:** 在DP矩阵中只计算主对角线附近的一个带状区域（band），假设最优比对不会偏离太远。显著降低计算量，但可能丢失最优解如果真实比对有大量indel。
    *   **得分阈值:** 在扩展过程中，如果当前得分低于某个阈值，则提前终止该路径的计算。
    *   **多重尝试/救援（Rescue）:** 对没有找到高质量比对的读段，尝试使用更敏感（但更慢）的参数或策略重新比对。

### 4. BWA算法原理 (Burrows-Wheeler Aligner)

BWA是由李恒博士开发的广泛使用的比对工具系列。

*   **BWT变换与FM-index：** BWA的核心是利用BWT和FM-index对参考基因组进行索引，实现了内存高效和快速的种子查找。
*   **不同模式及其演化：**
    *   **BWA-backtrack (原版BWA)：** 主要设计用于早期较短的Illumina读段（<100bp）。使用BWT进行种子查找，然后通过回溯（backtracking）算法在BWT索引上进行扩展，允许一定数量的错配和indel。对indel处理不够灵活，速度相对较快。
    *   **BWA-SW：** 基于Smith-Waterman算法的启发式版本，设计用于更长、错误率稍高的读段（如早期的454或长Illumina读段）。不再依赖BWT进行全局搜索，而是采用更传统的seed-and-extend策略，但仍然使用BWT来辅助种子查找。对indel处理更好，但比backtrack慢。**此模式已较少使用，被BWA-MEM取代。**
    *   **BWA-MEM (Maximal Exact Match)：** **目前推荐和最常用的BWA模式。** 适用于从70bp到几Mbp的各种读段长度（包括Illumina, PacBio CLR/HiFi, ONT）。
        *   采用**最大精确匹配（MEMs）**作为种子，有时会使用**超最大精确匹配（Super-Maximal Exact Matches, SMEMs）**。
        *   种子查找仍然基于FM-index。
        *   采用**启发式的Smith-Waterman算法（SW-like extension）**进行扩展。
        *   **自适应策略：** 能根据读段长度和特性调整策略。
        *   **智能配对末端救援（Smart pairing）：** 能有效利用配对信息拯救单端未能比对上的reads。
        *   **支持长读段和剪接比对（split alignment）。**

*   **算法参数优化：**
    *   `-t` (线程数), `-k` (最小种子长度), `-T` (最低比对得分阈值), `-A` (匹配得分), `-B` (错配罚分), `-O` (gap open penalty), `-E` (gap extension penalty) 等。需要根据测序数据类型（读长、错误率）和分析目标（灵敏度vs特异性）进行调整。默认参数通常适用于标准Illumina数据。

*   **性能特点与适用场景：**
    *   **优点：** 速度快，内存占用相对较低，准确性高（尤其是BWA-MEM），支持多种测序数据，广泛使用，社区支持好。
    *   **适用场景：** WGS、WES、靶向测序数据比对，支持人和各种模式生物基因组。是许多标准分析流程（如GATK Best Practices）的推荐比对工具。

### 5. Bowtie2算法原理

Bowtie2是另一个非常流行的基于BWT/FM-index的比对工具，由Ben Langmead等人开发。

*   **双向BWT策略 (Forward/Reverse Index):** Bowtie2同时对参考基因组的正向和反向互补序列构建FM-index。这有助于更有效地查找种子，尤其是在读段包含较多错误时。
*   **种子与扩展机制：**
    *   同样采用**seed-and-extend**策略。
    *   使用**多重种子（multiseed alignment）**策略：尝试从读段的不同位置提取多个种子，并允许种子中存在错配（inexact seeding）。
    *   扩展阶段使用**动态规划**（类Smith-Waterman）。
    *   提供多种**比对模式**（`--end-to-end`, `--local`）和**敏感度预设**（`--very-fast`, `--fast`, `--sensitive`, `--very-sensitive`），方便用户选择速度和敏感性的平衡点。
*   **多线程并行实现：** `-p` 参数指定线程数，能有效利用多核CPU。
*   **与BWA的比较：**
    *   **速度：** Bowtie2通常在默认设置下比BWA-MEM**更快**。
    *   **内存：** 两者内存占用都相对较低，Bowtie2可能略低一些。
    *   **准确性/敏感性：** 在标准Illumina数据上，两者表现通常非常接近。BWA-MEM在处理较长indel或更长读段方面可能略有优势。Bowtie2的多种敏感度预设提供了更灵活的选择。
    *   **适用性：** 两者都非常适用于Illumina短读长数据的比对。BWA-MEM对长读长的支持更好。

### 6. 其他比对工具介绍

*   **SOAP系列 (SOAP, SOAP2, SOAP3-dp):** 由华大基因（BGI）开发，是早期NGS时代重要的比对工具之一，特别是在处理超短读长方面有特色。SOAP3-dp利用GPU加速。
*   **Novoalign (商业软件):** 以其高准确度和敏感性著称，尤其是在处理indel和低质量数据方面表现优异。但速度相对较慢，且需要购买许可。
*   **Minimap2:** 由李恒博士开发，**是目前长读长（PacBio, ONT）比对和全基因组比对（genome-to-genome）的黄金标准工具。**
    *   使用**Minimizers**作为种子（一种k-mer子采样技术），极大减少了种子数量和索引大小。
    *   采用**启发式的链式共线性（collinear chaining）**算法进行种子过滤和连接。
    *   **速度极快**，内存占用低。
    *   对长读长的高错误率有很好的容忍度。
    *   也支持短读长比对和RNA-seq比对。
*   **NGMLR (Next Generation Mapper for Long Reads):** 专门为**长读长测序数据**设计，特别**优化了对结构变异（SV）断点的敏感性**。常与SV检测工具（如Sniffles）配合使用。
*   **STAR Aligner:** 主要用于RNA-seq数据比对，因为它能高效处理剪接（splicing）。但也能用于DNA-seq比对，速度非常快。

### 7. 比对质量评估 (Alignment Quality Assessment)

比对完成后，需要评估比对结果的质量。主要通过SAM/BAM文件中的指标。

*   **比对质量分数 (Mapping Quality Score, MAPQ):**
    *   SAM/BAM格式文件中的第5列。
    *   衡量的是**比对位置错误的概率**，采用Phred标度（Phred-scaled）：`MAPQ = -10 * log10(P_error)`。
    *   例如，MAPQ=20 表示比对错误的可能性是1% (10^-2)，MAPQ=30 表示0.1% (10^-3)。
    *   MAPQ值**不仅考虑了序列本身的匹配程度，还考虑了该读段在基因组中是否有其他相似的潜在匹配位置（唯一性）**。
    *   **MAPQ=0 通常表示该读段有多个同样好的比对位置（非唯一比对）**。
    *   是下游分析（如变异检测）过滤低质量比对的重要依据。

*   **唯一比对 vs 多重比对 (Uniquely Mapped vs Multiply Mapped Reads):**
    *   **唯一比对：** 读段只有一个最佳比对位置（通常MAPQ > 0）。这些是信息量最高的读段。
    *   **多重比对：** 读段有多个得分相同或相近的最佳比对位置（通常MAPQ = 0）。常见于重复序列区域。
    *   **处理策略：** 下游分析通常只使用唯一比对的读段，或对比对到多个位置的读段进行特殊处理（如按比例分配，或保留其中一个随机/最佳位置）。

*   **比对覆盖度与均匀性 (Coverage and Uniformity):**
    *   **覆盖度（Coverage Depth）：** 参考基因组上每个碱基被多少条读段覆盖。
    *   **覆盖广度（Coverage Breadth）：** 参考基因组有多大比例被至少一条（或N条）读段覆盖。
    *   **均匀性（Uniformity）：** 覆盖深度在整个基因组（或目标区域）的分布是否均匀。GC含量、重复序列、文库制备偏差等都会影响均匀性。
    *   使用 `samtools depth`, `bedtools coverage`, `Qualimap` 等工具进行评估。 高且均匀的覆盖是高质量变异检测的前提。

![SAM/BAM文件格式](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/sam_bam_format.svg)
*图3：SAM/BAM文件格式结构示意图。包含头部（header）信息和比对记录（alignment records）。每条记录包含读段名称、比对标志（FLAG）、参考序列名、比对起始位置、MAPQ、CIGAR串（描述比对细节）、配对末端信息、序列、碱基质量等。*

*   **常见比对问题与解决方案：**
    *   **比对率低 (Low Mapping Rate):**
        *   **可能原因：** 样本污染（如混入其他物种DNA）、参考基因组错误或不匹配（如物种亚型差异大）、测序质量差（大量低质量碱基）、接头二聚体（Adapter dimers）比例高、文库构建问题。
        *   **解决方案：** 检查样本纯度；使用正确的参考基因组；加强数据质控（QC），过滤低质量读段和接头；优化文库制备流程。
    *   **大量多重比对 (High Multi-mapping Rate):**
        *   **可能原因：** 基因组本身含有大量重复序列；读段太短。
        *   **解决方案：** 使用更长的读段（如果可能）；使用能更好处理多重比对的下游分析策略；关注唯一比对结果。
    *   **比对结果偏向特定区域/覆盖不均 (Mapping Bias / Uneven Coverage):**
        *   **可能原因：** PCR扩增偏好（GC含量极端区域）；捕获效率不均（WES/Targeted）；参考基因组中的重复序列或组装错误；片段化偏好。
        *   **解决方案：** 采用PCR-free文库；使用能校正GC偏好的分析工具；优化捕获探针设计；使用更完善的参考基因组；评估片段化方法。

**本章小结：** 参考基因组比对是NGS数据分析的核心环节。理解动态规划、索引技术（特别是BWT/FM-index）和启发式策略（seed-and-extend）是掌握现代比对工具（如BWA, Bowtie2, Minimap2）的关键。对比对结果进行严格的质量评估（MAPQ, 覆盖度等）是保证下游分析可靠性的前提。

---

## 三、 变异检测方法学 (Variant Calling)

**引言：** 比对完成后，下一步是识别样本基因组相对于参考基因组的差异，即变异检测（Variant Calling）。这是遗传学、肿瘤学等众多研究领域的核心目标。

![变异类型](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_types.svg)
*图4：常见的基因组变异类型示意图，包括SNP、Indel和多种结构变异（SV）。*

### 1. 变异类型概述

*   **单核苷酸多态性 (Single Nucleotide Polymorphism, SNP)：**
    *   基因组上单个核苷酸的改变。是最常见的变异类型。
    *   **替换（Substitution）类型：**
        *   **转换（Transition）：** 嘌呤替换嘌呤（A <-> G）或嘧啶替换嘧啶（C <-> T）。通常更常见。
        *   **颠换（Transversion）：** 嘌呤替换嘧啶或反之（A/G <-> C/T）。
*   **插入缺失 (Insertion/Deletion, Indel)：**
    *   基因组中短片段（通常定义为1bp至~50bp，有时界限更宽）的插入或缺失。
    *   长度是3的倍数的Indel（在编码区内）称为**框内Indel（in-frame indel）**，不引起后续氨基酸读码框的改变，但会增删氨基酸。
    *   长度不是3的倍数的Indel（在编码区内）称为**移码突变（frameshift indel）**，会改变下游所有氨基酸序列，通常导致蛋白质功能丧失（提前出现终止密码子）。
*   **结构变异 (Structural Variation, SV)：**
    *   基因组上较大片段（通常>50bp，可达Mb级别）的改变。类型复杂，对基因组功能和疾病影响显著。
    *   主要类型包括：
        *   **缺失（Deletion, DEL）：** 基因组片段丢失。
        *   **插入（Insertion, INS）：** 基因组中插入了新的DNA片段（来源可以是基因组内、病毒或转座子等）。
        *   **重复（Duplication, DUP）：** 基因组片段的拷贝数增加。
            *   **串联重复（Tandem Duplication）：** 重复单元彼此相邻。
        *   **拷贝数变异 (Copy Number Variation, CNV)：** 更广义的概念，指DNA片段（>1kb）的拷贝数目的变化（增加或减少）。包括缺失和重复。
        *   **倒位 (Inversion, INV)：** 基因组片段的方向颠倒。
        *   **易位 (Translocation, TRA/BND - Breakend)：** 基因组片段从一个位置移动到另一个位置（可能在同一染色体或不同染色体之间）。
            *   **平衡易位：** 基因组总含量不变。
            *   **非平衡易位：** 伴随DNA片段的丢失或增加。
        *   **复杂重排 (Complex Rearrangements):** 涉及多种SV类型的组合事件。

*   **变异检测的挑战 (Challenges in Variant Calling):**
    *   **测序错误：** NGS本身存在一定的错误率，可能被误判为低频变异。
    *   **比对错误：** 尤其是在重复序列、低复杂度区域、或存在Indel/SV的区域，比对可能不准确或模糊。
    *   **基因组复杂区域：** 重复序列、片段重复（segmental duplications）、高GC/AT区域等给比对和变异检测带来困难。
    *   **等位基因偏好性（Allelic Bias）：** 由于文库构建或测序过程的偏差，导致两个等位基因的读段比例不符合预期（如杂合位点非1:1）。
    *   **样本质量和纯度：** DNA降解、污染、肿瘤样本中的正常细胞混杂（影响体细胞变异频率）。
    *   **计算资源：** 处理大规模测序数据需要大量的计算和存储资源。
    *   **区分真假变异：** 如何有效过滤掉系统性错误和随机噪声，是变异检测算法的核心挑战。

### 2. SNP与小Indel检测原理

![变异检测流程](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_calling_workflow.svg)
*图5：典型的SNP/Indel变异检测流程示意图，从比对后的BAM文件到最终的VCF文件。*

*   **基于比对的变异检测流程 (Alignment-based Variant Calling Workflow):**
    1.  **输入：** 经过质控和比对的BAM文件（通常需要排序和去除重复）。
    2.  **（可选但推荐）数据预处理：**
        *   **标记/去除PCR重复 (Mark/Remove Duplicates):** 使用`Picard MarkDuplicates`或`samtools markdup`。重复读段源自同一原始DNA片段，保留它们会人为抬高支持某个碱基的证据，导致假阳性。
        *   **（GATK流程）局部重比对 (Local Realignment around Indels):** 早期GATK流程步骤，现在通常整合进HaplotypeCaller。目的是校正Indel附近的比对错误。
        *   **（GATK流程）碱基质量得分重校正 (Base Quality Score Recalibration, BQSR):** 利用已知的变异位点数据库，建立错误模型，调整原始碱基质量得分，使其更准确反映真实的测序错误概率。
    3.  **变异位点识别 (Variant Site Identification):** 遍历基因组或目标区域，在每个位置收集覆盖该位点的读段信息（碱基、碱基质量、比对质量、链来源等）。
    4.  **基因型推断 (Genotyping):** 基于收集到的证据，使用统计模型（通常是贝叶斯模型）计算每个可能基因型（如纯合参考/杂合/纯合变异）的后验概率或似然度。给出最可能的基因型及其置信度。
    5.  **输出：** 原始变异列表，通常为VCF（Variant Call Format）或其压缩版BCF/gVCF格式。
    6.  **变异过滤 (Variant Filtering):** 应用一系列质量控制标准，去除可能是假阳性的变异位点。这是保证结果可靠性的关键步骤。
    7.  **（后续步骤）变异注释 (Variant Annotation):** 见下一章节。

*   **变异位点识别与基因型推断算法核心思想：**
    *   **Pileup方法（早期方法，如SAMtools mpileup早期版本）：**
        *   在每个基因组位置，简单统计覆盖该位置的不同碱基及其质量。
        *   基于碱基计数和频率设定阈值来判断是否存在变异。
        *   基因型推断可能基于简单的频率阈值或基础的统计检验。
        *   **缺点：** 对比对错误、测序错误、Indel处理敏感，容易产生假阳性/假阴性。
    *   **基于统计模型的方法（主流方法，如GATK HaplotypeCaller, FreeBayes, SAMtools/bcftools call）：**
        *   **核心：贝叶斯推理框架。** `P(Genotype | Data) ∝ P(Data | Genotype) * P(Genotype)`
        *   **似然度计算 `P(Data | Genotype)`：** 对于一个给定的基因型（如杂合A/G），计算观测到当前覆盖该位点的所有读段（考虑碱基、碱基质量、比对错误概率等）的可能性。这是模型的核心。
        *   **先验概率 `P(Genotype)`：** 可以是均一的（无偏好），也可以基于群体频率（如dbSNP）或连锁不平衡信息。
        *   **后验概率 `P(Genotype | Data)`：** 通过贝叶斯公式计算得到不同基因型的后验概率，选择概率最高的作为推断的基因型。
        *   **质量评分：** 同时会计算一个变异置信度评分（QUAL score in VCF），表示该位点存在变异（相对于参考基因型）的可信度。
        *   **Haplotype-based方法（如GATK HaplotypeCaller）：** 不仅考虑单个位点，而是在一个活跃区域（active region）内，尝试**局部组装**出可能的单倍型（haplotypes），然后计算每条读段支持哪个单倍型的似然度。这种方法对**Indel和小区域内密集变异的检测**特别有效。

*   **过滤策略与质量控制 (Filtering Strategies & QC):**
    *   过滤是去除假阳性的关键。VCF文件提供了丰富的质量信息用于过滤。
    *   **硬过滤 (Hard Filtering):** 基于设定的固定阈值对单个指标进行过滤。常用指标：
        *   **QUAL:** 变异置信度总分 (Phred-scaled)。过滤低分。
        *   **DP (Depth):** 位点总覆盖深度。过滤过低（证据不足）或过高（可能重复区域/扩增偏好）的位点。
        *   **QD (Quality by Depth):** QUAL / DP。标准化后的质量，避免高深度位点QUAL虚高。过滤低QD。
        *   **FS (Fisher Strand Bias):** 评估变异证据在正反链上的分布是否显著不平衡（指示测序或比对错误）。过滤高FS。
        *   **MQ (Mapping Quality):** 支持变异的读段的平均MAPQ值。过滤低MQ。
        *   **SOR (Strand Odds Ratio):** 另一种链偏好性度量。
        *   **MQRankSum / ReadPosRankSum:** 比较支持参考和变异等位基因的读段在某些属性（MAPQ、在读段上的位置）上是否存在显著差异。
    *   **GATK变异质量得分重校正 (Variant Quality Score Recalibration, VQSR):**
        *   **机器学习方法，是GATK推荐的最佳实践。**
        *   需要一个高质量的“真集”（Truth Set, 如HapMap, 1000 Genomes Omni chip SNPs）和一个已知但可能包含错误的数据库（如dbSNP）。
        *   训练一个高斯混合模型（Gaussian Mixture Model）来学习真变异的特征分布（基于QD, FS, MQ等多种注释信息）。
        *   将模型应用到当前样本的变异集上，给每个变异打一个VQSLOD得分（log-odds of being a true variant）。
        *   根据目标真阳性率（Target Sensitivity, e.g., 99.5%）设定VQSLOD阈值进行过滤。
        *   **优点：** 比硬过滤更智能，能更好地区分系统性错误，平衡敏感性和特异性。
        *   **缺点：** 需要高质量的训练集，计算量大，不适用于没有良好训练集的物种或非模式生物。

*   **常见工具比较 (GATK, FreeBayes, SAMtools/bcftools):**
    *   **GATK (Genome Analysis Toolkit):**
        *   **优点：** 功能全面（覆盖预处理到注释），提供"最佳实践"流程，社区庞大，文献引用多，VQSR过滤效果好。HaplotypeCaller对Indel和复杂区域处理优秀。
        *   **缺点：** 流程相对复杂，计算资源需求大，运行时间长。
    *   **FreeBayes:**
        *   **优点：** 基于单倍型的贝叶斯算法，对SNP和Indel检测性能良好，尤其在多种群或混合样本（如肿瘤、宏基因组）中有优势。速度通常比GATK快。不依赖外部数据库进行校正。
        *   **缺点：** 参数较多，过滤策略可能需要用户自定义。有时被认为特异性稍低于GATK VQSR。
    *   **SAMtools (mpileup) + bcftools (call):**
        *   **优点：** 轻量级，速度快，内存占用小，参数相对简单。对非模式生物或不需要复杂模型的场景很实用。`bcftools`提供了丰富的VCF文件操作和过滤功能。
        *   **缺点：** 基础模型相对简单（虽然`bcftools call`的多等位基因和HMM模型已很强大），对复杂Indel的处理可能不如基于局部组装的方法。

### 3. GATK变异检测流程详解 (Focus on GATK Best Practices)

GATK Best Practices是广泛遵循的SNP/Indel检测标准流程（可能随版本更新）。

*   **最佳实践工作流程 (Best Practices Workflow for Germline Short Variants):**
    1.  **数据预处理 (Data Pre-processing):**
        *   `FastQC` (原始数据质控) -> `Trimmomatic`/`fastp` (去接头和低质量碱基)
        *   `BWA-MEM` (比对) -> `samtools sort` (排序)
        *   `Picard MarkDuplicates` (标记PCR重复)
        *   `GATK BaseRecalibrator` + `ApplyBQSR` (碱基质量重校正) -> 生成分析就绪的BAM (analysis-ready BAM)。
    2.  **变异检测 (Variant Calling):**
        *   `GATK HaplotypeCaller` (以`-ERC GVCF`模式运行，为每个样本生成基因组VCF, gVCF)。gVCF记录了每个位点（包括未变异位点）的基因型似然度信息和覆盖度等，便于后续多样本联合分析。
    3.  **多样本联合基因分型 (Joint Genotyping):**
        *   （可选但推荐用于队列研究）`GATK GenomicsDBImport` (将多个样本的gVCF导入数据库)。
        *   `GATK GenotypeGVCFs` (基于数据库中的所有样本信息，进行联合基因型推断，生成包含所有样本基因型的多样本VCF)。**联合分析能借助群体信息提高低覆盖位点变异检测的敏感性。**
    4.  **变异过滤 (Variant Filtering):**
        *   `GATK VariantRecalibrator` + `ApplyVQSR` (进行SNP和Indel的VQSR过滤)。需要提供合适的训练资源文件（resource files）。

*   **HaplotypeCaller算法核心：**
    1.  **确定活跃区域（Active Regions）：** 识别基因组上可能有变异信号的区域。
    2.  **局部组装（Local Assembly）：** 对每个活跃区域内的读段进行局部 *de novo* 组装，构建出若干最可能的候选单倍型序列。
    3.  **读段-单倍型配对似然度计算（PairHMM）：** 对每条覆盖该区域的读段，计算其源自各个候选单倍型的似然度。
    4.  **基因型分配（Genotyping）：** 基于每个样本的读段似然度数据，使用贝叶斯方法计算每个位点上不同基因型的后验概率，并输出最终的基因型和置信度。

*   **碱基质量重校正 (BQSR) 原理：**
    *   测序仪报告的碱基质量分数可能存在系统性偏差（受机器循环数、序列上下文等影响）。
    *   BQSR利用样本中已知（大概率是真）的变异位点（来自dbSNP等数据库）作为参照。
    *   统计在这些已知位点处，报告的碱基质量与实际观测到的错误率之间的关系，建立协变量模型（covariates: read group, quality score, cycle, context）。
    *   应用这个模型调整BAM文件中所有碱基的质量分数，使其更接近真实的错误概率。
    *   **目的：减少由于系统性测序错误导致的假阳性SNP。**

*   **变异质量评分重校正 (VQSR) 原理回顾：**
    *   利用机器学习区分真变异和假变异（通常是系统性错误）。
    *   基于已知的高质量变异（truth set）和大量变异特征（annotations like QD, FS, MQ, etc.）训练模型。
    *   对新检出的变异进行打分，并根据设定的敏感度阈值进行过滤。

*   **多样本联合分析策略 (Joint Analysis Strategy via gVCFs):**
    *   **优势：**
        *   提高在低覆盖度位点检测变异的能力（一个样本覆盖低，但其他样本覆盖高且显示变异）。
        *   确保所有样本在所有位点都有基因型信息（即使是参考纯合），便于后续比较分析。
        *   一次性完成基因分型，结果一致性好。
    *   **流程：** HaplotypeCaller (GVCF mode per sample) -> CombineGVCFs (小队列) or GenomicsDBImport (大队列) -> GenotypeGVCFs -> VQSR.

### 4. 结构变异检测方法 (Structural Variation Detection)

SV检测比SNP/Indel更具挑战性，因为SV信号模式多样且易受基因组复杂性影响。通常需要整合多种信号。

*   **基于读长深度 (Read Depth, RD) 的方法：**
    *   **原理：** 基因组区域的拷贝数变化会引起该区域测序深度的相应改变（缺失区域深度降低，重复区域深度升高）。
    *   **方法：** 将基因组划分成窗口（bins），计算每个窗口的归一化深度。通过分割算法（segmentation algorithms）识别出深度发生显著、持续变化的区域。
    *   **优点：** 能检测较大范围的CNV（缺失和重复）。
    *   **缺点：** 分辨率受窗口大小限制；对局部、小的SV不敏感；难以检测平衡性SV（倒位、平衡易位）；易受GC偏好、重复序列导致的比对偏差影响，需要仔细的归一化。
    *   **工具：** `CNVnator`, `Control-FREEC`, `GATK CNV`.

*   **基于配对末端不一致 (Paired-End Mapping, PEM) 的方法：**
    *   **原理：** 利用双端测序读段对（read pairs）的预期距离（insert size）和方向（orientation）。如果比对后发现距离异常（过大/过小）或方向异常（e.g., FF, RR, RF），则提示可能存在SV。
        *   **距离变大：** 可能提示缺失（DEL）。
        *   **距离变小：** 可能提示插入（INS）或串联重复。
        *   **方向异常（e.g., +/- -> +/+ or -/-）：** 可能提示倒位（INV）。
        *   **比对到不同染色体/远距离：** 可能提示易位（TRA）或大片段插入/缺失。
    *   **优点：** 能检测多种类型的SV（DEL, INS, DUP, INV, TRA），分辨率较高（取决于插入片段大小）。
    *   **缺点：** 插入片段大小分布不均会产生噪音；对插入序列的内容无法直接获知；在重复区域附近信号可能模糊。
    *   **工具：** `Delly`, `Lumpy`, `Manta`. (这些工具通常也整合其他信号)

*   **基于分割读长 (Split Read, SR) 的方法：**
    *   **原理：** 当一条测序读段跨越一个SV断点（breakpoint）时，它会被比对到基因组的两个（或多个）不连续的位置。这种“劈开”的比对模式直接标示了断点的位置和类型。
    *   **优点：** 提供**碱基级别的断点分辨率**；对检测小插入、缺失和复杂重排很有用。
    *   **缺点：** 要求读段足够长能跨越断点；在重复序列区域可能产生大量假阳性劈开比对；比对算法需要支持split alignment（如BWA-MEM, Minimap2）。
    *   **工具：** `Delly`, `Lumpy`, `Manta` (通常与PEM结合使用)。

*   **基于从头组装 (Assembly-based, AS) 的方法：**
    *   **原理：**
        *   **局部组装：** 在检测到SV信号的区域（如PEM/SR提示的区域），收集相关读段进行局部*de novo*组装，然后将组装出的contig与参考基因组比对，精确解析SV结构。
        *   **全基因组组装：** 对样本进行*de novo*组装，然后将组装结果与参考基因组进行比对（whole-genome alignment），识别结构差异。
    *   **优点：** 能精确解析SV（包括断点序列和插入序列），发现新的、复杂的SV。是发现大插入（novel insertions）的主要方法。
    *   **缺点：** 计算成本高；对测序数据质量和深度要求高；组装本身可能产生错误。
    *   **工具：** `Manta` (整合了局部组装), `SvABA`, 以及依赖全基因组组装的流程。

*   **长读长测序在SV检测中的优势：**
    *   **关键优势：** 长读段（kb-Mb级别）能够**完整地跨越大多数SV事件和重复序列区域**。
    *   **使得：**
        *   SV的检测和分型（typing）更直接、准确（一条读段就可能包含完整SV信息）。
        *   断点定位更精确。
        *   能够发现以往短读长难以检测的复杂SV和在重复区域内的SV。
        *   更容易进行单倍型定相（phasing），区分两个染色拷贝上的SV。
    *   **常用工具（基于长读长）：** `pbsv` (PacBio官方), `Sniffles` (广泛使用), `SVIM`, `cuteSV`.

*   **常用SV检测工具 (整合多种信号):**
    *   **`Delly`:** 主要基于PEM和SR信号。
    *   **`Lumpy`:** 整合PEM, SR, RD信号的概率框架。常与`SVTyper`配合进行基因分型。
    *   **`Manta`:** 专门为肿瘤样本设计（但也可用于germline），整合PEM, SR, 局部组装信息，对小Indel和SV都有较好表现。速度快。
    *   **`GRIDSS`:** 另一个整合多种信号的工具，以高敏感性著称。

### 5. 拷贝数变异检测 (Copy Number Variation, CNV) - 深入RD方法

CNV检测是SV检测的一个重要子集，RD方法是其基础。

*   **读长深度归一化 (Read Depth Normalization):**
    *   **目标：** 去除技术性因素（如GC含量、基因组可比对性 mappability）对测序深度的影响，使深度能真实反映拷贝数。
    *   **方法：**
        *   **GC含量校正：** 不同GC含量的区域PCR扩增效率和测序效率不同。通常根据每个窗口的GC含量进行校正。
        *   **可比对性校正：** 基因组上某些区域（如重复序列）的比对唯一性差，导致有效覆盖深度偏低。需要根据预先计算的mappability map进行校正。
        *   **批次效应校正：** 不同测序批次、不同文库可能存在系统性差异，需要进行样本间或组间的归一化。使用对照样本集或主成分分析（PCA）等方法。

*   **分段算法 (Segmentation Algorithms):**
    *   **目标：** 在归一化后的深度数据中，识别出拷贝数发生改变的连续区域（segments）。
    *   **常用算法：**
        *   **Circular Binary Segmentation (CBS):** 经典的递归分割算法，寻找使两段均值差异最大的断点。
        *   **Hidden Markov Models (HMM):** 将拷贝数状态（如纯合缺失、单拷贝缺失、正常、单拷贝增益等）作为隐藏状态，观测值为深度数据，通过模型找到最可能的拷贝数状态序列。
        *   **其他：** 基于平滑、聚类等方法。

*   **拷贝数状态推断 (Copy Number State Inference):**
    *   将分段后的区域的平均深度值转化为具体的拷贝数（如0, 1, 2, 3, ...）。
    *   对于二倍体正常样本，期望拷贝数为2。需要确定基线（baseline）。
    *   对于肿瘤样本，情况更复杂，需要考虑**肿瘤纯度（tumor purity）**和**平均倍性（ploidy）**。通常需要专门的算法（如`ASCAT`, `ABSOLUTE`, `Sequenza`）来同时估计这些参数并推断**等位基因特异性拷贝数（allele-specific copy number）**。

*   **常用CNV检测工具 (基于WGS/WES深度):**
    *   **`CNVnator`:** 基于RD方法，使用均值漂移（mean-shift）聚类进行分割。适用于WGS。
    *   **`Control-FREEC`:** 可以结合对照样本信息，利用RD和可选的B等位基因频率（BAF）信息。适用于WGS和WES。
    *   **`GATK gCNV`:** GATK 4+提供的CNV检测工具，基于贝叶斯模型，能处理批次效应，适用于WES和WGS。
    *   **`XHMM`:** 专门为WES数据设计的CNV检测工具，基于HMM模型，利用主成分分析（PCA）进行归一化。

### 6. 变异检测结果评估与验证

变异检测结果（尤其是SV和低频变异）需要进行严格的评估和验证。

*   **基本概念：**
    *   **真阳性 (True Positive, TP):** 真实存在且被检测到的变异。
    *   **假阳性 (False Positive, FP):** 真实不存在但被检测到的变异（错误）。
    *   **真阴性 (True Negative, TN):** 真实不存在且未被检测到的变异。
    *   **假阴性 (False Negative, FN):** 真实存在但未被检测到的变异（漏检）。

*   **评估指标：**
    *   **敏感性 (Sensitivity / Recall):** `TP / (TP + FN)`，检测到真变异的能力。
    *   **特异性 (Specificity):** `TN / (TN + FP)`，正确排除非变异位点的能力。
    *   **精确率 (Precision / Positive Predictive Value, PPV):** `TP / (TP + FP)`，检测到的变异中真实比例。
    *   **F1分数 (F1 Score):** `2 * (Precision * Recall) / (Precision + Recall)`，精确率和召回率的调和平均数。

*   **变异检测的黄金标准 (Gold Standard):**
    *   获得公认的、高质量的变异数据集用于比较评估。
    *   **NIST/GIAB (Genome in a Bottle) 标准品：** 对特定人类样本（如NA12878）通过整合多种技术平台和分析方法，生成了高置信度的SNP、Indel和部分SV基准数据集（benchmark dataset）和定义区域（high-confidence regions）。是评估变异检测流程性能的重要资源。
    *   **模拟数据 (Simulated Data):** 在已知基因组背景上人工引入变异，生成模拟测序数据。可以精确知道真变异位置，但可能无法完全模拟真实数据的复杂性。

*   **技术验证方法 (Experimental Validation):**
    *   对计算预测出的关键变异（如潜在致病变异、新发现的SV）进行实验验证。
    *   **Sanger测序：** **验证SNP和小Indel的金标准。**
    *   **定量PCR (qPCR) 或数字PCR (dPCR/ddPCR):** **验证CNV的金标准。** 可精确定量特定区域的拷贝数。
    *   **长读长测序 (PacBio/ONT):** 可用于验证复杂SV、Indel，并提供定相信息。
    *   **光学图谱 (Optical Mapping, e.g., Bionano Genomics):** 提供大尺度基因组结构信息，可独立验证大型SV。
    *   **细胞遗传学方法 (Karyotyping, FISH):** 验证大的染色体畸变和易位。

**本章小结：** 变异检测是从比对数据中提取生物学意义的关键步骤。SNP/Indel检测依赖于复杂的统计模型（如贝叶斯方法、HMM），而SV检测则需要整合多种信号（RD, PEM, SR, AS）。GATK提供了一套被广泛认可的最佳实践流程。长读长测序显著提升了SV检测能力。理解各种变异类型、检测原理、常用工具的优缺点以及结果的评估与验证方法，对于准确解读基因组变异至关重要。

---

## 四、 变异注释与功能预测原理

**引言：** 变异检测产生了大量的变异位点（通常成千上万甚至数百万）。下一步是**注释（Annotation）**这些变异，即赋予它们生物学意义，包括它们在基因组上的位置、对基因功能可能产生的影响，以及在群体中的频率等。功能预测（Functional Prediction）则更进一步，尝试评估变异（尤其是非同义突变）对蛋白质功能或基因调控的潜在影响程度。

![变异注释工作流程](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_annotation_workflow.svg)
*图6：变异注释工作流程示意图，展示了VCF文件如何结合各种数据库资源进行注释。*

### 1. 变异注释的基本概念

变异注释就是将变异信息与已知的基因组特征和生物学知识库进行关联。

*   **基因组注释数据库 (Genome Annotation Databases):**
    *   是变异注释的基础，提供了基因、转录本、蛋白质、调控元件等信息及其在基因组上的坐标。
    *   **主要来源：**
        *   **NCBI RefSeq:** (Reference Sequence) 提供了一套全面的、非冗余的基因、转录本和蛋白质序列标准。
        *   **Ensembl:** (European Bioinformatics Institute & Wellcome Sanger Institute) 提供多物种的基因组注释，包含基因、转录本、调控元件、比较基因组学信息等。
        *   **UCSC Genome Browser:** 提供了丰富的基因组注释轨道（tracks）和可视化工具，整合了来自多个来源的数据。
        *   **GENCODE:** (ENCyclopedia Of DNA Elements project) 旨在提供最完整、最高质量的人类和小鼠基因注释，是Ensembl注释的主要来源。
    *   **关键信息：** 基因位置（染色体、起始终止坐标、链方向）、外显子/内含子结构、编码区（CDS）位置、非编码RNA信息、假基因、调控区域（启动子、增强子）等。

*   **变异位置注释 (Variant Location Annotation):**
    *   确定变异落在基因组的哪个区域，以及与哪个基因（或多个基因）相关。
    *   **基本注释类型：**
        *   **基因间区 (Intergenic):** 位于基因之外。
        *   **基因内 (Intronic):** 位于基因的内含子区域。
        *   **外显子区 (Exonic):** 位于基因的外显子区域。这是最受关注的区域，因为直接影响编码蛋白。
        *   **剪接位点区 (Splicing Region):** 位于外显子-内含子边界附近（通常是内含子最后几个碱基或外显子最前/最后几个碱基），可能影响mRNA剪接。
            *   **经典剪接位点（Canonical Splice Site）：** 内含子边界的GT-AG（或GC-AG）二核苷酸。
        *   **UTR区 (Untranslated Region):** 位于编码区（CDS）上游（5' UTR）或下游（3' UTR）的外显子区域，影响mRNA稳定性、翻译效率和定位。
        *   **上游/下游区域 (Upstream/Downstream):** 位于基因转录起始位点上游或转录终止位点下游一定距离内，可能包含启动子等调控元件。
        *   **调控区域 (Regulatory Region):** 如增强子、启动子、CTCF结合位点等（需要专门的调控元件数据库注释）。

*   **变异功能注释 (Variant Consequence Annotation):**
    *   预测变异对基因产物（主要是蛋白质）的影响。这是解释变异生物学意义的核心。
    *   **常见功能注释类型（基于对编码序列的影响）：**
        *   **同义突变 (Synonymous / Silent):** 改变了密码子，但编码的氨基酸**不变**。曾被认为是中性的，但现在发现可能影响mRNA稳定性、剪接、翻译效率等。
        *   **错义突变 (Missense):** 改变了密码子，导致编码的氨基酸**改变**。其影响从无害到严重破坏蛋白质功能不等。
        *   **无义突变 (Nonsense):** 密码子变为**终止密码子**，导致蛋白质翻译提前终止，通常产生截短的、无功能的蛋白质（除非发生在基因末端）。
        *   **移码突变 (Frameshift):** 由非3倍数长度的Indel引起，改变了读码框，导致下游氨基酸序列完全改变，并通常很快遇到提前终止密码子。**通常是功能丧失型（Loss-of-Function, LoF）变异。**
        *   **起始密码子变异 (Start Codon Modification / Start Lost):** 改变了起始密码子（通常是AUG），可能导致翻译无法起始或从下游的密码子开始，产生N端截短或不同的蛋白质。
        *   **终止密码子变异 (Stop Codon Modification / Stop Lost):** 改变了终止密码子，导致翻译继续进行，产生C端延长的蛋白质。
        *   **框内插入/缺失 (In-frame Insertion/Deletion):** 插入或删除了整数个密码子（3n bp），不改变读码框，但增加或减少了氨基酸。
        *   **剪接位点变异 (Splice Site Variant):** 发生在剪接供体（donor, GT）或受体（acceptor, AG）位点，很可能破坏正常的mRNA剪接，导致外显子跳跃、内含子保留等。**通常是LoF变异。**
        *   **剪接区域变异 (Splice Region Variant):** 发生在剪接位点附近（非经典位点），也可能影响剪接效率或导致使用隐蔽剪接位点。
    *   **影响程度分类（Sequence Ontology / VEP / SnpEff 定义）：** 通常将这些后果按潜在影响严重程度分类，如 HIGH (移码、无义、剪接位点等), MODERATE (错义), LOW (同义), MODIFIER (UTR、内含子、基因间区等)。

*   **群体频率注释 (Population Frequency Annotation):**
    *   查询变异在大型参考人群数据库中的等位基因频率（Allele Frequency, AF）。
    *   **常用数据库：**
        *   **1000 Genomes Project (1kGP):** 较早的大规模人群测序计划，覆盖多个主要族裔。
        *   **Exome Aggregation Consortium (ExAC):** 聚合了约6万人的外显子组数据。
        *   **Genome Aggregation Database (gnomAD):** 目前规模最大（数十万人级别）的公开人群基因组（WGS+WES）数据库，提供多个人群（包括不同族裔细分）的频率信息。**是评估变异稀有度的首选资源。**
        *   **dbSNP:** 虽然主要是变异的注册库，但也整合了来自多个来源的频率信息。
    *   **重要性：**
        *   **区分常见多态性与罕见变异：** 对于孟德尔遗传病，致病变异通常是罕见的（e.g., AF < 1% or 0.1%）。在群体中频率很高的变异通常被认为是良性的多态性。
        *   **体细胞突变过滤：** 在肿瘤研究中，一个变异如果在正常人群中有较高频率，则不太可能是体细胞突变，而更可能是胚系变异或系统性错误。

### 2. 变异注释工具原理

多种工具可以自动化完成变异注释过程，它们通常整合上述多种数据库资源。

*   **ANNOVAR (Annotate Variation) 工作原理：**
    *   非常流行和灵活的命令行工具。
    *   **流程：**
        1.  读取输入的VCF文件（或其他格式）。
        2.  将变异坐标映射到不同的基因注释系统（RefSeq, Ensembl, UCSC等）。
        3.  根据基因模型，确定变异的位置（exonic, intronic, etc.）和功能后果（synonymous, missense, etc.）。
        4.  查询并添加来自各种数据库的注释信息，如群体频率（1kGP, ExAC, gnomAD）、临床意义（ClinVar）、功能预测得分（SIFT, PolyPhen2, CADD）、保守性得分、调控元件信息等。
    *   **特点：** 支持极其丰富的数据库（需用户自行下载），可定制性强。

*   **SnpEff (SNP Effect Predictor) 工作原理：**
    *   另一个广泛使用的注释工具。
    *   **流程：**
        1.  需要预先为目标物种和基因组版本构建或下载一个**数据库文件（database file）**。这个数据库包含了基因模型和预计算的一些信息。
        2.  读取VCF文件，快速将变异映射到数据库中的基因模型，预测功能后果。
        3.  可以添加一些外部数据库注释（如ClinVar），但数据库集成不如ANNOVAR灵活。
    *   **特点：** 速度快（因为使用预编译数据库），功能后果预测遵循Sequence Ontology标准，输出格式（VCF INFO字段的ANN/EFF tag）标准化。

*   **VEP (Variant Effect Predictor) 工作原理：**
    *   由Ensembl团队开发和维护的工具，与Ensembl数据库紧密集成。
    *   **流程：**
        1.  可以通过在线网页、REST API或本地安装的命令行版本使用。
        2.  将变异映射到Ensembl的基因和转录本模型。
        3.  提供非常详细的功能后果预测（支持多套转录本）。
        4.  能够整合大量来自Ensembl数据库的注释信息，包括调控元件、保守性、其他物种同源基因等。
        5.  可以方便地接入并添加来自dbSNP, ClinVar, gnomAD等的注释，以及多种功能预测工具（SIFT, PolyPhen2, CADD等）的得分（通过插件或缓存）。
    *   **特点：** 与Ensembl生态系统无缝对接，注释信息非常全面，功能后果预测详细，支持自定义注释。

*   **注释格式与输出解读 (VCF INFO field):**
    *   注释结果通常添加到VCF文件的INFO字段中，以`Key=Value`对的形式存在，多个值用逗号分隔。
    *   **ANNOVAR:** 通常输出多个文件或在VCF INFO中添加多个字段，如 `Gene.refGene`, `Func.refGene`, `ExonicFunc.refGene`, `gnomAD_AF`, `SIFT_score`, `Polyphen2_HDIV_score` 等。
    *   **SnpEff:** 主要添加 `ANN` (或旧版的`EFF`) 字段。该字段包含由`|`分隔的多个子字段，每个子字段代表对一个转录本的影响，格式为 `Allele | Annotation | Annotation_Impact | Gene_Name | Gene_ID | Feature_Type | Feature_ID | Transcript_BioType | Rank | HGVS.c | HGVS.p | cDNA.pos / cDNA.length | CDS.pos / CDS.length | Protein.pos / Protein.length | Distance to feature | Errors / Warnings / Info`。
    *   **VEP:** 主要添加 `CSQ` (Consequence) 字段，格式与SnpEff类似，也是`|`分隔的多个子字段，包含对每个转录本的影响及各种相关注释。字段内容和顺序可以通过参数配置。

### 3. 变异功能预测方法 (In Silico Functional Prediction)

对于非同义突变（错义突变）或可能影响调控的变异，需要进一步预测其对功能的潜在影响。

*   **保守性分析 (Conservation Analysis):**
    *   **原理：** 进化上保守的氨基酸位点或基因组区域通常具有重要的功能，这些位置的改变更可能是有害的。
    *   **方法：** 比较多个物种（从近缘到远缘）的同源蛋白质序列或基因组区域。计算每个位点的保守性得分。
    *   **常用得分：** PhyloP, PhastCons (基于多物种比对的概率模型)。 GERP++ (Genomic Evolutionary Rate Profiling)。
    *   **应用：** 保守性高的位点发生的错义突变，更可能是功能相关的。

*   **蛋白质结构预测 (Protein Structure Prediction / Analysis):**
    *   **原理：** 氨基酸替换可能影响蛋白质的结构稳定性、折叠、与其他分子的相互作用（蛋白-蛋白、蛋白-DNA、蛋白-配体）。
    *   **方法：**
        *   如果蛋白质结构已知（来自PDB数据库），可以直接分析突变位点在三维结构中的位置（核心/表面）、是否涉及活性位点/结合界面、是否破坏关键的化学键（如二硫键）或二级结构。
        *   如果结构未知，可以通过同源建模或从头预测方法构建模型，然后进行分析。
    *   **应用：** 预测突变是否会破坏蛋白质结构或关键功能位点。

*   **基于序列特征和机器学习的方法 (Sequence-based Features & Machine Learning):**
    *   **原理：** 利用氨基酸的物理化学性质（大小、电荷、疏水性等）、序列上下文、保守性、结构预测信息等作为特征，训练机器学习模型（如SVM、随机森林、神经网络）来区分已知的致病变异和良性变异。
    *   **方法：**
        1.  收集大量的已知致病和良性变异作为训练集。
        2.  为每个变异提取多种生物信息学特征。
        3.  训练分类器模型。
        4.  用模型预测新变异的致病可能性（通常输出一个得分或概率）。
    *   **应用：** 这是目前主流的功能预测工具（如SIFT, PolyPhen-2, CADD, REVEL）的核心。

*   **整合预测算法 / 元预测器 (Ensemble / Meta-predictors):**
    *   **原理：** 单个预测工具各有优劣。整合多个不同工具的预测结果，可以提高预测的准确性和鲁棒性。
    *   **方法：** 使用加权平均、投票机制或训练一个新的机器学习模型（以其他工具的得分为输入特征）来得到最终的综合预测。
    *   **应用：** REVEL, M-CAP 等是典型的元预测器。

### 4. 变异功能预测工具 (Examples of Functional Prediction Tools)

*   **SIFT (Sorting Intolerant From Tolerant):**
    *   **原理：** 基于**序列同源性**和氨基酸替换的**物理化学性质**。它假设重要的氨基酸位点在进化中会保持保守。通过搜索同源序列数据库，构建一个比对谱（profile），评估待预测的氨基酸替换在该位置是否能够被容忍。
    *   **输出：** 一个得分（SIFT score）和一个定性预测（"Tolerated" 或 "Deleterious"）。得分越低（<0.05），越可能有害。
    *   **应用：** 广泛用于预测错义突变的功能影响。

*   **PolyPhen-2 (Polymorphism Phenotyping v2):**
    *   **原理：** 整合了**序列保守性信息**、**蛋白质结构信息**（如果可用，如预测对结构稳定性的影响、是否在跨膜区或结合位点）以及**序列注释特征**（如是否在关键功能域）。使用朴素贝叶斯分类器进行预测。
    *   **输出：** 一个概率得分（0到1）和三个定性预测（"Benign", "Possibly damaging", "Probably damaging"）。得分越高，越可能有害。提供HumDiv和HumVar两个模型，分别针对孟德尔疾病和复杂疾病训练。
    *   **应用：** 也是非常流行的错义突变预测工具。

*   **CADD (Combined Annotation Dependent Depletion):**
    *   **原理：** 一个**整合性的框架**，不直接预测致病性，而是**预测一个变异的“有害程度”（deleteriousness）**。它整合了极其广泛的注释信息（~63种），包括保守性、调控元件信息、基因模型信息、功能预测得分（来自SIFT/PolyPhen等）等。使用支持向量机（SVM）模型，通过比较人类中存活下来的衍生等位基因与模拟的（理论上可能发生但未存活的）变异进行训练。
    *   **输出：** Phred-scaled CADD score。得分越高，表示该变异越可能是有害的（相对于所有可能的单核苷酸变异）。例如，得分10表示在所有可能变异中排名前10%有害，20表示前1%，30表示前0.1%。
    *   **应用：** 提供了一个相对客观的、全基因组范围的变异有害性度量，常用于变异优先级排序。

*   **REVEL (Rare Exome Variant Ensemble Learner):**
    *   **原理：** 一个**元预测器**，专门为**评估罕见错义变异的致病性**而设计。它整合了来自多个工具（包括SIFT, PolyPhen-2, MutationAssessor, FATHMM, MutationTaster, LRT, GERP++, PhyloP, PhastCons等共13个）的得分，使用随机森林模型进行训练。
    *   **输出：** 一个单一的得分（0到1）。得分越高，致病性可能性越大。
    *   **应用：** 在临床遗传学中被广泛用于对罕见错义变异进行优先级排序，表现优于单个工具。

*   **M-CAP (Mendelian Clinically Applicable Pathogenicity):**
    *   **原理：** 另一个**元预测器**，也专注于**孟德尔疾病相关的错义变异**。它整合了多个特征和预测得分，使用梯度提升树（gradient boosting tree）模型进行训练，并特别优化了在临床应用中的性能。
    *   **输出：** 一个得分（0到1）和预测（"Pathogenic" or "Benign"）。
    *   **应用：** 旨在提供一个更适用于临床决策的预测工具。

### 5. 变异优先级排序策略 (Variant Prioritization Strategies)

在实际研究中（如寻找疾病致病基因），需要从海量注释后的变异中筛选出最可疑的候选者。

*   **疾病研究中的变异筛选漏斗 (Filtering Funnel):**
    1.  **质量过滤：** 首先去除低质量的变异（基于QUAL, DP, VQSR等）。
    2.  **群体频率过滤：** 对于罕见病，通常过滤掉在gnomAD等数据库中频率较高（>1% 或 0.1%，取决于疾病模式）的常见变异。
    3.  **功能后果过滤：** 优先关注影响蛋白质编码或剪接的变异（如无义、移码、剪接位点、错义）。有时也会考虑调控区域变异。
    4.  **功能预测得分过滤：** 对错义突变，使用SIFT/PolyPhen2/CADD/REVEL等工具预测其有害性，保留得分提示有害的变异。
    5.  **结合临床表型和遗传模式：** 这是最关键的步骤。

*   **候选基因分析方法：**
    *   **基因功能知识：** 变异所在的基因的功能是否与疾病表型相关？（查询OMIM, GeneCards, UniProt等数据库，文献检索）。
    *   **基因表达谱：** 基因是否在受影响的组织或发育阶段表达？
    *   **动物模型：** 该基因的敲除或突变是否在模式生物中产生类似表型？
    *   **基因网络/通路分析：** 多个候选基因是否富集在某个已知的疾病相关通路？

*   **家系分析策略 (Family-based Analysis):**
    *   **共分离分析 (Co-segregation):** 在家系中，致病变异应该与疾病状态共分离（即所有患者携带，所有健康成员不携带，或符合特定遗传模式）。
    *   **遗传模式过滤：**
        *   **常染色体隐性 (Autosomal Recessive):** 患者应携带两个致病等位基因（纯合或复合杂合），父母通常是携带者。
        *   **常染色体显性 (Autosomal Dominant):** 患者应携带一个致病等位基因，通常由一个患病的亲代遗传（除非是新生突变 *de novo*）。
        *   **X连锁 (X-linked):** 遗传模式与性别相关。
        *   ***De Novo* 突变分析：** 对于散发病例（父母正常），寻找只在患者中出现而父母没有的新生突变，这是发现显性遗传病致病基因的有力策略（需要父母样本，即Trio分析）。

*   **功能通路富集分析 (Pathway Enrichment Analysis):**
    *   将筛选出的候选基因列表输入到通路分析工具（如DAVID, Metascape, g:Profiler, ReactomePA）中。
    *   检查这些基因是否显著富集在特定的生物学通路（如KEGG pathways, GO terms, Reactome pathways）中。
    *   如果多个候选基因指向同一通路，可以增强这些基因与疾病相关的可能性，并提供对疾病机制的洞察。

### 6. 变异数据库资源回顾

*   **dbSNP:** NCBI维护的变异数据库，主要收集和编录SNP及其他短变异。提供rsID作为变异的通用标识符。整合了来自多方的频率和注释信息。
*   **gnomAD / ExAC:** **群体频率信息的主要来源**，对评估变异稀有度至关重要。提供不同人群、WGS/WES来源的频率。
*   **ClinVar:** NCBI维护的**临床相关变异数据库**。收集了实验室和研究人员提交的变异及其与人类健康关系的解释（Pathogenic, Likely pathogenic, Benign, Likely benign, Uncertain significance - VUS）。是解释变异临床意义的关键资源。遵循ACMG/AMP指南进行分类。
*   **COSMIC (Catalogue Of Somatic Mutations In Cancer):** 最大的、专业 curated 的**癌症体细胞突变数据库**。收集来自文献和大型癌症项目的体细胞变异信息，及其在不同癌症类型中的分布。
*   **OMIM (Online Mendelian Inheritance in Man):** 权威的**人类基因和遗传表型目录**，详细描述了基因、相关疾病以及致病变异。是连接基因型和表型的桥梁。
*   **HGMD (Human Gene Mutation Database):** (商业) 收集已发表的引起或与人类遗传病相关的基因突变信息。
*   **特定疾病变异数据库：** 针对特定疾病（如阿尔茨海默病、心血管病）或研究项目（如TCGA - The Cancer Genome Atlas）建立的数据库，可能包含更专门化的数据和注释。

**本章小结：** 变异注释和功能预测是将原始变异列表转化为生物学见解和临床相关性的关键步骤。利用基因组注释数据库、群体频率数据库、临床数据库和功能预测工具（如ANNOVAR, VEP, SIFT, PolyPhen2, CADD, REVEL），结合遗传模式和生物学知识，可以有效地筛选和排序候选变异，最终服务于疾病基因发现、临床诊断和精准医疗。

---

## 五、 基因组组装策略与算法 (Genome Assembly)

**引言：** 对于没有参考基因组的物种，或者需要构建高质量基因组图谱的研究，核心任务是将测序产生的海量短或长读段（reads）拼接起来，重建出完整的基因组序列。这个过程称为基因组组装（Genome Assembly）。组装的质量直接决定了下游分析（如基因注释、比较基因组学）的可靠性。

### 1. 基因组组装基本概念

*   **从头组装 vs 参考辅助组装 (*De novo* vs Reference-assisted Assembly)**
    *   **从头组装 (*De novo* assembly)：** **完全不依赖**任何已有的参考基因组序列。仅根据测序读段之间的重叠（overlap）信息，像拼图一样将它们拼接起来。适用于新物种基因组构建、研究参考基因组未覆盖的区域、或构建个体特异性的高质量基因组。**是本节讨论的重点。**
    *   **参考辅助组装 (Reference-assisted / Reference-guided assembly)：** 利用一个**近缘物种**或**同种不同品系**的参考基因组作为指导或骨架。将读段先比对到参考基因组上，然后利用这些比对信息来辅助拼接过程，或者用于填充参考基因组中的空隙（gap filling）、纠正错误、或构建共有序列（consensus）。可以提高组装的连续性和准确性，但结果会受到参考基因组质量和相似度的限制，可能无法发现与参考基因组差异巨大的结构。

*   **组装质量评估指标 (Assembly Quality Metrics):**
    *   **Contig:** 通过读段重叠拼接起来的**连续无gap**的序列片段。
    *   **Scaffold:** 通过利用配对末端读段（paired-end reads）、连锁读段（linked reads）或长读段（long reads）等信息，将**多个contigs按照正确的顺序和方向连接起来**的序列片段。Scaffold内部可能包含由未知序列（通常用'N'表示）组成的gap。
    *   **N50 / NG50:**
        *   **N50:** 一个衡量**组装连续性（contiguity）**的常用指标。将所有contigs（或scaffolds）按长度从长到短排序，然后累加它们的长度，当累加长度达到**组装总长度（total assembly length）**的50%时，最后一个contig（或scaffold）的长度即为N50。N50越大，表示组装的连续性越好。
        *   **NG50:** 类似于N50，但累加长度是与**预估的基因组大小（estimated genome size）**进行比较，而不是组装总长度。这可以避免由于组装不完整或过度组装导致N50值虚高。
    *   **L50 / LG50:**
        *   **L50:** 长度**大于等于N50**的contigs（或scaffolds）的**数量**。L50越小，表示得到一半基因组所需的片段数越少，连续性越好。
        *   **LG50:** 类似于L50，但基于NG50长度。
    *   **Contig / Scaffold 数量 (Number of contigs/scaffolds):** 数量越少，通常意味着组装越连续。
    *   **最大 Contig / Scaffold 长度 (Longest contig/scaffold):** 反映了组装的最佳连续性水平。
    *   **组装总长度 (Total assembly length):** 应接近预估的基因组大小。过大或过小可能指示组装问题（如重复区域处理不当、污染、杂合性等）。
    *   **基因组覆盖度 / 完整性 (Genome Coverage / Completeness):**
        *   **BUSCO (Benchmarking Universal Single-Copy Orthologs):** 通过检查组装结果中包含了多少预期的、在特定进化谱系中保守的单拷贝基因来评估**基因区域的完整性**。输出结果包括 Complete (Single-copy, Duplicated), Fragmented, Missing 的基因比例。是目前评估组装完整性的金标准之一。
        *   **将原始读段比对回组装结果：** 检查比对率和覆盖均匀性。
        *   **与已知遗传图谱或物理图谱比较。**

*   **组装挑战 (Assembly Challenges):**
    *   **重复序列 (Repeats):** 基因组中存在大量的重复序列（短串联重复STR, 散在重复元件SINE/LINE, 片段重复Segmental Duplication等）。如果重复单元的长度**大于**测序读段的长度，读段就无法唯一地跨越重复区域，导致组装在此处中断或产生错误的连接（嵌合体 chimeras）。**这是基因组组装最主要的障碍。**
    *   **杂合性 (Heterozygosity):** 对于二倍体或多倍体生物，同源染色体之间存在的差异（SNP, Indel）会使得组装图谱变得复杂，可能导致等位基因被组装成独立的contigs，人为增加组装片段数量和总长度（需要进行haplotype merging或phasing）。
    *   **测序错误 (Sequencing Errors):** 随机的测序错误可能被误认为是真实的序列差异，干扰重叠判断和一致性序列（consensus）生成。
    *   **覆盖不均 (Uneven Coverage):** 由于GC偏好、文库制备偏差等原因，基因组不同区域的测序深度可能差异很大，低覆盖区域难以组装，高覆盖区域可能与重复序列混淆。
    *   **嵌合读段/文库 (Chimeric reads/libraries):** 实验过程中可能产生连接了基因组不相邻区域的DNA片段，干扰组装。

### 2. 短读长组装算法 (Short-Read Assembly Algorithms)

主要基于Illumina等短读长数据（几十到几百bp）。

*   **De Bruijn 图 (DBG) 方法:**
    *   **核心思想：** 将问题从寻找读段间的重叠（计算量大）转化为在图中寻找路径。
    *   **流程：**
        1.  **k-mer 化：** 将所有测序读段（reads）打断成长度为 k 的短子串（k-mers）。k值的选择非常关键，影响组装结果。
        2.  **构建De Bruijn图：**
            *   图的**节点（nodes）**是所有在读段中出现的 (k-1)-mers。
            *   图的**边（edges）**代表原始读段中存在的 k-mers。一条从节点A（一个(k-1)-mer）指向节点B（另一个(k-1)-mer）的边，表示存在一个k-mer，其前k-1位是A，后k-1位是B。
            *   （另一种等价定义：节点是k-mers，如果两个k-mers有k-1的重叠，则在对应节点间连边）。
        3.  **简化图：** 去除由于测序错误产生的稀疏路径（tips）和气泡（bubbles），合并线性路径。
        4.  **路径寻找：** 理想情况下，基因组序列对应于图中的一条（或多条，对应染色体）能遍历所有相关边的路径（欧拉路径 Eulerian path）。现实中，由于重复序列（导致图分叉、汇合）和杂合性（产生气泡），图会很复杂。组装器需要在图中寻找最优路径来构建contigs。重复序列表现为图中的复杂结构（如交叉点、环）。
    *   **优点：** 计算效率高，内存占用相对较低（相比OLC处理短读长），对测序错误有一定容忍度（错误k-mer丰度低）。**是短读长组装的主流方法。**
    *   **缺点：** k值选择困难；短重复序列（<k）信息丢失；长重复序列（>k）导致图复杂化，难以解析，使得contig较短。
    *   **常用工具：**
        *   **SPAdes:** 功能强大，适用于细菌、宏基因组、单细胞等多种场景，能处理多种数据类型（PE, MP, PacBio, ONT），包含错误校正和组装模块。
        *   **SOAPdenovo / SOAPdenovo2:** 早期成功应用于大型基因组的DBG组装器。
        *   **Velvet:** 另一个经典的DBG组装器，常用于小型基因组。
        *   **ABySS:** 可并行化，适用于大型基因组。
        *   **IDBA-UD:** 针对覆盖深度不均的宏基因组数据优化。

*   **重叠-布局-一致性 (Overlap-Layout-Consensus, OLC) 方法:**
    *   **核心思想：** 直接利用读段之间的重叠信息。
    *   **流程：**
        1.  **重叠 (Overlap):** 计算所有读段对（read pairs）之间的重叠区域和相似度。这是计算量最大的一步，对于海量短读长数据尤其耗时 O(N^2)，N为读段数。
        2.  **布局 (Layout):** 根据重叠关系构建一个**重叠图（overlap graph）**，其中节点是读段，边表示它们之间存在显著重叠。然后在这个图中寻找能最好地解释读段排列顺序的路径（通常是哈密顿路径 Hamiltonian path，但这是NP难问题，所以使用启发式方法）。
        3.  **一致性 (Consensus):** 对于布局确定的每条路径（对应一个contig），将路径上的所有读段进行多序列比对，生成该contig的一致性序列（consensus sequence），纠正测序错误。
    *   **优点：** 能更好地利用读段的全部信息，对重复序列的处理在理论上更直接（如果读段能跨越）。
    *   **缺点：** **计算复杂度极高**（特别是Overlap步骤），不适用于海量的短读长数据。对测序错误敏感。
    *   **应用：** **是Sanger测序时代的主流组装方法**（读长长，数量少）。**随着长读长测序（PacBio, ONT）的兴起，OLC及其变种（如String Graph）再次成为长读长组装的主流策略。**
    *   **常用工具（主要用于Sanger或长读长）：** Celera Assembler, Newbler (454), Falcon, Canu, Flye (基于String Graph)。

*   **贪婪算法 (Greedy Algorithm):**
    *   **思路：** 一种简化的OLC策略。从一个读段开始，迭代地寻找与之重叠最好（如最长或最相似）的下一个读段，并将其合并，直到无法再延伸为止。然后从未被使用的读段中再选一个开始，重复此过程。
    *   **优点：** 实现简单，速度快。
    *   **缺点：** 容易陷入局部最优解，对重复序列和测序错误处理能力差，通常只能用于非常简单或小型的基因组。

### 3. 长读长组装算法 (Long-Read Assembly Algorithms)

基于PacBio (CLR, HiFi) 或 Oxford Nanopore (ONT) 产生的长读段（kb-Mb级别）。长读长能跨越大多数重复序列，极大地简化了组装问题。

*   **基于OLC / 字符串图 (String Graph) 的方法:**
    *   **OLC的复兴：** 长读长数量相对较少（相比短读长），使得 O(N^2) 的Overlap计算变得可行（虽然仍需优化）。长读长的高错误率（尤其对PacBio CLR和ONT）是新的挑战。
    *   **字符串图（String Graph）：** OLC的一种改进。在重叠图中，如果读段A完全包含在读段B中，或者读段A的某个末端部分与读段B的另一末端部分重叠，这些“冗余”的信息可以通过图简化（transitive reduction）去除，得到更简洁的图结构，节点代表读段的末端，边代表不可简化的重叠关系。组装的目标是在字符串图中寻找路径。
    *   **流程通常包括：**
        1.  **（可选但常需）错误校正 (Error Correction):** 由于PacBio CLR和ONT原始读段错误率较高（~5-15%），需要先进行校正。可以使用其他长读段进行自校正（self-correction），或用高精度的短读长（Illumina）进行混合校正（hybrid correction）。PacBio HiFi读段（>99.9%精度）通常不需要或只需要少量校正。
        2.  **构建重叠图/字符串图 (Overlap/String Graph Construction):** 使用快速的算法（如Minimap2/Minimap）寻找长读段之间的重叠。
        3.  **图简化与路径寻找 (Graph Simplification & Path Finding):** 在图中去除噪音边，解析简单的重复结构，寻找代表基因组序列的路径。
        4.  **一致性序列生成 (Consensus Generation):** 对每条路径上的读段进行比对，生成高精度的contig序列。可以使用如Racon, Medaka, Polypolish等工具进行多轮polishing。
    *   **常用工具：**
        *   **Canu:** 继承自Celera Assembler，专门为长读长（PacBio/ONT）设计，采用OLC策略，包含校正、修剪、组装三个阶段。对资源消耗较大。
        *   **Flye:** 基于重复图（repeat graph）的概念（一种简化的DBG思想用于长读长），能很好地处理重复序列和不均匀覆盖。速度快，内存适中。特别适合ONT数据。
        *   **wtdbg2 (WTDBG - Wire Digraph ToolKit):** 基于一种称为“模糊Bruijn图”（Fuzzy Bruijn Graph）的概念，直接对原始（可能含错）的长读段构建图。**速度极快，内存占用低**。需要后续的polishing。
        *   **Falcon / Falcon-Unzip:** 用于PacBio数据的OLC组装器，能输出定相的单倍型序列（phased assembly）。
        *   **Shasta:** 专门为ONT数据设计的组装器，速度非常快。
        *   **Hifiasm:** 专门为**PacBio HiFi**数据设计，利用了HiFi读段的高精度和长度，能同时进行组装和**高质量的单倍型定相**，常能直接产生接近染色体级别的、高度准确的两个单倍型组装结果。效果非常好。

*   **混合组装策略 (Hybrid Assembly Strategy):**
    *   **目标：** 结合短读长的高精度和长读长的连续性优势。
    *   **常见方法：**
        *   **短读长辅助长读长组装：**
            *   用短读长数据**校正**长读段错误（e.g., using tools like `lordec`, `proovread` or integrated in some assemblers）。
            *   用短读长数据**验证或填充**长读长组装结果中的gap。
        *   **长读长辅助短读长组装：**
            *   用长读长数据将短读长组装产生的**contigs进行脚手架连接（scaffolding）**，显著提高N50。
            *   用长读长数据**解决**短读长组装图中由于重复序列造成的**复杂区域**。
    *   **常用工具：**
        *   **SPAdes:** 有专门的hybrid模式。
        *   **Unicycler:** 专门为细菌基因组设计的混合组装流程，效果好。
        *   **MaSuRCA:** (Maryland Super-Read Celera Assembler) 将短读长先组装成"超级读段"（super-reads），再结合长读长进行OLC组装。
        *   **Wengan:** 另一个混合组装流程。

*   **错误校正方法 (Error Correction Methods for Long Reads):**
    *   **自校正 (Self-correction):** 利用长读段之间的重叠。如果多个读段在某位置的碱基一致，则认为是正确的；如果某个读段在该位置不同，则可能是错误。需要较高的覆盖深度（>30X）。(e.g., Canu's correction step, Falconsense)
    *   **基于短读长的校正 (Short-read polishing):** 将高精度的短读长比对到长读长或长读长组装的contigs上，利用短读长的多数票来纠正长读长中的错误。(e.g., Pilon, Racon, NextPolish)

### 4. 组装后处理与改进 (Post-Assembly Processing and Improvement)

得到初步的contigs/scaffolds后，通常还需要进一步处理来提高组装质量。

*   **支架构建 (Scaffolding):**
    *   **目标：** 将已有的contigs按照正确的顺序、方向和距离连接成更长的scaffolds。
    *   **利用的信息：**
        *   **配对末端/伴侣对读段 (Paired-end / Mate-pair reads):** 如果一对读段分别比对到两个不同的contigs上，并且它们的距离和方向符合预期，则提供了这两个contig相邻且方向正确的证据。
        *   **连锁读段 (Linked-reads, e.g., 10x Genomics):** 来自同一长DNA分子的短读段被加上了相同的条形码（barcode）。如果来自同一barcode的读段分布在多个contigs上，则提示这些contigs在基因组上是邻近的。
        *   **长读长 (Long reads):** 单条长读长可能跨越多个contigs，直接提供了它们的连接关系。
        *   **光学图谱 (Optical maps):** 提供基因组范围的、基于特定酶切位点模式的物理图谱，可以将contigs/scaffolds锚定到图谱上，进行排序和方向确定。
        *   **遗传图谱 (Genetic maps):** 基于标记在后代中重组频率构建的标记顺序图，也可用于辅助scaffolding。
    *   **常用工具：** SSPACE, BESST (mate-pair based), ScaffMatch (long read based), AGP scaffolder (map based).

*   **间隙填充 (Gap Closing):**
    *   **目标：** 填充scaffolds内部由于重复序列或其他原因未能拼接上的未知序列（N's）。
    *   **方法：**
        *   利用配对末端读段，如果一对读段恰好跨越一个gap，可以用它们之间的序列来尝试填充。
        *   利用长读长数据，如果长读长能跨越gap，可以直接用其序列填充。
        *   迭代地将原始读段比对回含gap的scaffolds，尝试局部组装来填充。
    *   **常用工具：** GapFiller, PBJelly (PacBio based), LR_Gapcloser (long read based), Sealer (ABySS package).

*   **组装错误校正 (Assembly Error Correction / Polishing):**
    *   **目标：** 纠正组装序列中残留的单个碱基错误或小的Indel错误。
    *   **方法：** 将高精度的短读长（Illumina）或高质量的长读长（PacBio HiFi）比对回最终的组装序列上，识别出与大部分读段不一致的位置，并进行修正。通常需要进行多轮polishing。
    *   **常用工具：** Pilon, Racon, NextPolish, Polypolish, FreeBayes (用于polishing).

*   **染色体级别组装方法 (Chromosome-level Assembly):**
    *   **目标：** 将scaffolds进一步锚定（anchor）、排序（order）和定向（orient）到染色体上，构建出端粒到端粒（Telomere-to-Telomere, T2T）或接近染色体级别的组装。
    *   **关键技术：**
        *   **Hi-C / Chromatin Conformation Capture:** 利用染色质在三维空间中的交互频率信息。同一染色体上的区域交互频率远高于不同染色体之间，且交互频率随基因组距离增加而衰减。通过分析Hi-C数据产生的接触图谱（contact map），可以将scaffolds分组到染色体，并进行排序和定向。**是目前实现染色体级别组装最主流的技术。**
        *   **光学图谱 (Optical Mapping):** 提供长距离的物理图谱信息，可以直接将scaffolds锚定到图谱上。
        *   **遗传图谱 (Genetic Maps):** 提供标记的顺序信息。
    *   **常用工具 (Hi-C based scaffolding):** 3D-DNA, SALSA2, HiRise, Juicer + Juicebox Assembly Tools.

### 5. 组装质量评估 (Assembly Quality Assessment) - 再强调

评估是组装过程中不可或缺的一环，用于指导优化和衡量最终结果。

*   **N50, L50 (NG50, LG50) 等统计指标：** 衡量连续性。
*   **BUSCO 评估方法：** 评估基因区域的完整性。**是必备的评估步骤。**
*   **比对一致性评估 (Read-mapping Validation):**
    *   将原始测序读段（短读长和/或长读长）比对回组装结果。
    *   检查**比对率**（mapping rate，应很高），**覆盖度**（coverage depth/breadth），**覆盖均匀性**。
    *   检查**配对末端读段的一致性**（insert size distribution, orientation）。异常信号可能指示组装错误（misassemblies）。
    *   使用工具如 `QUAST` (Quality Assessment Tool for Genome Assemblies)，它可以计算多种统计指标，并将组装结果与参考基因组（如果可用）进行比较，报告错配、Indel、重排等错误。
*   **结构准确性评估 (Structural Accuracy):**
    *   与光学图谱、遗传图谱或已知的细胞遗传学结果（如核型）进行比较。
    *   检查Hi-C接触图谱是否符合预期的染色体结构模式。
*   **基因组完整性与准确性综合评估：**
    *   除了BUSCO，还可以检查组装结果中rRNA基因等其他预期元件的拷贝数和完整性。
    *   进行基因注释，评估预测出的基因数量和结构是否合理。
    *   与近缘物种的高质量基因组进行共线性（synteny）比较。

**本章小结：** 基因组组装是将测序读段转化为基因组序列的关键过程。短读长组装主要依赖De Bruijn图方法，而长读长组装则复兴了OLC/字符串图方法。长读长技术（PacBio HiFi, ONT）极大地提升了组装的连续性和对重复序列的解析能力。混合组装策略结合了二者的优点。组装后的支架构建、间隙填充、错误校正以及利用Hi-C等技术实现染色体级别组装是获得高质量基因组图谱的重要步骤。全面的质量评估（N50/NG50, BUSCO, QUAST等）贯穿始终，是衡量组装成功与否的标准。

---

**课程总结：** 本专题系统介绍了基因组测序（DNA-seq）数据分析的核心理论和方法学。从实验设计的源头考量，到关键的生物信息学步骤——参考基因组比对、变异检测（SNP/Indel/SV/CNV）、变异注释与功能预测，再到基因组从头组装，我们探讨了其基本原理、主流算法（如BWA/Bowtie2/Minimap2, GATK HaplotypeCaller, SPAdes/Canu/Flye等）、常用工具、质量控制和评估方法。掌握这些知识，将为同学们理解和开展各类基因组学研究打下坚实的基础。后续实验课程将着重于实践操作这些分析流程。