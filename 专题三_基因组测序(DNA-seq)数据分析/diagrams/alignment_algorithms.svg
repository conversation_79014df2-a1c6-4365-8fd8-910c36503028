<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 基因组比对算法原理图 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4e79a7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#283d5b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bwaGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f28e2b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b35818;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bowtieGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#76b7b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3d7a76;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="starGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#59a14f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#326129;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <rect x="50" y="20" width="700" height="60" rx="10" ry="10" fill="url(#headerGrad)" />
  <text x="400" y="60" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">基因组比对算法原理</text>
  
  <!-- 比对原理示意图 -->
  <rect x="50" y="100" width="700" height="120" rx="10" ry="10" fill="#e9ecef" stroke="#ced4da" stroke-width="1" />
  <text x="400" y="125" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#333">比对基本原理</text>
  
  <!-- 参考基因组 -->
  <rect x="100" y="140" width="600" height="30" fill="#dee2e6" stroke="#adb5bd" stroke-width="1" />
  <text x="400" y="160" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#333">参考基因组序列 (Reference Genome)</text>
  
  <!-- 测序读段 -->
  <rect x="200" y="180" width="150" height="25" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="275" y="197" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">测序读段 (Read)</text>
  <rect x="400" y="180" width="150" height="25" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="475" y="197" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">测序读段 (Read)</text>
  
  <!-- 箭头指示 -->
  <line x1="275" y1="180" x2="275" y2="170" stroke="#333" stroke-width="1" stroke-dasharray="3,3" />
  <line x1="475" y1="180" x2="475" y2="170" stroke="#333" stroke-width="1" stroke-dasharray="3,3" />
  
  <!-- BWA算法 -->
  <rect x="50" y="240" width="220" height="160" rx="10" ry="10" fill="url(#bwaGrad)" />
  <text x="160" y="265" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">BWA (Burrows-Wheeler Aligner)</text>
  <rect x="70" y="280" width="180" height="100" fill="white" opacity="0.9" rx="5" ry="5" />
  <text x="160" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">原理:</text>
  <text x="160" y="320" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">基于Burrows-Wheeler变换</text>
  <text x="160" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">构建FM索引</text>
  <text x="160" y="360" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">支持长读段和短读段比对</text>
  
  <!-- Bowtie2算法 -->
  <rect x="290" y="240" width="220" height="160" rx="10" ry="10" fill="url(#bowtieGrad)" />
  <text x="400" y="265" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">Bowtie2</text>
  <rect x="310" y="280" width="180" height="100" fill="white" opacity="0.9" rx="5" ry="5" />
  <text x="400" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">原理:</text>
  <text x="400" y="320" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">基于FM索引</text>
  <text x="400" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">双端比对优化</text>
  <text x="400" y="360" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">支持局部比对和全局比对</text>
  
  <!-- STAR算法 -->
  <rect x="530" y="240" width="220" height="160" rx="10" ry="10" fill="url(#starGrad)" />
  <text x="640" y="265" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">STAR</text>
  <rect x="550" y="280" width="180" height="100" fill="white" opacity="0.9" rx="5" ry="5" />
  <text x="640" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">原理:</text>
  <text x="640" y="320" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">最大可映射片段连接</text>
  <text x="640" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">支持剪接位点检测</text>
  <text x="640" y="360" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">适用于RNA-seq数据</text>
  
  <!-- 比对算法比较 -->
  <rect x="50" y="420" width="700" height="160" rx="10" ry="10" fill="#e9ecef" stroke="#ced4da" stroke-width="1" />
  <text x="400" y="445" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#333">比对算法比较</text>
  
  <!-- 表格标题 -->
  <rect x="70" y="460" width="150" height="30" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="145" y="480" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">算法</text>
  
  <rect x="220" y="460" width="150" height="30" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="295" y="480" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">优势</text>
  
  <rect x="370" y="460" width="150" height="30" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="445" y="480" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">劣势</text>
  
  <rect x="520" y="460" width="150" height="30" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="595" y="480" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">适用场景</text>
  
  <!-- BWA行 -->
  <rect x="70" y="490" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="145" y="510" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">BWA</text>
  
  <rect x="220" y="490" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="295" y="510" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">精确度高</text>
  
  <rect x="370" y="490" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="445" y="510" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">内存消耗大</text>
  
  <rect x="520" y="490" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="595" y="510" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">全基因组重测序</text>
  
  <!-- Bowtie2行 -->
  <rect x="70" y="520" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="145" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">Bowtie2</text>
  
  <rect x="220" y="520" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="295" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">速度快</text>
  
  <rect x="370" y="520" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="445" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">对复杂区域敏感性低</text>
  
  <rect x="520" y="520" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="595" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">ChIP-seq等短读段</text>
  
  <!-- STAR行 -->
  <rect x="70" y="550" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="145" y="570" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">STAR</text>
  
  <rect x="220" y="550" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="295" y="570" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">支持剪接位点</text>
  
  <rect x="370" y="550" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="445" y="570" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">内存需求极高</text>
  
  <rect x="520" y="550" width="150" height="30" fill="#f8f9fa" stroke="#333" stroke-width="1" />
  <text x="595" y="570" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">RNA-seq数据</text>
</svg>