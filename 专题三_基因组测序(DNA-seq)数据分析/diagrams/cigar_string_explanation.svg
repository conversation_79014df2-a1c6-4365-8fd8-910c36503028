<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">CIGAR字符串解析</text>
  <text x="400" y="70" font-family="Arial" font-size="16" text-anchor="middle" fill="#555">SAM/BAM文件中表示序列比对信息的关键元素</text>
  
  <!-- CIGAR字符串定义 -->
  <rect x="100" y="90" width="600" height="80" rx="5" ry="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
  <text x="400" y="115" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1565c0">CIGAR字符串定义</text>
  <text x="400" y="140" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">CIGAR = Compact Idiosyncratic Gapped Alignment Report</text>
  <text x="400" y="160" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">用于描述序列比对中的匹配、插入、删除等信息</text>
  
  <!-- CIGAR操作符 -->
  <rect x="100" y="190" width="600" height="200" rx="5" ry="5" fill="#fff3e0" stroke="#ff9800" stroke-width="2" />
  <text x="400" y="215" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#e65100">CIGAR操作符</text>
  
  <!-- 操作符表格 -->
  <line x1="150" y1="230" x2="650" y2="230" stroke="#bdbdbd" stroke-width="1" />
  <line x1="150" y1="230" x2="150" y2="370" stroke="#bdbdbd" stroke-width="1" />
  <line x1="200" y1="230" x2="200" y2="370" stroke="#bdbdbd" stroke-width="1" />
  <line x1="650" y1="230" x2="650" y2="370" stroke="#bdbdbd" stroke-width="1" />
  
  <text x="175" y="250" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">操作符</text>
  <text x="425" y="250" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">描述</text>
  
  <line x1="150" y1="260" x2="650" y2="260" stroke="#bdbdbd" stroke-width="1" />
  
  <text x="175" y="280" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">M</text>
  <text x="425" y="280" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">匹配或错配 (Match or Mismatch)</text>
  
  <line x1="150" y1="290" x2="650" y2="290" stroke="#bdbdbd" stroke-width="1" />
  
  <text x="175" y="310" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">I</text>
  <text x="425" y="310" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">插入 (Insertion) - 参考序列中不存在的碱基</text>
  
  <line x1="150" y1="320" x2="650" y2="320" stroke="#bdbdbd" stroke-width="1" />
  
  <text x="175" y="340" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">D</text>
  <text x="425" y="340" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">删除 (Deletion) - 参考序列中存在但读段中不存在的碱基</text>
  
  <line x1="150" y1="350" x2="650" y2="350" stroke="#bdbdbd" stroke-width="1" />
  
  <text x="175" y="370" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">其他</text>
  <text x="425" y="370" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">N(跳过), S(软裁剪), H(硬裁剪), P(填充), =(完全匹配), X(错配)</text>
  
  <!-- CIGAR字符串示例 -->
  <rect x="100" y="410" width="600" height="170" rx="5" ry="5" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
  <text x="400" y="435" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#2e7d32">CIGAR字符串示例</text>
  
  <!-- 示例1 -->
  <text x="120" y="465" font-family="Arial" font-size="16" font-weight="bold" fill="#333">示例: 8M2I4M1D3M</text>
  
  <!-- 示例1图解 -->
  <rect x="120" y="475" width="560" height="85" rx="3" ry="3" fill="#f1f8e9" stroke="#aed581" stroke-width="1" />
  
  <!-- 参考序列 -->
  <text x="130" y="495" font-family="Arial" font-size="14" fill="#333">参考序列:</text>
  <rect x="220" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="260" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="300" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="340" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="380" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="420" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="460" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="500" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="540" y="480" width="40" height="25" fill="#f44336" stroke="#e57373" stroke-width="1" />
  <rect x="580" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="620" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="660" y="480" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  
  <!-- 读段序列 -->
  <text x="130" y="535" font-family="Arial" font-size="14" fill="#333">读段序列:</text>
  <rect x="220" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="260" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="300" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="340" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="380" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="420" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="460" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="500" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="540" y="520" width="40" height="25" fill="#ffeb3b" stroke="#fdd835" stroke-width="1" />
  <rect x="580" y="520" width="40" height="25" fill="#ffeb3b" stroke="#fdd835" stroke-width="1" />
  <rect x="620" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="660" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <rect x="700" y="520" width="40" height="25" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  
  <!-- CIGAR解释 -->
  <text x="400" y="570" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">8M (8个匹配) + 2I (2个插入) + 4M (4个匹配) + 1D (1个删除) + 3M (3个匹配)</text>
  
  <!-- 图例 -->
  <rect x="120" y="580" width="15" height="15" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <text x="145" y="592" font-family="Arial" font-size="12" fill="#333">匹配(M)</text>
  
  <rect x="200" y="580" width="15" height="15" fill="#ffeb3b" stroke="#fdd835" stroke-width="1" />
  <text x="225" y="592" font-family="Arial" font-size="12" fill="#333">插入(I)</text>
  
  <rect x="280" y="580" width="15" height="15" fill="#f44336" stroke="#e57373" stroke-width="1" />
  <text x="305" y="592" font-family="Arial" font-size="12" fill="#333">删除(D)</text>
</svg>