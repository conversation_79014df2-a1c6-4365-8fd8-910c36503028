<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 基因组组装策略图 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4e79a7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#283d5b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="debruijnGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f28e2b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b35818;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="olapGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#76b7b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3d7a76;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hybridGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#59a14f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#326129;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <rect x="50" y="20" width="700" height="60" rx="10" ry="10" fill="url(#headerGrad)" />
  <text x="400" y="60" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">基因组组装策略与算法</text>
  
  <!-- 组装概述 -->
  <rect x="50" y="100" width="700" height="80" rx="10" ry="10" fill="#e9ecef" stroke="#ced4da" stroke-width="1" />
  <text x="400" y="125" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#333">基因组组装基本概念</text>
  <text x="400" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#333">从短读段重建完整基因组序列的计算过程</text>
  <text x="400" y="170" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#333">组装质量评估：N50、覆盖度、连续性、完整性</text>
  
  <!-- De Bruijn图方法 -->
  <rect x="50" y="200" width="340" height="180" rx="10" ry="10" fill="url(#debruijnGrad)" />
  <text x="220" y="225" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">De Bruijn图方法</text>
  
  <!-- De Bruijn图示意 -->
  <rect x="70" y="240" width="300" height="120" fill="white" opacity="0.9" rx="5" ry="5" />
  
  <!-- 节点 -->
  <circle cx="120" cy="280" r="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="120" y="285" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">ATG</text>
  
  <circle cx="200" cy="280" r="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="200" y="285" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">TGC</text>
  
  <circle cx="280" cy="280" r="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="280" y="285" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">GCA</text>
  
  <circle cx="160" cy="330" r="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="160" y="335" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">TGA</text>
  
  <circle cx="240" cy="330" r="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="240" y="335" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">GAT</text>
  
  <!-- 边 -->
  <line x1="140" y1="280" x2="180" y2="280" stroke="#333" stroke-width="2" />
  <line x1="220" y1="280" x2="260" y2="280" stroke="#333" stroke-width="2" />
  <line x1="120" y1="300" x2="160" y2="310" stroke="#333" stroke-width="2" />
  <line x1="180" y1="330" x2="220" y2="330" stroke="#333" stroke-width="2" />
  <line x1="240" y1="310" x2="280" y2="300" stroke="#333" stroke-width="2" />
  
  <!-- De Bruijn图说明 -->
  <text x="220" y="370" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">适用于短读段高覆盖度数据</text>
  <text x="220" y="390" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">代表软件: Velvet, SPAdes, ABySS</text>
  
  <!-- 重叠-布局-一致性方法 -->
  <rect x="410" y="200" width="340" height="180" rx="10" ry="10" fill="url(#olapGrad)" />
  <text x="580" y="225" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">重叠-布局-一致性(OLC)方法</text>
  
  <!-- OLC示意图 -->
  <rect x="430" y="240" width="300" height="120" fill="white" opacity="0.9" rx="5" ry="5" />
  
  <!-- 读段 -->
  <rect x="450" y="260" width="120" height="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="510" y="275" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">Read 1</text>
  
  <rect x="500" y="290" width="120" height="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="560" y="305" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">Read 2</text>
  
  <rect x="550" y="320" width="120" height="20" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <text x="610" y="335" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">Read 3</text>
  
  <!-- 重叠区域 -->
  <rect x="500" y="260" width="70" height="20" fill="#f28e2b" stroke="none" />
  <rect x="500" y="290" width="70" height="20" fill="#f28e2b" stroke="none" />
  
  <rect x="550" y="290" width="70" height="20" fill="#59a14f" stroke="none" />
  <rect x="550" y="320" width="70" height="20" fill="#59a14f" stroke="none" />
  
  <!-- OLC说明 -->
  <text x="580" y="370" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">适用于长读段数据</text>
  <text x="580" y="390" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">代表软件: Canu, Flye, Miniasm</text>
  
  <!-- 混合组装策略 -->
  <rect x="50" y="400" width="700" height="180" rx="10" ry="10" fill="url(#hybridGrad)" />
  <text x="400" y="425" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">混合组装策略</text>
  
  <!-- 混合组装示意图 -->
  <rect x="70" y="440" width="660" height="120" fill="white" opacity="0.9" rx="5" ry="5" />
  
  <!-- 短读段 -->
  <rect x="100" y="460" width="60" height="15" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <rect x="120" y="480" width="60" height="15" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <rect x="90" y="500" width="60" height="15" fill="#4e79a7" stroke="#333" stroke-width="1" />
  <rect x="110" y="520" width="60" height="15" fill="#4e79a7" stroke="#333" stroke-width="1" />
  
  <!-- 长读段 -->
  <rect x="200" y="470" width="200" height="20" fill="#f28e2b" stroke="#333" stroke-width="1" />
  <rect x="250" y="500" width="200" height="20" fill="#f28e2b" stroke="#333" stroke-width="1" />
  
  <!-- Hi-C数据 -->
  <path d="M 450,460 Q 500,440 550,460 Q 600,480 650,460" stroke="#76b7b2" stroke-width="2" fill="none" />
  <path d="M 450,520 Q 500,540 550,520 Q 600,500 650,520" stroke="#76b7b2" stroke-width="2" fill="none" />
  
  <!-- 组装结果 -->
  <rect x="100" y="550" width="600" height="5" fill="#59a14f" stroke="none" />
  
  <!-- 混合组装说明 -->
  <text x="400" y="460" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">混合组装策略</text>
  <text x="130" y="450" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">短读段</text>
  <text x="300" y="450" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">长读段</text>
  <text x="550" y="450" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">Hi-C/光学图谱</text>
  
  <text x="400" y="480" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">1. 短读段提供高准确度</text>
  <text x="400" y="500" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">2. 长读段跨越重复区域</text>
  <text x="400" y="520" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">3. Hi-C/光学图谱辅助搭建染色体水平组装</text>
  
  <text x="400" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333">代表软件: MaSuRCA, Unicycler, HybridSPAdes</text>
</svg>