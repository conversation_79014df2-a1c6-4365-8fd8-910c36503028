<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">SAM/BAM 文件格式结构</text>
  
  <!-- SAM文件整体结构 -->
  <rect x="100" y="70" width="600" height="180" rx="5" ry="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
  <text x="400" y="90" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1565c0">SAM文件结构</text>
  
  <!-- 头部信息 -->
  <rect x="120" y="100" width="560" height="60" rx="5" ry="5" fill="#bbdefb" stroke="#64b5f6" stroke-width="1" />
  <text x="400" y="130" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">头部信息 (Header)</text>
  <text x="400" y="150" font-family="Arial" font-size="12" text-anchor="middle" fill="#555">@HD, @SQ, @RG, @PG, @CO</text>
  
  <!-- 比对记录 -->
  <rect x="120" y="170" width="560" height="60" rx="5" ry="5" fill="#bbdefb" stroke="#64b5f6" stroke-width="1" />
  <text x="400" y="200" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">比对记录 (Alignment)</text>
  <text x="400" y="220" font-family="Arial" font-size="12" text-anchor="middle" fill="#555">每行一条比对记录，包含11个必填字段和可选字段</text>
  
  <!-- 比对记录详细结构 -->
  <rect x="100" y="270" width="600" height="300" rx="5" ry="5" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
  <text x="400" y="290" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#2e7d32">比对记录字段详解</text>
  
  <!-- 必填字段 -->
  <rect x="120" y="310" width="560" height="160" rx="5" ry="5" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <text x="400" y="330" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">必填字段 (11个)</text>
  
  <!-- 必填字段列表 -->
  <text x="140" y="350" font-family="Arial" font-size="12" fill="#333">1. QNAME: 序列名称</text>
  <text x="140" y="370" font-family="Arial" font-size="12" fill="#333">2. FLAG: 比对标志 (二进制值)</text>
  <text x="140" y="390" font-family="Arial" font-size="12" fill="#333">3. RNAME: 参考序列名称</text>
  <text x="140" y="410" font-family="Arial" font-size="12" fill="#333">4. POS: 比对位置 (1-based)</text>
  <text x="140" y="430" font-family="Arial" font-size="12" fill="#333">5. MAPQ: 比对质量分数</text>
  <text x="140" y="450" font-family="Arial" font-size="12" fill="#333">6. CIGAR: 比对信息</text>
  
  <text x="400" y="350" font-family="Arial" font-size="12" fill="#333">7. RNEXT: 下一片段参考序列名称</text>
  <text x="400" y="370" font-family="Arial" font-size="12" fill="#333">8. PNEXT: 下一片段比对位置</text>
  <text x="400" y="390" font-family="Arial" font-size="12" fill="#333">9. TLEN: 模板长度</text>
  <text x="400" y="410" font-family="Arial" font-size="12" fill="#333">10. SEQ: 序列</text>
  <text x="400" y="430" font-family="Arial" font-size="12" fill="#333">11. QUAL: 质量分数</text>
  
  <!-- 可选字段 -->
  <rect x="120" y="480" width="560" height="70" rx="5" ry="5" fill="#c8e6c9" stroke="#81c784" stroke-width="1" />
  <text x="400" y="500" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">可选字段</text>
  <text x="400" y="520" font-family="Arial" font-size="12" text-anchor="middle" fill="#555">格式: TAG:TYPE:VALUE</text>
  <text x="400" y="540" font-family="Arial" font-size="12" text-anchor="middle" fill="#555">例如: NM:i:1 (编辑距离), MD:Z:29A20 (错配位置), AS:i:49 (比对得分)</text>
  
  <!-- SAM与BAM关系 -->
  <line x1="400" y1="580" x2="400" y2="560" stroke="#9e9e9e" stroke-width="2" stroke-dasharray="5,5" />
  <text x="400" y="595" font-family="Arial" font-size="14" font-style="italic" text-anchor="middle" fill="#555">BAM是SAM的二进制压缩格式，结构相同但占用空间更小，读取速度更快</text>
</svg>