<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
  <!-- 变异检测流程图 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4e79a7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#283d5b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f28e2b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b35818;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#76b7b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3d7a76;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#59a14f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#326129;stop-opacity:1" />
    </linearGradient>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <rect x="50" y="20" width="700" height="50" rx="10" ry="10" fill="url(#headerGrad)" />
  <text x="400" y="50" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="white">基因组变异检测流程</text>
  
  <!-- 输入数据 -->
  <rect x="150" y="90" width="500" height="50" rx="10" ry="10" fill="#e9ecef" stroke="#ced4da" stroke-width="1" />
  <text x="400" y="120" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#333">排序后的BAM文件 + 参考基因组FASTA文件</text>
  
  <!-- 箭头1 -->
  <line x1="400" y1="140" x2="400" y2="160" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 步骤1：比对前处理 -->
  <rect x="150" y="160" width="500" height="60" rx="10" ry="10" fill="url(#processGrad1)" />
  <text x="400" y="190" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">标记重复序列 → 重校正碱基质量值 → 局部重比对</text>
  
  <!-- 箭头2 -->
  <line x1="400" y1="220" x2="400" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 步骤2：变异检测 -->
  <rect x="150" y="240" width="500" height="60" rx="10" ry="10" fill="url(#processGrad2)" />
  <text x="400" y="265" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">变异检测</text>
  <text x="400" y="285" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">GATK HaplotypeCaller / SAMtools / FreeBayes</text>
  
  <!-- 箭头3 -->
  <line x1="400" y1="300" x2="400" y2="320" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 步骤3：变异过滤 -->
  <rect x="150" y="320" width="500" height="60" rx="10" ry="10" fill="url(#processGrad3)" />
  <text x="400" y="345" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">变异过滤</text>
  <text x="400" y="365" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">质量过滤 → 深度过滤 → 等位基因频率过滤 → 硬过滤/VQSR</text>
  
  <!-- 箭头4 -->
  <line x1="400" y1="380" x2="400" y2="400" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 步骤4：变异注释 -->
  <rect x="150" y="400" width="500" height="60" rx="10" ry="10" fill="#edc948" />
  <text x="400" y="425" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">变异注释与解释</text>
  <text x="400" y="445" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#333">功能注释(ANNOVAR/SnpEff) → 致病性预测 → 生物学解释</text>
  
  <!-- 变异类型和输出格式 -->
  <text x="400" y="480" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#666">变异类型: SNP, Indel, 结构变异 | 输出格式: VCF, gVCF, BCF</text>
</svg>