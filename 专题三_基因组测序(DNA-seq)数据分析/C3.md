# **Chapter 3: Analysis of Genome Sequencing (DNA-Seq) Data**

**Learning Objectives:**

*   Understand the principles, applications, and design considerations for various genome sequencing strategies (WGS, WES, Targeted Sequencing).
*   Grasp the fundamental concepts and challenges of read alignment to a reference genome.
*   Compare and contrast major alignment algorithms and tools (e.g., BWA, Bowtie2, Minimap2), including the role of indexing (FM-index) and heuristic strategies (seed-and-extend).
*   Evaluate alignment quality using metrics like MAPQ and coverage statistics.
*   Describe the major types of genomic variation (SNP, Indel, SV, CNV) and the challenges in their detection.
*   Explain the principles behind variant calling algorithms for SNPs/Indels (e.g., Bayesian methods, HaplotypeCaller) and structural variations (e.g., read depth, paired-end, split-read, assembly methods).
*   Understand the GATK Best Practices workflow, including preprocessing steps (BQSR) and filtering strategies (VQSR).
*   Appreciate the importance and methods of variant annotation, including positional, functional consequence, and population frequency annotation using key databases (RefSeq, Ensembl, gnomAD, ClinVar).
*   Learn about *in silico* functional prediction tools (e.g., SIFT, PolyPhen-2, CADD, REVEL) and strategies for variant prioritization in research and clinical contexts.
*   Comprehend the concepts and challenges of *de novo* genome assembly.
*   Compare short-read (De Bruijn Graph) and long-read (Overlap-Layout-Consensus/String Graph) assembly algorithms and tools (e.g., SPAdes, Canu, Flye, Hifiasm).
*   Understand post-assembly processes like scaffolding, gap closing, polishing, and methods for achieving chromosome-level assemblies (e.g., Hi-C).
*   Evaluate genome assembly quality using metrics like N50/NG50 and BUSCO scores.

---

## **3.1 Introduction to Genome Sequencing: Experimental Strategies and Applications**

The genome, the complete set of deoxyribonucleic acid (DNA) within an organism, holds the blueprint for its structure, function, and development. Genome sequencing technologies allow us to decipher this blueprint at an unprecedented scale and resolution. High-throughput sequencing (HTS), often referred to as Next-Generation Sequencing (NGS), has revolutionized biological research by enabling rapid and cost-effective sequencing of entire genomes or specific genomic regions. Understanding the different sequencing strategies, their underlying principles, and the critical factors in experimental design is fundamental to generating meaningful biological insights from sequencing data.

This section outlines the primary types of DNA sequencing experiments, compares their advantages and limitations, details key considerations for designing sequencing projects, and surveys the broad range of applications where genome sequencing is employed.

![Genome Sequencing Workflow](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/genome_sequencing_workflow.svg)
*Figure 3.1: A generalized workflow for genome sequencing experiments. Starting from biological sample collection and DNA extraction, the process involves library preparation, sequencing, and subsequent bioinformatics analysis including alignment, variant calling, annotation, and potentially genome assembly.*

### **3.1.1 Major Genome Sequencing Strategies**

The choice of sequencing strategy depends heavily on the research question, the available resources, and the characteristics of the organism being studied.

*   **Whole Genome Sequencing (WGS)**
    *   **Principle:** Aims to sequence the entire genomic DNA of an organism.
    *   **Sub-types based on reference availability:**
        *   **Resequencing:** This is performed when a high-quality reference genome for the species (or a closely related one) already exists. The primary goal is to identify variations (SNPs, Indels, SVs, CNVs) in the sequenced sample(s) compared to the reference. Reads are aligned *back* to the reference genome. This is the dominant approach in human genetics, cancer genomics, and population studies of well-characterized species.
        *   ***De novo* Sequencing:** This is undertaken when no reference genome is available for the target organism or when the goal is to construct a novel, high-quality genome assembly independent of existing references (even if one exists, it might be of poor quality or represent a different strain/individual). Reads are assembled together based on overlaps to reconstruct the genome sequence from scratch. This is essential for studying new species, highly divergent individuals, or complex genomic regions poorly represented in existing references.
    *   **Applications:**
        *   *Resequencing:* Comprehensive variant discovery for genetic disease research (Mendelian and complex traits via GWAS), population genetics (diversity, selection, history), cancer genomics (somatic mutations, CNVs, SVs driving tumorigenesis), pharmacogenomics, and marker-assisted selection in agriculture.
        *   *De novo Sequencing:* Generating reference genomes for new species, comparative genomics, studying genome structure evolution, resolving complex regions (e.g., centromeres, segmental duplications), characterizing microbial genomes.
    *   **Sequencing Depth Considerations:** Depth refers to the average number of times each base in the genome is sequenced (e.g., 30X).
        *   *Resequencing (Human, Diploid):* Minimum ~30X is generally required for reliable heterozygous SNP calling. Higher depth (e.g., 50X+) improves sensitivity for Indels, low-frequency variants, and CNV detection. Coverage *uniformity* is also critical.
        *   *Resequencing (Cancer, Somatic):* Often requires significantly higher depth (e.g., 60X-100X for tumor, 30X for matched normal) to confidently detect somatic mutations present at sub-clonal frequencies, especially when accounting for tumor purity and heterogeneity.
        *   *Resequencing (Population):* Large sample sizes at lower depth (e.g., 5X-15X) might be cost-effective for estimating population allele frequencies or broad GWAS scans, but individual genotype calls are less certain.
        *   *De novo Sequencing:* Typically requires higher depth than resequencing. For bacteria, >100X short reads might suffice, but combination with long reads is preferred. For complex eukaryotic genomes, a combination strategy is standard: moderate depth Illumina data (e.g., 50-100X) for accuracy and cost-effectiveness, coupled with significant long-read data (e.g., 20-50X PacBio HiFi or ONT) to span repeats and generate long contigs.

*   **Whole Exome Sequencing (WES)**
    *   **Principle:** Selectively sequences only the protein-coding regions of the genome (exons), which constitute roughly 1-2% of the human genome but harbor the majority (~85%) of known disease-causing mutations. This is achieved through **target enrichment**, typically using hybridization capture. Biotinylated oligonucleotide probes complementary to exonic sequences are used to 'capture' corresponding DNA fragments from a fragmented genomic library. These captured fragments are then sequenced.
    *   **Advantages over WGS:**
        *   **Cost-Effective:** Significantly cheaper per sample, allowing for larger cohort studies.
        *   **Data Manageability:** Produces much less data, reducing storage and computational burden.
        *   **Focus on Interpretable Regions:** Directly targets regions with clear functional interpretation (protein coding).
    *   **Applications:** Primarily used for identifying coding variants in:
        *   Mendelian disease diagnosis and gene discovery.
        *   Complex disease research (identifying rare coding variants associated with risk).
        *   Cancer genomics (detecting somatic mutations in coding regions).
    *   **Limitations:**
        *   **Cannot detect non-coding variants:** Misses mutations in promoters, enhancers, introns (except splice sites), UTRs, and intergenic regions, which can be pathogenic.
        *   **Limited detection of SVs and CNVs:** Especially those whose breakpoints lie outside or span across captured exons. Coverage variations can hint at CNVs, but calling is less robust than WGS.
        *   **Uneven Coverage:** Capture efficiency varies across exons due to GC content, sequence complexity, and probe design, leading to some exons having low or no coverage.
        *   **Incomplete Target:** Relies on existing gene annotations; novel exons or poorly annotated genes might be missed.

*   **Targeted Sequencing (Panel Sequencing)**
    *   **Principle:** Sequences only a specific, predefined set of genes or genomic regions of interest (a "panel"). Enrichment is achieved either by:
        *   **Amplicon-based:** Using multiplex PCR to amplify specific target regions. Best suited for small target regions, low DNA input, and detecting very low-frequency variants (like in ctDNA), but prone to amplification bias and uneven coverage.
        *   **Hybridization capture-based:** Similar to WES but using probes designed only for the panel genes/regions. Offers better coverage uniformity and scalability for larger panels compared to amplicon methods.
    *   **Advantages:**
        *   **Highest Cost-Effectiveness:** Lowest cost per sample for analyzing specific gene sets.
        *   **Ultra-High Depth:** Allows for very deep sequencing (often >1000X), enabling highly sensitive detection of rare variants (e.g., low-frequency somatic mutations in cancer, mosaic variants).
        *   **Fast Turnaround Time:** Smaller data volume and focused analysis lead to quicker results.
    *   **Applications:**
        *   Clinical diagnostics for known genetic disorders (e.g., hereditary cancer panels, cardiomyopathy panels).
        *   Cancer companion diagnostics (testing for specific mutations to guide therapy).
        *   Liquid biopsy analysis (detecting circulating tumor DNA - ctDNA).
        *   Microbial typing and resistance gene profiling.
        *   Validation of findings from WGS/WES studies.
    *   **Depth and Coverage Requirements:** Depth requirements are application-dependent but generally very high (500X to >10,000X). Near-complete coverage (>99%) of all target bases at a minimum threshold depth (e.g., 100X) is crucial, especially for clinical applications.

### **3.1.2 Key Factors in Experimental Design**

Careful planning before starting a sequencing experiment is critical for obtaining high-quality, interpretable data.

*   **Sample Selection and Quality Control (QC)**
    *   **DNA Source and Extraction:** The choice of biological material (blood, saliva, fresh/frozen tissue, FFPE tissue, cell culture) impacts DNA quality. Extraction methods (e.g., column-based kits, magnetic beads, phenol-chloroform) must yield DNA with sufficient quantity, high purity, and adequate integrity for the chosen library preparation method. FFPE (Formalin-Fixed Paraffin-Embedded) samples are particularly challenging due to DNA degradation and chemical modifications, requiring specialized extraction and repair protocols.
    *   **DNA Quality Assessment:** Essential QC steps include:
        *   **Quantification:** Accurate measurement of DNA concentration, preferably using fluorescence-based methods (e.g., Qubit, PicoGreen) which are specific for dsDNA, unlike spectrophotometry (e.g., NanoDrop) which measures all nucleic acids.
        *   **Purity:** Spectrophotometric assessment using OD260/280 ratio (ideal ~1.8 indicates minimal protein contamination) and OD260/230 ratio (ideal >2.0 indicates minimal contamination from salts, organic solvents).
        *   **Integrity:** Assessment of DNA fragmentation. Agarose gel electrophoresis provides a visual check for degradation (smearing). More quantitative measures like the DNA Integrity Number (DIN) from Agilent TapeStation/Bioanalyzer are preferred, especially for long-read sequencing which requires High Molecular Weight (HMW) DNA.
    *   **Sample Size and Cohort Design:** The number of samples needed depends on the study design (e.g., case-control, family-based, population survey) and the desired statistical power to detect associations or identify causal variants. Proper matching of cases and controls (age, sex, ethnicity) is crucial in association studies.
    *   **Control Samples:** Including appropriate controls is vital.
        *   *Matched Normal:* In cancer studies, sequencing a normal tissue sample (e.g., blood) from the same individual alongside the tumor is essential to distinguish somatic (tumor-specific) mutations from germline (inherited) variants.
        *   *Family Members:* In Mendelian disease studies, sequencing parents (trio analysis) or affected/unaffected relatives helps identify *de novo* mutations and track segregation patterns.
        *   *Population Controls:* Data from large public databases (e.g., gnomAD) serve as essential frequency filters.
        *   *Technical Replicates/Standards:* May be included to assess reproducibility or benchmark performance.

*   **Sequencing Platform Selection**
    *   **Short-Read Platforms (Dominant: Illumina):**
        *   *Technology:* Sequencing by Synthesis (SBS). Produces highly accurate (Q30 > 80-90%) but relatively short reads (50-300 bp).
        *   *Pros:* Highest throughput, lowest cost per base, mature technology with extensive software support. Ideal for SNP/small Indel detection, CNV detection (from depth), WES, and targeted panels where high accuracy is paramount.
        *   *Cons:* Difficulty resolving long repeats, complex SVs, and phasing haplotypes. Susceptible to GC bias.
    *   **Long-Read Platforms (Major players: Pacific Biosciences (PacBio), Oxford Nanopore Technologies (ONT)):**
        *   *Technology:* PacBio uses Single Molecule, Real-Time (SMRT) sequencing; ONT uses nanopore sensing. Both produce much longer reads (PacBio HiFi: 10-25 kb with >99.9% accuracy; ONT: up to Mb scale, accuracy improving rapidly, near HiFi levels with newest chemistry/basecalling).
        *   *Pros:* Excellent at spanning repeats, resolving complex SVs, phasing haplotypes, and enabling high-quality *de novo* assembly. Can also detect base modifications (epigenetics) directly. ONT offers real-time data generation and portable devices.
        *   *Cons:* Historically higher cost per base (though decreasing rapidly), lower raw throughput than Illumina (also increasing). Older PacBio CLR and standard ONT reads had higher error rates requiring bioinformatics correction, though PacBio HiFi and newer ONT protocols largely overcome this. Require HMW DNA input.
    *   **Choosing the Right Platform:** The decision involves balancing accuracy needs, required read length (for repeats/SVs/assembly), throughput requirements, budget, and specific application goals (e.g., base modification detection).
    *   **Hybrid Sequencing Approaches:** Combining the strengths of both platforms is increasingly common, especially for *de novo* assembly (long reads for scaffolding, short reads for polishing) or comprehensive SV characterization.

*   **Sequencing Depth Design**
    *   As discussed under WGS/WES/Targeted sections, the required depth varies greatly.
    *   **Statistical Power:** Higher depth provides more evidence per locus, increasing the statistical power to detect variants, especially heterozygous ones or those with low allele frequency (somatic/mosaic).
    *   **Overcoming Biases:** Higher average depth can help ensure sufficient coverage even in regions prone to low coverage due to GC content or capture inefficiency.
    *   **Addressing Non-uniformity:** While higher depth helps mitigate low coverage spots, experimental strategies like PCR-free library prep and optimized capture designs are crucial for achieving uniform coverage *a priori*. Analytical corrections for GC bias can be applied post-sequencing.
    *   **Cost-Benefit Trade-off:** Sequencing depth is a major cost driver. The chosen depth should be the minimum required to confidently achieve the study's objectives. Pilot experiments can sometimes inform optimal depth selection.

*   **Library Preparation Strategy**
    *   **Fragmentation and Size Selection:** Genomic DNA is first fragmented (mechanically via sonication or enzymatically). The distribution of fragment sizes (insert size) is important. Standard Illumina libraries use ~300-500 bp inserts. Consistent insert sizes are crucial for paired-end analysis (especially SV detection).
    *   **PCR Amplification vs. PCR-Free:**
        *   *PCR-based libraries:* Involve PCR amplification after adapter ligation to generate enough material for sequencing. This is the standard method but introduces biases: GC bias (regions with extreme GC content amplify poorly/preferentially), PCR duplicates (multiple reads originating from the same initial fragment, inflating coverage artificially), and potential PCR errors.
        *   *PCR-free libraries:* Omit the PCR step, directly sequencing the adapter-ligated fragments. This minimizes GC bias and eliminates PCR duplicates, leading to more uniform coverage and more accurate quantification (e.g., for CNV analysis). However, it typically requires higher DNA input amounts. Highly recommended for high-quality WGS.
    *   **Single-End (SE) vs. Paired-End (PE) Sequencing:**
        *   *SE:* Sequences only one end of the DNA fragments. Cheaper, sufficient for some applications like gene expression counting (RNA-Seq) or simple microbial resequencing.
        *   *PE:* Sequences both ends of the fragments. **Strongly recommended for most genome sequencing applications.** Provides several advantages: improves read alignment accuracy (especially in repetitive regions); allows detection of Indels and SVs by analyzing the distance and orientation of the read pairs (discordant pairs); aids in scaffolding during *de novo* assembly.

### **3.1.3 Major Application Domains**

Genome sequencing fuels discovery across virtually all fields of biology and medicine.

*   **Reference Genome Construction:** *De novo* assembly provides the foundational genomic map for newly studied species.
*   **Population Genetics and Evolutionary Biology:** Resequencing diverse individuals reveals patterns of genetic variation, population structure, migration history, adaptation (selection signatures), and phylogenetic relationships.
*   **Functional Genomics:** The genome sequence provides the context for interpreting data from other 'omics' studies (transcriptomics, epigenomics, proteomics) to understand gene function and regulation.
*   **Clinical Genetics and Precision Medicine:**
    *   *Rare Disease Diagnosis:* WGS/WES are powerful tools for identifying causal mutations in Mendelian disorders.
    *   *Cancer Genomics:* Characterizing tumor genomes informs diagnosis, prognosis, targeted therapy selection (precision oncology), and immunotherapy response prediction.
    *   *Pharmacogenomics:* Identifying genetic variants influencing drug efficacy and toxicity.
    *   *Non-invasive Prenatal Testing (NIPT):* Detecting fetal chromosomal aneuploidies from maternal blood.
*   **Agriculture and Breeding:** Accelerating crop and livestock improvement through genomic selection, marker-assisted breeding, and identification of genes underlying desirable traits.
*   **Microbiology and Metagenomics:** Studying microbial genomes for pathogen identification, tracking outbreaks, understanding antibiotic resistance, and characterizing complex microbial communities (e.g., human gut microbiome, environmental samples).

**Section Summary:** Genome sequencing encompasses diverse strategies tailored to specific research goals. WGS provides a comprehensive view, WES offers a cost-effective focus on coding regions, and targeted sequencing enables ultra-deep analysis of specific gene sets. Success hinges on meticulous experimental design, including careful sample selection and QC, appropriate platform choice, adequate sequencing depth, and optimized library preparation techniques. The applications of genome sequencing are vast, driving progress from fundamental biology to clinical practice and agriculture.

---

## **3.2 Reference Genome Alignment: Algorithms and Principles**

For resequencing studies, the first crucial bioinformatics step after obtaining raw sequencing reads is to determine their original location on the reference genome. This process is known as read alignment or mapping. The accuracy and efficiency of alignment are critical, as errors or ambiguities at this stage will propagate to downstream analyses like variant calling.

![Alignment Algorithms Principle](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/alignment_algorithms.svg)
*Figure 3.2: Conceptual illustration of read alignment. Short sequences (reads) generated from the sample are compared against a long reference genome sequence to find their best matching position(s).*

### **3.2.1 Fundamental Concepts in Sequence Alignment**

*   **Global vs. Local Alignment:**
    *   **Global Alignment:** Attempts to align two sequences across their entire lengths, from beginning to end. It assumes the sequences are generally homologous and seeks the optimal alignment score over the full length. The classic algorithm is Needleman-Wunsch. Suitable for comparing closely related genes or proteins of similar length.
    *   **Local Alignment:** Aims to find the region(s) of highest similarity between two sequences, even if the sequences are dissimilar overall or have different lengths. It identifies the best-scoring sub-sequence alignment(s). The classic algorithm is Smith-Waterman. **This is the relevant paradigm for aligning short HTS reads to a large reference genome**, as we are looking for the best local match for each read within the vast genome.

*   **Sequence Similarity Metrics:** Quantify the relatedness between sequences.
    *   **Percent Identity:** The percentage of identical characters (bases) in the aligned region.
    *   **Edit Distance:** The minimum number of single-character edits (insertions, deletions, substitutions) required to change one sequence into the other.
    *   **Hamming Distance:** The number of positions at which two *equal-length* sequences differ. Less applicable to HTS reads which can have indels relative to the reference.

*   **Alignment Scoring Systems:** Define the value of matches, mismatches, and gaps to score the quality of an alignment.
    *   **Match Score:** A positive score awarded for identical characters.
    *   **Mismatch Penalty:** A negative score (penalty) for non-identical characters. Penalties can be uniform or different for transitions (A<->G, C<->T) vs. transversions (purine<->pyrimidine).
    *   **Gap Penalty:** A negative score for introducing gaps (representing insertions or deletions).
        *   *Linear Gap Penalty:* Penalty is proportional to the length of the gap (`penalty = length * g`). Penalizes long gaps heavily.
        *   *Affine Gap Penalty:* Uses separate penalties for opening a gap (`o`) and extending it (`e`), where `|o| > |e|`. The total penalty is `penalty = o + e * (length - 1)`. This is biologically more realistic, as a single mutational event often creates a multi-base indel. This is the standard model in modern aligners.

*   **Dynamic Programming (DP) Foundation:**
    *   Needleman-Wunsch (global) and Smith-Waterman (local) algorithms use DP to find the optimal alignment score and corresponding alignment path.
    *   They construct a 2D matrix where cell `(i, j)` stores the best score for aligning the first `i` characters of sequence A with the first `j` characters of sequence B.
    *   Each cell's value is calculated based on the scores of adjacent cells (left, top, top-left) and the scoring system (match/mismatch/gap penalties).
    *   The optimal alignment path(s) are found by tracing back through the matrix from the highest-scoring cell (local) or the bottom-right cell (global).

### **3.2.2 Challenges of Aligning High-Throughput Sequencing Data**

Directly applying classic DP algorithms like Smith-Waterman to align billions of HTS reads (length `m` ≈ 100-300 bp) against a multi-gigabase reference genome (length `n` ≈ 3x10^9 bp for human) is computationally infeasible. The O(m*n) complexity per read leads to astronomical computation times (O(N*m*n) for N reads). Furthermore:
*   **Massive Data Volume:** Billions of reads per run.
*   **Repetitive Nature of Genomes:** Many reads originate from repetitive regions, leading to multiple potential alignment locations (ambiguity).
*   **Sequencing Errors:** Reads contain errors that need to be tolerated during alignment.
*   **Genomic Variation:** Reads contain true biological differences (SNPs, Indels, SVs) relative to the reference, which must also be accommodated.

### **3.2.3 Innovations for Fast HTS Alignment: Indexing and Heuristics**

Modern HTS aligners overcome the computational bottleneck by using two key strategies: indexing the reference genome for rapid searching, and employing heuristic algorithms (shortcuts) that trade guaranteed optimality for speed.

*   **Reference Genome Indexing Strategies:** Pre-processing the reference genome into a data structure that allows for extremely fast searching of short sequences (seeds).
    *   **Hash Table / k-mer Index:** The genome is broken into short k-mers. A hash table stores the locations of all occurrences of each k-mer. To align a read, its k-mers are looked up in the table to quickly identify potential mapping regions. Simple but memory-intensive and less flexible for inexact matching.
    *   **Suffix Tree / Suffix Array:** Powerful stringology data structures. A suffix tree contains all suffixes of the genome in a tree format, allowing linear-time substring searches (O(m)). A suffix array stores the starting positions of all lexicographically sorted suffixes. While theoretically powerful, their large memory footprint makes them impractical for large genomes.
    *   **Burrows-Wheeler Transform (BWT) and FM-index:** **The breakthrough technology underpinning most modern aligners (BWA, Bowtie2).**
        *   *BWT:* A reversible permutation of the characters in the text (genome) that groups identical characters together locally. This makes the transformed text highly compressible.
        *   *FM-index:* A compressed, self-indexing data structure built on the BWT. It allows for:
            1.  `count(pattern)`: Efficiently finding the number of occurrences of a query pattern (read/seed) in the original text without explicitly storing all suffix locations.
            2.  `locate(pattern)`: Efficiently finding the exact genomic coordinates of those occurrences.
        *   *Advantages:* Extremely memory-efficient (index size can be smaller than the genome itself) and exceptionally fast search times.

*   **Seed-and-Extend Heuristic Strategy:** Most fast aligners follow this paradigm:
    1.  **Seeding:** Identify one or more short, exact or near-exact matches (seeds) between the read and the reference genome using the index. Seeds can be fixed-length k-mers, or variable-length maximal exact matches (MEMs). This step rapidly narrows down the potential alignment locations.
    2.  **Extension:** From the anchor points provided by the seeds, extend the alignment outwards into the surrounding regions of the read and reference. This step typically uses more sensitive (but slower) algorithms like banded Smith-Waterman to score the full alignment, allowing for mismatches and gaps outside the seed region.
    3.  **Scoring and Selection:** Evaluate the scores of alignments generated from different seeds or locations. Select the best-scoring alignment(s) based on the scoring scheme. Assign a mapping quality score (MAPQ).

*   **Other Heuristic Optimizations:**
    *   *Banding:* Restricting the DP alignment calculation to a diagonal band around the expected path, reducing computation.
    *   *Score Thresholds:* Abandoning extension early if the alignment score drops below a certain threshold.
    *   *Paired-End Information:* Using the expected distance and orientation of read pairs to rescue alignments where one read maps well but the other doesn't, or to resolve ambiguous mappings.

### **3.2.4 BWA (Burrows-Wheeler Aligner)**

Developed by Heng Li, BWA is one of the most widely used aligners.

*   **Core Technology:** Employs BWT and the FM-index for efficient genome indexing and seed finding.
*   **Algorithm Evolution:**
    *   **`BWA-backtrack`:** The original algorithm, optimized for very short (<100bp) Illumina reads. Uses BWT for finding potential hits and a backtracking search on the BWT structure to extend alignments, allowing mismatches and gaps. Less flexible for longer reads or complex indels.
    *   **`BWA-SW`:** Designed for longer reads (e.g., 454, longer Illumina) with potentially higher error rates. Uses BWT seeding combined with a Smith-Waterman-like extension phase. Largely superseded by BWA-MEM.
    *   **`BWA-MEM`:** **The current standard and recommended BWA algorithm.** Highly versatile, suitable for reads from 70bp up to megabases (Illumina, PacBio, ONT).
        *   Uses **Maximal Exact Matches (MEMs)** as seeds, found efficiently using the FM-index.
        *   Employs a heuristic **Smith-Waterman extension** step.
        *   Features adaptive seeding strategies, smart paired-end rescue logic, and support for long reads and split alignments (important for SV detection).
*   **Key Parameters:** `-t` (threads), `-k` (min seed length), `-T` (min score threshold), `-A` (match score), `-B` (mismatch penalty), `-O` (gap open), `-E` (gap extend). Default parameters are generally well-tuned for Illumina data.
*   **Performance:** Fast, relatively low memory usage, highly accurate, robust, and widely adopted in standard pipelines (e.g., GATK).

### **3.2.5 Bowtie2**

Developed by Ben Langmead et al., Bowtie2 is another highly popular BWT-based aligner.

*   **Core Technology:** Also uses BWT/FM-index, but employs both forward and reverse indices of the genome for potentially faster seeding.
*   **Alignment Strategy:** Seed-and-extend.
    *   Uses **multiple, potentially inexact seeds** extracted from different parts of the read.
    *   Extension phase uses dynamic programming.
    *   Offers pre-set modes balancing speed vs. sensitivity (`--very-fast` to `--very-sensitive`) and end-to-end vs. local alignment modes.
*   **Parallelism:** Efficient multi-threading via `-p` parameter.
*   **Comparison with BWA-MEM:**
    *   *Speed:* Often faster than BWA-MEM in default modes.
    *   *Memory:* Similar low memory footprint, perhaps slightly lower than BWA.
    *   *Sensitivity/Accuracy:* Generally comparable for standard Illumina reads. BWA-MEM might have an edge for very long reads or complex indels. Bowtie2's sensitivity presets offer user flexibility.
    *   *Use Case:* Excellent choice for Illumina data; BWA-MEM is generally preferred for long reads.

### **3.2.6 Other Notable Alignment Tools**

*   **SOAP Series (SOAP, SOAP2, SOAP3-dp):** Early influential aligners from BGI, known for speed with very short reads. SOAP3-dp leveraged GPU acceleration.
*   **Novoalign:** A commercial aligner known for high accuracy and sensitivity, particularly for indels, but slower and requires a license.
*   **Minimap2:** Also developed by Heng Li. **The current de facto standard for aligning long reads (PacBio, ONT) and for whole-genome alignment.**
    *   Uses **minimizers** (a subsequence sampling technique) for highly efficient seeding and indexing.
    *   Employs fast, heuristic **collinear chaining** for seed aggregation.
    *   Extremely fast and memory-efficient. Handles high error rates well. Also supports short reads and RNA-seq.
*   **NGMLR (Next Generation Mapper for Long Reads):** Specifically designed for long reads with an emphasis on **sensitivity to structural variation breakpoints**. Often used upstream of SV callers like Sniffles.
*   **STAR Aligner:** Primarily designed for **RNA-seq data** due to its highly efficient spliced alignment capability, but can also be used for DNA-seq alignment and is very fast.

### **3.2.7 Assessing Alignment Quality**

Evaluating the output of the alignment process is crucial before proceeding. The standard format for alignment output is SAM (Sequence Alignment/Map), and its binary, compressed version BAM.

*   **Mapping Quality Score (MAPQ):**
    *   Reported in the 5th column of SAM/BAM.
    *   A Phred-scaled measure of the confidence that a read is aligned *correctly* to its reported position. `MAPQ = -10 * log10(P(mapping error))`.
    *   MAPQ=20 means 1% chance of error; MAPQ=30 means 0.1% chance.
    *   Crucially, **MAPQ reflects mapping uniqueness.** A read that could align equally well to multiple locations in the genome typically receives MAPQ=0. High MAPQ indicates a confident, unique alignment.
    *   Used heavily in downstream filtering (e.g., variant calling often ignores reads with low MAPQ).

*   **Alignment Flags (SAM/BAM Column 2):** A bitwise flag providing information about the alignment, e.g., whether the read mapped, if its mate mapped, strand, whether it's a PCR duplicate, etc.

*   **CIGAR String (SAM/BAM Column 6):** Compact Idiosyncratic Gapped Alignment Report. Encodes the specifics of the alignment: number of matches (M), insertions (I), deletions (D), skipped regions (N, for RNA-seq), soft clipping (S, unaligned ends), hard clipping (H), padding (P). Essential for visualizing the alignment and for variant calling.

*   **Uniquely Mapped vs. Multi-Mapped Reads:**
    *   *Uniquely Mapped Reads:* Align confidently to only one location (high MAPQ). Most informative.
    *   *Multi-Mapped Reads:* Align to multiple locations with similar scores (low or zero MAPQ). Common in repetitive regions. Handling depends on the downstream application – sometimes discarded, sometimes one location is chosen randomly/best, sometimes handled specially (e.g., in RNA-seq quantification).

*   **Coverage Depth, Breadth, and Uniformity:**
    *   *Depth:* Average number of reads covering each base.
    *   *Breadth:* Percentage of the genome/target region covered by at least X reads (e.g., >95% covered by >= 10 reads).
    *   *Uniformity:* How evenly the depth is distributed. High variance indicates bias.
    *   Tools like `samtools depth`, `bedtools coverage`, `Qualimap`, `Picard CollectWgsMetrics` are used to calculate these statistics, which are vital indicators of library quality and sequencing success.

*   **Common Alignment Issues and Diagnostics:**
    *   *Low Mapping Rate:* Check for sample contamination, wrong reference genome, poor sequencing quality (run `FastQC`), high adapter content (run adapter trimming).
    *   *High Multi-mapping Rate:* May indicate short reads, highly repetitive genome, or potential issues with library insert size.
    *   *Coverage Bias (e.g., GC bias):* Assess using Qualimap or Picard. May indicate library prep issues (PCR bias). Consider PCR-free libraries or analytical corrections.
    *   *Discordant Paired-End Reads:* High fraction of pairs with unexpected insert sizes or orientations can indicate extensive structural variation in the sample or library prep artifacts.

**Section Summary:** Aligning billions of sequencing reads to a reference genome is a fundamental step in resequencing analysis, fraught with computational and biological challenges. Modern aligners like BWA, Bowtie2, and Minimap2 leverage sophisticated indexing techniques (primarily BWT/FM-index or minimizers) and heuristic seed-and-extend strategies to achieve remarkable speed and accuracy. Understanding alignment principles, tool choices, and the critical evaluation of alignment quality through metrics like MAPQ and coverage statistics (using SAM/BAM format) is essential for reliable downstream genomic interpretation.

---

## **3.3 Variant Calling: Identifying Genomic Differences**

Once sequencing reads are aligned to the reference genome, the next step is to identify the positions where the sequenced sample differs from the reference. This process, known as variant calling, aims to detect various types of genetic variations, from single base changes to large structural rearrangements. Accurate variant calling is the foundation for nearly all applications of resequencing data, including disease gene discovery, population genetics, and cancer genomics.

![Variant Types](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_types.svg)
*Figure 3.3: Illustration of common genomic variant types. SNPs involve single base changes. Indels involve insertion or deletion of short sequences. Structural Variations (SVs) encompass larger rearrangements like deletions, duplications, inversions, and translocations.*

### **3.3.1 Overview of Genomic Variation Types**

Variant callers aim to identify several classes of variation:

*   **Single Nucleotide Polymorphisms (SNPs):** Changes affecting a single DNA base pair. The most common type of variation. Classified as:
    *   *Transitions:* Purine to purine (A↔G) or pyrimidine to pyrimidine (C↔T). Generally more frequent than transversions due to biochemical similarities.
    *   *Transversions:* Purine to pyrimidine or vice versa (A/G ↔ C/T).
*   **Insertions and Deletions (Indels):** Insertion or removal of one or more nucleotides. Typically refers to small events (e.g., 1-50 bp, though the boundary is arbitrary).
    *   *Frameshift Indels:* Occur in coding regions and have a length not divisible by 3. They alter the reading frame, usually leading to a premature stop codon and a non-functional protein product (Loss-of-Function, LoF).
    *   *In-frame Indels:* Occur in coding regions with a length divisible by 3. They add or remove one or more amino acids but maintain the reading frame. Effects can range from benign to severe.
*   **Structural Variations (SVs):** Alterations affecting larger segments of DNA (typically >50 bp, up to many megabases). SVs are diverse and can have significant phenotypic consequences. Major types include:
    *   *Deletions (DEL):* Loss of a DNA segment.
    *   *Duplications (DUP):* Gain of one or more copies of a DNA segment. Can be tandem (adjacent) or interspersed.
    *   *Insertions (INS):* Addition of a DNA segment (which can be novel sequence, a mobile element, or a duplicated segment from elsewhere).
    *   *Inversions (INV):* Reversal of the orientation of a DNA segment.
    *   *Translocations (TRA/BND):* Movement of a DNA segment to a different genomic location, either on the same chromosome or a different one. Can be balanced (no net gain/loss of material) or unbalanced. BND refers to the specific breakend junctions.
    *   *Copy Number Variations (CNVs):* A broader term often encompassing large (>1 kb) deletions and duplications, referring specifically to changes in the copy number of genomic regions.
    *   *Complex SVs:* Combinations of multiple SV events (e.g., deletions with insertions at the breakpoint, chromothripsis).

### **3.3.2 Challenges in Variant Calling**

Detecting variants accurately is challenging due to several factors:

*   **Sequencing Errors:** Random errors in reads can mimic true variants, especially low-frequency ones.
*   **Alignment Ambiguity/Errors:** Reads, especially in repetitive or complex regions, might be mismapped or aligned with incorrect gaps, leading to false positive or false negative calls. Indels and SVs are particularly prone to causing alignment artifacts.
*   **PCR Duplicates and Bias:** Can artificially inflate the evidence for a specific allele (including errors) and cause uneven coverage.
*   **Allelic Bias:** One allele might be preferentially amplified or sequenced over the other, skewing allele frequencies at heterozygous sites.
*   **Sample Quality/Purity:** DNA degradation can introduce errors. In tumor samples, admixture with normal cells complicates the detection of somatic variants due to varying allele frequencies. Ploidy changes in tumors also add complexity.
*   **Genomic Context:** Repetitive sequences, regions of extreme GC content, segmental duplications, and reference genome gaps or errors make variant calling difficult.
*   **Distinguishing True Variants from Noise:** The core challenge is developing statistical models robust enough to separate true biological variation from the various sources of technical noise.

### **3.3.3 Principles of SNP and Small Indel Calling**

Most modern SNP/Indel callers operate on alignment data (BAM files).

![Variant Calling Workflow](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_calling_workflow.svg)
*Figure 3.4: A typical workflow for SNP and Indel calling. Input is an analysis-ready BAM file (aligned, sorted, duplicates marked, potentially BQSR'd). Variant calling tools produce a VCF file, which is then subjected to filtering and annotation.*

*   **Data Pre-processing for Variant Calling:** Generating an "analysis-ready" BAM file is crucial. Common steps (often following GATK Best Practices) include:
    1.  **Alignment:** Using an appropriate aligner (e.g., BWA-MEM).
    2.  **Sorting:** Ordering alignments by coordinate (`samtools sort`).
    3.  **Marking Duplicates:** Identifying and flagging PCR duplicates (`Picard MarkDuplicates` or `samtools markdup`). Callers typically ignore duplicate reads to avoid bias.
    4.  **(GATK specific) Base Quality Score Recalibration (BQSR):** Adjusting base quality scores based on machine cycle, sequence context, and known variant sites to make them more accurately reflect the probability of error. This reduces false positive SNP calls caused by systematic sequencing errors. (`GATK BaseRecalibrator` + `ApplyBQSR`).
    5.  **(Optional, older GATK) Local Realignment Around Indels:** Realigning reads in regions containing Indels to correct mapping artifacts near Indel sites. This functionality is now largely integrated into GATK's HaplotypeCaller.

*   **Variant Calling Algorithms:**
    *   **Pileup-based Methods (Simpler Approach):** Tools like early `SAMtools mpileup` or `VarScan` examine the "pileup" of bases at each genomic position. They count the occurrences of each base (A, C, G, T, insertions, deletions) from the aligned reads covering that position, considering base qualities. Variants are called if the frequency of a non-reference allele exceeds certain thresholds (e.g., minimum variant allele frequency, minimum supporting reads) and meets quality criteria. Simple and fast, but can be less accurate, especially for Indels and in noisy regions.
    *   **Statistical/Bayesian Methods (Standard Approach):** Tools like `GATK HaplotypeCaller`, `FreeBayes`, `SAMtools/bcftools call` employ more sophisticated statistical models. The core idea is often Bayesian inference:
        *   **Goal:** Calculate the posterior probability of each possible genotype `G` (e.g., homozygous reference 0/0, heterozygous 0/1, homozygous variant 1/1) given the observed sequencing data `D` at that locus: `P(G | D)`.
        *   **Bayes' Theorem:** `P(G | D) ∝ P(D | G) * P(G)`
            *   `P(D | G)` (Likelihood): The probability of observing the aligned reads (considering base calls, quality scores, alignment uncertainty) *if* the true genotype were `G`. This is the most complex part, modeling sequencing errors and alignment probabilities.
            *   `P(G)` (Prior): The prior probability of genotype `G`. Can be uniform (uninformative) or informed by population frequencies (e.g., from dbSNP) or linkage disequilibrium.
        *   **Output:** The caller typically reports the most likely genotype (Maximum A Posteriori or MAP estimate) and a quality score (QUAL) reflecting the confidence in the variant call (often related to the probability that the site is non-reference).

    *   **Haplotype-Based Calling (e.g., GATK HaplotypeCaller):** A significant advance, particularly for Indel detection. Instead of considering each site independently, HaplotypeCaller:
        1.  Identifies "active regions" with signs of possible variation.
        2.  Performs local *de novo* assembly of reads within each active region to construct candidate haplotypes (short stretches of sequence representing potential local variations).
        3.  Calculates the likelihood of each read originating from each candidate haplotype using a Pair-HMM alignment model.
        4.  Uses these haplotype likelihoods in the Bayesian framework to call SNPs and Indels simultaneously with higher accuracy, especially robustly handling linked variants and Indels.

*   **Variant Filtering and Quality Control:** Raw variant calls contain numerous false positives. Filtering is essential for reliable results.
    *   **Hard Filtering:** Applying fixed thresholds to various annotation fields in the VCF file generated by the caller. Common filter criteria include:
        *   `QUAL`: Minimum variant quality score.
        *   `DP`: Minimum (and sometimes maximum) read depth at the site.
        *   `QD` (Quality by Depth): QUAL normalized by depth; filters variants with high QUAL solely due to high DP.
        *   `FS` (Fisher Strand Bias): Tests if evidence for the variant is biased towards reads from one strand (forward vs. reverse), often indicative of artifacts.
        *   `MQ` (Mapping Quality): Filters based on the root-mean-square MAPQ of reads supporting the variant.
        *   `SOR` (Strand Odds Ratio): Another measure of strand bias.
        *   Rank Sum Tests (`MQRankSum`, `ReadPosRankSum`): Test if properties (like MAPQ or position within the read) differ significantly between reads supporting reference vs. variant alleles.
    *   **(GATK specific) Variant Quality Score Recalibration (VQSR):** A sophisticated machine learning approach, considered best practice when applicable.
        1.  Uses a set of known, high-quality "true" variants (e.g., from HapMap, 1000 Genomes Omni SNPs) and known variant sites (e.g., dbSNP).
        2.  Trains a Gaussian Mixture Model to learn the multi-dimensional profile of annotations (QD, FS, MQ, etc.) associated with true variants versus likely artifacts.
        3.  Applies the model to score all variants in the call set with a VQSLOD (log-odds score of being true).
        4.  Filters variants based on a VQSLOD threshold chosen to achieve a desired sensitivity level (e.g., retain 99.5% of known true positives).
        *   *Pros:* More powerful than hard filtering, better at distinguishing subtle artifacts.
        *   *Cons:* Requires high-quality training/truth datasets (may not exist for all species), computationally intensive.

*   **Comparison of Common SNP/Indel Callers:**
    *   **GATK (HaplotypeCaller + VQSR):** Industry standard, comprehensive, well-documented best practices. Excellent accuracy, especially for Indels due to haplotype-based approach and robust VQSR filtering. Computationally demanding.
    *   **FreeBayes:** Haplotype-based Bayesian caller. Fast, highly sensitive, particularly good for pooled samples, polyploid organisms, or detecting complex multi-nucleotide polymorphisms (MNPs). Filtering often requires careful tuning by the user.
    *   **SAMtools mpileup + BCFtools call:** Classic, lightweight, fast combination. `bcftools call` now includes sophisticated multi-allelic and consensus calling models. A solid choice, especially for non-model organisms or when computational resources are limited.

### **3.3.4 GATK Best Practices Workflow for Germline SNPs/Indels**

The Broad Institute's GATK provides widely adopted workflows. A typical pipeline for germline short variants (SNPs/Indels) involves:

1.  **Data Pre-processing:** Generate analysis-ready BAMs (Alignment, Sorting, Mark Duplicates, BQSR).
2.  **Variant Calling (Per-Sample):** Run `GATK HaplotypeCaller` in `-ERC GVCF` (Genomic VCF) mode. GVCFs store likelihood information for all sites (variant and non-variant), enabling scalable joint genotyping.
3.  **Consolidation/Joint Genotyping (Multi-Sample):**
    *   For small cohorts: `GATK CombineGVCFs`.
    *   For large cohorts (>100 samples): Import GVCFs into a database using `GATK GenomicsDBImport`.
    *   Perform joint genotyping across all samples using `GATK GenotypeGVCFs` on the combined GVCF or GenomicsDB. This leverages cross-sample information to improve accuracy, especially at low-coverage sites. Output is a multi-sample VCF.
4.  **Variant Filtering:** Apply `GATK VariantRecalibrator` (VQSR) separately for SNPs and Indels, followed by `GATK ApplyVQSR` to filter the multi-sample VCF based on the trained models and desired sensitivity tranche.

### **3.3.5 Methods for Structural Variation (SV) Detection**

Detecting SVs from short-read data is more challenging than SNPs/Indels because SVs often span regions larger than the read length. SV callers typically integrate evidence from multiple types of signals present in the alignment data:

*   **Read Depth (RD) Methods:**
    *   *Signal:* Changes in average read coverage along the genome. Deletions cause decreased coverage; duplications cause increased coverage.
    *   *Approach:* Genome is divided into bins. Normalized coverage is calculated per bin (correcting for GC bias, mappability). Segmentation algorithms identify contiguous regions with significantly altered coverage.
    *   *Pros:* Good for detecting large CNVs (DELs/DUPs).
    *   *Cons:* Low resolution (bin size dependent); insensitive to copy-neutral SVs (INVs, balanced TRAs); sensitive to coverage biases.
    *   *Tools:* CNVnator, Control-FREEC, GATK gCNV.

*   **Paired-End Mapping (PEM) Methods:**
    *   *Signal:* Discordant read pairs – pairs where the distance between aligned reads is significantly different from the expected library insert size, or where their relative orientation is abnormal (e.g., eversion, inversion).
    *   *Approach:* Identify clusters of discordant pairs supporting a specific type of SV.
        *   *Increased insert size:* Suggests a deletion between the reads.
        *   *Decreased insert size:* Suggests an insertion or tandem duplication.
        *   *Abnormal orientation (e.g., F/F, R/R):* Suggests an inversion or complex rearrangement.
        *   *Mates mapping to different chromosomes/distant locations:* Suggests a translocation or interchromosomal insertion.
    *   *Pros:* Can detect various SV types (DEL, INS, DUP, INV, TRA); resolution depends on insert size.
    *   *Cons:* Insert size variability creates noise; cannot determine inserted sequence content; complex in repetitive regions.
    *   *Tools:* DELLY, Lumpy, Manta (often integrate PEM with other signals).

*   **Split Read (SR) Methods:**
    *   *Signal:* Reads that span an SV breakpoint. Part of the read aligns to one side of the breakpoint, and the other part aligns to the other side (which might be distant or on a different chromosome). Requires an aligner capable of producing split alignments (e.g., BWA-MEM, Minimap2).
    *   *Approach:* Identify clusters of split reads supporting the same breakpoint junction.
    *   *Pros:* Provides base-pair resolution of breakpoints; essential for detecting small Indels, mobile element insertions, and precise SV junctions.
    *   *Cons:* Requires reads to physically cross the breakpoint; can generate many false positives in repetitive regions; sensitivity depends on read length.
    *   *Tools:* DELLY, Lumpy, Manta (integrate SR with PEM).

*   **Assembly (AS) Methods:**
    *   *Signal:* Discrepancies between a *de novo* assembly of the sample's reads and the reference genome.
    *   *Approach:*
        *   *Local Assembly:* Assemble reads mapped near a putative SV region (identified by RD, PEM, or SR signals) to reconstruct the variant allele sequence. Compare the assembled contig to the reference.
        *   *Global Assembly:* Perform whole-genome *de novo* assembly and then align the assembly to the reference genome to identify large structural differences.
    *   *Pros:* Can resolve complex SVs, determine inserted sequences accurately, and potentially discover novel SVs missed by other methods.
    *   *Cons:* Computationally intensive; sensitive to assembly quality; may miss SVs in regions that fail to assemble well.
    *   *Tools:* Manta (includes local assembly), SV callers designed for assembly-based input (e.g., Assemblytics).

*   **Long-Read Sequencing for SVs:** Long reads (PacBio HiFi, ONT) dramatically improve SV detection because single reads can often span entire SV events and repetitive regions. This simplifies signal interpretation (RD, SR become more powerful) and allows for more accurate characterization and phasing of SVs. Dedicated long-read SV callers (e.g., `Sniffles`, `pbsv`, `SVIM`, `cuteSV`) are typically used.

*   **Integrated SV Callers:** Most modern SV callers (e.g., `Manta`, `DELLY`, `Lumpy`, `GRIDSS`) integrate evidence from multiple signals (RD, PEM, SR) using sophisticated statistical models or heuristics to improve sensitivity and specificity.

### **3.3.6 Copy Number Variation (CNV) Detection**

CNV detection, often considered a subset of SV calling, frequently relies heavily on read-depth analysis, especially for WGS data.

*   **Read Depth Normalization:** Critical first step to remove technical biases. Requires correcting for:
    *   *GC Content:* Read coverage often correlates with local GC percentage.
    *   *Mappability:* Regions where reads cannot be uniquely mapped will appear to have lower coverage.
    *   *Other factors:* Replication timing, batch effects. Normalization often involves comparing to a panel of normal samples or using statistical modeling (e.g., PCA).
*   **Segmentation:** Algorithms (e.g., Circular Binary Segmentation - CBS, Hidden Markov Models - HMMs) are applied to the normalized depth profile to identify contiguous segments of constant copy number.
*   **Copy Number Calling:** Assigning an integer copy number (0, 1, 2, 3, ...) to each segment. In diploid organisms, baseline is 2. In cancer, this is complicated by tumor purity (fraction of tumor cells in sample) and aneuploidy (changes in overall chromosome number), requiring specialized tools (e.g., `ASCAT`, `Sequenza`, `TitanCNA`) that often co-estimate purity, ploidy, and allele-specific copy number using both read depth and B-allele frequencies (BAF) from heterozygous SNPs.
*   **Tools:** `CNVnator`, `Control-FREEC`, `GATK gCNV`, `XHMM` (for WES), `FACETS` (cancer).

### **3.3.7 Variant Call Evaluation and Validation**

Variant calls, especially SVs and those in difficult genomic regions or at low frequency, require rigorous evaluation.

*   **Benchmarking Metrics:** Using a "gold standard" or benchmark call set (e.g., NIST/GIAB reference materials like NA12878), performance is measured using:
    *   *True Positives (TP):* Variants present in benchmark and called by the tool.
    *   *False Positives (FP):* Variants called by the tool but not in benchmark.
    *   *False Negatives (FN):* Variants in benchmark but missed by the tool.
    *   *Sensitivity (Recall):* `TP / (TP + FN)` - Ability to detect true variants.
    *   *Precision (PPV):* `TP / (TP + FP)` - Proportion of called variants that are true.
    *   *F1-Score:* Harmonic mean of Precision and Recall.
*   **Gold Standard Sets:** NIST/GIAB provides high-confidence SNP, Indel, and increasingly SV calls for specific reference individuals, crucial for pipeline validation and tuning. Simulation datasets can also be used but may lack real-world complexity.
*   **Orthogonal Experimental Validation:** Confirming specific, high-impact variant calls using independent experimental methods:
    *   *Sanger Sequencing:* Gold standard for validating individual SNPs and small Indels.
    *   *qPCR / Digital PCR (ddPCR):* Gold standard for validating CNVs by precisely quantifying DNA copy number at specific loci.
    *   *Long-Read Sequencing:* Validating complex Indels or SVs, providing phasing information.
    *   *Optical Mapping (e.g., Bionano Genomics):* Validating large SVs by comparing restriction enzyme patterns.
    *   *Cytogenetics (Karyotyping, FISH):* Validating large chromosomal abnormalities.

**Section Summary:** Variant calling identifies genomic differences between a sample and the reference. SNPs and small Indels are typically detected using sophisticated statistical models (often Bayesian) applied to aligned reads, with haplotype-aware methods like GATK HaplotypeCaller offering improved accuracy, especially for Indels. Detecting larger Structural Variations is more complex, requiring integration of signals from read depth, paired-end discordance, split reads, and sometimes assembly. Long-read sequencing significantly enhances SV detection. Rigorous filtering (hard filters or VQSR) and benchmarking against gold standards (NIST/GIAB) are crucial for accuracy. High-impact variants often warrant orthogonal experimental validation.

---

## **3.4 Variant Annotation and Functional Prediction**

Detecting genomic variants is only the first step; understanding their potential biological significance is the ultimate goal. Variant annotation is the process of adding layers of information to variant calls, describing their location relative to genomic features (genes, exons, regulatory elements), predicting their impact on gene products (proteins), reporting their frequency in populations, and linking them to known clinical associations. Functional prediction tools further attempt to estimate the likelihood that a variant (particularly a coding variant) will disrupt protein function or gene regulation.

![Variant Annotation Workflow](/home/<USER>/data/course/NGS_master2/专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_annotation_workflow.svg)
*Figure 3.5: Schematic overview of the variant annotation process. A VCF file containing variant calls is enriched with information from various databases covering gene definitions, population frequencies, clinical significance, conservation scores, and functional predictions.*

### **3.4.1 Core Concepts in Variant Annotation**

Annotation integrates information from diverse database resources.

*   **Genomic Feature Databases:** Provide the map of genes, transcripts, exons, introns, UTRs, regulatory elements, etc.
    *   **Key Resources:** NCBI RefSeq, Ensembl/GENCODE, UCSC Genome Browser tracks.
    *   **Information Used:** Gene symbols, transcript IDs, exon/intron boundaries, coding sequence (CDS) coordinates, UTR coordinates, regulatory element locations (if available, e.g., from ENCODE).

*   **Positional Annotation:** Describes where a variant falls relative to these features.
    *   **Categories:** `exonic`, `intronic`, `splicing` (at or near exon-intron boundary), `UTR3` (3' untranslated region), `UTR5` (5' untranslated region), `intergenic`, `upstream` (near transcription start site), `downstream` (near transcription end site), `regulatory_region_variant` (overlaps known regulatory element).
    *   **Importance:** Variants in exons are most likely to directly affect protein sequence; variants in splice sites or UTRs can affect mRNA processing or translation; variants in regulatory regions can alter gene expression.

*   **Functional Consequence Annotation:** Predicts the effect of the variant on the gene product, primarily focusing on protein-coding effects based on the genetic code. Typically annotated per transcript, as a gene can have multiple isoforms.
    *   **Sequence Ontology (SO) Terms (Commonly Used):**
        *   **High Impact:** `frameshift_variant`, `nonsense` (stop_gained), `stop_lost`, `start_lost`, `splice_acceptor_variant`, `splice_donor_variant`. These are often assumed to cause Loss-of-Function (LoF).
        *   **Moderate Impact:** `missense_variant` (amino acid change). Impact is variable and requires further prediction. `inframe_insertion`, `inframe_deletion`.
        *   **Low Impact:** `synonymous_variant` (codon change, same amino acid). Generally considered benign, but can sometimes affect splicing or mRNA stability.
        *   **Modifier Impact:** `intron_variant`, `UTR_variant`, `intergenic_variant`, `upstream_gene_variant`, `downstream_gene_variant`, `regulatory_region_variant`. Impact is harder to predict but potentially significant for gene regulation.

*   **Population Frequency Annotation:** Reports the frequency of the variant allele in large reference populations. Crucial for distinguishing common, likely benign polymorphisms from rare variants that are more likely to be pathogenic (especially for Mendelian diseases).
    *   **Key Databases:**
        *   **gnomAD (Genome Aggregation Database):** The largest and most widely used resource, containing WGS and WES data from hundreds of thousands of individuals, stratified by population ancestry. Provides allele frequency (AF), allele count (AC), and number of alleles tested (AN).
        *   **1000 Genomes Project (1kGP):** An earlier, foundational project providing frequencies across global populations.
        *   **ExAC (Exome Aggregation Consortium):** Precursor to gnomAD, focused on exome data.
        *   **dbSNP:** Primarily a catalog of submitted variants (with `rs` IDs), but also aggregates frequency information from various sources.
    *   **Interpretation:** Variants common in the general population (e.g., AF > 1-5%) are highly unlikely to cause rare Mendelian disorders. Filtering based on population frequency is a standard step in rare disease analysis. For somatic variant calling in cancer, population databases help identify and filter out common germline polymorphisms.

*   **Clinical Significance Annotation:** Links variants to known clinical interpretations regarding pathogenicity.
    *   **Key Databases:**
        *   **ClinVar:** NCBI database archiving interpretations of variants submitted by clinical testing labs, research groups, and expert panels (e.g., ClinGen). Classifications typically follow ACMG/AMP guidelines: `Pathogenic`, `Likely pathogenic`, `Benign`, `Likely benign`, `Variant of Uncertain Significance (VUS)`. **Essential resource for clinical genetics.**
        *   **HGMD (Human Gene Mutation Database):** Commercial database curating published disease-causing mutations.
        *   **OMIM (Online Mendelian Inheritance in Man):** Catalog of human genes and genetic disorders, often describing associated genes and some key mutations.

*   **Other Annotations:** Conservation scores (PhyloP, PhastCons, GERP++), regulatory element overlaps (ENCODE, JASPAR), dbSNP IDs (`rs` numbers), protein domain information (Pfam), etc.

### **3.4.2 Major Variant Annotation Tools**

These tools automate the process of querying databases and integrating annotations into the VCF file.

*   **ANNOVAR (Annotate Variation):**
    *   *Workflow:* Command-line tool. User downloads desired database files. ANNOVAR takes VCF input, maps variants to gene models (RefSeq, Ensembl, etc.), determines functional consequences, and queries selected local databases (population frequency, clinical, prediction scores, etc.).
    *   *Pros:* Highly flexible, supports a vast range of databases, widely used.
    *   *Cons:* Requires manual database management (downloading, updating).

*   **SnpEff (SNP Effect Predictor):**
    *   *Workflow:* Command-line tool. Relies on pre-built database files specific to genome build and annotation version. Performs fast annotation based on these databases, predicting functional consequences using Sequence Ontology terms. Can integrate some external annotations like ClinVar.
    *   *Pros:* Fast execution, standardized output format (`ANN` tag in VCF INFO field), easy to set up for standard organisms.
    *   *Cons:* Database availability might be limited for non-standard organisms; less flexible database integration than ANNOVAR.

*   **VEP (Variant Effect Predictor):**
    *   *Workflow:* Developed by Ensembl. Available as web tool, REST API, or standalone command-line tool. Tightly integrated with Ensembl gene/transcript annotations. Predicts consequences based on Ensembl models, provides extensive annotations from Ensembl resources (regulation, conservation, orthologs). Can integrate data from external sources (dbSNP, gnomAD, ClinVar) and run prediction plugins (SIFT, PolyPhen, CADD) via local caches or direct lookups.
    *   *Pros:* Extremely comprehensive annotations, especially leveraging Ensembl data; highly configurable; well-maintained. Standard output uses `CSQ` tag in VCF INFO field.
    *   *Cons:* Can be resource-intensive; relies on Ensembl release cycle.

*   **Interpreting Annotation Output (VCF INFO field):** Annotations are typically added as key-value pairs within the INFO column of the VCF file. Tools like SnpEff and VEP often consolidate multiple annotations per transcript into a single tag (`ANN` or `CSQ`), with sub-fields separated by pipes (`|`). Understanding the format specified by the tool is crucial for parsing and utilizing the annotations.

### **3.4.3 *In Silico* Functional Prediction Methods**

For variants of unknown significance, particularly missense variants, *in silico* tools attempt to predict their impact on protein function or deleteriousness.

*   **Based on Evolutionary Conservation:**
    *   *Principle:* Amino acid residues or genomic regions critical for function tend to be conserved across species during evolution. Changes at conserved positions are more likely to be deleterious.
    *   *Methods:* Compare sequences/regions across multiple species alignments. Tools calculate conservation scores (e.g., `PhyloP`, `phastCons`, `GERP++`). Higher scores indicate stronger conservation and suggest variants in these regions might be impactful.

*   **Based on Protein Sequence and Structure:**
    *   *Principle:* Amino acid substitutions can alter protein stability, folding, charge distribution, or interfere with binding sites (ligand, protein, DNA).
    *   *Methods:* Analyze the physicochemical properties of the original and substituted amino acids (size, hydrophobicity, charge). If 3D structure is known (or can be modeled), analyze the location of the mutation (e.g., core vs. surface, proximity to active site) and predict its impact on stability or interactions.

*   **Machine Learning Approaches:**
    *   *Principle:* Train computational models (e.g., Support Vector Machines - SVM, Random Forests, Neural Networks) using features derived from conservation, sequence context, physicochemical properties, structural information, gene-level information, etc. The models learn to distinguish known disease-causing variants from presumably benign variants.
    *   *Methods:* Requires large, curated training datasets of pathogenic and benign variants. The trained model then predicts the likelihood of a novel variant being deleterious.

*   **Ensemble/Meta-Predictors:**
    *   *Principle:* Combine the output scores from multiple individual prediction tools to achieve better overall performance and robustness.
    *   *Methods:* Use simple averaging/voting or train a meta-classifier using the scores from individual tools as input features.

### **3.4.4 Common Functional Prediction Tools**

Numerous tools exist, often focusing on missense variants:

*   **SIFT (Sorting Intolerant From Tolerant):**
    *   *Input:* Amino acid substitution.
    *   *Method:* Primarily uses sequence homology. Assesses whether an amino acid substitution is likely to be tolerated based on the range of amino acids observed at that position in closely related sequences found through PSI-BLAST.
    *   *Output:* SIFT score (≤0.05 often considered 'deleterious') and a qualitative prediction ('Tolerated' or 'Deleterious').

*   **PolyPhen-2 (Polymorphism Phenotyping v2):**
    *   *Input:* Amino acid substitution.
    *   *Method:* Integrates sequence conservation, physicochemical properties, and structural information (if available, e.g., PSIC scores, DSSP). Uses a Naive Bayes classifier. Provides two models: HumDiv (trained to distinguish disease-causing mutations from common human variants) and HumVar (trained to distinguish disease-causing mutations from all types of human variants, including rare ones).
    *   *Output:* Probability score (0 to 1, higher means more likely damaging) and qualitative prediction ('benign', 'possibly damaging', 'probably damaging').

*   **CADD (Combined Annotation Dependent Depletion):**
    *   *Input:* Any SNV or small Indel.
    *   *Method:* A comprehensive framework integrating ~90 diverse annotations (conservation, regulatory, transcript/protein level features, other prediction scores). Uses an SVM trained to differentiate observed human variants (likely less deleterious) from simulated variants. Aims to provide a general measure of 'deleteriousness' rather than specifically pathogenicity.
    *   *Output:* Phred-scaled CADD score (raw and scaled). Higher scores indicate greater predicted deleteriousness (e.g., score >= 10 means top 10% most deleterious, >=20 means top 1%, >=30 means top 0.1%).

*   **REVEL (Rare Exome Variant Ensemble Learner):**
    *   *Input:* Missense variant.
    *   *Method:* Meta-predictor specifically trained on recent pathogenic/benign datasets to evaluate **rare** missense variants. Combines scores from 13 individual tools using a Random Forest model.
    *   *Output:* Score (0 to 1, higher means more likely pathogenic). Generally performs well in distinguishing clinically relevant missense variants.

*   **M-CAP (Mendelian Clinically Applicable Pathogenicity):**
    *   *Input:* Missense variant.
    *   *Method:* Another meta-predictor focusing on Mendelian disease variants, trained using gradient boosting trees. Optimized for clinical applicability.
    *   *Output:* Score (0 to 1) and prediction ('Pathogenic'/'Benign').

**Note:** *In silico* predictions are valuable for prioritizing variants but are not definitive proof of pathogenicity. They should be interpreted cautiously alongside genetic evidence (segregation), population frequency, and functional data.

### **3.4.5 Variant Prioritization Strategies**

In research (e.g., identifying a novel disease gene) or clinical diagnostics, annotation and prediction help filter down thousands/millions of variants to a manageable list of candidates.

*   **Filtering Funnel Approach:** A common strategy involves sequential filtering steps:
    1.  **Quality Control:** Remove low-quality calls (low QUAL, DP, VQSLOD fail).
    2.  **Population Frequency:** Remove common variants based on gnomAD AF (threshold depends on disease prevalence and inheritance model, e.g., <1% or <0.1% for rare Mendelian).
    3.  **Functional Consequence:** Focus on variants predicted to have significant impact (nonsense, frameshift, splice site, missense). Synonymous and deep intronic variants usually ranked lower unless specific evidence suggests otherwise.
    4.  **Functional Prediction Scores:** For missense variants, retain those predicted deleterious by tools like REVEL, CADD, PolyPhen-2, SIFT (using appropriate score thresholds).
    5.  **Clinical Databases:** Check if the variant is already known in ClinVar/HGMD and classified as pathogenic or benign.
    6.  **Gene/Phenotype Context:** Prioritize variants in genes known to be associated with the phenotype or related pathways.
    7.  **Inheritance Pattern / Segregation:** Apply genetic model filters based on family structure (see below).

*   **Leveraging Gene Information:** Prioritize variants in genes that:
    *   Have biological functions consistent with the observed phenotype (use OMIM, GeneCards, literature).
    *   Are expressed in the relevant tissues/cell types.
    *   Have knockout/mutant models with similar phenotypes.
    *   Are part of pathways known to be involved in the disease. Pathway enrichment analysis on candidate gene lists can be informative.

*   **Family-Based Analysis (Crucial for Mendelian Disease):**
    *   **Co-segregation:** Does the variant segregate with the disease status across the family? (Present in affected individuals, absent or carrier status in unaffected).
    *   **Filtering by Inheritance Model:**
        *   *Recessive:* Look for homozygous or compound heterozygous (two different pathogenic variants in the same gene) rare variants in affected individuals.
        *   *Dominant:* Look for rare heterozygous variants present in affected individuals (and potentially an affected parent, unless *de novo*).
        *   *X-linked:* Apply filters consistent with X-linked inheritance patterns.
        *   ***De Novo* Variants:** In trio analysis (patient + parents), identify variants present in the patient but absent in both parents. A powerful way to find causal variants for dominant disorders, especially with severe, early-onset phenotypes.

**Section Summary:** Variant annotation transforms raw variant calls into biologically meaningful information by integrating data on genomic location, functional consequence, population frequency, and clinical significance from numerous databases using tools like ANNOVAR, SnpEff, and VEP. *In silico* functional prediction tools like SIFT, PolyPhen-2, CADD, and REVEL help assess the potential impact of variants, particularly missense changes. Effective variant prioritization combines these annotations with filtering strategies (quality, frequency, consequence, prediction scores), gene context, and genetic evidence (inheritance patterns, segregation, *de novo* status) to identify the most promising candidates for causing disease or influencing traits.

---

## **3.5 Genome Assembly: Reconstructing Genomic Sequences**

While reference genomes exist for many model organisms and humans, numerous research scenarios require constructing a genome sequence from scratch (*de novo* assembly). This is necessary for species without a reference, for generating high-quality references for specific individuals or strains, or for studying genomic regions (like large insertions or highly diverged haplotypes) not well represented in an existing reference. Genome assembly involves computationally piecing together millions or billions of short or long sequencing reads to reconstruct the original chromosomes.

### **3.5.1 Fundamental Concepts in Genome Assembly**

*   ***De novo* vs. Reference-Assisted Assembly:**
    *   ***De novo* Assembly:** Builds the genome sequence solely from the overlaps between sequencing reads, without guidance from an existing genome map. The primary focus of this section.
    *   *Reference-Assisted Assembly:* Uses a related reference genome as a template or scaffold to guide the assembly process or improve an existing assembly (e.g., gap filling, error correction).

*   **Assembly Terminology:**
    *   **Contig:** A contiguous stretch of assembled sequence with no gaps, formed by overlapping reads.
    *   **Scaffold:** A set of contigs that are ordered and oriented relative to each other, with gaps (represented by 'N's) between them. Scaffolding information comes from paired-end reads, linked reads, long reads, optical maps, or Hi-C data that span the gaps between contigs.
    *   **Chromosome-Level Assembly:** An assembly where scaffolds have been assigned, ordered, and oriented onto chromosomes, ideally spanning from telomere to telomere (T2T).

*   **Assembly Quality Metrics:** Quantify the success of the assembly process.
    *   **Contiguity Metrics:**
        *   *N50 (Contig/Scaffold):* The length of the smallest contig/scaffold in the set that contains 50% of the total assembly length when sorted by size (longer is better). A primary measure of assembly continuity.
        *   *NG50:* Similar to N50, but normalized by the estimated genome size instead of the assembly size (provides a fairer comparison if assembly size differs from genome size).
        *   *L50 (Contig/Scaffold):* The number of contigs/scaffolds whose length sum constitutes 50% of the assembly length (smaller is better).
        *   *Max Contig/Scaffold Length:* The length of the longest piece.
        *   *Number of Contigs/Scaffolds:* Fewer generally indicates a more contiguous assembly.
    *   **Completeness Metrics:**
        *   *Total Assembly Length:* Should be close to the expected genome size.
        *   **BUSCO (Benchmarking Universal Single-Copy Orthologs):** Assesses genome completeness by searching for the presence and integrity of a curated set of expected single-copy genes for the organism's lineage. Reports % Complete (Single/Duplicated), Fragmented, and Missing genes. **A standard measure of gene space completeness.**
    *   **Correctness Metrics:**
        *   *Read Mapping Consistency:* Mapping the original reads back to the assembly should show high alignment rates, proper paired-end distances/orientations, and uniform coverage (tools like `QUAST` evaluate this). Misassemblies often manifest as regions with abnormal read mapping patterns.
        *   *Comparison to External Data:* Validation against genetic maps, physical maps (optical mapping), cytogenetics (FISH), or known gene structures.

*   **Major Challenges in Genome Assembly:**
    *   **Repetitive Sequences:** The single largest obstacle. If a repeat unit is longer than the read length, reads originating from different copies of the repeat look identical, making it impossible to determine their correct placement and connectivity. This fragments the assembly (contigs end at repeats) or leads to misassemblies (incorrect joining of regions flanking repeats).
    *   **Heterozygosity (in Diploid/Polyploid Genomes):** Differences between homologous chromosomes (alleles) can confuse assemblers. High heterozygosity can lead to alleles being assembled into separate contigs, artificially inflating assembly size and fragmentation, or creating chimeric contigs. Requires ploidy-aware assemblers or haplotype phasing techniques.
    *   **Sequencing Errors:** Can obscure true overlaps or create false ones, leading to fragmented or incorrect assemblies. Error correction is often a necessary pre-processing step, especially for high-error-rate long reads (older ONT/PacBio CLR).
    *   **Coverage Non-Uniformity:** Regions with low coverage are difficult to assemble; regions with extremely high coverage might be mistaken for repeats or cause computational issues.
    *   **Computational Complexity:** Assembling large, complex genomes requires significant computational resources (CPU time, memory).

### **3.5.2 Short-Read Assembly Algorithms (Primarily De Bruijn Graphs)**

Algorithms designed for the massive number of short, accurate reads from platforms like Illumina.

*   **De Bruijn Graph (DBG) Approach:** The dominant paradigm for short-read assembly.
    *   *Principle:* Avoids computationally expensive all-vs-all read overlap calculation by focusing on shared k-mers (short subsequences of length k).
    *   *Steps:*
        1.  **k-merization:** All reads are decomposed into overlapping k-mers. The choice of 'k' is critical: smaller k captures more overlaps but increases graph complexity due to short repeats; larger k resolves short repeats but requires deeper coverage and may miss overlaps if k is too large or reads have errors. Assemblers often use multiple k values.
        2.  **Graph Construction:** A directed graph is built where nodes represent (k-1)-mers (or sometimes k-mers). An edge exists from node A to node B if the k-mer corresponding to A followed by the last base of B exists in the reads. Essentially, edges represent k-mers, and nodes represent the overlaps between them.
        3.  **Graph Simplification:** The raw graph is complex due to sequencing errors (creating dead-end paths or 'tips') and heterozygosity (creating 'bubbles'). Algorithms remove tips, resolve bubbles (by choosing one path or collapsing them if alleles are similar), and merge unambiguous linear paths into single nodes/edges.
        4.  **Contig Generation:** Contigs correspond to unambiguous paths through the simplified graph. The graph traversal typically stops at complex branching points caused by repeats longer than k or unresolved heterozygosity.
    *   *Pros:* Computationally efficient for large datasets, relatively robust to sequencing errors (errors create low-frequency k-mers that can be filtered).
    *   *Cons:* Sensitive to k value; information from reads longer than k is partially lost; repeats significantly longer than k fragment the assembly.
    *   *Tools:* `SPAdes` (versatile, handles various data types), `Velvet`, `ABySS`, `SOAPdenovo`, `IDBA-UD` (metagenomics).

*   **Overlap-Layout-Consensus (OLC) Approach:** (Less common for *de novo* short-read assembly due to computational cost)
    *   *Principle:* Explicitly finds overlaps between reads, builds a graph representing overlaps, finds paths in the graph, and computes a consensus sequence.
    *   *Steps:* Overlap (find pairwise overlaps), Layout (build graph and find paths), Consensus (derive final sequence).
    *   *Challenge for short reads:* The O(N^2) overlap step is prohibitive for billions of reads.

### **3.5.3 Long-Read Assembly Algorithms (Primarily OLC/String Graphs)**

Algorithms designed for PacBio (CLR, HiFi) or ONT reads, whose length can span most repeats.

*   **OLC / String Graph Approach:** This paradigm is well-suited for fewer, longer reads.
    *   *Principle:* Finds overlaps between long reads, builds a graph, simplifies it (String Graph formulation removes transitive overlaps, where if A overlaps B and B overlaps C, the direct A-C overlap might be redundant), and finds paths to generate contigs.
    *   *Steps:*
        1.  **(Often Required for CLR/ONT) Error Correction:* Reduces high error rates using either self-correction (comparing overlapping long reads) or hybrid correction (using accurate short reads). PacBio HiFi reads often bypass this step due to their high intrinsic accuracy (>99.9%).
        2.  **Overlap Detection:** Find all significant pairwise overlaps between (corrected) long reads. Fast algorithms like `Minimap2` are crucial here.
        3.  **Layout/Graph Construction:** Build an overlap graph where nodes are reads and edges represent overlaps. Simplify the graph (e.g., transitive reduction, removing spurious edges caused by remaining errors or repeats).
        4.  **Contig Generation:** Traverse the graph to find paths representing contiguous genomic segments. Algorithms need heuristics to resolve complex structures caused by remaining errors or unresolved repeats/haplotypes.
        5.  **Consensus/Polishing:** Generate a high-accuracy consensus sequence for each contig by aligning the original reads back to the path and correcting errors. Often requires multiple rounds using tools like `Racon`, `Medaka`, `NextPolish`, `Arrow`/`pbmm2` (for PacBio).
    *   *Pros:* Long reads effectively bridge repeats, leading to much more contiguous assemblies (higher N50). Can resolve complex genomic structures. Enables haplotype phasing.
    *   *Cons:* Requires sufficient coverage of long reads; managing high error rates (for non-HiFi data) is critical; computational resources still significant.
    *   *Tools:* `Canu` (OLC, robust, resource-intensive), `Flye` (Repeat Graph, fast, good for ONT), `wtdbg2` (Fuzzy Bruijn Graph, very fast, low memory), `Falcon` (OLC, PacBio), `Shasta` (OLC-like, ONT, fast), `Hifiasm` (String Graph variant, excels with HiFi data, performs assembly and phasing simultaneously).

*   **Hybrid Assembly Approaches:** Combine short and long reads to leverage the strengths of both.
    *   *Long reads scaffold short-read contigs:* Assemble short reads first (DBG), then use long reads to order and orient the contigs and potentially fill some gaps.
    *   *Short reads polish long-read assembly:* Assemble long reads first (OLC/String Graph), then use short reads for accurate error correction (polishing) of the resulting contigs.
    *   *Integrated hybrid assemblers:* Tools designed to use both data types simultaneously during the assembly graph construction and resolution.
    *   *Tools:* `SPAdes` (hybrid mode), `Unicycler` (bacterial hybrid), `MaSuRCA`, `DBGDuel`.

### **3.5.4 Post-Assembly Improvement and Finishing**

The initial output of an assembler (contigs or scaffolds) often requires further refinement.

*   **Scaffolding:** Ordering and orienting contigs using long-range information.
    *   *Methods:* Utilize paired-end/mate-pair reads, linked reads (10x Genomics), long reads (PacBio/ONT), optical maps (Bionano Genomics), Hi-C data, or genetic maps. Hi-C is particularly powerful for achieving chromosome-level scaffolding.
    *   *Tools:* `SSPACE`, `BESST`, `ScaffMatch`, `SALSA2` (Hi-C), `3D-DNA` (Hi-C), `ALLHiC` (Hi-C).

*   **Gap Closing:** Filling the sequence gaps ('N's) within scaffolds.
    *   *Methods:* Use paired-end reads where one read maps on either side of the gap, or use long reads that span the gap. Local re-assembly or alignment can fill the missing sequence.
    *   *Tools:* `GapFiller`, `PBJelly`, `LR_Gapcloser`, `Sealer`.

*   **Polishing:** Correcting remaining base-level errors (SNPs, small Indels) in the consensus sequence.
    *   *Methods:* Align high-accuracy short reads (Illumina) or high-accuracy long reads (PacBio HiFi) back to the assembly and use the read evidence to fix discrepancies. Often performed iteratively.
    *   *Tools:* `Pilon`, `Racon`, `NextPolish`, `Polypolish`.

*   **Chromosome-Level Assembly:** The ultimate goal for many projects. Requires robust scaffolding information, typically from Hi-C, optical mapping, and/or genetic linkage maps, to group, order, and orient scaffolds into chromosome-sized pseudomolecules. Manual curation using visualization tools (e.g., Juicebox for Hi-C maps) is often required for the final steps. Telomere-to-Telomere (T2T) assemblies, representing complete chromosomes, are becoming achievable with high-quality long-read data.

### **3.5.5 Evaluating Assembly Quality**

Rigorous assessment is crucial throughout the assembly process.

*   **Contiguity:** N50/NG50, L50/LG50, max contig/scaffold length, number of contigs/scaffolds.
*   **Completeness:** Total assembly size vs. estimated genome size. **BUSCO scores** are essential for assessing gene content completeness. Check for expected elements like rRNA genes.
*   **Correctness:**
    *   Map original reads back: High mapping rate? Uniform coverage? Correct paired-end distances/orientations? Tools like `QUAST` automate many checks and comparisons (if a reference is available). `REAPR` specifically looks for signs of misassembly based on read mapping.
    *   Compare to optical maps or genetic maps if available.
    *   Assess synteny (gene order conservation) with related species' genomes.
    *   Check Hi-C contact maps for consistency with expected chromosomal interaction patterns.

**Section Summary:** *De novo* genome assembly reconstructs genome sequences from sequencing reads. Short-read assembly typically uses De Bruijn Graphs (e.g., SPAdes), while long-read assembly favors Overlap-Layout-Consensus or String Graph approaches (e.g., Canu, Flye, Hifiasm), significantly improving contiguity by spanning repeats. Hybrid strategies combine both read types. Achieving a high-quality assembly requires post-processing steps like scaffolding (ordering contigs, often using Hi-C for chromosome scale), gap closing, and polishing (error correction). Assembly quality is assessed using metrics for contiguity (N50), completeness (BUSCO), and correctness (read mapping validation, comparison to external data).

---

**Chapter Summary:**

This chapter provided a comprehensive overview of DNA sequencing data analysis, starting from the crucial considerations in experimental design (WGS, WES, Targeted Seq; sample QC; platform choice; depth; library prep) and extending through the core bioinformatics workflows. We detailed the principles and algorithms behind reference genome alignment (indexing, seed-and-extend, BWA, Bowtie2, Minimap2) and the importance of quality assessment (MAPQ, coverage). We explored the complexities of variant calling for SNPs, Indels (pileup vs. statistical/haplotype methods, GATK workflows, filtering), and challenging Structural Variations (using RD, PEM, SR, AS signals, impact of long reads). The critical step of variant annotation was discussed, covering positional, functional, population, and clinical annotations using key databases and tools (ANNOVAR, VEP), along with *in silico* functional prediction methods (SIFT, PolyPhen2, CADD, REVEL) for prioritizing variants. Finally, we examined the strategies and algorithms for *de novo* genome assembly (DBG for short reads, OLC/String Graph for long reads), post-assembly refinement (scaffolding, gap closing, polishing), and the metrics used to evaluate assembly quality (N50, BUSCO). Mastery of these concepts forms the bedrock for interpreting genome sequencing data and driving discovery in genomics.

---

**Key Terms:**

*   High-Throughput Sequencing (HTS) / Next-Generation Sequencing (NGS)
*   Whole Genome Sequencing (WGS)
*   Whole Exome Sequencing (WES)
*   Targeted Sequencing / Panel Sequencing
*   Resequencing
*   *De novo* Sequencing
*   Sequencing Depth / Coverage
*   Library Preparation (PCR-free, Paired-End)
*   DNA Quality Control (Purity, Integrity, DIN)
*   Reference Genome
*   Read Alignment / Mapping
*   Global Alignment (Needleman-Wunsch)
*   Local Alignment (Smith-Waterman)
*   Scoring Matrix / Gap Penalty (Affine)
*   Indexing (k-mer, BWT, FM-index)
*   Seed-and-Extend
*   BWA, Bowtie2, Minimap2
*   Mapping Quality (MAPQ)
*   SAM/BAM Format (FLAG, CIGAR)
*   Variant Calling
*   SNP (Transition, Transversion)
*   Indel (Frameshift, In-frame)
*   Structural Variation (SV) (DEL, DUP, INS, INV, TRA, CNV)
*   Base Quality Score Recalibration (BQSR)
*   Variant Quality Score Recalibration (VQSR)
*   GATK HaplotypeCaller
*   FreeBayes, SAMtools/BCFtools
*   Read Depth (RD), Paired-End Mapping (PEM), Split Read (SR) Methods (for SVs)
*   Variant Annotation (Positional, Functional Consequence, Population Frequency)
*   VCF Format (INFO field)
*   ANNOVAR, SnpEff, VEP
*   gnomAD, ClinVar, dbSNP, OMIM
*   Functional Prediction (*in silico*)
*   SIFT, PolyPhen-2, CADD, REVEL
*   Variant Prioritization
*   Genome Assembly
*   Contig, Scaffold
*   N50 / NG50, L50 / LG50
*   BUSCO
*   De Bruijn Graph (DBG)
*   Overlap-Layout-Consensus (OLC) / String Graph
*   Hybrid Assembly
*   Scaffolding, Gap Closing, Polishing
*   Hi-C Sequencing

---

**Discussion Questions / Exercises:**

1.  A researcher wants to identify the genetic cause of a rare, suspected Mendelian disorder in a family (proband and parents). Compare and contrast WGS and WES strategies for this purpose. What are the key advantages and disadvantages of each in this context? What sequencing depth would you recommend for the chosen strategy?
2.  Explain why the Burrows-Wheeler Transform (BWT) and FM-index are crucial for modern short-read alignment tools like BWA and Bowtie2. How do they overcome the computational limitations of traditional dynamic programming algorithms?
3.  Describe the concept of Mapping Quality (MAPQ). Why is a read mapping to a repetitive region of the genome likely to have a low MAPQ? How is MAPQ typically used in downstream variant calling?
4.  Compare and contrast the strategies for detecting small Indels (e.g., using GATK HaplotypeCaller) versus large Structural Variations (e.g., using Manta or Lumpy). Why are SVs generally harder to detect accurately from short-read data? How do long reads alleviate some of these challenges?
5.  A variant call yields a missense variant annotated as 'Possibly Damaging' by PolyPhen-2 and having a CADD score of 25. However, its allele frequency in gnomAD (non-cancer) is 2%. How would you interpret this combination of information, particularly in the context of searching for a rare disease variant versus a cancer driver mutation?
6.  Explain the N50 metric for genome assembly evaluation. Why is NG50 sometimes preferred over N50? If Assembler A yields an N50 of 1 Mb and Assembler B yields an N50 of 500 kb for the same dataset, what does this tell you? What other metrics (like BUSCO) are essential for a complete picture of assembly quality?
7.  Describe the primary challenge that repetitive sequences pose for *de novo* genome assembly using (a) short reads with a De Bruijn Graph approach, and (b) long reads with an OLC/String Graph approach. How do long reads help overcome this challenge?
8.  What is the purpose of Base Quality Score Recalibration (BQSR) and Variant Quality Score Recalibration (VQSR) in the GATK pipeline? Why are they considered important steps for generating high-quality variant calls?

---