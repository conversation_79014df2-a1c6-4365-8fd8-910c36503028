# 专题三：基因组测序(DNA-seq)数据分析 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握BWA和Bowtie2比对工具的使用
2. 学会SAMtools进行SAM/BAM文件处理
3. 掌握GATK进行变异检测的流程
4. 学会SnpEff进行变异注释

## 实验环境准备

### 软件环境配置
```bash
# 创建专题三专用环境
conda create -n dna_seq_analysis python=3.8 -y
conda activate dna_seq_analysis

# 安装核心软件
conda install -c bioconda bwa bowtie2 samtools bcftools gatk4 snpeff picard -y

# 验证安装
echo "=== 软件版本验证 ==="
bwa          # 显示帮助信息
bowtie2 --version
samtools --version
gatk --version
bcftools --version

echo "软件安装完成！"
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p dna_seq_practice/{data/{reference,raw,processed},results/{alignment,variants,annotation},scripts,logs}
cd dna_seq_practice

# 创建简化的参考基因组（用于教学演示）
cat > data/reference/chr22_mini.fa << 'EOF'
>chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

# 创建模拟双端测序数据
cat > data/raw/sample1_R1.fastq << 'EOF'
@SRR001_1/1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_2/1
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR001_3/1
TCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_4/1
CAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat > data/raw/sample1_R2.fastq << 'EOF'
@SRR001_1/2
TGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_2/2
ACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR001_3/2
TGCAGACATTCAATTGTTATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_4/2
TGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

# 创建变异位点文件（用于模拟变异检测）
cat > data/reference/known_sites.vcf << 'EOF'
##fileformat=VCFv4.2
##contig=<ID=chr22>
#CHROM	POS	ID	REF	ALT	QUAL	FILTER	INFO
chr22	100	rs001	A	G	60	PASS	.
chr22	200	rs002	T	C	50	PASS	.
chr22	300	rs003	G	A	70	PASS	.
EOF

echo "实验数据准备完成！"
```

---

## 第一部分：BWA/Bowtie2参考基因组比对实践

### 1. 参考基因组准备

*   **参考基因组下载**
    *   从NCBI、Ensembl等数据库下载参考基因组序列（FASTA格式）。
    *   例如，下载人类基因组：
        ```bash
        wget ftp://ftp.ncbi.nlm.nih.gov/genomes/Homo_sapiens/GRCh38_p13/seqs_for_alignment_pipelines.ucsc_ids/Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa.gz
        gunzip Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa.gz
        ```

*   **参考基因组索引构建**
    *   使用BWA或Bowtie2构建参考基因组索引，用于加速比对过程。
    *   BWA索引构建：
        ```bash
        bwa index Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa
        ```
    *   Bowtie2索引构建：
        ```bash
        bowtie2-build Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa Homo_sapiens.GRCh38_p13
        ```

*   **参考基因组注释文件准备**
    *   下载参考基因组的注释文件（GTF/GFF格式），用于后续的变异注释和功能分析。
    *   例如，从Ensembl下载人类基因组注释文件。

### 2. BWA比对实践

*   **BWA安装与配置**
    *   使用conda安装BWA：
        ```bash
        conda install -c bioconda bwa
        ```

*   **BWA索引构建命令**
    *   `bwa index <reference.fa>`
    *   例如：
        ```bash
        bwa index Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa
        ```

*   **BWA-MEM比对命令详解**
    *   `bwa mem [options] <reference.fa> <input.fq.gz>`
    *   常用参数：
        *   `-t <threads>`: 指定线程数
        *   `-M`: 标记PCR重复序列
        *   `-R <read_group>`: 指定读段组信息
    *   例如：
        ```bash
        bwa mem -t 8 -M -R "@RG\tID:sample1\tSM:sample1\tLB:lib1\tPL:illumina" Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa sample1.fastq.gz > sample1.sam
        ```

*   **比对参数优化**
    *   根据测序数据特点和研究目标，调整BWA算法的参数，以获得最佳的比对效果。
    *   例如，调整`-k`参数（最小种子长度）、`-w`参数（Z-dropoff参数）等。

*   **实际操作演示**
    1.  下载测序数据（FASTQ格式）。
    2.  构建参考基因组索引。
    3.  使用BWA-MEM进行比对。
    4.  将SAM文件转换为BAM文件。
    5.  对比对结果进行排序和索引。

### 3. Bowtie2比对实践

*   **Bowtie2安装与配置**
    *   使用conda安装Bowtie2：
        ```bash
        conda install -c bioconda bowtie2
        ```

*   **Bowtie2索引构建命令**
    *   `bowtie2-build <reference.fa> <index_base>`
    *   例如：
        ```bash
        bowtie2-build Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa Homo_sapiens.GRCh38_p13
        ```

*   **Bowtie2比对命令详解**
    *   `bowtie2 [options] -x <index_base> -U <input.fq.gz> -S <output.sam>`
    *   常用参数：
        *   `-p <threads>`: 指定线程数
        *   `--very-sensitive`: 提高比对灵敏度
        *   `--dovetail`: 允许末端到末端的比对
    *   例如：
        ```bash
        bowtie2 -p 8 --very-sensitive -x Homo_sapiens.GRCh38_p13 -U sample1.fastq.gz -S sample1.sam
        ```

*   **比对参数优化**
    *   根据测序数据特点和研究目标，调整Bowtie2算法的参数，以获得最佳的比对效果。
    *   例如，调整`--seed-length`参数（种子长度）、`--score-min`参数（最小比对得分）等。

*   **实际操作演示**
    1.  下载测序数据（FASTQ格式）。
    2.  构建参考基因组索引。
    3.  使用Bowtie2进行比对。
    4.  将SAM文件转换为BAM文件。
    5.  对比对结果进行排序和索引。

### 4. 比对结果评估

*   **比对统计信息获取**
    *   使用SAMtools的`flagstat`命令获取比对统计信息。
    *   例如：
        ```bash
        samtools flagstat sample1.bam
        ```
    *   常用的统计信息包括：
        *   总reads数
        *   比对上的reads数
        *   比对率
        *   唯一比对的reads数
        *   多重比对的reads数

*   **比对质量分布分析**
    *   使用SAMtools的`stats`命令获取比对质量分布信息。
    *   例如：
        ```bash
        samtools stats sample1.bam
        ```

*   **覆盖度分析**
    *   使用SAMtools的`depth`命令计算每个位置的测序深度。
    *   例如：
        ```bash
        samtools depth sample1.bam > sample1.depth
        ```

*   **常见问题排查**
    *   比对率低：
        *   可能原因：测序数据质量差、参考基因组污染、物种不匹配等。
        *   解决方案：提高测序数据质量、更换参考基因组、去除污染序列等。
    *   比对结果偏向特定区域：
        *   可能原因：GC含量偏好性、重复序列等。
        *   解决方案：使用PCR-free文库构建方法、使用长读长测序技术等。

**总结**

参考基因组比对是基因组测序数据分析的关键步骤。掌握BWA和Bowtie2的使用方法，并对比对结果进行评估，可以为后续的变异检测和功能分析奠定基础。 

## SAMtools进行SAM/BAM文件处理

### 1. SAM/BAM格式详解

*   **格式规范回顾**
    *   SAM (Sequence Alignment/Map) 是一种文本格式，用于存储比对后的测序数据。
    *   BAM (Binary Alignment/Map) 是SAM的二进制压缩格式，占用空间更小，读取速度更快。

*   **头部信息解读**
    *   SAM/BAM文件的头部信息以`@`开头，包含以下信息：
        *   `@HD`: 头部信息，包括版本号、排序方式等。
        *   `@SQ`: 参考基因组序列信息，包括序列名称、长度等。
        *   `@RG`: 读段组信息，包括样本ID、文库ID、测序平台等。
        *   `@PG`: 程序信息，包括比对工具名称、版本号等。

*   **比对记录字段含义**
    *   每一行代表一个比对结果，包含以下字段：
        *   `QNAME`: 序列名称
        *   `FLAG`: 比对标志，表示比对的方向、是否配对、是否唯一比对等信息。
        *   `RNAME`: 参考基因组序列名称
        *   `POS`: 比对起始位置（1-based）
        *   `MAPQ`: 比对质量分数
        *   `CIGAR`: 压缩比对信息，表示比对的匹配、插入、删除等情况。
        *   `RNEXT`: 下一个片段比对的参考基因组序列名称
        *   `PNEXT`: 下一个片段比对的起始位置
        *   `TLEN`: 模板长度
        *   `SEQ`: 序列
        *   `QUAL`: 质量分数

*   **FLAG值解读**
    *   FLAG值是一个二进制数，每一位代表不同的比对状态。
    *   可以使用SAMtools的`flag`命令解读FLAG值。
    *   例如：
        ```bash
        samtools flag 0x40
        ```
        *   输出结果表示该序列是read1。

### 2. SAMtools基本操作

*   **格式转换(view)**
    *   将SAM文件转换为BAM文件：
        ```bash
        samtools view -bS input.sam > output.bam
        ```
    *   将BAM文件转换为SAM文件：
        ```bash
        samtools view -h input.bam > output.sam
        ```

*   **排序(sort)**
    *   按照比对位置对BAM文件进行排序：
        ```bash
        samtools sort input.bam -o output.sorted.bam
        ```

*   **索引构建(index)**
    *   为BAM文件构建索引，用于快速访问：
        ```bash
        samtools index input.sorted.bam
        ```

*   **合并文件(merge)**
    *   将多个BAM文件合并成一个BAM文件：
        ```bash
        samtools merge output.merged.bam input1.bam input2.bam
        ```

*   **子集提取(view -b)**
    *   提取BAM文件中特定区域的比对结果：
        ```bash
        samtools view -b input.bam chr1:1000000-2000000 > output.subset.bam
        ```

### 3. SAMtools高级功能

*   **比对统计(flagstat, idxstats)**
    *   使用`flagstat`命令获取比对统计信息：
        ```bash
        samtools flagstat input.bam
        ```
    *   使用`idxstats`命令获取每个参考基因组序列的比对统计信息：
        ```bash
        samtools idxstats input.bam
        ```

*   **覆盖度计算(depth, bedcov)**
    *   使用`depth`命令计算每个位置的测序深度：
        ```bash
        samtools depth input.bam > output.depth
        ```
    *   使用`bedcov`命令计算每个BED区域的平均测序深度：
        ```bash
        samtools bedcov regions.bed input.bam > output.bedcov
        ```

*   **碱基质量统计(stats)**
    *   使用`stats`命令获取碱基质量统计信息：
        ```bash
        samtools stats input.bam > output.stats
        ```

*   **过滤操作(view -q, -F, -f)**
    *   使用`-q`参数过滤比对质量分数低于指定值的比对结果：
        ```bash
        samtools view -b -q 20 input.bam > output.filtered.bam
        ```
    *   使用`-F`参数过滤掉具有指定FLAG值的比对结果：
        ```bash
        samtools view -b -F 0x4 input.bam > output.filtered.bam
        ```
    *   使用`-f`参数只保留具有指定FLAG值的比对结果：
         ```bash
        samtools view -b -f 0x2 input.bam > output.filtered.bam
        ```

*   **PCR重复标记(markdup)**
    *   使用`markdup`命令标记PCR重复序列：
        ```bash
        samtools markdup input.bam output.marked.bam
        ```

### 4. BAM文件处理最佳实践

*   **处理流程设计**
    1.  比对
    2.  排序
    3.  标记重复序列
    4.  碱基质量重校正
    5.  变异检测

*   **文件命名规范**
    *   `sample_id.bam`: 比对后的BAM文件
    *   `sample_id.sorted.bam`: 排序后的BAM文件
    *   `sample_id.marked.bam`: 标记重复序列后的BAM文件
    *   `sample_id.recal.bam`: 碱基质量重校正后的BAM文件

*   **中间文件管理**
    *   删除不必要的中间文件，节省存储空间。

*   **并行处理策略**
    *   使用多线程并行处理BAM文件，提高处理速度。

*   **实际操作演示**
    1.  下载BAM文件。
    2.  使用SAMtools进行排序、索引、统计等操作。
    3.  使用SAMtools进行过滤操作。
    4.  使用SAMtools标记PCR重复序列。

**总结**

SAMtools是基因组测序数据分析中不可或缺的工具。掌握SAMtools的基本操作和高级功能，可以有效地管理和处理BAM文件，为后续的分析提供高质量的数据。 

## GATK/FreeBayes等工具进行变异检测

### 1. GATK变异检测流程

*   **GATK安装与配置**
    *   GATK (Genome Analysis Toolkit) 是一种常用的变异检测工具。
    *   GATK需要Java环境，请确保已安装Java。
    *   从GATK官网下载GATK安装包，并配置环境变量。
    *   或者使用conda安装：
        ```bash
        conda install -c bioconda gatk4
        ```

*   **数据预处理步骤**
    *   **标记重复序列**
        *   使用GATK的`MarkDuplicates`工具标记PCR重复序列：
            ```bash
            gatk MarkDuplicates -I input.bam -O output.marked.bam -M metrics.txt
            ```
    *   **碱基质量重校准(BQSR)**
        1.  使用`BaseRecalibrator`工具生成重校准模型：
            ```bash
            gatk BaseRecalibrator -I input.marked.bam -R reference.fa -known-sites dbsnp.vcf.gz -O recal_data.table
            ```
        2.  使用`ApplyBQSR`工具应用重校准模型：
            ```bash
            gatk ApplyBQSR -I input.marked.bam -R reference.fa -bqsr recal_data.table -O output.recal.bam
            ```

*   **HaplotypeCaller使用**
    *   使用`HaplotypeCaller`工具进行变异检测：
        ```bash
        gatk HaplotypeCaller -I input.recal.bam -R reference.fa -O output.vcf.gz
        ```

    *   **命令参数详解**
        *   `-I`: 输入BAM文件
        *   `-R`: 参考基因组文件
        *   `-O`: 输出VCF文件
        *   `-ERC GVCF`: 生成GVCF文件，用于后续的多样本联合分析

    *   **GVCF模式vs标准模式**
        *   标准模式：直接生成VCF文件，适用于单样本分析。
        *   GVCF模式：生成GVCF文件，适用于多样本联合分析。

    *   **多样本联合分析**
        1.  使用`GenomicsDBImport`工具将多个GVCF文件导入到GenomicsDB中：
            ```bash
            gatk GenomicsDBImport -v gvcf1.vcf.gz -v gvcf2.vcf.gz -v gvcf3.vcf.gz -genomicsdb-workspace-path my_database
            ```
        2.  使用`GenotypeGVCFs`工具对GenomicsDB中的变异进行基因型推断：
            ```bash
            gatk GenotypeGVCFs -R reference.fa -V gendb://my_database -O output.vcf.gz
            ```

*   **变异过滤策略**
    *   **硬过滤参数设置**
        *   根据GATK官方推荐的硬过滤参数，对变异进行过滤：
            ```bash
            gatk VariantFiltration -V input.vcf.gz -filter "QD < 2.0 || FS > 60.0 || MQ < 40.0 || MQRankSum < -12.5 || ReadPosRankSum < -8.0 || SOR > 3.0" -O output.filtered.vcf.gz
            ```

    *   **变异质量评分重校正(VQSR)**
        1.  使用`VariantRecalibrator`工具构建变异质量重校正模型：
            ```bash
            gatk VariantRecalibrator -R reference.fa -V input.vcf.gz -resource:known_sites,known=false,training=true,truth=true,prior=15.0 dbsnp.vcf.gz -an QD -an MQ -an MQRankSum -an ReadPosRankSum -an FS -an SOR -O recal.table
            ```
        2.  使用`ApplyVQSR`工具应用变异质量重校正模型：
            ```bash
            gatk ApplyVQSR -R reference.fa -V input.vcf.gz -ts_filter_level 99.0 -tranche 1000.0to99.0 -recalFile recal.table -O output.vqsr.vcf.gz
            ```

*   **实际操作演示**
    1.  下载BAM文件。
    2.  使用GATK进行数据预处理（标记重复序列、碱基质量重校正）。
    3.  使用GATK进行变异检测（HaplotypeCaller）。
    4.  使用GATK进行变异过滤（硬过滤或VQSR）。

### 2. FreeBayes变异检测

*   **FreeBayes安装与配置**
    *   使用conda安装FreeBayes：
        ```bash
        conda install -c bioconda freebayes
        ```

*   **基本命令格式**
    *   `freebayes -f <reference.fa> <input.bam> > <output.vcf>`
    *   例如：
        ```bash
        freebayes -f Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa sample1.bam > sample1.vcf
        ```

*   **关键参数设置**
    *   `-f`: 参考基因组文件
    *   `-r`: 指定变异检测区域
    *   `--min-mapping-quality`: 最小比对质量分数
    *   `--min-base-quality`: 最小碱基质量分数

*   **结果过滤策略**
    *   根据FreeBayes输出的质量分数和深度信息，对变异进行过滤。

*   **实际操作演示**
    1.  下载BAM文件。
    2.  使用FreeBayes进行变异检测。
    3.  对FreeBayes输出的VCF文件进行过滤。

### 3. 结构变异检测实践

*   **Delly/Lumpy工具使用**
    *   Delly和Lumpy是常用的结构变异检测工具。
    *   Delly：
        ```bash
        delly call -o output.bcf -g reference.fa input.bam
        ```
    *   Lumpy：需要结合其他工具（如samblaster）使用。

*   **命令参数设置**
    *   根据工具的说明文档，设置合适的参数。

*   **结果过滤与解读**
    *   根据工具输出的质量分数和支持reads数，对结构变异进行过滤。

*   **简单演示**
    1.  下载BAM文件。
    2.  使用Delly或Lumpy进行结构变异检测。
    3.  对检测结果进行过滤和解读。

### 4. 变异检测结果比较与合并

*   **bcftools比较功能**
    *   使用bcftools的`isec`命令比较多个VCF文件中的变异：
        ```bash
        bcftools isec -p prefix input1.vcf.gz input2.vcf.gz
        ```

*   **VCF文件合并方法**
    *   使用bcftools的`merge`命令合并多个VCF文件：
        ```bash
        bcftools merge input1.vcf.gz input2.vcf.gz -o output.vcf.gz
        ```

*   **变异集交集/并集操作**
    *   使用bcftools的`view`命令和`grep`命令，对变异集进行交集和并集操作。

*   **一致性评估**
    *   比较不同变异检测工具的结果，评估变异检测的一致性。

**总结**

变异检测是基因组测序数据分析的核心内容。掌握GATK、FreeBayes等工具的使用方法，并对检测结果进行比较和评估，可以获得准确可靠的变异信息。 

## ANNOVAR/SnpEff进行变异注释与解读

### 1. ANNOVAR变异注释

*   **ANNOVAR安装与配置**
    *   ANNOVAR (Annotate Variation) 是一种常用的变异注释工具，使用Perl语言编写。
    *   从ANNOVAR官网下载ANNOVAR安装包，并解压。
    *   下载ANNOVAR所需的注释数据库。
    *   配置ANNOVAR的环境变量。

*   **注释数据库下载**
    *   使用ANNOVAR自带的`annotate_variation.pl`脚本下载注释数据库：
        ```bash
        perl annotate_variation.pl -buildver hg38 -downdb refGene hg38
        perl annotate_variation.pl -buildver hg38 -downdb exac03 hg38
        perl annotate_variation.pl -buildver hg38 -downdb gnomad211_genome hg38
        ```
    *   常用的注释数据库包括：
        *   refGene：基因信息
        *   exac03：ExAC数据库，人群频率信息
        *   gnomad211_genome：gnomAD数据库，人群频率信息
        *   dbnsfp41a：功能预测信息

*   **基本注释命令**
    *   使用`annotate_variation.pl`脚本进行变异注释：
        ```bash
        perl annotate_variation.pl -geneanno -dbtype refGene input.vcf.gz hg38 humandb/
        ```

*   **注释结果解读**
    *   ANNOVAR输出多个文件，常用的包括：
        *   `input.vcf.gz.exonic_variant_function`: 包含外显子区域变异的功能注释信息。
        *   `input.vcf.gz.variant_function`: 包含所有变异的位置注释信息。

*   **实际操作演示**
    1.  下载VCF文件。
    2.  下载ANNOVAR所需的注释数据库。
    3.  使用ANNOVAR进行变异注释。
    4.  解读ANNOVAR的输出结果。

### 2. SnpEff变异注释

*   **SnpEff安装与配置**
    *   SnpEff (SNP Effect Predictor) 是一种快速的变异注释工具，使用Java语言编写。
    *   从SnpEff官网下载SnpEff安装包，并解压。
    *   下载SnpEff所需的数据库。
    *   配置SnpEff的环境变量。

*   **数据库构建与选择**
    *   下载SnpEff数据库：
        ```bash
        java -jar snpeff.jar download hg38
        ```
    *   常用的数据库包括：
        *   hg38：人类基因组hg38版本

*   **注释命令详解**
    *   使用`java -jar snpeff.jar`命令进行变异注释：
        ```bash
        java -jar snpeff.jar hg38 input.vcf.gz > output.ann.vcf
        ```

*   **结果文件解读**
    *   SnpEff输出一个VCF文件，其中包含变异的注释信息。
    *   注释信息位于VCF文件的INFO字段中，以`ANN=`开头。

*   **实际操作演示**
    1.  下载VCF文件。
    2.  下载SnpEff所需的数据库。
    3.  使用SnpEff进行变异注释。
    4.  解读SnpEff的输出结果。

### 3. 变异过滤与优先级排序

*   **基于注释信息的过滤策略**
    *   根据变异的注释信息，对变异进行过滤。
    *   常用的过滤策略包括：
        *   过滤掉位于基因间区的变异
        *   过滤掉同义突变
        *   过滤掉群体频率过高的变异

*   **候选变异筛选标准**
    *   根据研究目标，制定候选变异的筛选标准。
    *   常用的筛选标准包括：
        *   变异类型
        *   功能影响
        *   群体频率
        *   保守性

*   **自动化过滤脚本示例**
    *   可以使用Shell脚本、Python脚本等自动化过滤变异。
    *   例如，使用bcftools和awk命令过滤掉群体频率大于0.01的变异：
        ```bash
        bcftools view -i 'INFO/AF<0.01' input.vcf.gz -o output.filtered.vcf.gz
        ```

*   **实际操作演示**
    1.  下载VCF文件。
    2.  使用ANNOVAR或SnpEff对变异进行注释。
    3.  根据注释信息，使用自动化脚本过滤变异。
    4.  根据筛选标准，筛选出候选变异。

### 4. 变异结果统计与可视化

*   **变异类型分布统计**
    *   统计不同类型变异（SNP、Indel、SV）的数量和比例。

*   **变异功能影响统计**
    *   统计不同功能影响（同义突变、错义突变、无义突变等）的变异数量和比例。

*   **简单可视化方法**
    *   使用R语言或Python语言绘制变异类型分布图和变异功能影响图。

*   **结果导出与分享**
    *   将统计结果和可视化图表导出为文件，方便分享和交流。

**总结**

变异注释和解读是基因组测序数据分析的重要环节。掌握ANNOVAR和SnpEff的使用方法，并根据注释信息对变异进行过滤和优先级排序，可以帮助我们找到与研究目标相关的关键变异。 

## IGV等可视化工具使用

### 1. IGV工具介绍

*   **软件安装与配置**
    *   IGV (Integrative Genomics Viewer) 是一种常用的基因组可视化工具。
    *   从IGV官网下载IGV安装包，并解压。
    *   IGV需要Java环境，请确保已安装Java。

*   **界面布局与功能区**
    *   IGV界面主要包括以下几个区域：
        *   参考基因组导航区：用于选择和浏览参考基因组。
        *   数据轨道区：用于显示各种数据，如比对结果、变异信息、基因注释等。
        *   工具栏：包含常用的操作按钮，如放大、缩小、搜索等。
        *   状态栏：显示当前鼠标位置的基因组坐标和数据信息。

*   **数据加载方法**
    *   可以通过以下方法加载数据到IGV：
        *   File -> Load Data from File：加载本地文件（如BAM、VCF、GTF等）。
        *   File -> Load Data from URL：加载远程文件。
        *   File -> Load Data from Server：从IGV数据服务器加载数据。

*   **基本操作技巧**
    *   使用鼠标滚轮放大和缩小。
    *   使用鼠标拖动浏览基因组。
    *   使用搜索框搜索基因或区域。
    *   使用右键菜单进行各种操作，如调整轨道高度、显示/隐藏轨道等。

### 2. IGV数据可视化

*   **参考基因组导航**
    *   在参考基因组导航区选择要浏览的染色体和区域。
    *   可以使用搜索框快速定位到指定的基因或区域。
    *   支持输入基因名称、染色体坐标或特定变异位点（如chr7:55,191,822）。
    *   使用放大/缩小按钮或鼠标滚轮调整查看的基因组区域范围。

    ![IGV简化界面示意图](../专题三_基因组测序(DNA-seq)数据分析/diagrams/igv_visualization_simple.svg)

*   **比对数据可视化**
    *   加载BAM文件，可以查看比对结果。
    *   IGV会显示每个reads的比对位置、比对质量、CIGAR信息等。
    *   可以使用不同的颜色表示不同的比对状态：
        *   灰色：完全匹配的reads
        *   彩色：包含错配、插入或删除的reads
        *   紫色：配对reads之间的距离异常
        *   红色/蓝色：表示正向链/反向链比对
    *   右键点击比对轨道可以调整显示设置，如按插入大小着色、按配对方向着色等。
    *   双击任意read可以查看其详细信息，包括序列、质量分数和比对状态。

*   **变异数据可视化**
    *   加载VCF文件，可以查看变异信息。
    *   IGV会显示每个变异的位置、类型、基因型、注释信息等。
    *   变异类型颜色编码：
        *   紫色：SNP（单核苷酸多态性）
        *   红色：缺失（deletion）
        *   蓝色：插入（insertion）
        *   绿色：复杂变异（complex）
    *   点击变异位点可以查看详细的变异信息，包括变异质量、深度和基因型等。

    ![IGV变异注释与可视化](../专题三_基因组测序(DNA-seq)数据分析/diagrams/igv_variant_annotation.svg)

*   **注释轨道显示**
    *   加载GTF/GFF文件，可以查看基因注释信息。
    *   IGV会显示基因的位置、结构、功能等信息。
    *   基因结构表示：
        *   矩形：外显子
        *   线条：内含子
        *   箭头：转录方向
    *   可以将注释轨道与比对数据和变异数据结合起来，进行综合分析。
    *   右键点击注释轨道可以调整显示设置，如颜色、高度和标签显示等。

*   **多样本比较视图**
    *   同时加载多个样本的BAM文件和VCF文件，可以进行多样本比较分析。
    *   IGV可以显示不同样本在同一区域的比对结果和变异信息，方便进行比较和分析。
    *   使用"File -> New Panel"创建多个面板，可以同时查看不同基因组区域。
    *   使用"View -> Preferences"调整样本显示顺序和分组方式。
    *   特别适用于家系分析、肿瘤-正常样本比较和时间序列样本分析。

### 3. IGV高级功能

*   **截图与导出**
    *   使用"File -> Save Image"将当前视图保存为PNG、SVG或PDF格式。
    *   使用快捷键Ctrl+Alt+P（Windows/Linux）或Cmd+Alt+P（Mac）快速截图。
    *   自定义截图设置：
        *   调整分辨率和比例
        *   选择是否包含标尺和轨道名称
        *   设置透明背景选项
    *   使用"File -> Export"将数据导出为文本文件，支持导出区域内的变异、覆盖度和注释信息。
    *   批量截图示例：
        ```bash
        # 使用IGV批处理模式截取多个区域的图像
        igv -b batch_commands.txt
        
        # batch_commands.txt内容示例
        new
        load sample.bam
        snapshotDirectory ./screenshots
        goto chr7:55,191,822
        snapshot EGFR_mutation.png
        goto chr17:7,674,250
        snapshot TP53_mutation.png
        exit
        ```

*   **自定义轨道**
    *   使用"File -> Load from File"加载自定义数据文件（BED、WIG、BigWig等格式）。
    *   创建自定义注释轨道：
        *   基因表达量热图
        *   甲基化水平
        *   保守性得分
        *   调控元件信息
    *   使用"Tracks -> Fit Data to Window"自动调整轨道高度。
    *   右键点击轨道可以调整颜色、高度和渲染方式。
    *   使用"Tracks -> Group By"功能按样本类型或实验条件分组显示轨道。

*   **批量处理与自动化**
    *   使用IGV的命令行模式和批处理脚本自动化分析流程：
        ```bash
        # 启动IGV并执行批处理脚本
        igv -b commands.bat
        ```
    *   批处理命令示例：
        *   `load` - 加载数据文件
        *   `goto` - 导航到特定位置
        *   `snapshot` - 保存截图
        *   `maxPanelHeight` - 设置面板高度
        *   `setSleepInterval` - 设置命令间隔时间
    *   结合Shell脚本批量处理变异位点：
        ```bash
        # 为VCF文件中的每个变异生成IGV截图
        cat variants.vcf | grep -v "^#" | cut -f1,2 | while read chr pos; do
          echo "goto $chr:$pos" >> commands.bat
          echo "snapshot $chr-$pos.png" >> commands.bat
        done
        ```

*   **会话保存与恢复**
    *   使用"File -> Save Session"将当前的IGV会话保存为.xml或.session文件。
    *   会话文件包含以下信息：
        *   加载的数据文件及其路径
        *   当前查看的基因组区域
        *   轨道显示设置（颜色、高度、渲染方式等）
        *   面板布局和分组信息
    *   使用"File -> Open Session"恢复之前保存的会话。
    *   会话文件可以与团队成员共享，确保所有人查看相同的数据视图。
    *   使用相对路径保存会话文件，便于在不同计算机之间迁移。

### 4. 其他可视化工具简介

*   **UCSC Genome Browser**
    *   UCSC Genome Browser 是一种功能强大的在线基因组浏览器。
    *   特点与优势：
        *   提供丰富的预置注释轨道（基因、变异、保守性等）
        *   支持自定义轨道上传和共享
        *   内置多种分析工具（如Liftover、Table Browser）
        *   适合生成发表级别的基因组可视化图像
    *   使用场景：
        *   需要访问综合注释数据库时
        *   共享基因组可视化结果给其他研究者
        *   进行跨物种比较基因组学分析
    *   访问地址：https://genome.ucsc.edu/

*   **Tablet**
    *   Tablet 是一种专门用于可视化比对结果的轻量级工具。
    *   特点与优势：
        *   高效处理大规模比对数据
        *   内存占用低，适合普通计算机使用
        *   提供多种比对视图模式（碱基、覆盖度、质量等）
        *   支持从头组装结果的可视化
    *   使用场景：
        *   检查比对质量和覆盖度
        *   验证变异位点的可靠性
        *   分析从头组装结果

*   **JBrowse**
    *   JBrowse 是一种基于Web的现代基因组浏览器。
    *   特点与优势：
        *   快速响应的用户界面，支持大规模数据集
        *   模块化设计，支持插件扩展
        *   可以部署在本地服务器上，便于团队协作
        *   支持多种数据格式和自定义轨道
    *   使用场景：
        *   建立实验室或项目专用的基因组浏览平台
        *   需要共享大型基因组数据集
        *   开发自定义基因组可视化功能

*   **Genome Browser in the Cloud (GBiC)**
    *   基于云计算的基因组浏览器，适合处理大规模数据。
    *   特点与优势：
        *   无需下载大型数据文件
        *   利用云计算资源进行快速处理
        *   支持团队协作和数据共享
    *   使用场景：
        *   处理超大型基因组数据集
        *   多人协作的基因组分析项目

*   **工具选择建议**

    | 工具 | 适用场景 | 优势 | 局限性 |
    |------|---------|------|--------|
    | IGV | 本地详细分析，变异验证 | 功能全面，操作简便，支持多种数据格式 | 大数据集可能性能受限 |
    | UCSC Genome Browser | 整合公共数据，共享结果 | 注释数据丰富，可视化选项多样 | 需要网络连接，上传大文件不便 |
    | Tablet | 大规模比对数据检查 | 轻量级，性能优化，专注于比对数据 | 功能相对单一，注释有限 |
    | JBrowse | 团队协作，Web部署 | 响应快速，可扩展性强 | 需要一定的配置和维护 |
    | GBiC | 超大数据集分析 | 利用云资源，无需下载数据 | 可能产生云服务费用 |

*   **选择工具的考虑因素**
    *   数据规模：大型数据集考虑使用JBrowse或GBiC
    *   分析目的：变异验证选IGV，注释浏览选UCSC
    *   计算资源：资源有限选Tablet，资源充足选IGV
    *   协作需求：团队协作选JBrowse或UCSC
    *   数据隐私：敏感数据选择本地工具如IGV

![变异检测工作流程详细图](../专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_detection_workflow_detailed.svg)

**总结**

基因组可视化是基因组测序数据分析的重要手段。掌握IGV等可视化工具的使用方法，可以帮助我们更好地理解和分析基因组数据。选择合适的可视化工具对于提高分析效率和准确性至关重要，应根据具体的研究需求和数据特点进行选择。
