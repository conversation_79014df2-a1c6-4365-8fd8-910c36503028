# 专题六：单细胞测序技术与数据分析 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握Seurat和Scanpy单细胞数据分析框架
2. 学会单细胞数据质控、标准化和批次效应校正
3. 掌握细胞聚类、细胞类型注释和轨迹分析
4. 学会差异表达分析和功能富集分析

## 实验环境准备

### 软件环境配置
```bash
# 创建专题六专用环境
conda create -n single_cell_analysis python=3.8 r-base=4.1 -y
conda activate single_cell_analysis

# 安装Python包
conda install -c conda-forge scanpy pandas numpy scipy matplotlib seaborn -y
conda install -c conda-forge scikit-learn anndata -y

# 安装R包管理器
conda install -c conda-forge r-devtools r-biocmanager -y

# 启动R进行R包安装
echo "安装Seurat及相关R包..."
R --slave << 'EOF'
# 安装Bioconductor包管理器
if (!require("BiocManager", quietly = TRUE))
    install.packages("BiocManager")

# 安装Seurat和相关包
BiocManager::install(c("Seurat", "ggplot2", "patchwork", "dplyr", "tidyr"))
BiocManager::install(c("SingleR", "celldex", "scater", "scran"))

# 验证安装
library(Seurat)
library(ggplot2)
cat("R包安装完成！Seurat版本:", as.character(packageVersion("Seurat")), "\n")
EOF

# 验证Python环境
python3 -c "
import scanpy as sc
import pandas as pd
import numpy as np
print('Python包安装完成！Scanpy版本:', sc.__version__)
"

echo "软件环境配置完成！"
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p single_cell_practice/{data/{raw,processed},results/{qc,analysis,plots},scripts,logs}
cd single_cell_practice

# 创建模拟10x Genomics Cell Ranger输出格式的数据
echo "=== 创建模拟单细胞数据 ==="

# 1. 创建基因列表文件
cat > data/raw/features.tsv << 'EOF'
ENSG00000001	GENE1	Gene Expression
ENSG00000002	GENE2	Gene Expression
ENSG00000003	GENE3	Gene Expression
ENSG00000004	GENE4	Gene Expression
ENSG00000005	MT-GENE1	Gene Expression
ENSG00000006	GENE6	Gene Expression
ENSG00000007	GENE7	Gene Expression
ENSG00000008	GENE8	Gene Expression
ENSG00000009	RPS1	Gene Expression
ENSG00000010	RPS2	Gene Expression
EOF

# 2. 创建细胞条形码文件
cat > data/raw/barcodes.tsv << 'EOF'
AAACATACAACCAC-1
AAACATTGAGCTAC-1
AAACATTGATCAGC-1
AAACCGTGCTTCCG-1
AAACCGTGTATGCG-1
AAACGCACTGGTAC-1
AAACGCTGACCAGT-1
AAACGCTGGTTCTT-1
AAACGCTGTAGCCA-1
AAACGCTGTTTCTG-1
AAACTTGAAAAACG-1
AAACTTGATCCAGA-1
AAAGAGACGAGATA-1
AAAGAGACGCGAGA-1
AAAGAGACGGACTT-1
AAAGAGACGGGTAG-1
AAAGAGACGTGGAG-1
AAAGAGACGTTTGG-1
AAAGAGACTGAAAC-1
AAAGAGACTGTAGA-1
EOF

# 3. 创建稀疏表达矩阵
cat > data/raw/matrix.mtx << 'EOF'
%%MatrixMarket matrix coordinate integer general
%
10 20 40
1 1 5
1 2 3
2 1 8
2 3 4
3 2 12
3 4 7
4 1 15
4 5 9
5 6 2
5 7 25
6 8 6
6 9 11
7 10 3
7 11 8
8 12 14
8 13 5
9 14 7
9 15 19
10 16 4
10 17 6
1 18 3
2 19 5
3 20 8
4 1 2
5 2 7
6 3 12
7 4 5
8 5 9
9 6 6
10 7 11
1 8 4
2 9 7
3 10 13
4 11 6
5 12 8
6 13 10
7 14 5
8 15 7
9 16 9
EOF

# 4. 创建高质量细胞数据集（用于对比）
mkdir -p data/processed/high_quality
cp data/raw/* data/processed/high_quality/

echo "模拟数据创建完成！"
```

---

## 第一部分：Seurat/Scanpy数据处理基础

### 1.1 数据导入和初步探索

#### 1.1.1 Seurat数据导入实践

**实验目标：** 学会使用Seurat读取10x Genomics数据，创建Seurat对象

```bash
# 创建Seurat数据导入脚本
cat > scripts/seurat_data_import.R << 'EOF'
#!/usr/bin/env Rscript
# Seurat数据导入和初步分析脚本

library(Seurat)
library(ggplot2)
library(patchwork)
library(dplyr)

# 1. 读取10x Genomics数据
cat("=== 读取10x Genomics数据 ===\n")
data_dir <- "data/raw"

# 检查数据文件
cat("检查数据文件:\n")
cat("- features.tsv:", file.exists(file.path(data_dir, "features.tsv")), "\n")
cat("- barcodes.tsv:", file.exists(file.path(data_dir, "barcodes.tsv")), "\n") 
cat("- matrix.mtx:", file.exists(file.path(data_dir, "matrix.mtx")), "\n")

# 读取数据
expression_matrix <- Read10X(data.dir = data_dir)
cat("表达矩阵维度:", dim(expression_matrix), "\n")
cat("基因数量:", nrow(expression_matrix), "\n")
cat("细胞数量:", ncol(expression_matrix), "\n")

# 2. 创建Seurat对象
cat("\n=== 创建Seurat对象 ===\n")
seurat_obj <- CreateSeuratObject(
    counts = expression_matrix,
    project = "SingleCell_Practice",
    min.cells = 1,    # 至少在1个细胞中表达的基因
    min.features = 1  # 至少表达1个基因的细胞
)

# 显示Seurat对象信息
cat("Seurat对象信息:\n")
print(seurat_obj)

# 3. 初步数据探索
cat("\n=== 初步数据探索 ===\n")

# 计算线粒体基因比例
seurat_obj[["percent.mt"]] <- PercentageFeatureSet(seurat_obj, pattern = "^MT-")

# 计算核糖体基因比例
seurat_obj[["percent.ribo"]] <- PercentageFeatureSet(seurat_obj, pattern = "^RP[SL]")

# 显示细胞元数据
cat("细胞元数据概览:\n")
head(<EMAIL>)

# 4. 基本统计信息
cat("\n=== 基本统计信息 ===\n")
meta_data <- <EMAIL>

cat("每个细胞检测到的基因数统计:\n")
print(summary(meta_data$nFeature_RNA))

cat("\n每个细胞的UMI计数统计:\n")
print(summary(meta_data$nCount_RNA))

cat("\n线粒体基因比例统计:\n")
print(summary(meta_data$percent.mt))

# 5. 保存Seurat对象
saveRDS(seurat_obj, file = "results/analysis/seurat_initial.rds")
cat("\nSeurat对象已保存到: results/analysis/seurat_initial.rds\n")

# 6. 生成初步可视化
cat("\n=== 生成初步可视化 ===\n")

# 创建质量指标小提琴图
p1 <- VlnPlot(seurat_obj, 
              features = c("nFeature_RNA", "nCount_RNA", "percent.mt"), 
              ncol = 3,
              pt.size = 0.1)

# 保存图片
ggsave("results/plots/initial_qc_violin.png", plot = p1, width = 12, height = 4)
cat("质量控制小提琴图已保存到: results/plots/initial_qc_violin.png\n")

# 创建特征散点图
p2 <- FeatureScatter(seurat_obj, feature1 = "nCount_RNA", feature2 = "percent.mt") +
      ggtitle("UMI计数 vs 线粒体基因比例")

p3 <- FeatureScatter(seurat_obj, feature1 = "nCount_RNA", feature2 = "nFeature_RNA") +
      ggtitle("UMI计数 vs 检测基因数")

combined_scatter <- p2 + p3
ggsave("results/plots/initial_scatter.png", plot = combined_scatter, width = 12, height = 6)
cat("特征散点图已保存到: results/plots/initial_scatter.png\n")

cat("\n=== Seurat数据导入完成 ===\n")
EOF

# 运行Seurat数据导入
echo "=== 运行Seurat数据导入 ==="
Rscript scripts/seurat_data_import.R
```

#### 1.1.2 Scanpy数据导入实践

```bash
# 创建Scanpy数据导入脚本
cat > scripts/scanpy_data_import.py << 'EOF'
#!/usr/bin/env python3
"""
Scanpy数据导入和初步分析脚本
"""

import scanpy as sc
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置scanpy参数
sc.settings.verbosity = 3  # 详细输出
sc.settings.set_figure_params(dpi=80, facecolor='white')
sc.settings.figdir = 'results/plots/'

def main():
    print("=== Scanpy数据导入和分析 ===")
    
    # 1. 读取10x Genomics数据
    print("\n1. 读取10x Genomics数据")
    try:
        adata = sc.read_10x_mtx(
            'data/raw/',  # 数据目录
            var_names='gene_symbols',  # 使用基因符号作为变量名
            cache=True  # 缓存结果
        )
        
        # 使基因名唯一
        adata.var_names_make_unique()
        
        print(f"AnnData对象维度: {adata.shape}")
        print(f"基因数量: {adata.n_vars}")
        print(f"细胞数量: {adata.n_obs}")
        
    except Exception as e:
        print(f"读取数据时出错: {e}")
        return
    
    # 2. 数据结构探索
    print("\n2. 数据结构探索")
    print("AnnData对象信息:")
    print(adata)
    
    # 显示基因信息
    print("\n基因信息 (前5个):")
    print(adata.var.head())
    
    # 显示细胞信息
    print("\n细胞信息 (前5个):")
    print(adata.obs.head())
    
    # 3. 计算质量控制指标
    print("\n3. 计算质量控制指标")
    
    # 标记线粒体基因
    adata.var['mt'] = adata.var_names.str.startswith('MT-')
    
    # 标记核糖体基因
    adata.var['ribo'] = adata.var_names.str.startswith(('RPS', 'RPL'))
    
    # 计算QC指标
    sc.pp.calculate_qc_metrics(
        adata, 
        percent_top=None, 
        log1p=False, 
        inplace=True,
        qc_vars=['mt', 'ribo']
    )
    
    # 显示计算的QC指标
    print("计算的QC指标:")
    print(adata.obs.columns.tolist())
    
    # 4. 基本统计信息
    print("\n4. 基本统计信息")
    print("每个细胞检测到的基因数统计:")
    print(adata.obs['n_genes_by_counts'].describe())
    
    print("\n每个细胞的总UMI计数统计:")
    print(adata.obs['total_counts'].describe())
    
    print("\n线粒体基因比例统计:")
    print(adata.obs['pct_counts_mt'].describe())
    
    # 5. 生成初步可视化
    print("\n5. 生成初步可视化")
    
    # 小提琴图
    fig, axes = plt.subplots(1, 3, figsize=(12, 4))
    
    # 基因数量
    sc.pl.violin(adata, 'n_genes_by_counts', 
                jitter=0.4, ax=axes[0], show=False)
    axes[0].set_title('检测基因数')
    
    # UMI计数
    sc.pl.violin(adata, 'total_counts', 
                jitter=0.4, ax=axes[1], show=False)
    axes[1].set_title('总UMI计数')
    
    # 线粒体基因比例
    sc.pl.violin(adata, 'pct_counts_mt', 
                jitter=0.4, ax=axes[2], show=False)
    axes[2].set_title('线粒体基因比例(%)')
    
    plt.tight_layout()
    plt.savefig('results/plots/scanpy_initial_qc_violin.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("质量控制小提琴图已保存到: results/plots/scanpy_initial_qc_violin.png")
    
    # 散点图
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # UMI vs 基因数
    sc.pl.scatter(adata, x='total_counts', y='n_genes_by_counts', 
                 ax=axes[0], show=False)
    axes[0].set_title('总UMI计数 vs 检测基因数')
    
    # UMI vs 线粒体比例
    sc.pl.scatter(adata, x='total_counts', y='pct_counts_mt', 
                 ax=axes[1], show=False)
    axes[1].set_title('总UMI计数 vs 线粒体基因比例')
    
    plt.tight_layout()
    plt.savefig('results/plots/scanpy_initial_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("特征散点图已保存到: results/plots/scanpy_initial_scatter.png")
    
    # 6. 保存AnnData对象
    adata.write('results/analysis/scanpy_initial.h5ad')
    print("\nAnnData对象已保存到: results/analysis/scanpy_initial.h5ad")
    
    # 7. 生成数据概览报告
    print("\n=== 数据概览报告 ===")
    print(f"数据集包含 {adata.n_obs} 个细胞和 {adata.n_vars} 个基因")
    
    # 质量阈值建议
    print(f"\n质量阈值建议:")
    gene_threshold = adata.obs['n_genes_by_counts'].quantile(0.05)
    umi_threshold = adata.obs['total_counts'].quantile(0.05)
    mt_threshold = adata.obs['pct_counts_mt'].quantile(0.95)
    
    print(f"最少基因数阈值 (5%分位数): {gene_threshold:.0f}")
    print(f"最少UMI数阈值 (5%分位数): {umi_threshold:.0f}")
    print(f"最大线粒体比例阈值 (95%分位数): {mt_threshold:.1f}%")
    
    print("\n=== Scanpy数据导入完成 ===")

if __name__ == "__main__":
    main()
EOF

# 运行Scanpy数据导入
echo "=== 运行Scanpy数据导入 ==="
python3 scripts/scanpy_data_import.py
```

### 1.2 数据质量控制详细实践

#### 1.2.1 质量指标深度分析

```bash
# 创建质量控制分析脚本
cat > scripts/quality_control_analysis.R << 'EOF'
#!/usr/bin/env Rscript
# 单细胞数据质量控制详细分析

library(Seurat)
library(ggplot2)
library(dplyr)
library(gridExtra)

# 读取之前保存的Seurat对象
seurat_obj <- readRDS("results/analysis/seurat_initial.rds")

cat("=== 单细胞数据质量控制详细分析 ===\n")

# 1. 详细质量指标计算
cat("\n1. 计算额外质量指标\n")

# 计算基因类型比例
seurat_obj[["percent.ribo"]] <- PercentageFeatureSet(seurat_obj, pattern = "^RP[SL]")

# 计算细胞复杂度（log10(基因数)/log10(UMI数)）
seurat_obj$complexity <- log10(seurat_obj$nFeature_RNA) / log10(seurat_obj$nCount_RNA)

# 显示所有质量指标
cat("质量指标概览:\n")
print(head(<EMAIL>))

# 2. 质量阈值确定
cat("\n2. 质量阈值分析\n")

meta_data <- <EMAIL>

# 基因数量阈值
gene_lower <- quantile(meta_data$nFeature_RNA, 0.05)
gene_upper <- quantile(meta_data$nFeature_RNA, 0.95)

# UMI计数阈值
umi_lower <- quantile(meta_data$nCount_RNA, 0.05)
umi_upper <- quantile(meta_data$nCount_RNA, 0.95)

# 线粒体基因比例阈值
mt_upper <- quantile(meta_data$percent.mt, 0.95)

cat("建议的质量阈值:\n")
cat("基因数量:", gene_lower, "-", gene_upper, "\n")
cat("UMI计数:", umi_lower, "-", umi_upper, "\n")
cat("线粒体基因比例: <", mt_upper, "%\n")

# 3. 质量分布可视化
cat("\n3. 生成详细质量分布图\n")

# 创建多面板质量图
p1 <- ggplot(meta_data, aes(x = nFeature_RNA)) +
      geom_histogram(bins = 50, fill = "skyblue", alpha = 0.7) +
      geom_vline(xintercept = c(gene_lower, gene_upper), color = "red", linetype = "dashed") +
      labs(title = "基因数量分布", x = "检测基因数", y = "细胞数量") +
      theme_minimal()

p2 <- ggplot(meta_data, aes(x = nCount_RNA)) +
      geom_histogram(bins = 50, fill = "lightgreen", alpha = 0.7) +
      geom_vline(xintercept = c(umi_lower, umi_upper), color = "red", linetype = "dashed") +
      labs(title = "UMI计数分布", x = "总UMI计数", y = "细胞数量") +
      theme_minimal()

p3 <- ggplot(meta_data, aes(x = percent.mt)) +
      geom_histogram(bins = 50, fill = "orange", alpha = 0.7) +
      geom_vline(xintercept = mt_upper, color = "red", linetype = "dashed") +
      labs(title = "线粒体基因比例分布", x = "线粒体基因比例(%)", y = "细胞数量") +
      theme_minimal()

p4 <- ggplot(meta_data, aes(x = complexity)) +
      geom_histogram(bins = 50, fill = "purple", alpha = 0.7) +
      labs(title = "细胞复杂度分布", x = "复杂度", y = "细胞数量") +
      theme_minimal()

combined_plot <- grid.arrange(p1, p2, p3, p4, ncol = 2)
ggsave("results/plots/detailed_qc_distributions.png", plot = combined_plot, 
       width = 12, height = 10)
cat("详细质量分布图已保存到: results/plots/detailed_qc_distributions.png\n")

# 4. 质量筛选效果预览
cat("\n4. 质量筛选效果预览\n")

# 应用质量筛选条件
cells_before <- ncol(seurat_obj)
filtered_cells <- meta_data %>%
  filter(nFeature_RNA >= gene_lower & nFeature_RNA <= gene_upper &
         nCount_RNA >= umi_lower & nCount_RNA <= umi_upper &
         percent.mt <= mt_upper)

cells_after <- nrow(filtered_cells)
retention_rate <- (cells_after / cells_before) * 100

cat("筛选前细胞数:", cells_before, "\n")
cat("筛选后细胞数:", cells_after, "\n")
cat("细胞保留率:", round(retention_rate, 1), "%\n")

# 5. 基因表达统计
cat("\n5. 基因表达统计\n")

# 计算每个基因在多少细胞中表达
gene_expression_stats <- data.frame(
  gene = rownames(seurat_obj),
  cells_expressed = rowSums(GetAssayData(seurat_obj, slot = "counts") > 0),
  total_expression = rowSums(GetAssayData(seurat_obj, slot = "counts"))
)

gene_expression_stats$expression_frequency <- 
  gene_expression_stats$cells_expressed / ncol(seurat_obj) * 100

cat("基因表达频率统计:\n")
print(summary(gene_expression_stats$expression_frequency))

# 显示高表达基因
cat("\n表达频率最高的基因 (前10个):\n")
top_genes <- gene_expression_stats %>% 
  arrange(desc(expression_frequency)) %>% 
  head(10)
print(top_genes)

# 6. 保存质量控制报告
cat("\n6. 保存质量控制分析结果\n")

# 保存质量统计
write.csv(meta_data, "results/analysis/cell_quality_metrics.csv", row.names = TRUE)
write.csv(gene_expression_stats, "results/analysis/gene_expression_stats.csv", row.names = FALSE)

cat("质量控制分析完成！\n")
cat("- 细胞质量指标: results/analysis/cell_quality_metrics.csv\n")
cat("- 基因表达统计: results/analysis/gene_expression_stats.csv\n")
EOF

# 运行质量控制分析
echo "=== 运行质量控制分析 ==="
Rscript scripts/quality_control_analysis.R
```
