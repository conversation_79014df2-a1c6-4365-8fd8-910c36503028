<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">Seurat分析流程</text>
  
  <!-- R Code Box -->
  <rect x="50" y="80" width="700" height="480" fill="#ffffff" rx="10" ry="10" stroke="#6c757d" stroke-width="2"/>
  
  <!-- Code Header -->
  <rect x="50" y="80" width="700" height="30" fill="#e9ecef" rx="10" ry="10" stroke="#6c757d" stroke-width="2"/>
  <text x="400" y="100" font-family="Courier New" font-size="16" text-anchor="middle" font-weight="bold">R代码示例</text>
  
  <!-- Code Content -->
  <text x="70" y="140" font-family="Courier New" font-size="14" text-anchor="start"># 加载Seurat包</text>
  <text x="70" y="160" font-family="Courier New" font-size="14" text-anchor="start">library(Seurat)</text>
  <text x="70" y="180" font-family="Courier New" font-size="14" text-anchor="start">library(dplyr)</text>
  <text x="70" y="200" font-family="Courier New" font-size="14" text-anchor="start">library(ggplot2)</text>
  
  <text x="70" y="230" font-family="Courier New" font-size="14" text-anchor="start"># 读取数据</text>
  <text x="70" y="250" font-family="Courier New" font-size="14" text-anchor="start">counts <- Read10X("filtered_feature_bc_matrix/")</text>
  <text x="70" y="270" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- CreateSeuratObject(counts = counts, min.cells = 3, min.features = 200)</text>
  
  <text x="70" y="300" font-family="Courier New" font-size="14" text-anchor="start"># 质量控制</text>
  <text x="70" y="320" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj[["percent.mt"]] <- PercentageFeatureSet(seurat_obj, pattern = "^MT-")</text>
  <text x="70" y="340" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- subset(seurat_obj, subset = nFeature_RNA > 200 & nFeature_RNA < 2500 & </text>
  <text x="70" y="360" font-family="Courier New" font-size="14" text-anchor="start">                   percent.mt < 5)</text>
  
  <text x="70" y="390" font-family="Courier New" font-size="14" text-anchor="start"># 标准化与特征选择</text>
  <text x="70" y="410" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- NormalizeData(seurat_obj)</text>
  <text x="70" y="430" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- FindVariableFeatures(seurat_obj, selection.method = "vst", nfeatures = 2000)</text>
  
  <text x="70" y="460" font-family="Courier New" font-size="14" text-anchor="start"># 降维与聚类</text>
  <text x="70" y="480" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- ScaleData(seurat_obj)</text>
  <text x="70" y="500" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- RunPCA(seurat_obj, features = VariableFeatures(object = seurat_obj))</text>
  <text x="70" y="520" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- FindNeighbors(seurat_obj, dims = 1:15)</text>
  <text x="70" y="540" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- FindClusters(seurat_obj, resolution = 0.5)</text>
  <text x="70" y="560" font-family="Courier New" font-size="14" text-anchor="start">seurat_obj <- RunUMAP(seurat_obj, dims = 1:15)</text>
</svg>
