<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">细胞轨迹分析</text>
  <!-- 比较区域 -->
  <rect x="50" y="80" width="700" height="150" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="110" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle">拟时序分析 vs 真实时间序列</text>
  <!-- Pseudotime部分 -->
  <rect x="80" y="130" width="300" height="80" fill="#ffffff" rx="5" ry="5" stroke="#6c757d"/>
  <text x="230" y="155" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle">拟时序分析</text>
  <path d="M100,170 L340,170" stroke="#6c757d" stroke-width="2" fill="none"/>
  <circle cx="100" cy="170" r="10" fill="#0d6efd"/>
  <circle cx="140" cy="170" r="10" fill="#0d6efd" opacity="0.8"/>
  <circle cx="180" cy="170" r="10" fill="#0d6efd" opacity="0.6"/>
  <circle cx="220" cy="170" r="10" fill="#dc3545" opacity="0.4"/>
  <circle cx="260" cy="170" r="10" fill="#dc3545" opacity="0.6"/>
  <circle cx="300" cy="170" r="10" fill="#dc3545" opacity="0.8"/>
  <circle cx="340" cy="170" r="10" fill="#dc3545"/>
  <text x="230" y="205" font-family="Arial" font-size="14" text-anchor="middle">基于基因表达推断发育顺序</text>
  <!-- 真实时间部分 -->
  <rect x="420" y="130" width="300" height="80" fill="#ffffff" rx="5" ry="5" stroke="#6c757d"/>
  <text x="570" y="155" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle">真实时间序列</text>
  <rect x="440" y="150" width="60" height="40" fill="#0d6efd" rx="5" ry="5"/>
  <text x="470" y="175" fill="white" font-family="Arial" font-size="14" dominant-baseline="middle" text-anchor="middle">Day 0</text>
  <rect x="520" y="150" width="60" height="40" fill="#0d6efd" opacity="0.6" rx="5" ry="5"/>
  <text x="550" y="175" fill="white" font-family="Arial" font-size="14" dominant-baseline="middle" text-anchor="middle">Day 3</text>
  <rect x="600" y="150" width="60" height="40" fill="#dc3545" opacity="0.6" rx="5" ry="5"/>
  <text x="630" y="175" fill="white" font-family="Arial" font-size="14" dominant-baseline="middle" text-anchor="middle">Day 7</text>
  <rect x="680" y="150" width="60" height="40" fill="#dc3545" rx="5" ry="5"/>
  <text x="710" y="175" fill="white" font-family="Arial" font-size="14" dominant-baseline="middle" text-anchor="middle">Day 14</text>
  <text x="570" y="205" font-family="Arial" font-size="14" text-anchor="middle">不同时间点采样测序</text>
  <!-- 算法区域 -->
  <rect x="50" y="250" width="340" height="300" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="220" y="280" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle">轨迹分析算法</text>
  <!-- Monocle -->
  <rect x="80" y="300" width="280" height="70" fill="#fff" rx="5" ry="5" stroke="#6c757d"/>
  <text x="100" y="325" font-family="Arial" font-size="16" font-weight="bold">Monocle</text>
  <text x="100" y="350" font-family="Arial" font-size="14">• 使用DDRTree/UMAP+PQ树</text>
  <text x="100" y="370" font-family="Arial" font-size="14">• 可处理复杂分支结构</text>
  <!-- RNA Velocity -->
  <rect x="80" y="380" width="280" height="70" fill="#fff" rx="5" ry="5" stroke="#6c757d"/>
  <text x="100" y="405" font-family="Arial" font-size="16" font-weight="bold">RNA Velocity</text>
  <text x="100" y="430" font-family="Arial" font-size="14">• 使用剪接前RNA和成熟RNA</text>
  <text x="100" y="450" font-family="Arial" font-size="14">• 预测细胞运动方向</text>
  <!-- Slingshot PAGA -->
  <rect x="80" y="460" width="280" height="70" fill="#fff" rx="5" ry="5" stroke="#6c757d"/>
  <text x="100" y="485" font-family="Arial" font-size="16" font-weight="bold">Slingshot/PAGA</text>
  <text x="100" y="510" font-family="Arial" font-size="14">• Slingshot: 最小生成树</text>
  <text x="100" y="530" font-family="Arial" font-size="14">• PAGA: 分区图抽象</text>
  <!-- 可视化区域 -->
  <rect x="410" y="250" width="340" height="300" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="580" y="280" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle">轨迹可视化</text>
  <!-- 轨迹图框 -->
  <rect x="440" y="300" width="280" height="200" fill="#ffffff" rx="5" ry="5" stroke="#6c757d"/>
  <!-- 轨迹图内容 -->
  <circle cx="480" cy="400" r="10" fill="#0d6efd"/>
  <text x="480" y="380" font-family="Arial" font-size="12" text-anchor="middle">起点</text>
  <circle cx="560" cy="400" r="10" fill="#ffc107"/>
  <text x="560" y="380" font-family="Arial" font-size="12" text-anchor="middle">分支点</text>
  <circle cx="640" cy="350" r="10" fill="#dc3545"/>
  <text x="640" y="330" font-family="Arial" font-size="12" text-anchor="middle">终点1</text>
  <circle cx="640" cy="450" r="10" fill="#198754"/>
  <text x="640" y="470" font-family="Arial" font-size="12" text-anchor="middle">终点2</text>
  <path d="M480 400 L560 400 L640 350" stroke="#6c757d" stroke-width="2" fill="none"/>
  <path d="M560 400 L640 450" stroke="#6c757d" stroke-width="2" fill="none"/>
  <!-- 轨迹上的细胞 -->
  <circle cx="500" cy="400" r="3" fill="#0d6efd" opacity="0.8"/>
  <circle cx="520" cy="400" r="3" fill="#0d6efd" opacity="0.6"/>
  <circle cx="540" cy="400" r="3" fill="#0d6efd" opacity="0.4"/>
  <circle cx="580" cy="390" r="3" fill="#dc3545" opacity="0.2"/>
  <circle cx="600" cy="380" r="3" fill="#dc3545" opacity="0.4"/>
  <circle cx="620" cy="365" r="3" fill="#dc3545" opacity="0.6"/>
  <circle cx="580" cy="410" r="3" fill="#198754" opacity="0.2"/>
  <circle cx="600" cy="420" r="3" fill="#198754" opacity="0.4"/>
  <circle cx="620" cy="435" r="3" fill="#198754" opacity="0.6"/>
  <!-- 伪时间渐变条 -->
  <rect x="440" y="510" width="280" height="20" fill="url(#ptgradient)" rx="5" ry="5"/>
  <text x="440" y="545" font-family="Arial" font-size="12" text-anchor="start">早期</text>
  <text x="720" y="545" font-family="Arial" font-size="12" text-anchor="end">晚期</text>
  <text x="580" y="545" font-family="Arial" font-size="12" text-anchor="middle">伪时间</text>
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="ptgradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#0d6efd"/>
      <stop offset="50%" stop-color="#ffc107"/>
      <stop offset="100%" stop-color="#dc3545"/>
    </linearGradient>
  </defs>
</svg>
