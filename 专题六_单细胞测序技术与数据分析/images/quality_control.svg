<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">单细胞数据质控</text>
  
  <!-- Cell-level QC -->
  <rect x="50" y="80" width="340" height="220" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="220" y="110" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">细胞水平质控</text>
  
  <!-- Gene/UMI count -->
  <rect x="80" y="130" width="280" height="60" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="100" y="155" font-family="Arial" font-size="14" text-anchor="start">检测到的基因/UMI数量</text>
  
  <!-- Violin plot for gene count -->
  <path d="M 240 140 C 260 130, 260 150, 240 160 C 220 150, 220 130, 240 140" 
        stroke="#0d6efd" stroke-width="1" fill="#cfe2ff"/>
  <line x1="230" y1="150" x2="250" y2="150" stroke="#0d6efd" stroke-width="2"/>
  
  <!-- Violin plot for UMI count -->
  <path d="M 290 140 C 310 130, 310 150, 290 160 C 270 150, 270 130, 290 140" 
        stroke="#dc3545" stroke-width="1" fill="#f8d7da"/>
  <line x1="280" y1="150" x2="300" y2="150" stroke="#dc3545" stroke-width="2"/>
  
  <!-- Mitochondrial gene percentage -->
  <rect x="80" y="200" width="280" height="60" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="100" y="225" font-family="Arial" font-size="14" text-anchor="start">线粒体基因比例</text>
  
  <!-- Scatter plot for mitochondrial gene percentage -->
  <circle cx="240" cy="220" r="3" fill="#0d6efd"/>
  <circle cx="250" cy="230" r="3" fill="#0d6efd"/>
  <circle cx="260" cy="225" r="3" fill="#0d6efd"/>
  <circle cx="270" cy="215" r="3" fill="#0d6efd"/>
  <circle cx="280" cy="240" r="3" fill="#dc3545"/>
  <circle cx="290" cy="245" r="3" fill="#dc3545"/>
  <line x1="270" y1="200" x2="270" y2="260" stroke="#dc3545" stroke-width="1" stroke-dasharray="5,5"/>
  <text x="270" y="270" font-family="Arial" font-size="10" text-anchor="middle">阈值</text>
  
  <text x="80" y="280" font-family="Arial" font-size="14" text-anchor="start">• 去除低质量细胞</text>
  <text x="80" y="300" font-family="Arial" font-size="14" text-anchor="start">• 去除双细胞/多细胞</text>
  
  <!-- Gene-level QC -->
  <rect x="410" y="80" width="340" height="220" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="580" y="110" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">基因水平质控</text>
  
  <!-- Mean expression vs variance -->
  <rect x="440" y="130" width="280" height="130" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="460" y="150" font-family="Arial" font-size="14" text-anchor="start">平均表达量 vs 变异系数</text>
  
  <!-- Scatter plot for mean expression vs variance -->
  <line x1="460" y1="230" x2="700" y2="230" stroke="#6c757d" stroke-width="1"/>
  <text x="580" y="250" font-family="Arial" font-size="10" text-anchor="middle">平均表达量</text>
  
  <line x1="460" y1="230" x2="460" y2="160" stroke="#6c757d" stroke-width="1"/>
  <text x="450" y="195" font-family="Arial" font-size="10" text-anchor="middle" transform="rotate(-90, 450, 195)">变异系数</text>
  
  <circle cx="480" cy="220" r="3" fill="#6c757d"/>
  <circle cx="500" cy="210" r="3" fill="#6c757d"/>
  <circle cx="520" cy="200" r="3" fill="#6c757d"/>
  <circle cx="540" cy="190" r="3" fill="#6c757d"/>
  <circle cx="560" cy="180" r="3" fill="#0d6efd"/>
  <circle cx="580" cy="170" r="3" fill="#0d6efd"/>
  <circle cx="600" cy="180" r="3" fill="#0d6efd"/>
  <circle cx="620" cy="190" r="3" fill="#6c757d"/>
  <circle cx="640" cy="200" r="3" fill="#6c757d"/>
  <circle cx="660" cy="210" r="3" fill="#6c757d"/>
  <circle cx="680" cy="220" r="3" fill="#6c757d"/>
  
  <path d="M 460 190 C 520 190, 580 170, 640 190 C 680 200, 700 220, 700 230" 
        stroke="#dc3545" stroke-width="1" stroke-dasharray="5,5" fill="none"/>
  
  <text x="440" y="280" font-family="Arial" font-size="14" text-anchor="start">• 去除低表达基因</text>
  <text x="440" y="300" font-family="Arial" font-size="14" text-anchor="start">• 选择高变异基因</text>
  
  <!-- Normalization -->
  <rect x="50" y="320" width="340" height="220" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="220" y="350" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">数据标准化</text>
  
  <!-- Library size normalization -->
  <rect x="80" y="370" width="280" height="40" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="100" y="395" font-family="Arial" font-size="14" text-anchor="start">文库大小标准化</text>
  <text x="300" y="395" font-family="Arial" font-size="14" text-anchor="end">CPM/RPM</text>
  
  <!-- Log normalization -->
  <rect x="80" y="420" width="280" height="40" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="100" y="445" font-family="Arial" font-size="14" text-anchor="start">Log标准化</text>
  <text x="300" y="445" font-family="Arial" font-size="14" text-anchor="end">log2(CPM+1)</text>
  
  <!-- SCTransform -->
  <rect x="80" y="470" width="280" height="40" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="100" y="495" font-family="Arial" font-size="14" text-anchor="start">SCTransform</text>
  <text x="300" y="495" font-family="Arial" font-size="14" text-anchor="end">负二项回归</text>
  
  <text x="80" y="530" font-family="Arial" font-size="14" text-anchor="start">• 校正技术噪声</text>
  
  <!-- Batch Effect Correction -->
  <rect x="410" y="320" width="340" height="220" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="580" y="350" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">批次效应校正</text>
  
  <!-- Before correction -->
  <rect x="440" y="370" width="120" height="120" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="500" y="370" font-family="Arial" font-size="12" text-anchor="middle" transform="translate(0, -10)">校正前</text>
  
  <circle cx="470" cy="400" r="5" fill="#0d6efd"/>
  <circle cx="480" cy="410" r="5" fill="#0d6efd"/>
  <circle cx="490" cy="420" r="5" fill="#0d6efd"/>
  <circle cx="500" cy="430" r="5" fill="#0d6efd"/>
  <circle cx="510" cy="440" r="5" fill="#0d6efd"/>
  
  <circle cx="470" cy="450" r="5" fill="#dc3545"/>
  <circle cx="480" cy="460" r="5" fill="#dc3545"/>
  <circle cx="490" cy="470" r="5" fill="#dc3545"/>
  <circle cx="500" cy="480" r="5" fill="#dc3545"/>
  <circle cx="510" cy="490" r="5" fill="#dc3545"/>
  
  <!-- After correction -->
  <rect x="600" y="370" width="120" height="120" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="660" y="370" font-family="Arial" font-size="12" text-anchor="middle" transform="translate(0, -10)">校正后</text>
  
  <circle cx="630" cy="420" r="5" fill="#0d6efd"/>
  <circle cx="640" cy="430" r="5" fill="#0d6efd"/>
  <circle cx="650" cy="420" r="5" fill="#0d6efd"/>
  <circle cx="660" cy="430" r="5" fill="#0d6efd"/>
  <circle cx="670" cy="420" r="5" fill="#0d6efd"/>
  
  <circle cx="630" cy="440" r="5" fill="#dc3545"/>
  <circle cx="640" cy="430" r="5" fill="#dc3545"/>
  <circle cx="650" cy="440" r="5" fill="#dc3545"/>
  <circle cx="660" cy="430" r="5" fill="#dc3545"/>
  <circle cx="670" cy="440" r="5" fill="#dc3545"/>
  
  <!-- Arrow -->
  <line x1="570" y1="430" x2="590" y2="430" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <text x="440" y="510" font-family="Arial" font-size="14" text-anchor="start">• Harmony, BBKNN, Seurat整合</text>
  <text x="440" y="530" font-family="Arial" font-size="14" text-anchor="start">• 校正批次间的系统性差异</text>
  
  <!-- Arrow definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d"/>
    </marker>
  </defs>
</svg>
