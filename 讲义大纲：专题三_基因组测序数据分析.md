# 专题三：基因组测序(DNA-seq)数据分析

## 理论课教学大纲 (2小时)

### 教学目标
- 理解基因组测序的实验设计与应用场景
- 掌握参考基因组比对算法的基本原理
- 了解变异检测的方法学与流程
- 掌握变异注释与功能预测的基本原理
- 了解基因组组装的策略与算法

### 第一部分：基因组测序实验设计与应用场景 (25分钟)
1. 基因组测序类型与应用
   - 全基因组测序(WGS)
     - 重测序与从头测序的区别
     - 适用场景与研究问题
     - 测序深度要求与考虑因素
   - 外显子组测序(WES)
     - 捕获技术原理
     - 与WGS的比较优势
     - 适用场景与局限性
   - 靶向测序
     - 设计策略与方法
     - 应用场景与优势
     - 深度与覆盖度要求

2. 基因组测序实验设计关键因素
   - 样本选择与质量控制
     - DNA提取与质量评估
     - 样本量要求
     - 对照样本设计
   - 测序平台选择
     - 短读长vs长读长平台
     - 准确度vs成本的权衡
     - 混合测序策略
   - 测序深度设计
     - 不同应用的深度要求
     - 非均匀覆盖的处理策略
     - 成本效益分析
   - 文库构建策略
     - 插入片段大小选择
     - PCR扩增vs PCR-free
     - 双端vs单端测序

3. 基因组测序的主要应用场景
   - 物种参考基因组构建
   - 种群遗传变异研究
   - 进化与系统发育分析
   - 功能基因组学研究
   - 临床诊断与精准医疗
   - 农业育种与改良

### 第二部分：参考基因组比对算法原理 (30分钟)
1. 序列比对的基本概念
   - 全局比对vs局部比对
   - 序列相似性度量
   - 比对评分系统
   - 动态规划算法基础

2. 传统比对算法及其局限性
   - Smith-Waterman算法
   - Needleman-Wunsch算法
   - 计算复杂度挑战
   - 大规模数据的算法瓶颈

3. 高通量测序数据比对算法创新
   - 索引策略
     - 哈希表索引
     - 后缀树/数组
     - FM-index与BWT变换
   - 种子与扩展策略
   - 启发式算法优化

4. BWA算法原理
   - BWT变换与FM-index
   - 不同模式(BWA-backtrack, BWA-SW, BWA-MEM)
   - 算法参数优化
   - 性能特点与适用场景

5. Bowtie2算法原理
   - 双向BWT策略
   - 种子与扩展机制
   - 多线程并行实现
   - 与BWA的比较

6. 其他比对工具介绍
   - SOAP系列
   - Novoalign
   - Minimap2(长读长比对)
   - NGMLR(结构变异敏感比对)

7. 比对质量评估
   - 比对质量分数(MAPQ)
   - 唯一比对vs多重比对
   - 比对覆盖度与均匀性
   - 常见比对问题与解决方案

### 第三部分：变异检测方法学 (30分钟)
1. 变异类型概述
   - 单核苷酸多态性(SNP)
   - 插入缺失(Indel)
   - 结构变异(SV)
     - 拷贝数变异(CNV)
     - 倒位(Inversion)
     - 易位(Translocation)
     - 大片段插入/缺失
   - 变异检测的挑战

2. SNP与小Indel检测原理
   - 基于比对的变异检测流程
   - 变异位点识别算法
   - 基因型推断方法
   - 过滤策略与质量控制
   - 常见工具比较(GATK, FreeBayes, SAMtools等)

3. GATK变异检测流程详解
   - 最佳实践工作流程
   - 局部重比对原理
   - 碱基质量重校正
   - HaplotypeCaller算法
   - 变异质量评分重校正(VQSR)
   - 多样本联合分析策略

4. 结构变异检测方法
   - 基于读长深度的方法
   - 基于不一致比对的方法
   - 基于分割读长的方法
   - 基于从头组装的方法
   - 长读长测序在SV检测中的优势
   - 常用SV检测工具(Delly, Lumpy, Manta等)

5. 拷贝数变异检测
   - 读长深度归一化
   - 分段算法
   - 拷贝数状态推断
   - 常用CNV检测工具(CNVnator, Control-FREEC等)

6. 变异检测结果评估
   - 真阳性/假阳性/假阴性概念
   - 敏感性与特异性评估
   - 变异检测的黄金标准
   - 技术验证方法(Sanger测序、qPCR等)

### 第四部分：变异注释与功能预测原理 (20分钟)
1. 变异注释的基本概念
   - 基因组注释数据库
   - 变异位置注释(基因组区域、基因结构)
   - 变异功能注释(同义、错义、无义等)
   - 群体频率注释

2. 变异注释工具原理
   - ANNOVAR工作原理
   - SnpEff工作原理
   - VEP工作原理
   - 注释格式与输出解读

3. 变异功能预测方法
   - 保守性分析
   - 蛋白质结构预测
   - 机器学习方法
   - 整合预测算法

4. 变异功能预测工具
   - SIFT原理与应用
   - PolyPhen-2原理与应用
   - CADD原理与应用
   - REVEL、M-CAP等整合算法

5. 变异优先级排序策略
   - 疾病研究中的变异筛选
   - 候选基因分析方法
   - 家系分析策略
   - 功能通路分析

6. 变异数据库资源
   - dbSNP数据库
   - gnomAD/ExAC数据库
   - ClinVar数据库
   - COSMIC数据库(癌症)
   - 特定疾病变异数据库

### 第五部分：基因组组装策略与算法 (15分钟)
1. 基因组组装基本概念
   - 从头组装vs参考辅助组装
   - 组装质量评估指标
   - 组装挑战(重复序列、异质性等)

2. 短读长组装算法
   - De Bruijn图方法
   - 重叠-布局-一致性(OLC)方法
   - 贪婪算法
   - 常用组装工具(SPAdes, SOAPdenovo等)

3. 长读长组装算法
   - 基于字符串图的方法
   - 混合组装策略
   - 错误校正方法
   - 常用工具(Canu, Flye, wtdbg2等)

4. 组装后处理与改进
   - 支架构建(Scaffolding)
   - 间隙填充(Gap closing)
   - 组装错误校正
   - 染色体级别组装方法(Hi-C, 光学图谱等)

5. 组装质量评估
   - N50, L50等统计指标
   - BUSCO评估方法
   - 比对一致性评估
   - 基因组完整性评估

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握BWA/Bowtie2进行参考基因组比对的方法
- 学习SAMtools进行SAM/BAM文件处理的基本操作
- 了解GATK/FreeBayes等工具进行变异检测的流程
- 实践ANNOVAR/SnpEff进行变异注释与解读
- 学习IGV等可视化工具的使用方法

### 第一部分：BWA/Bowtie2进行参考基因组比对实践 (30分钟)
1. 参考基因组准备
   - 参考基因组下载
   - 参考基因组索引构建
   - 参考基因组注释文件准备

2. BWA比对实践
   - BWA安装与配置
   - BWA索引构建命令
   - BWA-MEM比对命令详解
   - 比对参数优化
   - 实际操作演示

3. Bowtie2比对实践
   - Bowtie2安装与配置
   - Bowtie2索引构建命令
   - Bowtie2比对命令详解
   - 比对参数优化
   - 实际操作演示

4. 比对结果评估
   - 比对统计信息获取
   - 比对质量分布分析
   - 覆盖度分析
   - 常见问题排查

### 第二部分：SAMtools进行SAM/BAM文件处理 (30分钟)
1. SAM/BAM格式详解
   - 格式规范回顾
   - 头部信息解读
   - 比对记录字段含义
   - FLAG值解读

2. SAMtools基本操作
   - 格式转换(view)
   - 排序(sort)
   - 索引构建(index)
   - 合并文件(merge)
   - 子集提取(view -b)

3. SAMtools高级功能
   - 比对统计(flagstat, idxstats)
   - 覆盖度计算(depth, bedcov)
   - 碱基质量统计(stats)
   - 过滤操作(view -q, -F, -f)
   - PCR重复标记(markdup)

4. BAM文件处理最佳实践
   - 处理流程设计
   - 文件命名规范
   - 中间文件管理
   - 并行处理策略
   - 实际操作演示

### 第三部分：GATK/FreeBayes等工具进行变异检测 (30分钟)
1. GATK变异检测流程
   - GATK安装与配置
   - 数据预处理步骤
     - 标记重复序列
     - 基础重校准(BQSR)
   - HaplotypeCaller使用
     - 命令参数详解
     - GVCF模式vs标准模式
     - 多样本联合分析
   - 变异过滤策略
     - 硬过滤参数设置
     - VQSR流程(高级)
   - 实际操作演示

2. FreeBayes变异检测
   - FreeBayes安装与配置
   - 基本命令格式
   - 关键参数设置
   - 结果过滤策略
   - 实际操作演示

3. 结构变异检测实践
   - Delly/Lumpy工具使用
   - 命令参数设置
   - 结果过滤与解读
   - 简单演示

4. 变异检测结果比较与合并
   - bcftools比较功能
   - VCF文件合并方法
   - 变异集交集/并集操作
   - 一致性评估

### 第四部分：ANNOVAR/SnpEff进行变异注释与解读 (20分钟)
1. ANNOVAR变异注释
   - ANNOVAR安装与配置
   - 注释数据库下载
   - 基本注释命令
   - 注释结果解读
   - 实际操作演示

2. SnpEff变异注释
   - SnpEff安装与配置
   - 数据库构建与选择
   - 注释命令详解
   - 结果文件解读
   - 实际操作演示

3. 变异过滤与优先级排序
   - 基于注释信息的过滤策略
   - 候选变异筛选标准
   - 自动化过滤脚本示例
   - 实际操作演示

4. 变异结果统计与可视化
   - 变异类型分布统计
   - 变异功能影响统计
   - 简单可视化方法
   - 结果导出与分享

### 第五部分：IGV等可视化工具使用 (10分钟)
1. IGV工具介绍
   - 软件安装与配置
   - 界面布局与功能区
   - 数据加载方法
   - 基本操作技巧

2. IGV数据可视化
   - 参考基因组导航
   - 比对数据可视化
   - 变异数据可视化
   - 注释轨道显示
   - 多样本比较视图

3. IGV高级功能
   - 截图与导出
   - 自定义轨道
   - 批量处理
   - 会话保存与恢复

4. 其他可视化工具简介
   - UCSC Genome Browser
   - Tablet
   - Savant
   - 工具选择建议

## 课后作业
1. 使用BWA-MEM对提供的测序数据进行参考基因组比对，并评估比对质量
2. 使用GATK或FreeBayes对比对结果进行变异检测，生成VCF文件
3. 使用ANNOVAR或SnpEff对检测到的变异进行注释，并筛选出功能重要的变异
4. 使用IGV可视化几个关键变异位点，并截图分析变异的可靠性
5. 撰写完整的分析报告，包括方法、结果和解释

## 参考资料
1. Li, H., & Durbin, R. (2009). Fast and accurate short read alignment with Burrows-Wheeler transform. Bioinformatics, 25(14), 1754-1760.
2. Langmead, B., & Salzberg, S. L. (2012). Fast gapped-read alignment with Bowtie 2. Nature Methods, 9(4), 357-359.
3. McKenna, A., Hanna, M., Banks, E., Sivachenko, A., Cibulskis, K., Kernytsky, A., ... & DePristo, M. A. (2010). The Genome Analysis Toolkit: a MapReduce framework for analyzing next-generation DNA sequencing data. Genome Research, 20(9), 1297-1303.
4. Garrison, E., & Marth, G. (2012). Haplotype-based variant detection from short-read sequencing. arXiv preprint arXiv:1207.3907.
5. Wang, K., Li, M., & Hakonarson, H. (2010). ANNOVAR: functional annotation of genetic variants from high-throughput sequencing data. Nucleic Acids Research, 38(16), e164.
6. Cingolani, P., Platts, A., Wang, L. L., Coon, M., Nguyen, T., Wang, L., ... & Ruden, D. M. (2012). A program for annotating and predicting the effects of single nucleotide polymorphisms, SnpEff. Fly, 6(2), 80-92.
7. Robinson, J. T., Thorvaldsdóttir, H., Winckler, W., Guttman, M., Lander, E. S., Getz, G., & Mesirov, J. P. (2011). Integrative genomics viewer. Nature Biotechnology, 29(1), 24-26.