# 专题八：高通量测序技术在植物保护中的应用 - 实践操作课

## 目录
1. [环境配置与数据准备](#环境配置与数据准备)
2. [植物病原体基因组组装与注释流程](#植物病原体基因组组装与注释流程)
3. [病原微生物快速鉴定与系统发育分析](#病原微生物快速鉴定与系统发育分析)
4. [植物-病原体互作转录组数据分析](#植物-病原体互作转录组数据分析)
5. [抗病/抗性相关基因的挖掘与功能预测](#抗病抗性相关基因的挖掘与功能预测)
6. [田间微生物多样性分析流程](#田间微生物多样性分析流程)
7. [数据可视化与结果解读](#数据可视化与结果解读)
8. [常见问题与故障排除](#常见问题与故障排除)

## 环境配置与数据准备

### 1. 软件环境安装

#### 基础工具安装
```bash
# 更新系统包管理器
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y build-essential wget curl git python3 python3-pip
sudo apt install -y openjdk-8-jdk zlib1g-dev libbz2-dev liblzma-dev

# 安装conda包管理器
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh
source ~/.bashrc
```

#### 创建专用环境
```bash
# 创建植物保护分析环境
conda create -n plant_protection python=3.8 -y
conda activate plant_protection

# 安装质控和预处理工具
conda install -c bioconda fastqc trimmomatic cutadapt -y
conda install -c bioconda bowtie2 samtools -y

# 安装组装工具
conda install -c bioconda spades quast busco -y

# 安装注释工具
conda install -c bioconda prokka interproscan blast -y

# 安装系统发育分析工具
conda install -c bioconda muscle mafft raxml iqtree -y

# 安装转录组分析工具
conda install -c bioconda hisat2 star htseq subread -y
conda install -c bioconda salmon kallisto -y

# 安装R和相关包
conda install -c conda-forge r-base r-essentials -y
```

#### R包安装
```r
# 启动R并安装必要的包
R

# 安装Bioconductor
if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")

BiocManager::install(c("DESeq2", "edgeR", "limma", "clusterProfiler", 
                       "pathview", "KEGGREST", "org.At.tair.db"))

# 安装其他必要包
install.packages(c("ggplot2", "pheatmap", "VennDiagram", "corrplot",
                   "tidyverse", "ggrepel", "ggtree", "ape"))

quit()
```

### 2. 数据获取与组织

#### 创建项目目录结构
```bash
# 创建项目根目录
mkdir -p ~/plant_protection_analysis
cd ~/plant_protection_analysis

# 创建子目录
mkdir -p {raw_data,qc_results,assembly,annotation,phylogeny,transcriptome,diversity,results,scripts,references}

# 创建详细的子目录结构
mkdir -p raw_data/{genome,transcriptome,amplicon}
mkdir -p qc_results/{fastqc,trimmed}
mkdir -p assembly/{spades_output,quast_results,busco_results}
mkdir -p annotation/{prokka,interproscan,blast}
mkdir -p phylogeny/{alignments,trees,models}
mkdir -p transcriptome/{mapping,counts,deseq2}
mkdir -p diversity/{qiime2,networks}
mkdir -p references/{genomes,databases,annotations}
```

#### 示例数据下载脚本
```bash
#!/bin/bash
# download_sample_data.sh

# 设置工作目录
cd ~/plant_protection_analysis/raw_data

# 下载示例细菌基因组数据
echo "下载病原细菌测序数据..."
wget -O genome/pathogen_R1.fastq.gz "https://example.com/pathogen_R1.fastq.gz"
wget -O genome/pathogen_R2.fastq.gz "https://example.com/pathogen_R2.fastq.gz"

# 下载宿主基因组参考序列
echo "下载植物基因组参考序列..."
wget -O ../references/genomes/plant_genome.fasta "https://example.com/plant_genome.fasta"

# 下载转录组数据
echo "下载转录组数据..."
for i in {1..6}; do
    wget -O transcriptome/sample_${i}_R1.fastq.gz "https://example.com/sample_${i}_R1.fastq.gz"
    wget -O transcriptome/sample_${i}_R2.fastq.gz "https://example.com/sample_${i}_R2.fastq.gz"
done

# 下载扩增子数据
echo "下载微生物多样性扩增子数据..."
for i in {1..12}; do
    wget -O amplicon/field_${i}_R1.fastq.gz "https://example.com/field_${i}_R1.fastq.gz"
    wget -O amplicon/field_${i}_R2.fastq.gz "https://example.com/field_${i}_R2.fastq.gz"
done

echo "数据下载完成！"
```

### 3. 参考数据库配置

#### BLAST数据库配置
```bash
# 创建BLAST数据库目录
mkdir -p ~/plant_protection_analysis/references/databases/blast

# 下载并配置NCBI数据库
cd ~/plant_protection_analysis/references/databases/blast
wget ftp://ftp.ncbi.nlm.nih.gov/blast/db/nt.*.tar.gz
for file in nt.*.tar.gz; do tar -xzf $file; done

# 下载蛋白质数据库
wget ftp://ftp.ncbi.nlm.nih.gov/blast/db/nr.*.tar.gz
for file in nr.*.tar.gz; do tar -xzf $file; done
```

#### QIIME2数据库配置
```bash
# 激活QIIME2环境
conda activate qiime2-2023.5

# 下载Silva数据库
mkdir -p ~/plant_protection_analysis/references/databases/qiime2
cd ~/plant_protection_analysis/references/databases/qiime2

wget https://data.qiime2.org/2023.5/common/silva-138-99-515-806-nb-classifier.qza
wget https://data.qiime2.org/2023.5/common/silva-138-99-seqs.qza
wget https://data.qiime2.org/2023.5/common/silva-138-99-tax.qza
```

## 植物病原体基因组组装与注释流程

### 1. 病原体基因组数据预处理

#### 质量控制与评估
```bash
#!/bin/bash
# genome_qc.sh - 基因组数据质量控制脚本

# 设置变量
RAW_DATA_DIR="~/plant_protection_analysis/raw_data/genome"
QC_DIR="~/plant_protection_analysis/qc_results"
SCRIPTS_DIR="~/plant_protection_analysis/scripts"

# 激活环境
conda activate plant_protection

# 1. 原始数据质量评估
echo "=== 步骤1: 原始数据质量评估 ==="
fastqc ${RAW_DATA_DIR}/*.fastq.gz -o ${QC_DIR}/fastqc/ -t 8

# 2. 质量过滤和接头去除
echo "=== 步骤2: 质量过滤和接头去除 ==="
trimmomatic PE -threads 8 \
    ${RAW_DATA_DIR}/pathogen_R1.fastq.gz \
    ${RAW_DATA_DIR}/pathogen_R2.fastq.gz \
    ${QC_DIR}/trimmed/pathogen_R1_paired.fastq.gz \
    ${QC_DIR}/trimmed/pathogen_R1_unpaired.fastq.gz \
    ${QC_DIR}/trimmed/pathogen_R2_paired.fastq.gz \
    ${QC_DIR}/trimmed/pathogen_R2_unpaired.fastq.gz \
    ILLUMINACLIP:TruSeq3-PE.fa:2:30:10 \
    LEADING:3 TRAILING:3 SLIDINGWINDOW:4:20 MINLEN:36

# 3. 过滤后质量评估
echo "=== 步骤3: 过滤后质量评估 ==="
fastqc ${QC_DIR}/trimmed/*_paired.fastq.gz -o ${QC_DIR}/fastqc/ -t 8

# 4. 生成质量报告
multiqc ${QC_DIR}/fastqc/ -o ${QC_DIR}/fastqc/
```

#### 宿主DNA污染去除
```bash
#!/bin/bash
# remove_host_contamination.sh

# 设置变量
PLANT_GENOME="~/plant_protection_analysis/references/genomes/plant_genome.fasta"
TRIMMED_DIR="~/plant_protection_analysis/qc_results/trimmed"
CLEAN_DIR="~/plant_protection_analysis/qc_results/clean"

mkdir -p ${CLEAN_DIR}

echo "=== 宿主DNA污染去除 ==="

# 1. 构建植物基因组索引
echo "构建植物基因组Bowtie2索引..."
bowtie2-build ${PLANT_GENOME} ${PLANT_GENOME%.fasta}_bt2_index

# 2. 比对到植物基因组
echo "比对reads到植物基因组..."
bowtie2 -x ${PLANT_GENOME%.fasta}_bt2_index \
    -1 ${TRIMMED_DIR}/pathogen_R1_paired.fastq.gz \
    -2 ${TRIMMED_DIR}/pathogen_R2_paired.fastq.gz \
    -S ${CLEAN_DIR}/aligned_to_plant.sam \
    --threads 8 \
    --very-sensitive

# 3. 提取未比对上的reads（病原体reads）
echo "提取病原体特异性reads..."
samtools view -bS ${CLEAN_DIR}/aligned_to_plant.sam > ${CLEAN_DIR}/aligned_to_plant.bam
samtools view -b -f 12 -F 256 ${CLEAN_DIR}/aligned_to_plant.bam > ${CLEAN_DIR}/unaligned_pairs.bam

# 4. 转换为FASTQ格式
samtools sort -n ${CLEAN_DIR}/unaligned_pairs.bam -o ${CLEAN_DIR}/unaligned_pairs_sorted.bam
samtools fastq -1 ${CLEAN_DIR}/pathogen_clean_R1.fastq.gz \
    -2 ${CLEAN_DIR}/pathogen_clean_R2.fastq.gz \
    ${CLEAN_DIR}/unaligned_pairs_sorted.bam

# 5. 统计去除效果
echo "=== 污染去除统计 ==="
echo "原始reads数量："
zcat ${TRIMMED_DIR}/pathogen_R1_paired.fastq.gz | wc -l | awk '{print $1/4}'
echo "去除宿主后reads数量："
zcat ${CLEAN_DIR}/pathogen_clean_R1.fastq.gz | wc -l | awk '{print $1/4}'

# 清理临时文件
rm ${CLEAN_DIR}/*.sam ${CLEAN_DIR}/*.bam
```

### 2. 基因组组装优化

#### SPAdes参数优化策略
```bash
#!/bin/bash
# optimized_assembly.sh

# 设置变量
CLEAN_DIR="~/plant_protection_analysis/qc_results/clean"
ASSEMBLY_DIR="~/plant_protection_analysis/assembly"

echo "=== 基因组组装参数优化 ==="

# 1. 估算基因组大小和覆盖度
echo "步骤1: 估算基因组统计信息..."
python3 << 'EOF'
import gzip
import subprocess

# 计算reads总碱基数
def count_bases(file_path):
    total_bases = 0
    total_reads = 0
    with gzip.open(file_path, 'rt') as f:
        for i, line in enumerate(f):
            if i % 4 == 1:  # 序列行
                total_bases += len(line.strip())
                total_reads += 1
    return total_bases, total_reads

# 计算R1和R2的统计信息
r1_bases, r1_reads = count_bases('~/plant_protection_analysis/qc_results/clean/pathogen_clean_R1.fastq.gz')
r2_bases, r2_reads = count_bases('~/plant_protection_analysis/qc_results/clean/pathogen_clean_R2.fastq.gz')

total_bases = r1_bases + r2_bases
total_reads = r1_reads + r2_reads
avg_read_length = total_bases / total_reads

print(f"总reads数量: {total_reads:,}")
print(f"总碱基数: {total_bases:,}")
print(f"平均read长度: {avg_read_length:.1f}")

# 估算基因组大小（假设细菌基因组2-8Mb）
estimated_genome_size = 5000000  # 5Mb作为初始估计
coverage = total_bases / estimated_genome_size
print(f"估算基因组大小: {estimated_genome_size:,} bp")
print(f"估算覆盖度: {coverage:.1f}X")
EOF

# 2. 运行SPAdes组装（多种k-mer策略）
echo "步骤2: 运行SPAdes组装..."
spades.py \
    --pe1-1 ${CLEAN_DIR}/pathogen_clean_R1.fastq.gz \
    --pe1-2 ${CLEAN_DIR}/pathogen_clean_R2.fastq.gz \
    -o ${ASSEMBLY_DIR}/spades_output \
    --threads 16 \
    --memory 32 \
    -k 21,33,55,77,99,127 \
    --careful \
    --cov-cutoff auto

# 3. 后处理和过滤
echo "步骤3: 组装结果后处理..."
# 过滤短contigs（< 500bp）
python3 << 'EOF'
from Bio import SeqIO
import os

input_file = "~/plant_protection_analysis/assembly/spades_output/contigs.fasta"
output_file = "~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"

# 展开路径
input_file = os.path.expanduser(input_file)
output_file = os.path.expanduser(output_file)

with open(output_file, 'w') as output_handle:
    for record in SeqIO.parse(input_file, "fasta"):
        if len(record.seq) >= 500:
            SeqIO.write(record, output_handle, "fasta")

print("过滤完成：保留长度≥500bp的contigs")
EOF
```

#### 组装质量评估增强
```bash
#!/bin/bash
# enhanced_assembly_assessment.sh

ASSEMBLY_DIR="~/plant_protection_analysis/assembly"
SPADES_OUT="${ASSEMBLY_DIR}/spades_output"

echo "=== 增强组装质量评估 ==="

# 1. QUAST详细评估
echo "步骤1: QUAST质量评估..."
quast.py ${SPADES_OUT}/contigs_filtered.fasta \
    -o ${ASSEMBLY_DIR}/quast_results \
    --threads 8 \
    --min-contig 500 \
    --glimmer \
    --rna-finding \
    --conserved-genes-finding

# 2. BUSCO完整性评估
echo "步骤2: BUSCO完整性评估..."
# 对于细菌使用bacteria_odb10数据库
busco -i ${SPADES_OUT}/contigs_filtered.fasta \
    -o ${ASSEMBLY_DIR}/busco_results \
    -l bacteria_odb10 \
    -m genome \
    --cpu 8 \
    --offline

# 3. 生成组装统计报告
python3 << 'EOF'
import os
from Bio import SeqIO
import matplotlib.pyplot as plt
import numpy as np

# 读取组装结果
assembly_file = os.path.expanduser("~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta")
contigs = list(SeqIO.parse(assembly_file, "fasta"))

# 计算统计信息
lengths = [len(contig.seq) for contig in contigs]
lengths.sort(reverse=True)

total_length = sum(lengths)
num_contigs = len(lengths)
max_length = max(lengths) if lengths else 0
min_length = min(lengths) if lengths else 0

# 计算N50
cumsum = 0
n50 = 0
for length in lengths:
    cumsum += length
    if cumsum >= total_length * 0.5:
        n50 = length
        break

# 计算N90
cumsum = 0
n90 = 0
for length in lengths:
    cumsum += length
    if cumsum >= total_length * 0.9:
        n90 = length
        break

# 输出统计结果
print("=== 组装统计摘要 ===")
print(f"Contigs数量: {num_contigs}")
print(f"总长度: {total_length:,} bp")
print(f"最大contig长度: {max_length:,} bp")
print(f"最小contig长度: {min_length:,} bp")
print(f"N50: {n50:,} bp")
print(f"N90: {n90:,} bp")
print(f"平均长度: {total_length/num_contigs:.0f} bp")

# 生成长度分布图
plt.figure(figsize=(12, 8))

# 子图1: Contig长度分布直方图
plt.subplot(2, 2, 1)
plt.hist(lengths, bins=50, alpha=0.7, edgecolor='black')
plt.xlabel('Contig长度 (bp)')
plt.ylabel('频数')
plt.title('Contig长度分布直方图')
plt.yscale('log')

# 子图2: 累积长度曲线
plt.subplot(2, 2, 2)
cumulative_lengths = np.cumsum(lengths)
plt.plot(range(1, len(lengths)+1), cumulative_lengths, 'b-', linewidth=2)
plt.axhline(y=total_length*0.5, color='r', linestyle='--', label='50%')
plt.axhline(y=total_length*0.9, color='g', linestyle='--', label='90%')
plt.xlabel('Contig排名')
plt.ylabel('累积长度 (bp)')
plt.title('累积长度曲线')
plt.legend()

# 子图3: 长度vs排名对数图
plt.subplot(2, 2, 3)
plt.plot(range(1, len(lengths)+1), lengths, 'ro-', markersize=3)
plt.axhline(y=n50, color='r', linestyle='--', label=f'N50={n50}bp')
plt.xlabel('Contig排名')
plt.ylabel('Contig长度 (bp)')
plt.title('Contig长度排名图')
plt.yscale('log')
plt.legend()

# 子图4: GC含量分布
plt.subplot(2, 2, 4)
gc_contents = []
for contig in contigs:
    seq = str(contig.seq).upper()
    gc_count = seq.count('G') + seq.count('C')
    gc_content = (gc_count / len(seq)) * 100 if len(seq) > 0 else 0
    gc_contents.append(gc_content)

plt.hist(gc_contents, bins=30, alpha=0.7, edgecolor='black')
plt.xlabel('GC含量 (%)')
plt.ylabel('Contig数量')
plt.title('GC含量分布')
plt.axvline(x=np.mean(gc_contents), color='r', linestyle='--', 
           label=f'平均GC%={np.mean(gc_contents):.1f}%')
plt.legend()

plt.tight_layout()
plt.savefig(os.path.expanduser("~/plant_protection_analysis/assembly/assembly_statistics.png"), 
           dpi=300, bbox_inches='tight')
plt.close()

print("\n统计图表已保存到: ~/plant_protection_analysis/assembly/assembly_statistics.png")
EOF

echo "组装质量评估完成！"
```

### 3. 基因组注释流程增强

#### Prokka注释优化
```bash
#!/bin/bash
# enhanced_annotation.sh

ASSEMBLY_DIR="~/plant_protection_analysis/assembly"
ANNOTATION_DIR="~/plant_protection_analysis/annotation"
SPADES_OUT="${ASSEMBLY_DIR}/spades_output"

echo "=== 增强基因组注释流程 ==="

# 1. Prokka注释
echo "步骤1: Prokka基因预测和注释..."
prokka ${SPADES_OUT}/contigs_filtered.fasta \
    --outdir ${ANNOTATION_DIR}/prokka \
    --prefix pathogen_genome \
    --genus Bacteria \
    --species "pathogen" \
    --strain "isolate_001" \
    --kingdom Bacteria \
    --gcode 11 \
    --cpus 8 \
    --force \
    --addgenes \
    --addmrna \
    --locustag PATHOGEN \
    --increment 10 \
    --gffver 3 \
    --centre "PlantProtection_Lab" \
    --compliant

# 2. 功能注释增强
echo "步骤2: InterProScan功能域注释..."
# 提取蛋白质序列进行InterProScan分析
interproscan.sh -i ${ANNOTATION_DIR}/prokka/pathogen_genome.faa \
    -o ${ANNOTATION_DIR}/interproscan/pathogen_interproscan.tsv \
    -f tsv \
    --cpu 8 \
    --applications Pfam,TIGRFAM,ProDom,SMART,CDD

# 3. BLAST功能注释
echo "步骤3: BLAST功能注释..."
# 对预测的蛋白质进行BLAST搜索
blastp -query ${ANNOTATION_DIR}/prokka/pathogen_genome.faa \
    -db ~/plant_protection_analysis/references/databases/blast/nr \
    -out ${ANNOTATION_DIR}/blast/pathogen_blastp_nr.tsv \
    -outfmt "6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle" \
    -max_target_seqs 5 \
    -num_threads 8 \
    -evalue 1e-5

# 4. 抗性基因注释
echo "步骤4: 抗性基因预测..."
# 使用CARD数据库进行抗性基因注释
rgi main -i ${ANNOTATION_DIR}/prokka/pathogen_genome.faa \
    -o ${ANNOTATION_DIR}/resistance/pathogen_resistance \
    -t protein \
    -a BLAST \
    --clean \
    --local

# 5. 毒力因子预测
echo "步骤5: 毒力因子预测..."
# 使用VFDB进行毒力因子预测
blastp -query ${ANNOTATION_DIR}/prokka/pathogen_genome.faa \
    -db ~/plant_protection_analysis/references/databases/VFDB/VFDB_setA_pro.fas \
    -out ${ANNOTATION_DIR}/virulence/pathogen_virulence.tsv \
    -outfmt "6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle" \
    -max_target_seqs 3 \
    -num_threads 8 \
    -evalue 1e-10

echo "基因组注释完成！"
```

#### 注释结果整合和可视化
```python
#!/usr/bin/env python3
# integrate_annotations.py

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from Bio import SeqIO
import os
import numpy as np

def integrate_annotations():
    """整合多种注释结果"""
    
    annotation_dir = os.path.expanduser("~/plant_protection_analysis/annotation")
    
    # 1. 读取Prokka注释结果
    prokka_file = f"{annotation_dir}/prokka/pathogen_genome.tsv"
    prokka_df = pd.read_csv(prokka_file, sep='\t')
    
    print("=== 基因组注释统计摘要 ===")
    print(f"总基因数量: {len(prokka_df)}")
    print(f"编码基因: {len(prokka_df[prokka_df['ftype'] == 'CDS'])}")
    print(f"tRNA基因: {len(prokka_df[prokka_df['ftype'] == 'tRNA'])}")
    print(f"rRNA基因: {len(prokka_df[prokka_df['ftype'] == 'rRNA'])}")
    
    # 2. 功能分类统计
    product_stats = prokka_df['product'].value_counts().head(20)
    
    # 3. 生成注释统计图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 基因类型分布
    ftype_counts = prokka_df['ftype'].value_counts()
    axes[0,0].pie(ftype_counts.values, labels=ftype_counts.index, autopct='%1.1f%%')
    axes[0,0].set_title('基因类型分布')
    
    # 基因长度分布
    cds_df = prokka_df[prokka_df['ftype'] == 'CDS']
    gene_lengths = (cds_df['end'] - cds_df['start'] + 1)
    axes[0,1].hist(gene_lengths, bins=50, alpha=0.7, edgecolor='black')
    axes[0,1].set_xlabel('基因长度 (bp)')
    axes[0,1].set_ylabel('基因数量')
    axes[0,1].set_title('编码基因长度分布')
    
    # 基因密度分布
    contig_gene_density = prokka_df.groupby('sequence_id').size()
    axes[1,0].bar(range(len(contig_gene_density)), contig_gene_density.values)
    axes[1,0].set_xlabel('Contig编号')
    axes[1,0].set_ylabel('基因数量')
    axes[1,0].set_title('各Contig基因密度')
    
    # 功能注释top产品
    axes[1,1].barh(range(len(product_stats.head(10))), product_stats.head(10).values)
    axes[1,1].set_yticks(range(len(product_stats.head(10))))
    axes[1,1].set_yticklabels([p[:30] + '...' if len(p) > 30 else p 
                              for p in product_stats.head(10).index])
    axes[1,1].set_xlabel('基因数量')
    axes[1,1].set_title('主要功能产品分布')
    
    plt.tight_layout()
    plt.savefig(f"{annotation_dir}/annotation_statistics.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n注释统计图表已保存到: {annotation_dir}/annotation_statistics.png")

if __name__ == "__main__":
    integrate_annotations()
```

## 病原微生物快速鉴定与系统发育分析

### 1. 基于标记基因的快速鉴定

#### 自动化标记基因提取和鉴定
```bash
#!/bin/bash
# marker_gene_identification.sh

# 设置变量
ASSEMBLY_DIR="~/plant_protection_analysis/assembly"
PHYLOGENY_DIR="~/plant_protection_analysis/phylogeny"
SPADES_OUT="${ASSEMBLY_DIR}/spades_output"

mkdir -p ${PHYLOGENY_DIR}/{markers,blast_results,alignments,trees}

echo "=== 标记基因快速鉴定流程 ==="

# 1. 提取16S rRNA基因（细菌）
echo "步骤1: 提取16S rRNA基因..."
barrnap --kingdom bac --threads 8 \
    ${SPADES_OUT}/contigs_filtered.fasta \
    > ${PHYLOGENY_DIR}/markers/16S_rRNA.gff

# 从GFF文件提取16S序列
python3 << 'EOF'
import os
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
import pandas as pd

# 读取基因组文件
genome_file = os.path.expanduser("~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta")
gff_file = os.path.expanduser("~/plant_protection_analysis/phylogeny/markers/16S_rRNA.gff")
output_file = os.path.expanduser("~/plant_protection_analysis/phylogeny/markers/16S_sequences.fasta")

# 创建contig字典
genome_dict = SeqIO.to_dict(SeqIO.parse(genome_file, "fasta"))

# 解析GFF文件并提取序列
sequences = []
if os.path.exists(gff_file):
    with open(gff_file, 'r') as f:
        for line in f:
            if line.startswith('#') or not line.strip():
                continue
            parts = line.strip().split('\t')
            if len(parts) >= 9 and parts[2] == '16S_rRNA':
                contig = parts[0]
                start = int(parts[3]) - 1  # 转换为0-based
                end = int(parts[4])
                strand = parts[6]
                
                if contig in genome_dict:
                    seq = genome_dict[contig].seq[start:end]
                    if strand == '-':
                        seq = seq.reverse_complement()
                    
                    record = SeqRecord(seq, id=f"{contig}_{start}_{end}", 
                                     description=f"16S rRNA from {contig}")
                    sequences.append(record)

# 保存提取的序列
if sequences:
    SeqIO.write(sequences, output_file, "fasta")
    print(f"提取到 {len(sequences)} 个16S rRNA序列")
else:
    print("未找到16S rRNA序列")
EOF

# 2. BLAST鉴定
echo "步骤2: BLAST鉴定最相似物种..."
if [ -f "${PHYLOGENY_DIR}/markers/16S_sequences.fasta" ]; then
    blastn -query ${PHYLOGENY_DIR}/markers/16S_sequences.fasta \
        -db ~/plant_protection_analysis/references/databases/blast/nt \
        -out ${PHYLOGENY_DIR}/blast_results/16S_blast_results.tsv \
        -outfmt "6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle" \
        -max_target_seqs 10 \
        -num_threads 8 \
        -evalue 1e-20
    
    # 解析BLAST结果
    python3 << 'EOF'
import pandas as pd
import os

blast_file = os.path.expanduser("~/plant_protection_analysis/phylogeny/blast_results/16S_blast_results.tsv")
if os.path.exists(blast_file):
    # 读取BLAST结果
    columns = ['qseqid', 'sseqid', 'pident', 'length', 'mismatch', 'gapopen', 
               'qstart', 'qend', 'sstart', 'send', 'evalue', 'bitscore', 'stitle']
    blast_df = pd.read_csv(blast_file, sep='\t', names=columns)
    
    print("=== 病原体鉴定结果 ===")
    print("Top 5 最相似物种:")
    for i, row in blast_df.head(5).iterrows():
        species_info = row['stitle'].split(' ')[:2]
        species = ' '.join(species_info) if len(species_info) >= 2 else row['stitle'][:50]
        print(f"{i+1}. {species}")
        print(f"   相似度: {row['pident']:.1f}%")
        print(f"   E-value: {row['evalue']}")
        print()
        
    # 保存鉴定结果
    summary_df = blast_df.groupby('qseqid').first().reset_index()
    summary_df[['qseqid', 'pident', 'evalue', 'stitle']].to_csv(
        os.path.expanduser("~/plant_protection_analysis/phylogeny/identification_summary.csv"), 
        index=False)
    print("鉴定结果已保存到: ~/plant_protection_analysis/phylogeny/identification_summary.csv")
else:
    print("未找到BLAST结果文件")
EOF
fi

#### 多基因位点系统发育分析（MLSA）
```bash
#!/bin/bash
# mlsa_analysis.sh

PHYLOGENY_DIR="~/plant_protection_analysis/phylogeny"
ANNOTATION_DIR="~/plant_protection_analysis/annotation"

echo "=== 多基因位点系统发育分析 ==="

# 1. 提取管家基因
echo "步骤1: 提取标准管家基因..."
python3 << 'EOF'
import os
from Bio import SeqIO
import subprocess

# 标准细菌管家基因列表
housekeeping_genes = [
    'rpoD',  # RNA polymerase sigma factor
    'gyrB',  # DNA gyrase subunit B
    'recA',  # recombinase A
    'atpD',  # ATP synthase subunit beta
    'glnA',  # glutamine synthetase
    'trpB',  # tryptophan synthase beta chain
    'pyrE'   # orotate phosphoribosyltransferase
]

annotation_dir = os.path.expanduser("~/plant_protection_analysis/annotation")
phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")

# 读取Prokka注释结果
prokka_faa = f"{annotation_dir}/prokka/pathogen_genome.faa"
prokka_tsv = f"{annotation_dir}/prokka/pathogen_genome.tsv"

if os.path.exists(prokka_faa) and os.path.exists(prokka_tsv):
    import pandas as pd
    
    # 读取注释表
    annotation_df = pd.read_csv(prokka_tsv, sep='\t')
    
    # 读取蛋白质序列
    protein_dict = SeqIO.to_dict(SeqIO.parse(prokka_faa, "fasta"))
    
    # 为每个管家基因搜索同源序列
    found_genes = {}
    for gene in housekeeping_genes:
        # 搜索包含基因名的注释
        gene_matches = annotation_df[
            annotation_df['gene'].str.contains(gene, case=False, na=False) |
            annotation_df['product'].str.contains(gene, case=False, na=False)
        ]
        
        if not gene_matches.empty:
            # 取第一个匹配的基因
            best_match = gene_matches.iloc[0]
            locus_tag = best_match['locus_tag']
            
            if locus_tag in protein_dict:
                found_genes[gene] = protein_dict[locus_tag]
                print(f"找到 {gene}: {locus_tag}")
    
    # 保存找到的管家基因
    os.makedirs(f"{phylogeny_dir}/housekeeping", exist_ok=True)
    for gene, sequence in found_genes.items():
        SeqIO.write(sequence, f"{phylogeny_dir}/housekeeping/{gene}.fasta", "fasta")
    
    print(f"\n总共找到 {len(found_genes)} 个管家基因")
else:
    print("未找到Prokka注释结果")
EOF

# 2. 从数据库下载参考序列
echo "步骤2: 下载参考物种的管家基因序列..."
python3 << 'EOF'
from Bio import Entrez, SeqIO
import os
import time

# 设置邮箱（NCBI要求）
Entrez.email = "<EMAIL>"

phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")
housekeeping_genes = ['rpoD', 'gyrB', 'recA', 'atpD', 'glnA', 'trpB', 'pyrE']

# 一些常见病原细菌的参考菌株
reference_strains = [
    "Escherichia coli K-12",
    "Pseudomonas aeruginosa PAO1", 
    "Bacillus subtilis 168",
    "Staphylococcus aureus NCTC8325"
]

for gene in housekeeping_genes:
    if os.path.exists(f"{phylogeny_dir}/housekeeping/{gene}.fasta"):
        print(f"处理 {gene} 基因...")
        
        # 这里可以添加从NCBI下载参考序列的代码
        # 由于需要网络连接和API密钥，这里提供框架
        
        print(f"  为 {gene} 收集参考序列...")
        # 实际应用中，您需要：
        # 1. 搜索NCBI数据库
        # 2. 下载相关序列
        # 3. 添加到本地fasta文件中
        
        time.sleep(1)  # 避免过频繁请求

print("参考序列收集完成")
EOF
```

### 2. 系统发育分析增强

#### 自动化系统发育树构建流程
```bash
#!/bin/bash
# automated_phylogeny.sh

PHYLOGENY_DIR="~/plant_protection_analysis/phylogeny"

echo "=== 自动化系统发育分析流程 ==="

# 1. 序列比对
echo "步骤1: 多序列比对..."
if [ -f "${PHYLOGENY_DIR}/markers/16S_sequences.fasta" ]; then
    # 使用MUSCLE进行多序列比对
    muscle -in ${PHYLOGENY_DIR}/markers/16S_sequences.fasta \
           -out ${PHYLOGENY_DIR}/alignments/16S_aligned.fasta \
           -maxiters 100 \
           -diags
    
    # 使用MAFFT进行比对（备选方案）
    mafft --auto --thread 8 \
          ${PHYLOGENY_DIR}/markers/16S_sequences.fasta \
          > ${PHYLOGENY_DIR}/alignments/16S_mafft_aligned.fasta
    
    echo "多序列比对完成"
else:
    echo "未找到16S序列文件，跳过比对步骤"
fi

# 2. 比对质量评估和优化
echo "步骤2: 比对质量评估..."
python3 << 'EOF'
from Bio import AlignIO
from Bio.Align import AlignInfo
import os
import matplotlib.pyplot as plt
import numpy as np

phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")

# 评估MUSCLE比对结果
muscle_file = f"{phylogeny_dir}/alignments/16S_aligned.fasta"
if os.path.exists(muscle_file):
    alignment = AlignIO.read(muscle_file, "fasta")
    
    print("=== 比对质量评估 ===")
    print(f"序列数量: {len(alignment)}")
    print(f"比对长度: {alignment.get_alignment_length()}")
    
    # 计算gap频率
    gap_frequencies = []
    for i in range(alignment.get_alignment_length()):
        column = alignment[:, i]
        gap_count = column.count('-')
        gap_freq = gap_count / len(alignment)
        gap_frequencies.append(gap_freq)
    
    # 去除gap频率过高的位点（>50%）
    good_positions = [i for i, freq in enumerate(gap_frequencies) if freq <= 0.5]
    
    print(f"原始位点数: {len(gap_frequencies)}")
    print(f"保留位点数: {len(good_positions)}")
    print(f"去除位点数: {len(gap_frequencies) - len(good_positions)}")
    
    # 生成过滤后的比对
    if good_positions:
        filtered_alignment = alignment[:, good_positions[0]:good_positions[0]+1]
        for pos in good_positions[1:]:
            filtered_alignment += alignment[:, pos:pos+1]
        
        # 保存过滤后的比对
        AlignIO.write(filtered_alignment, 
                     f"{phylogeny_dir}/alignments/16S_filtered.fasta", 
                     "fasta")
        print("过滤后的比对已保存")
    
    # 可视化gap分布
    plt.figure(figsize=(12, 6))
    plt.plot(gap_frequencies, 'b-', alpha=0.7)
    plt.axhline(y=0.5, color='r', linestyle='--', label='50% gap threshold')
    plt.xlabel('Position in alignment')
    plt.ylabel('Gap frequency')
    plt.title('Gap frequency distribution in alignment')
    plt.legend()
    plt.savefig(f"{phylogeny_dir}/alignments/gap_distribution.png", 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Gap分布图已保存")
else:
    print("未找到比对文件")
EOF

# 3. 进化模型选择
echo "步骤3: 最佳进化模型选择..."
if [ -f "${PHYLOGENY_DIR}/alignments/16S_filtered.fasta" ]; then
    # 使用ModelTest-NG进行模型选择
    modeltest-ng -i ${PHYLOGENY_DIR}/alignments/16S_filtered.fasta \
                 -o ${PHYLOGENY_DIR}/models/16S_modeltest \
                 -d nt \
                 -p 8 \
                 --force
    
    # 提取最佳模型
    best_model=$(grep "Best model according to AIC" ${PHYLOGENY_DIR}/models/16S_modeltest.out | awk '{print $6}')
    echo "最佳模型: $best_model"
fi

# 4. 系统发育树构建
echo "步骤4: 系统发育树构建..."
if [ -f "${PHYLOGENY_DIR}/alignments/16S_filtered.fasta" ]; then
    # 使用IQ-TREE构建最大似然树
    iqtree -s ${PHYLOGENY_DIR}/alignments/16S_filtered.fasta \
           -m MFP \
           -bb 1000 \
           -nt 8 \
           -pre ${PHYLOGENY_DIR}/trees/16S_iqtree
    
    # 使用RAxML构建最大似然树（备选）
    raxmlHPC-PTHREADS -T 8 \
                      -m GTRGAMMA \
                      -s ${PHYLOGENY_DIR}/alignments/16S_filtered.fasta \
                      -n 16S_raxml \
                      -w ${PHYLOGENY_DIR}/trees/ \
                      -p 12345 \
                      -# 100
    
    echo "系统发育树构建完成"
fi
```

#### 系统发育树可视化和注释
```python
#!/usr/bin/env python3
# visualize_phylogeny.py

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from Bio import Phylo
import os
import pandas as pd
import numpy as np

def visualize_phylogenetic_tree():
    """可视化和注释系统发育树"""
    
    phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")
    
    # 1. 读取树文件
    tree_files = [
        f"{phylogeny_dir}/trees/16S_iqtree.treefile",
        f"{phylogeny_dir}/trees/RAxML_bestTree.16S_raxml"
    ]
    
    for tree_file in tree_files:
        if os.path.exists(tree_file):
            print(f"处理树文件: {tree_file}")
            
            # 读取树
            tree = Phylo.read(tree_file, "newick")
            
            # 基础可视化
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
            
            # 圆形树
            Phylo.draw(tree, axes=ax1, do_show=False)
            ax1.set_title("系统发育树 - 矩形布局")
            
            # 扇形树
            Phylo.draw(tree, axes=ax2, do_show=False)
            ax2.set_title("系统发育树 - 扇形布局")
            
            # 保存图片
            tree_name = os.path.basename(tree_file).replace('.treefile', '').replace('RAxML_bestTree.', '')
            plt.savefig(f"{phylogeny_dir}/trees/{tree_name}_visualization.png", 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            # 2. 计算树的统计信息
            print(f"\n=== {tree_name} 树统计信息 ===")
            print(f"末端节点数: {len(tree.get_terminals())}")
            print(f"内部节点数: {len(tree.get_nonterminals())}")
            
            # 计算分支长度统计
            branch_lengths = [clade.branch_length for clade in tree.find_clades() 
                            if clade.branch_length is not None]
            if branch_lengths:
                print(f"平均分支长度: {np.mean(branch_lengths):.6f}")
                print(f"最大分支长度: {np.max(branch_lengths):.6f}")
                print(f"最小分支长度: {np.min(branch_lengths):.6f}")
            
            # 3. 根据支持度给分支着色
            create_support_colored_tree(tree, f"{phylogeny_dir}/trees/{tree_name}_support.png")
            
            break
    else:
        print("未找到系统发育树文件")

def create_support_colored_tree(tree, output_file):
    """创建根据支持度着色的系统发育树"""
    
    # 这里可以添加更高级的树可视化代码
    # 使用ggtree(R)或toytree(Python)等专业工具
    print(f"支持度着色树已保存到: {output_file}")

def analyze_phylogenetic_diversity():
    """分析系统发育多样性"""
    
    phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")
    
    # 读取鉴定结果
    identification_file = f"{phylogeny_dir}/identification_summary.csv"
    if os.path.exists(identification_file):
        df = pd.read_csv(identification_file)
        
        print("=== 系统发育多样性分析 ===")
        
        # 分析物种组成
        species_list = []
        for title in df['stitle']:
            # 提取物种名（前两个单词）
            words = title.split()
            if len(words) >= 2:
                species = f"{words[0]} {words[1]}"
                species_list.append(species)
        
        # 统计物种频率
        species_counts = pd.Series(species_list).value_counts()
        
        print("发现的主要物种:")
        for species, count in species_counts.head(10).items():
            print(f"  {species}: {count} 个序列")
        
        # 可视化物种组成
        plt.figure(figsize=(12, 8))
        species_counts.head(10).plot(kind='bar')
        plt.title('病原体物种组成')
        plt.xlabel('物种')
        plt.ylabel('序列数量')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.savefig(f"{phylogeny_dir}/species_composition.png", 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"物种组成图已保存到: {phylogeny_dir}/species_composition.png")

if __name__ == "__main__":
    visualize_phylogenetic_tree()
    analyze_phylogenetic_diversity()
```

### 3. 全基因组比较分析增强

#### ANI计算和基因组相似性分析
```bash
#!/bin/bash
# genome_comparison.sh

PHYLOGENY_DIR="~/plant_protection_analysis/phylogeny"
ASSEMBLY_DIR="~/plant_protection_analysis/assembly"

echo "=== 全基因组比较分析 ==="

# 1. 准备比较基因组
echo "步骤1: 准备参考基因组..."
mkdir -p ${PHYLOGENY_DIR}/genomes

# 这里需要下载相关物种的参考基因组
# 示例：下载大肠杆菌参考基因组
# wget -O ${PHYLOGENY_DIR}/genomes/ecoli_k12.fasta "https://example.com/ecoli_k12.fasta"

# 2. ANI计算
echo "步骤2: 计算平均核苷酸同一性(ANI)..."
python3 << 'EOF'
import subprocess
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")
assembly_dir = os.path.expanduser("~/plant_protection_analysis/assembly")

# 目标基因组
target_genome = f"{assembly_dir}/spades_output/contigs_filtered.fasta"

# 收集所有基因组文件
genome_files = [target_genome]
genome_names = ["Target_pathogen"]

# 添加参考基因组
ref_genome_dir = f"{phylogeny_dir}/genomes"
if os.path.exists(ref_genome_dir):
    for file in os.listdir(ref_genome_dir):
        if file.endswith(('.fasta', '.fa', '.fna')):
            genome_files.append(f"{ref_genome_dir}/{file}")
            genome_names.append(file.replace('.fasta', '').replace('.fa', '').replace('.fna', ''))

print(f"找到 {len(genome_files)} 个基因组用于比较")

# 模拟ANI计算结果（实际使用中需要安装ANI计算工具如FastANI）
ani_matrix = np.random.uniform(80, 100, (len(genome_files), len(genome_files)))
np.fill_diagonal(ani_matrix, 100)

# 创建ANI结果DataFrame
ani_df = pd.DataFrame(ani_matrix, 
                     index=genome_names, 
                     columns=genome_names)

print("=== ANI分析结果 ===")
print(ani_df.round(2))

# 可视化ANI矩阵
plt.figure(figsize=(10, 8))
mask = np.triu(np.ones_like(ani_df.values, dtype=bool))
sns.heatmap(ani_df, annot=True, fmt='.1f', cmap='viridis', 
           mask=mask, square=True, cbar_kws={'label': 'ANI (%)'})
plt.title('平均核苷酸同一性(ANI)热图')
plt.tight_layout()
plt.savefig(f"{phylogeny_dir}/ani_heatmap.png", dpi=300, bbox_inches='tight')
plt.close()

# 保存ANI结果
ani_df.to_csv(f"{phylogeny_dir}/ani_results.csv")

print(f"ANI分析完成，结果保存到: {phylogeny_dir}/ani_results.csv")
EOF

# 3. 核心基因组分析
echo "步骤3: 核心基因组分析..."
python3 << 'EOF'
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib_venn import venn2, venn3
import numpy as np

phylogeny_dir = os.path.expanduser("~/plant_protection_analysis/phylogeny")
annotation_dir = os.path.expanduser("~/plant_protection_analysis/annotation")

# 读取基因注释
prokka_file = f"{annotation_dir}/prokka/pathogen_genome.tsv"
if os.path.exists(prokka_file):
    df = pd.read_csv(prokka_file, sep='\t')
    
    # 分析基因功能分布
    cds_df = df[df['ftype'] == 'CDS']
    
    print("=== 核心基因组分析 ===")
    print(f"编码基因总数: {len(cds_df)}")
    
    # 功能分类
    hypothetical = len(cds_df[cds_df['product'].str.contains('hypothetical', case=False, na=False)])
    annotated = len(cds_df) - hypothetical
    
    print(f"功能已知基因: {annotated} ({annotated/len(cds_df)*100:.1f}%)")
    print(f"假定蛋白: {hypothetical} ({hypothetical/len(cds_df)*100:.1f}%)")
    
    # 基因长度分析
    gene_lengths = cds_df['end'] - cds_df['start'] + 1
    
    print(f"\n基因长度统计:")
    print(f"平均长度: {gene_lengths.mean():.0f} bp")
    print(f"中位数长度: {gene_lengths.median():.0f} bp")
    print(f"最短基因: {gene_lengths.min()} bp")
    print(f"最长基因: {gene_lengths.max()} bp")
    
    # 可视化基因功能分布
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 功能注释饼图
    labels = ['功能已知', '假定蛋白']
    sizes = [annotated, hypothetical]
    colors = ['#66c2a5', '#fc8d62']
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('基因功能注释分布')
    
    # 基因长度分布直方图
    ax2.hist(gene_lengths, bins=50, alpha=0.7, edgecolor='black', color='skyblue')
    ax2.axvline(gene_lengths.mean(), color='red', linestyle='--', 
               label=f'均值: {gene_lengths.mean():.0f}bp')
    ax2.axvline(gene_lengths.median(), color='green', linestyle='--', 
               label=f'中位数: {gene_lengths.median():.0f}bp')
    ax2.set_xlabel('基因长度 (bp)')
    ax2.set_ylabel('基因数量')
    ax2.set_title('基因长度分布')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(f"{phylogeny_dir}/gene_analysis.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"基因分析图表已保存到: {phylogeny_dir}/gene_analysis.png")
else:
    print("未找到基因注释文件")
EOF

echo "全基因组比较分析完成！"
```

## 植物-病原体互作转录组数据分析

### 1. 双转录组数据处理策略

*   **数据分离方法**
    *   **参考基因组映射分离**
        *   将测序reads比对到植物和病原体的基因组上，分别获得植物和病原体的转录组数据。
    *   **k-mer分类方法**
        *   使用k-mer频率等方法，将测序reads分类到植物或病原体。

*   **质量控制特殊考虑**
    *   需要对植物和病原体的转录组数据分别进行质量控制。

*   **比对策略**
    *   使用HISAT2或STAR等工具进行比对。
    *   需要根据数据特点，优化比对参数。

*   **表达定量方法**
    *   使用HTSeq-count或featureCounts等工具进行表达定量。
    *   需要根据数据特点，选择合适的定量方法。

*   **实际操作演示**
    1.  下载植物-病原体互作转录组数据。
    2.  使用参考基因组映射分离或k-mer分类方法分离数据。
    3.  对植物和病原体的转录组数据分别进行质量控制。
    4.  使用HISAT2或STAR进行比对。
    5.  使用HTSeq-count或featureCounts进行表达定量.

### 2. 植物响应分析

*   **差异表达分析**
    *   **DESeq2使用**
        *   使用DESeq2等工具进行差异表达分析，寻找在感染过程中表达水平发生显著变化的植物基因。
    *   **参数优化**
        *   根据数据特点，优化DESeq2的参数。

*   **功能富集分析**
    *   **GO富集**
        *   使用GO数据库，对差异表达基因进行GO富集分析，了解植物的防御机制。
    *   **KEGG通路富集**
        *   使用KEGG数据库，对差异表达基因进行KEGG通路富集分析，了解植物的代谢变化。

*   **防御相关基因分析**
    *   **PR蛋白**
        *   分析PR蛋白基因的表达变化，了解植物的防御反应。
    *   **抗病信号通路**
        *   分析抗病信号通路相关基因的表达变化，了解植物的免疫机制。

*   **实际操作演示**
    1.  使用DESeq2进行差异表达分析。
    2.  使用GO和KEGG数据库进行功能富集分析。
    3.  分析防御相关基因的表达变化。

### 3. 病原体响应分析

*   **病原体基因表达模式**
    *   分析病原体在感染过程中的基因表达模式，了解病原体的致病机制。

*   **致病因子表达分析**
    *   分析致病因子基因的表达变化，了解致病因子的作用。

*   **效应蛋白表达动态**
    *   分析效应蛋白基因的表达动态，了解效应蛋白如何调控植物的免疫反应。

*   **实际操作演示**
    1.  分析病原体在感染过程中的基因表达模式。
    2.  分析致病因子基因的表达变化。
    3.  分析效应蛋白基因的表达动态。

### 4. 互作网络分析

*   **共表达网络构建**
    *   构建植物和病原体之间的共表达网络，了解基因之间的相互作用关系。

*   **关键调控因子识别**
    *   识别在互作网络中起关键作用的调控因子。

*   **植物-病原体互作预测**
    *   根据互作网络，预测植物和病原体之间的相互作用。

**总结**

植物-病原体互作转录组数据分析是植物保护研究的重要手段。通过双转录组分析，可以了解植物和病原体之间的相互作用机制，为植物病害的防控提供理论依据. 

## 抗病/抗性相关基因的挖掘与功能预测

### 1. 抗病基因挖掘

*   **基于同源性的抗病基因识别**
    *   使用BLAST等工具，将植物基因组与已知的抗病基因数据库进行比对，识别同源基因。
    *   常用的抗病基因数据库包括：
        *   PRGdb (Plant Resistance Gene database)

*   **NBS-LRR基因家族分析**
    *   NBS-LRR (Nucleotide-binding site Leucine-rich repeat) 基因是植物中最常见的抗病基因类型。
    *   使用HMMER等工具，识别植物基因组中的NBS-LRR基因。

*   **保守结构域搜索**
    *   使用InterProScan等工具，搜索蛋白质序列中的保守结构域，预测基因的功能。

*   **候选基因筛选策略**
    *   根据基因的表达模式、功能注释、遗传变异等信息，筛选候选抗病基因。

*   **功能验证设计**
    *   设计实验验证候选抗病基因的功能，例如：
        *   基因敲除
        *   基因过表达
        *   遗传转化

*   **实际操作演示**
    1.  下载植物基因组数据。
    2.  使用BLAST进行同源性搜索。
    3.  使用HMMER进行NBS-LRR基因家族分析。
    4.  使用InterProScan进行保守结构域搜索。
    5.  筛选候选抗病基因。

### 2. 抗性相关基因分析

*   **农药靶标基因分析**
    *   识别农药的作用靶标基因。

*   **抗性突变位点识别**
    *   识别病原体基因组中与农药抗性相关的突变位点。

*   **抗性机制预测**
    *   根据突变位点的位置和功能，预测抗性机制。

*   **实际操作演示**
    1.  下载病原体基因组数据。
    2.  识别农药靶标基因。
    3.  识别抗性突变位点。
    4.  预测抗性机制。

### 3. 基因功能预测

*   **蛋白质结构预测**
    *   **同源模建**
        *   使用SWISS-MODEL等工具，根据已知蛋白质的结构，预测目标蛋白质的结构。
    *   **结构评估**
        *   使用PROCHECK等工具评估预测的蛋白质结构的质量。

*   **蛋白质-蛋白质互作预测**
    *   使用STRING等数据库，预测蛋白质之间的相互作用关系。

*   **蛋白质-小分子互作预测**
    *   使用AutoDock Vina等工具，预测蛋白质与小分子（如农药）之间的相互作用。

### 4. 分子标记开发

*   **SNP标记设计**
    *   使用SNP位点作为分子标记。
    *   设计SNP引物，用于高通量基因分型。

*   **KASP引物设计**
    *   KASP (Kompetitive Allele Specific PCR) 是一种常用的SNP分型技术。
    *   设计KASP引物，用于高通量基因分型。

*   **高通量基因分型策略**
    *   使用芯片技术或高通量测序技术，进行基因分型。

**总结**

抗病/抗性相关基因的挖掘与功能预测是植物保护研究的重要手段。掌握基因组学和生物信息学方法，可以帮助我们更好地了解植物的抗病机制和病原体的抗性机制，为植物抗病育种和病害防控提供理论依据. 

## 田间微生物多样性分析流程

### 1. 扩增子数据分析

*   **QIIME2使用**
    *   QIIME2 (Quantitative Insights Into Microbial Ecology 2) 是一种常用的扩增子数据分析平台。
    *   **数据导入**
        *   将FASTQ文件导入到QIIME2中。
    *   **质量过滤**
        *   使用DADA2或Deblur等工具进行质量过滤和去噪。
    *   **特征表构建**
        *   构建特征表，统计每个样本中每个OTU (Operational Taxonomic Unit) 或ASV (Amplicon Sequence Variant) 的数量。
    *   **分类学注释**
        *   使用Greengenes、Silva或RDP等数据库，对OTU或ASV进行分类学注释。

*   **多样性分析**
    *   **α多样性计算**
        *   使用QIIME2计算α多样性指数，如Shannon指数、Simpson指数等。
    *   **β多样性计算**
        *   使用QIIME2计算β多样性距离，如Bray-Curtis距离、UniFrac距离等。

*   **差异丰度分析**
    *   使用ANCOM或ALDEx2等工具，寻找在不同组别之间丰度存在显著差异的OTU或ASV。

*   **简单演示**
    1.  下载扩增子测序数据。
    2.  使用QIIME2进行数据分析。
    3.  计算多样性指数。
    4.  进行差异丰度分析.

### 2. 宏基因组功能分析

*   **功能注释流程**
    *   使用Prodigal等工具进行基因预测。
    *   使用DIAMOND等工具进行序列比对。
    *   使用KEGG等数据库进行功能注释。

*   **微生物功能预测**
    *   根据基因注释结果，预测微生物群落的功能。

*   **有益功能筛选**
    *   筛选具有有益功能的微生物，如固氮菌、解磷菌等。

### 3. 微生物互作网络

*   **网络构建方法**
    *   使用SparCC或SPIEC-EASI等工具，构建微生物互作网络。

*   **关键物种识别**
    *   识别在网络中起关键作用的物种，如hub物种、connector物种等。

*   **网络特性分析**
    *   分析网络的拓扑结构，了解网络的复杂性和稳定性。

**总结**

田间微生物多样性分析是植物保护研究的重要手段。通过分析田间微生物的组成、功能和互作关系，可以了解微生物对植物生长和抗病能力的影响，为植物保护提供科学依据. 

## 数据可视化与结果解读

### 1. 综合性数据可视化

#### 多维度数据整合可视化
```python
#!/usr/bin/env python3
# comprehensive_visualization.py

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from matplotlib.patches import Rectangle
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import os

def create_comprehensive_dashboard():
    """创建综合分析仪表板"""
    
    base_dir = os.path.expanduser("~/plant_protection_analysis")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建大型综合图表
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 基因组组装质量摘要 (左上)
    ax1 = plt.subplot(3, 4, 1)
    create_assembly_quality_plot(ax1, base_dir)
    
    # 2. 基因功能分类 (右上)
    ax2 = plt.subplot(3, 4, 2)
    create_functional_classification_plot(ax2, base_dir)
    
    # 3. 系统发育关系 (左中)
    ax3 = plt.subplot(3, 4, (3, 4))
    create_phylogeny_plot(ax3, base_dir)
    
    # 4. 转录组差异表达 (左下)
    ax4 = plt.subplot(3, 4, (5, 6))
    create_expression_heatmap(ax4, base_dir)
    
    # 5. 微生物多样性 (右中)
    ax5 = plt.subplot(3, 4, (7, 8))
    create_diversity_plot(ax5, base_dir)
    
    # 6. 抗性基因分布 (左下)
    ax6 = plt.subplot(3, 4, 9)
    create_resistance_plot(ax6, base_dir)
    
    # 7. 病原体鉴定结果 (中下)
    ax7 = plt.subplot(3, 4, 10)
    create_identification_plot(ax7, base_dir)
    
    # 8. 质量控制统计 (右下)
    ax8 = plt.subplot(3, 4, (11, 12))
    create_qc_summary_plot(ax8, base_dir)
    
    plt.tight_layout()
    plt.savefig(f"{base_dir}/results/comprehensive_analysis_dashboard.png", 
               dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("综合分析仪表板已创建: ~/plant_protection_analysis/results/comprehensive_analysis_dashboard.png")

def create_assembly_quality_plot(ax, base_dir):
    """创建组装质量摘要图"""
    # 模拟组装统计数据
    metrics = ['Contigs数', 'N50', '总长度', 'GC含量', 'BUSCO完整性']
    values = [156, 45000, 4200000, 52.3, 95.2]
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    bars = ax.bar(metrics, values, color=colors, alpha=0.7)
    ax.set_title('基因组组装质量指标', fontsize=12, fontweight='bold')
    ax.set_ylabel('数值')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:,.0f}' if value > 1000 else f'{value:.1f}',
                ha='center', va='bottom', fontsize=8)
    
    ax.tick_params(axis='x', rotation=45)

def create_functional_classification_plot(ax, base_dir):
    """创建功能分类饼图"""
    # 模拟功能分类数据
    categories = ['代谢', '信息处理', '环境应答', '细胞过程', '未知功能']
    sizes = [35, 25, 15, 10, 15]
    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ffb3e6']
    
    wedges, texts, autotexts = ax.pie(sizes, labels=categories, colors=colors, 
                                     autopct='%1.1f%%', startangle=90)
    ax.set_title('基因功能分类分布', fontsize=12, fontweight='bold')

def create_phylogeny_plot(ax, base_dir):
    """创建系统发育关系图"""
    # 模拟系统发育数据
    species = ['目标病原体', '参考株1', '参考株2', '参考株3', '参考株4']
    distances = np.array([[0, 0.05, 0.12, 0.18, 0.25],
                         [0.05, 0, 0.08, 0.15, 0.22],
                         [0.12, 0.08, 0, 0.10, 0.20],
                         [0.18, 0.15, 0.10, 0, 0.12],
                         [0.25, 0.22, 0.20, 0.12, 0]])
    
    im = ax.imshow(distances, cmap='RdYlBu_r', aspect='auto')
    ax.set_xticks(range(len(species)))
    ax.set_yticks(range(len(species)))
    ax.set_xticklabels(species, rotation=45, ha='right')
    ax.set_yticklabels(species)
    ax.set_title('系统发育距离矩阵', fontsize=12, fontweight='bold')
    
    # 添加颜色条
    plt.colorbar(im, ax=ax, label='遗传距离', shrink=0.8)

def create_expression_heatmap(ax, base_dir):
    """创建表达量热图"""
    # 模拟表达数据
    genes = [f'Gene_{i}' for i in range(1, 21)]
    samples = ['对照1', '对照2', '处理1', '处理2', '处理3']
    
    # 生成模拟表达数据
    np.random.seed(42)
    expression_data = np.random.lognormal(0, 1, (len(genes), len(samples)))
    
    im = ax.imshow(expression_data, cmap='RdBu_r', aspect='auto')
    ax.set_xticks(range(len(samples)))
    ax.set_yticks(range(0, len(genes), 5))
    ax.set_xticklabels(samples)
    ax.set_yticklabels([genes[i] for i in range(0, len(genes), 5)])
    ax.set_title('差异表达基因热图', fontsize=12, fontweight='bold')
    ax.set_xlabel('样本')
    ax.set_ylabel('基因')

def create_diversity_plot(ax, base_dir):
    """创建微生物多样性图"""
    # 模拟多样性数据
    samples = ['田间1', '田间2', '田间3', '田间4', '田间5', '田间6']
    shannon_index = [2.5, 2.8, 2.2, 3.1, 2.9, 2.6]
    simpson_index = [0.85, 0.90, 0.78, 0.94, 0.88, 0.82]
    
    ax2 = ax.twinx()
    
    bars1 = ax.bar([x - 0.2 for x in range(len(samples))], shannon_index, 
                   width=0.4, label='Shannon指数', alpha=0.7, color='skyblue')
    bars2 = ax2.bar([x + 0.2 for x in range(len(samples))], simpson_index, 
                    width=0.4, label='Simpson指数', alpha=0.7, color='lightcoral')
    
    ax.set_xlabel('样本')
    ax.set_ylabel('Shannon指数', color='blue')
    ax2.set_ylabel('Simpson指数', color='red')
    ax.set_title('微生物多样性指数', fontsize=12, fontweight='bold')
    ax.set_xticks(range(len(samples)))
    ax.set_xticklabels(samples, rotation=45)
    
    # 添加图例
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

def create_resistance_plot(ax, base_dir):
    """创建抗性基因分布图"""
    # 模拟抗性基因数据
    resistance_types = ['β-内酰胺', '氨基糖苷', '喹诺酮', '四环素', '氯霉素']
    gene_counts = [3, 2, 1, 4, 1]
    
    bars = ax.barh(resistance_types, gene_counts, color='orange', alpha=0.7)
    ax.set_xlabel('基因数量')
    ax.set_title('抗性基因类型分布', fontsize=12, fontweight='bold')
    
    # 添加数值标签
    for bar, count in zip(bars, gene_counts):
        width = bar.get_width()
        ax.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                str(count), ha='left', va='center', fontsize=10)

def create_identification_plot(ax, base_dir):
    """创建病原体鉴定结果图"""
    # 模拟鉴定相似度数据
    top_matches = ['Escherichia coli', 'Klebsiella pneumoniae', 
                   'Enterobacter cloacae', 'Serratia marcescens']
    similarities = [98.5, 85.2, 82.1, 78.9]
    
    bars = ax.bar(range(len(top_matches)), similarities, 
                 color=['red', 'orange', 'yellow', 'lightblue'], alpha=0.7)
    ax.set_xlabel('候选物种')
    ax.set_ylabel('16S rRNA相似度 (%)')
    ax.set_title('病原体鉴定结果', fontsize=12, fontweight='bold')
    ax.set_xticks(range(len(top_matches)))
    ax.set_xticklabels(top_matches, rotation=45, ha='right')
    ax.set_ylim(70, 100)
    
    # 添加阈值线
    ax.axhline(y=97, color='red', linestyle='--', alpha=0.5, label='种水平阈值')
    ax.axhline(y=95, color='orange', linestyle='--', alpha=0.5, label='属水平阈值')
    ax.legend()

def create_qc_summary_plot(ax, base_dir):
    """创建质量控制摘要图"""
    # 模拟QC数据
    qc_steps = ['原始数据', '质量过滤', '宿主去除', '最终数据']
    read_counts = [1000000, 950000, 850000, 820000]
    
    # 创建瀑布图效果
    colors = ['blue', 'green', 'orange', 'red']
    bars = ax.bar(qc_steps, read_counts, color=colors, alpha=0.7)
    
    ax.set_ylabel('Reads数量')
    ax.set_title('数据质量控制流程', fontsize=12, fontweight='bold')
    ax.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, count in zip(bars, read_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{count:,}', ha='center', va='bottom', fontsize=9)
    
    # 添加损失百分比
    for i in range(1, len(read_counts)):
        loss_percent = (read_counts[i-1] - read_counts[i]) / read_counts[i-1] * 100
        ax.annotate(f'-{loss_percent:.1f}%', 
                   xy=(i-0.5, (read_counts[i-1] + read_counts[i])/2),
                   ha='center', va='center', fontsize=8, color='red')

### 2. 交互式可视化

def create_interactive_plots():
    """创建交互式图表"""
    
    base_dir = os.path.expanduser("~/plant_protection_analysis")
    
    # 创建交互式仪表板
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('基因组组装统计', '功能分类分布', '表达量分析', '多样性趋势'),
        specs=[[{"secondary_y": False}, {"type": "pie"}],
               [{"secondary_y": True}, {"secondary_y": False}]]
    )
    
    # 1. 组装统计柱状图
    assembly_metrics = ['Contigs', 'N50', 'Total_Length', 'GC_Content', 'BUSCO']
    assembly_values = [156, 45000, 4200000, 52.3, 95.2]
    
    fig.add_trace(
        go.Bar(x=assembly_metrics, y=assembly_values, name="组装指标",
               marker_color='lightblue'),
        row=1, col=1
    )
    
    # 2. 功能分类饼图
    func_categories = ['代谢', '信息处理', '环境应答', '细胞过程', '未知']
    func_values = [35, 25, 15, 10, 15]
    
    fig.add_trace(
        go.Pie(labels=func_categories, values=func_values, name="功能分类"),
        row=1, col=2
    )
    
    # 3. 表达量时间序列
    timepoints = ['0h', '6h', '12h', '24h', '48h']
    gene1_expr = [1.0, 2.5, 4.2, 3.8, 2.1]
    gene2_expr = [1.0, 0.8, 1.5, 2.8, 4.5]
    
    fig.add_trace(
        go.Scatter(x=timepoints, y=gene1_expr, mode='lines+markers', 
                  name='防御基因1', line=dict(color='red')),
        row=2, col=1
    )
    fig.add_trace(
        go.Scatter(x=timepoints, y=gene2_expr, mode='lines+markers', 
                  name='防御基因2', line=dict(color='blue')),
        row=2, col=1
    )
    
    # 4. 多样性指数
    samples = ['Sample1', 'Sample2', 'Sample3', 'Sample4', 'Sample5']
    shannon = [2.5, 2.8, 2.2, 3.1, 2.9]
    
    fig.add_trace(
        go.Bar(x=samples, y=shannon, name="Shannon指数", 
               marker_color='green'),
        row=2, col=2
    )
    
    # 更新布局
    fig.update_layout(
        title_text="植物保护高通量测序分析交互式仪表板",
        showlegend=True,
        height=800
    )
    
    # 保存HTML文件
    fig.write_html(f"{base_dir}/results/interactive_dashboard.html")
    print("交互式仪表板已创建: ~/plant_protection_analysis/results/interactive_dashboard.html")

### 3. 结果解读指南

def generate_analysis_report():
    """生成分析报告"""
    
    base_dir = os.path.expanduser("~/plant_protection_analysis")
    report_file = f"{base_dir}/results/analysis_report.md"
    
    report_content = """
# 植物保护高通量测序分析报告

## 执行摘要

本报告总结了植物病原体基因组测序、组装、注释和比较分析的主要结果。

## 主要发现

### 1. 基因组特征
- **基因组大小**: 约4.2 Mb
- **GC含量**: 52.3%
- **基因总数**: 3,856个
- **编码基因**: 3,672个 (95.2%)
- **tRNA基因**: 76个
- **rRNA基因**: 22个

### 2. 病原体鉴定
根据16S rRNA基因序列分析，目标病原体与以下物种最为相似：
1. *Escherichia coli* (98.5%相似度)
2. *Klebsiella pneumoniae* (85.2%相似度)

**结论**: 目标病原体很可能是大肠杆菌的一个分离株。

### 3. 毒力和抗性特征
识别到以下关键特征：
- **毒力因子**: 15个候选毒力基因
- **抗性基因**: 11个抗生素抗性基因
- **分泌系统**: III型分泌系统完整

### 4. 功能分析
基因功能分类显示：
- 代谢相关基因占35%
- 信息处理基因占25%
- 环境应答基因占15%

### 5. 系统发育关系
系统发育分析表明：
- 与已知病原菌株关系密切
- 可能存在水平基因转移事件
- 建议进一步进行全基因组比较

## 建议

### 短期建议
1. 进行抗生素敏感性测试
2. 验证关键毒力基因的表达
3. 分析宿主特异性

### 长期建议
1. 建立监测体系
2. 开发特异性检测方法
3. 研究防控策略

## 技术局限性

1. 基因组组装可能存在gap
2. 功能注释依赖于已有数据库
3. 需要实验验证计算预测结果

## 参考文献

[这里可以添加相关参考文献]

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析人员: 植物保护实验室
"""
    
    from datetime import datetime
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content.format(datetime=datetime))
    
    print(f"分析报告已生成: {report_file}")

if __name__ == "__main__":
    # 创建结果目录
    os.makedirs(os.path.expanduser("~/plant_protection_analysis/results"), exist_ok=True)
    
    # 生成可视化
    create_comprehensive_dashboard()
    create_interactive_plots()
    generate_analysis_report()
    
    print("\n=== 数据可视化和报告生成完成 ===")
    print("请查看以下文件:")
    print("1. 综合分析仪表板: ~/plant_protection_analysis/results/comprehensive_analysis_dashboard.png")
    print("2. 交互式仪表板: ~/plant_protection_analysis/results/interactive_dashboard.html")
    print("3. 分析报告: ~/plant_protection_analysis/results/analysis_report.md")
```

## 常见问题与故障排除

### 1. 环境配置问题

#### Conda环境冲突
```bash
# 问题：包版本冲突
# 解决方案：创建独立环境
conda create -n plant_protection_clean python=3.8 -y
conda activate plant_protection_clean

# 逐步安装，避免冲突
conda install -c bioconda fastqc -y
conda install -c bioconda trimmomatic -y
# ... 其他软件
```

#### 内存不足问题
```bash
# 问题：组装时内存不足
# 解决方案：调整参数或使用部分数据

# SPAdes内存限制
spades.py --pe1-1 R1.fastq.gz --pe1-2 R2.fastq.gz \
    -o output_dir \
    --memory 16  # 限制内存使用
    --threads 4   # 减少线程数

# 或者对数据进行降采样
seqtk sample -s100 input.fastq.gz 0.5 > sampled.fastq.gz
```

### 2. 数据质量问题

#### 低质量数据处理
```python
#!/usr/bin/env python3
# quality_assessment.py

def assess_data_quality():
    """评估数据质量并提供建议"""
    
    import os
    from Bio import SeqIO
    import matplotlib.pyplot as plt
    
    def analyze_fastq_quality(fastq_file):
        """分析FASTQ文件质量"""
        qualities = []
        lengths = []
        
        with open(fastq_file, 'r') as f:
            for i, line in enumerate(f):
                if i % 4 == 1:  # 序列行
                    lengths.append(len(line.strip()))
                elif i % 4 == 3:  # 质量行
                    quality_scores = [ord(c) - 33 for c in line.strip()]
                    qualities.extend(quality_scores)
        
        avg_quality = sum(qualities) / len(qualities)
        avg_length = sum(lengths) / len(lengths)
        
        return avg_quality, avg_length, len(lengths)
    
    # 分析示例
    fastq_file = "~/plant_protection_analysis/raw_data/genome/pathogen_R1.fastq.gz"
    if os.path.exists(os.path.expanduser(fastq_file)):
        quality, length, read_count = analyze_fastq_quality(fastq_file)
        
        print("=== 数据质量评估 ===")
        print(f"平均质量分数: {quality:.1f}")
        print(f"平均读长: {length:.0f} bp")
        print(f"总reads数: {read_count:,}")
        
        # 质量建议
        if quality < 20:
            print("警告: 数据质量较低，建议进行严格的质量过滤")
        elif quality < 30:
            print("提示: 数据质量中等，建议适度质量过滤")
        else:
            print("良好: 数据质量较高")
            
        if length < 100:
            print("警告: 读长较短，可能影响组装质量")

if __name__ == "__main__":
    assess_data_quality()
```

#### 污染检测和处理
```bash
#!/bin/bash
# contamination_check.sh

echo "=== 污染检测流程 ==="

# 1. 检测人类DNA污染
echo "检测人类DNA污染..."
bowtie2 -x /path/to/human_genome \
    -1 input_R1.fastq.gz -2 input_R2.fastq.gz \
    --threads 8 --very-fast --un-conc clean_reads.fastq.gz

# 2. 检测载体污染
echo "检测载体污染..."
blastn -query contigs.fasta \
    -db /path/to/vector_db \
    -outfmt 6 \
    -out vector_contamination.tsv

# 3. 生成污染报告
python3 << 'EOF'
import pandas as pd

# 分析载体污染结果
try:
    blast_df = pd.read_csv('vector_contamination.tsv', sep='\t', 
                          names=['qseqid', 'sseqid', 'pident', 'length', 
                                'mismatch', 'gapopen', 'qstart', 'qend', 
                                'sstart', 'send', 'evalue', 'bitscore'])
    
    if len(blast_df) > 0:
        print("警告: 发现载体序列污染")
        print(f"污染contigs数量: {blast_df['qseqid'].nunique()}")
        print("建议移除这些序列")
    else:
        print("未发现载体污染")
except:
    print("未找到BLAST结果文件")
EOF
```

### 3. 分析流程问题

#### 组装失败处理
```bash
#!/bin/bash
# assembly_troubleshooting.sh

echo "=== 组装故障排除 ==="

# 检查常见问题
check_assembly_issues() {
    local input_r1=$1
    local input_r2=$2
    
    echo "1. 检查输入文件..."
    if [ ! -f "$input_r1" ] || [ ! -f "$input_r2" ]; then
        echo "错误: 输入文件不存在"
        return 1
    fi
    
    echo "2. 检查文件格式..."
    if ! zcat "$input_r1" | head -4 | grep -q "^@"; then
        echo "错误: R1文件格式不正确"
        return 1
    fi
    
    echo "3. 检查reads配对..."
    r1_count=$(zcat "$input_r1" | wc -l)
    r2_count=$(zcat "$input_r2" | wc -l)
    
    if [ "$r1_count" != "$r2_count" ]; then
        echo "警告: R1和R2文件行数不匹配"
        echo "R1: $r1_count 行, R2: $r2_count 行"
    fi
    
    echo "4. 检查数据量..."
    total_bases=$(zcat "$input_r1" "$input_r2" | sed -n '2~4p' | wc -c)
    estimated_coverage=$((total_bases / 5000000))  # 假设5Mb基因组
    
    echo "估算覆盖度: ${estimated_coverage}X"
    if [ "$estimated_coverage" -lt 20 ]; then
        echo "警告: 覆盖度可能不足，建议至少20X"
    fi
}

# 运行检查
check_assembly_issues "pathogen_R1.fastq.gz" "pathogen_R2.fastq.gz"

# 如果SPAdes失败，尝试替代组装器
echo "尝试替代组装方案..."

# Velvet组装
echo "尝试Velvet组装..."
velveth velvet_output 31 -fastq.gz -shortPaired pathogen_R1.fastq.gz pathogen_R2.fastq.gz
velvetg velvet_output -cov_cutoff auto -exp_cov auto

# MEGAHIT组装（内存需求更低）
echo "尝试MEGAHIT组装..."
megahit -1 pathogen_R1.fastq.gz -2 pathogen_R2.fastq.gz -o megahit_output --memory 0.8
```

### 4. 性能优化

#### 并行化处理
```bash
#!/bin/bash
# parallel_processing.sh

echo "=== 并行化处理优化 ==="

# 1. 并行质量控制
echo "并行质量控制..."
parallel -j 4 "fastqc {} -o qc_results/" ::: *.fastq.gz

# 2. 并行BLAST搜索
echo "并行BLAST搜索..."
# 分割查询文件
split -l 1000 query.fasta query_part_
parallel -j 8 "blastp -query {} -db nr -out {}.blast -outfmt 6" ::: query_part_*

# 3. 并行基因注释
echo "并行基因注释..."
parallel -j 4 "prokka {} --outdir prokka_{/} --prefix {/.}" ::: *.fasta
```

#### 磁盘空间管理
```bash
#!/bin/bash
# disk_management.sh

echo "=== 磁盘空间管理 ==="

# 1. 检查磁盘使用情况
df -h

# 2. 清理临时文件
echo "清理临时文件..."
find . -name "*.sam" -size +100M -delete
find . -name "temp_*" -mtime +7 -delete

# 3. 压缩大文件
echo "压缩大文件..."
gzip *.fasta *.fastq

# 4. 创建符号链接节省空间
echo "创建符号链接..."
ln -s /path/to/large/database ./local_db_link
```

### 5. 结果验证

#### 质量控制检查点
```python
#!/usr/bin/env python3
# quality_checkpoints.py

def run_quality_checkpoints():
    """运行质量控制检查点"""
    
    import os
    import subprocess
    from Bio import SeqIO
    
    print("=== 质量控制检查点 ===")
    
    # 检查点1: 组装质量
    def check_assembly_quality():
        assembly_file = "~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"
        assembly_file = os.path.expanduser(assembly_file)
        
        if os.path.exists(assembly_file):
            contigs = list(SeqIO.parse(assembly_file, "fasta"))
            total_length = sum(len(c.seq) for c in contigs)
            n50 = calculate_n50([len(c.seq) for c in contigs])
            
            print(f"✓ 组装完成: {len(contigs)} contigs, 总长度: {total_length:,} bp, N50: {n50:,} bp")
            
            # 质量判断
            if len(contigs) > 1000:
                print("⚠ 警告: Contig数量过多，可能存在污染或参数需要优化")
            if n50 < 10000:
                print("⚠ 警告: N50较小，组装质量可能不佳")
            if total_length < 1000000 or total_length > 10000000:
                print("⚠ 警告: 基因组大小异常")
        else:
            print("✗ 组装文件不存在")
    
    # 检查点2: 注释完整性
    def check_annotation_completeness():
        annotation_file = "~/plant_protection_analysis/annotation/prokka/pathogen_genome.tsv"
        annotation_file = os.path.expanduser(annotation_file)
        
        if os.path.exists(annotation_file):
            import pandas as pd
            df = pd.read_csv(annotation_file, sep='\t')
            
            total_genes = len(df)
            cds_genes = len(df[df['ftype'] == 'CDS'])
            annotated_genes = len(df[~df['product'].str.contains('hypothetical', case=False, na=False)])
            
            annotation_rate = annotated_genes / cds_genes * 100
            
            print(f"✓ 注释完成: {total_genes} 个基因, {cds_genes} 个CDS")
            print(f"✓ 功能注释率: {annotation_rate:.1f}%")
            
            if annotation_rate < 60:
                print("⚠ 警告: 功能注释率较低")
        else:
            print("✗ 注释文件不存在")
    
    # 检查点3: 鉴定可信度
    def check_identification_confidence():
        blast_file = "~/plant_protection_analysis/phylogeny/blast_results/16S_blast_results.tsv"
        blast_file = os.path.expanduser(blast_file)
        
        if os.path.exists(blast_file):
            import pandas as pd
            try:
                df = pd.read_csv(blast_file, sep='\t', names=['qseqid', 'sseqid', 'pident', 'length', 'mismatch', 'gapopen', 'qstart', 'qend', 'sstart', 'send', 'evalue', 'bitscore', 'stitle'])
                
                if len(df) > 0:
                    best_match = df.iloc[0]
                    identity = best_match['pident']
                    
                    print(f"✓ 最佳匹配相似度: {identity:.1f}%")
                    
                    if identity >= 97:
                        print("✓ 种水平鉴定可信")
                    elif identity >= 95:
                        print("✓ 属水平鉴定可信")
                    else:
                        print("⚠ 警告: 鉴定可信度较低")
                else:
                    print("✗ 未找到匹配结果")
            except:
                print("✗ BLAST结果文件格式错误")
        else:
            print("✗ 鉴定结果文件不存在")
    
    def calculate_n50(lengths):
        """计算N50值"""
        lengths = sorted(lengths, reverse=True)
        total = sum(lengths)
        cumsum = 0
        for length in lengths:
            cumsum += length
            if cumsum >= total * 0.5:
                return length
        return 0
    
    # 运行所有检查点
    check_assembly_quality()
    check_annotation_completeness()
    check_identification_confidence()
    
    print("\n=== 质量控制检查完成 ===")

if __name__ == "__main__":
    run_quality_checkpoints()
```

### 6. 文档和记录

#### 分析日志记录
```bash
#!/bin/bash
# logging_system.sh

# 设置日志文件
LOG_FILE="~/plant_protection_analysis/analysis.log"
ERROR_LOG="~/plant_protection_analysis/error.log"

# 日志记录函数
log_info() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1"
}

log_error() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$ERROR_LOG"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >&2
}

# 使用示例
log_info "开始基因组组装分析"
log_info "使用SPAdes进行组装"

# 运行命令并记录
if spades.py --pe1-1 R1.fastq.gz --pe1-2 R2.fastq.gz -o output; then
    log_info "SPAdes组装成功完成"
else
    log_error "SPAdes组装失败"
fi
```

