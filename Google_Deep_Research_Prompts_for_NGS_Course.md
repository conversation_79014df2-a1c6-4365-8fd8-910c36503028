# 高通量测序原理与数据分析课程 - Google Deep Research 提示词

## 专题一：高通量测序技术概论

### 核心问题：

**问题1：测序技术的演进历程与技术突破**
- 从第一代到第三代测序技术的关键技术突破是什么？
- 每一代技术解决了什么核心问题，又带来了什么新挑战？

**问题2：主流测序平台的技术原理与性能比较**
- Illumina、Ion Torrent、PacBio、Oxford Nanopore的核心技术原理有何不同？
- 在准确率、读长、通量、成本方面如何权衡选择？

**问题3：测序成本变化趋势与技术可及性**
- 测序成本下降的驱动因素是什么？未来趋势如何？
- 技术民主化对科研和临床应用产生了什么影响？

**问题4：新兴测序技术与未来发展方向**
- 第四代测序技术的发展现状如何？
- 哪些新技术可能颠覆现有的测序格局？

**问题5：测序技术在不同应用场景中的选择策略**
- 如何根据研究目标选择最适合的测序平台？
- 临床应用与科研应用在技术选择上有何不同考量？

### Deep Research Prompts：

**Prompt 1.1 - 测序技术演进分析：**
"Analyze the evolutionary trajectory of DNA sequencing technologies from Sanger sequencing to current third-generation platforms. Examine the key technological breakthroughs that enabled each generation transition, including the scientific challenges each generation solved and the new limitations they introduced. Focus on the timeline of major innovations, patent landscapes, and how each technological leap impacted the scale and scope of genomics research. Include analysis of failed or abandoned approaches and lessons learned."

**Prompt 1.2 - 平台技术原理深度比较：**
"Conduct a detailed technical comparison of major sequencing platforms (Illumina SBS, Ion Torrent semiconductor, PacBio SMRT, Oxford Nanopore). Examine the fundamental physical and chemical principles underlying each technology, including signal detection mechanisms, error profiles, and throughput limitations. Analyze recent technical improvements in each platform, performance benchmarking studies, and head-to-head comparisons in real-world applications. Include discussion of platform-specific biases and their impact on data interpretation."

**Prompt 1.3 - 测序经济学与市场动态：**
"Investigate the economics of DNA sequencing, including cost reduction trends, market dynamics, and factors driving accessibility. Analyze the relationship between technological improvements and cost reductions, examine the role of competition and market consolidation, and evaluate the impact of sequencing costs on research democratization. Include analysis of total cost of ownership beyond per-base costs, regional market differences, and predictions for future cost trajectories."

**Prompt 1.4 - 新兴技术前沿探索：**
"Explore emerging and next-generation sequencing technologies beyond current third-generation platforms. Investigate protein sequencing, direct RNA sequencing, epigenetic modification detection, and novel single-molecule approaches. Examine the technical challenges being addressed, proof-of-concept demonstrations, and potential timeline for commercialization. Include analysis of venture capital investments, patent filings, and academic research trends in sequencing innovation."

**Prompt 1.5 - 应用导向的技术选择框架：**
"Develop a comprehensive framework for selecting appropriate sequencing technologies based on application requirements. Analyze decision factors including sample type, target genome size, required accuracy, budget constraints, and turnaround time. Examine case studies from clinical diagnostics, population genomics, agricultural genomics, and environmental monitoring. Include cost-benefit analyses and recommendations for different use cases."

---

## 专题二：测序数据质量控制与预处理

### 核心问题：

**问题1：测序错误的来源、类型与平台特异性**
- 不同测序平台的错误模式有何特点？
- 系统性错误与随机错误如何区分和处理？

**问题2：质量评估指标体系与标准化**
- 现有质量评估指标的科学依据和局限性是什么？
- 如何建立跨平台、跨应用的质量标准？

**问题3：数据预处理策略的优化与自动化**
- 不同预处理步骤对下游分析的影响如何量化？
- 如何实现智能化、自适应的预处理流程？

**问题4：质控工具的算法原理与性能比较**
- 主流质控工具的核心算法有何差异？
- 在不同数据类型和规模下的性能表现如何？

**问题5：新兴技术带来的质控挑战与解决方案**
- 长读长、单细胞等新技术的质控需求有何特殊性？
- 如何应对大规模数据处理的计算挑战？

### Deep Research Prompts：

**Prompt 2.1 - 测序错误模式深度分析：**
"Conduct a comprehensive analysis of sequencing error patterns across different NGS platforms (Illumina, Ion Torrent, PacBio, Oxford Nanopore). Investigate the molecular and technical sources of errors, including systematic biases, random errors, and platform-specific artifacts. Examine how error rates vary with read position, sequence context, GC content, and homopolymer regions. Include analysis of error correction algorithms and their effectiveness in different scenarios."

**Prompt 2.2 - 质量评估标准化研究：**
"Investigate the development and standardization of sequencing data quality metrics. Analyze the scientific basis of Phred quality scores, per-base quality distributions, and aggregate quality measures. Examine efforts to standardize quality assessment across platforms and applications, including international standards development and best practice guidelines. Include evaluation of novel quality metrics for emerging sequencing technologies."

**Prompt 2.3 - 预处理算法优化分析：**
"Examine the algorithmic foundations and optimization strategies for sequencing data preprocessing. Analyze adapter trimming algorithms, quality filtering approaches, and contamination removal methods. Investigate the impact of different preprocessing parameters on downstream analysis accuracy, including variant calling, assembly, and expression quantification. Include benchmarking studies and recommendations for parameter optimization."

**Prompt 2.4 - 质控工具生态系统评估：**
"Evaluate the current ecosystem of sequencing data quality control tools, including FastQC, MultiQC, Trimmomatic, Cutadapt, and emerging alternatives. Analyze their computational efficiency, accuracy, and usability across different data types and scales. Examine integration capabilities, workflow compatibility, and community adoption patterns. Include assessment of cloud-based and containerized solutions."

**Prompt 2.5 - 新兴技术质控创新：**
"Explore quality control challenges and innovations for emerging sequencing technologies and applications. Investigate QC approaches for long-read sequencing, single-cell sequencing, spatial transcriptomics, and real-time sequencing. Examine machine learning applications in quality assessment, adaptive preprocessing, and automated parameter optimization. Include analysis of scalability solutions for population-scale sequencing projects."

---

## 专题三：基因组测序(DNA-seq)数据分析

### 核心问题：

**问题1：参考基因组比对算法的演进与优化**
- BWA、Bowtie2等主流比对工具的算法原理有何差异？
- 如何针对不同应用场景选择和优化比对策略？

**问题2：变异检测方法学的准确性与可靠性**
- SNP、Indel、结构变异检测的技术挑战是什么？
- 如何平衡检测敏感性与特异性？

**问题3：基因组组装策略与复杂区域解析**
- 短读长与长读长组装的优势互补如何实现？
- 重复序列、杂合区域等复杂结构如何有效解析？

**问题4：变异注释与功能预测的准确性**
- 现有变异注释工具的局限性在哪里？
- 如何提高功能预测的准确性和临床相关性？

**问题5：大规模基因组数据的分析挑战与解决方案**
- 人群级别基因组分析面临哪些计算和存储挑战？
- 如何实现高效的并行化和云计算解决方案？

### Deep Research Prompts：

**Prompt 3.1 - 比对算法核心技术分析：**
"Investigate the algorithmic foundations of genome alignment tools, focusing on BWA-MEM, Bowtie2, Minimap2, and emerging alternatives. Analyze the underlying data structures (BWT, FM-index, minimizers), search strategies, and optimization techniques. Examine performance characteristics across different genome sizes, read lengths, and error rates. Include analysis of memory usage, computational complexity, and parallelization strategies."

**Prompt 3.2 - 变异检测方法学进展：**
"Examine the evolution and current state of variant calling methodologies, including SNP, indel, and structural variant detection. Analyze the statistical frameworks underlying tools like GATK HaplotypeCaller, FreeBayes, and DeepVariant. Investigate machine learning approaches, ensemble methods, and the integration of multiple evidence types. Include benchmarking studies using gold standard datasets and real-world performance evaluations."

**Prompt 3.3 - 基因组组装技术革新：**
"Analyze advances in genome assembly technologies and methodologies, particularly the integration of short and long-read sequencing. Examine assembly algorithms (OLC, string graphs, de Bruijn graphs), scaffolding strategies, and gap-filling approaches. Investigate the challenges of assembling complex genomes, including repetitive regions, heterozygosity, and polyploidy. Include evaluation of assembly quality metrics and validation approaches."

**Prompt 3.4 - 变异注释与解释创新：**
"Explore the current landscape of variant annotation and functional prediction tools. Analyze the integration of multiple databases (ClinVar, gnomAD, COSMIC), prediction algorithms (SIFT, PolyPhen, CADD), and interpretation frameworks. Investigate machine learning approaches for variant pathogenicity prediction and the challenges of variant interpretation in diverse populations. Include analysis of clinical decision support systems."

**Prompt 3.5 - 大规模基因组分析架构：**
"Investigate computational architectures and methodologies for population-scale genome analysis. Examine distributed computing frameworks, cloud-based solutions, and workflow management systems. Analyze data storage strategies, compression techniques, and data sharing protocols. Include evaluation of cost-effectiveness, scalability, and security considerations for large-scale genomics projects."

---

## 专题四：转录组测序(RNA-seq)数据分析

### 核心问题：

**问题1：RNA-seq实验设计与技术选择策略**
- 不同RNA-seq技术（bulk、single-cell、spatial）的适用场景是什么？
- 如何设计实验以最大化检测效力并控制混杂因素？

**问题2：转录本比对与定量方法的准确性**
- 基于比对与无比对的定量方法各有什么优缺点？
- 如何处理可变剪接和转录本异构体的定量挑战？

**问题3：差异表达分析的统计方法学**
- DESeq2、edgeR等工具的统计假设和适用条件是什么？
- 如何处理批次效应、技术噪音和生物学变异？

**问题4：功能注释与通路分析的可靠性**
- 现有功能富集分析方法的局限性在哪里？
- 如何整合多层次的功能信息进行系统性解释？

**问题5：RNA-seq数据的整合分析与多组学融合**
- 如何有效整合转录组与其他组学数据？
- 时间序列和空间转录组数据的分析挑战是什么？

### Deep Research Prompts：

**Prompt 4.1 - RNA-seq技术演进与实验设计：**
"Analyze the evolution of RNA-seq technologies from bulk to single-cell to spatial transcriptomics. Examine experimental design principles, including sample size determination, replication strategies, and confounding factor control. Investigate the impact of library preparation methods, sequencing depth, and read length on data quality and downstream analysis. Include comparison of different RNA-seq protocols and their suitability for various research questions."

**Prompt 4.2 - 转录本定量方法学比较：**
"Investigate the methodological foundations of RNA-seq quantification approaches, comparing alignment-based (HISAT2/STAR + StringTie/Cufflinks) versus alignment-free (Salmon, Kallisto) methods. Analyze the accuracy of transcript-level quantification, isoform detection, and novel transcript discovery. Examine the challenges of quantifying lowly expressed genes, pseudogenes, and repetitive elements. Include benchmarking studies and recommendations for different applications."

**Prompt 4.3 - 差异表达分析统计框架：**
"Examine the statistical foundations of differential expression analysis tools, focusing on DESeq2, edgeR, limma-voom, and emerging methods. Analyze the assumptions underlying negative binomial models, normalization strategies, and multiple testing correction approaches. Investigate methods for handling complex experimental designs, batch effects, and confounding variables. Include evaluation of false discovery rate control and power analysis considerations."

**Prompt 4.4 - 功能分析与系统生物学整合：**
"Explore advanced approaches for functional interpretation of RNA-seq data beyond traditional gene set enrichment analysis. Investigate network-based methods, pathway topology analysis, and integration of regulatory information. Examine the use of prior biological knowledge, protein-protein interaction networks, and regulatory databases. Include analysis of tools for identifying transcriptional regulators and regulatory modules."

**Prompt 4.5 - 多组学整合与时空转录组学：**
"Investigate methodologies for integrating RNA-seq data with other omics layers (genomics, epigenomics, proteomics, metabolomics). Examine approaches for multi-modal data integration, including dimensionality reduction, clustering, and causal inference methods. Analyze the emerging field of spatial transcriptomics and temporal RNA-seq analysis. Include evaluation of computational frameworks for multi-omics data integration and interpretation."

---

## 专题五：表观基因组测序数据分析

### 核心问题：

**问题1：表观基因组测序技术的原理与应用范围**
- ChIP-seq、ATAC-seq、WGBS等技术的分子机制有何差异？
- 不同表观修饰检测技术的优势和局限性是什么？

**问题2：峰检测与染色质状态分析的算法原理**
- MACS2等峰检测工具的统计模型是什么？
- 如何准确识别和分类不同类型的调控元件？

**问题3：DNA甲基化分析的方法学挑战**
- 全基因组甲基化测序的技术难点在哪里？
- 如何处理甲基化数据的偏差和批次效应？

**问题4：单细胞表观基因组学的技术突破**
- 单细胞水平的表观修饰检测面临哪些技术挑战？
- 如何解析细胞异质性和发育轨迹中的表观调控？

**问题5：多组学整合中的表观调控机制解析**
- 如何整合表观基因组与转录组、基因组数据？
- 表观修饰与基因表达调控的因果关系如何建立？

### Deep Research Prompts：

**Prompt 5.1 - 表观基因组技术原理深度解析：**
"Analyze the molecular and technical foundations of major epigenomic sequencing technologies including ChIP-seq, ATAC-seq, FAIRE-seq, NOMe-seq, and bisulfite sequencing variants. Examine the biochemical principles, experimental protocols, and technical limitations of each approach. Investigate recent technological innovations such as CUT&RUN, CUT&Tag, and enzymatic methyl-seq. Include analysis of factors affecting data quality and reproducibility."

**Prompt 5.2 - 峰检测与调控元件识别算法：**
"Investigate the algorithmic foundations of peak calling and regulatory element identification in epigenomic data. Analyze the statistical models underlying tools like MACS2, HOMER, and SICER, including background modeling, significance testing, and multiple testing correction. Examine approaches for identifying different types of peaks (sharp vs broad), handling input controls, and integrating multiple datasets. Include evaluation of novel machine learning approaches for peak detection."

**Prompt 5.3 - DNA甲基化分析方法学：**
"Examine the computational methodologies for analyzing DNA methylation data from whole-genome bisulfite sequencing (WGBS), reduced representation bisulfite sequencing (RRBS), and targeted approaches. Investigate alignment challenges, methylation calling algorithms, and differential methylation analysis methods. Analyze approaches for identifying differentially methylated regions (DMRs), methylation patterns, and allele-specific methylation. Include assessment of tools like Bismark, methylKit, and DSS."

**Prompt 5.4 - 单细胞表观基因组学前沿：**
"Explore the emerging field of single-cell epigenomics, including scChIP-seq, scATAC-seq, scBS-seq, and multi-modal approaches. Investigate the technical challenges of single-cell epigenomic profiling, including low signal-to-noise ratios, sparsity, and technical artifacts. Examine computational methods for dimensionality reduction, clustering, and trajectory inference in single-cell epigenomic data. Include analysis of cell type identification and regulatory network reconstruction approaches."

**Prompt 5.5 - 表观基因组多组学整合策略：**
"Investigate methodologies for integrating epigenomic data with other omics layers to understand gene regulation. Examine approaches for correlating histone modifications, DNA methylation, chromatin accessibility, and gene expression. Analyze methods for identifying regulatory networks, enhancer-promoter interactions, and chromatin domains. Include evaluation of causal inference methods and machine learning approaches for predicting gene expression from epigenomic features."

---

## 专题六：单细胞测序技术与数据分析

### 核心问题：

**问题1：单细胞分离与测序技术的原理与选择**
- 10x Genomics、Drop-seq、Smart-seq等技术的优缺点是什么？
- 如何根据研究目标选择合适的单细胞技术平台？

**问题2：单细胞数据的质控与预处理挑战**
- 单细胞数据特有的技术噪音和偏差如何识别和校正？
- 如何处理dropout事件和批次效应？

**问题3：细胞聚类与类型鉴定的方法学**
- 不同聚类算法在单细胞数据上的表现如何？
- 如何准确进行细胞类型注释和新细胞类型发现？

**问题4：细胞轨迹与发育分析的计算方法**
- 拟时序分析的算法原理和适用条件是什么？
- 如何重构细胞分化轨迹和识别关键转录调控因子？

**问题5：单细胞多组学与空间转录组学**
- 如何整合单细胞多组学数据进行系统性分析？
- 空间转录组学技术的发展现状和分析挑战是什么？

### Deep Research Prompts：

**Prompt 6.1 - 单细胞技术平台比较分析：**
"Conduct a comprehensive comparison of single-cell RNA sequencing platforms including 10x Genomics Chromium, Drop-seq, inDrop, Smart-seq2/3, and emerging technologies like Parse Biosciences and BD Rhapsody. Analyze the technical principles, throughput capabilities, sensitivity, cost-effectiveness, and data quality characteristics. Investigate the impact of different protocols on gene detection, cell capture efficiency, and downstream analysis. Include evaluation of full-length versus 3'/5' end sequencing approaches."

**Prompt 6.2 - 单细胞数据质控与标准化：**
"Investigate quality control and normalization methodologies specific to single-cell RNA-seq data. Examine approaches for identifying low-quality cells, doublets, and ambient RNA contamination. Analyze normalization methods including CPM, TPM, SCTransform, and their impact on downstream analysis. Investigate batch effect correction methods like Harmony, Seurat integration, and scVI. Include evaluation of metrics for assessing data quality and normalization effectiveness."

**Prompt 6.3 - 细胞聚类与类型识别算法：**
"Analyze computational methods for cell clustering and type identification in single-cell data. Examine clustering algorithms including Louvain, Leiden, and hierarchical clustering, along with their parameter optimization strategies. Investigate dimensionality reduction techniques (PCA, t-SNE, UMAP) and their impact on clustering results. Analyze automated cell type annotation methods using reference datasets and marker gene databases. Include evaluation of clustering validation metrics and stability assessment."

**Prompt 6.4 - 轨迹推断与时间序列分析：**
"Explore computational methods for trajectory inference and pseudotime analysis in single-cell data. Investigate algorithms like Monocle, PAGA, Slingshot, and Velocyto for reconstructing developmental trajectories and cell state transitions. Examine RNA velocity analysis for predicting future cell states and identifying regulatory dynamics. Analyze methods for identifying branch points, terminal states, and driver genes along trajectories. Include evaluation of trajectory inference accuracy and biological validation approaches."

**Prompt 6.5 - 单细胞多组学与空间分析：**
"Investigate emerging technologies and computational methods for single-cell multi-omics and spatial transcriptomics. Examine platforms like CITE-seq, ATAC-seq, methylation profiling, and their integration strategies. Analyze spatial transcriptomics technologies including Visium, Slide-seq, MERFISH, and seqFISH. Investigate computational frameworks for multi-modal data integration, spatial pattern analysis, and cell-cell communication inference. Include evaluation of deconvolution methods and spatial clustering algorithms."

---

## 专题七：宏基因组测序与数据分析

### 核心问题：

**问题1：宏基因组测序策略与实验设计**
- 16S rRNA测序与shotgun宏基因组测序的适用场景是什么？
- 如何设计宏基因组实验以最大化物种发现和功能解析？

**问题2：宏基因组组装与分箱算法**
- 复杂微生物群落的组装面临哪些技术挑战？
- 不同分箱策略在物种分离和基因组重建中的效果如何？

**问题3：物种分类与功能注释的准确性**
- 现有分类学数据库的覆盖度和准确性如何？
- 如何提高低丰度物种和新物种的检测能力？

**问题4：微生物群落的生态学分析**
- 如何量化和比较不同样本间的微生物多样性？
- 微生物群落结构与环境因子的关联性如何分析？

**问题5：宿主-微生物组相互作用机制**
- 如何解析微生物组与宿主健康/疾病的因果关系？
- 微生物代谢网络与宿主生理功能的整合分析方法是什么？

### Deep Research Prompts：

**Prompt 7.1 - 宏基因组测序技术与策略：**
"Analyze the current landscape of metagenomic sequencing approaches, comparing 16S rRNA amplicon sequencing, shotgun metagenomics, and targeted gene sequencing. Examine the advantages and limitations of each approach for different research questions and sample types. Investigate experimental design considerations including sampling strategies, DNA extraction methods, library preparation protocols, and sequencing depth requirements. Include analysis of emerging technologies like long-read metagenomics and real-time sequencing applications."

**Prompt 7.2 - 宏基因组组装与分箱技术：**
"Investigate computational methods for metagenomic assembly and genome binning. Analyze assembly algorithms designed for metagenomic data including MEGAHIT, MetaSPAdes, and IDBA-UD, examining their performance on complex communities. Investigate binning approaches including composition-based (MetaBAT, CONCOCT), abundance-based, and hybrid methods. Examine the challenges of strain-level resolution, horizontal gene transfer, and contamination assessment. Include evaluation of assembly and binning quality metrics."

**Prompt 7.3 - 分类学与功能注释方法学：**
"Examine taxonomic classification and functional annotation methodologies for metagenomic data. Analyze tools like Kraken2, MetaPhlAn, Centrifuge for taxonomic profiling, and their accuracy across different taxonomic levels. Investigate functional annotation approaches using databases like KEGG, COG, and Pfam, and tools like HUMAnN and DIAMOND. Examine challenges in annotating novel genes and pathways, and approaches for improving annotation accuracy in understudied environments."

**Prompt 7.4 - 微生物生态学与多样性分析：**
"Investigate computational approaches for analyzing microbial community ecology and diversity. Examine alpha and beta diversity metrics, their statistical properties, and appropriate applications. Analyze methods for identifying core microbiomes, keystone species, and community assembly patterns. Investigate network analysis approaches for understanding microbial interactions and co-occurrence patterns. Include evaluation of statistical methods for differential abundance analysis and environmental association studies."

**Prompt 7.5 - 宿主-微生物组整合分析：**
"Explore methodologies for integrating metagenomic data with host genomics, transcriptomics, and metabolomics to understand host-microbiome interactions. Investigate approaches for identifying microbial biomarkers associated with health and disease states. Examine methods for analyzing microbial metabolic pathways and their impact on host physiology. Analyze causal inference methods for establishing microbiome-phenotype relationships. Include evaluation of machine learning approaches for microbiome-based diagnostics and therapeutics."

---

## 专题八：高通量测序技术在植物保护中的应用

### 核心问题：

**问题1：植物病原体基因组学与进化分析**
- 不同类型病原体（真菌、细菌、病毒、线虫）的基因组特征是什么？
- 病原体的致病机制和进化适应性如何通过基因组学解析？

**问题2：植物病害早期检测与监测技术**
- 高通量测序如何应用于病害的快速诊断和监测？
- 环境DNA（eDNA）技术在病原体检测中的优势和局限性是什么？

**问题3：植物-病原体互作的分子机制**
- 如何利用多组学技术解析植物免疫反应和病原体致病过程？
- 植物抗病基因与病原体毒性基因的共进化机制是什么？

**问题4：抗病基因资源挖掘与分子育种**
- 如何高效发现和验证新的植物抗病基因？
- 基因编辑技术在植物抗病育种中的应用前景如何？

**问题5：有益微生物组与生物防治**
- 植物根际和叶际微生物组的功能多样性如何解析？
- 如何开发基于微生物组的生物防治策略？

### Deep Research Prompts：

**Prompt 8.1 - 植物病原体比较基因组学：**
"Conduct a comprehensive analysis of plant pathogen genomics across different taxonomic groups including fungi, bacteria, viruses, and nematodes. Examine genome organization, size variation, repetitive elements, and horizontal gene transfer patterns. Investigate pathogenicity factors, effector proteins, and virulence gene evolution. Analyze comparative genomics approaches for understanding host specificity, pathotype differentiation, and emergence of new pathogenic strains. Include evaluation of pangenome analysis and phylogenomic reconstruction methods."

**Prompt 8.2 - 分子诊断与病害监测技术：**
"Investigate high-throughput sequencing applications for plant disease detection and monitoring. Examine metabarcoding approaches for pathogen identification, environmental DNA (eDNA) detection methods, and portable sequencing technologies for field diagnostics. Analyze the development of pathogen-specific molecular markers and multiplex detection systems. Investigate early warning systems based on environmental monitoring and predictive modeling. Include evaluation of sensitivity, specificity, and cost-effectiveness of different diagnostic approaches."

**Prompt 8.3 - 植物-病原体互作组学分析：**
"Explore multi-omics approaches for understanding plant-pathogen interactions, including dual RNA-seq, proteomics, and metabolomics studies. Investigate the molecular mechanisms of plant immune responses, including pattern-triggered immunity (PTI) and effector-triggered immunity (ETI). Analyze pathogen strategies for immune suppression and host manipulation. Examine the role of epigenetic modifications in plant-pathogen interactions. Include evaluation of computational methods for integrating multi-omics data and network analysis approaches."

**Prompt 8.4 - 抗病基因发现与功能验证：**
"Analyze genomic approaches for discovering and characterizing plant resistance genes. Investigate genome-wide association studies (GWAS), QTL mapping, and pangenome analysis for resistance gene identification. Examine the diversity and evolution of resistance gene families (NBS-LRR, RLK, etc.). Analyze functional validation approaches including transient expression, stable transformation, and gene editing techniques. Include evaluation of resistance gene deployment strategies and durability assessment methods."

**Prompt 8.5 - 植物微生物组与生物防治：**
"Investigate the application of microbiome research in plant protection and biological control. Examine the structure and function of plant-associated microbial communities in different plant compartments (rhizosphere, phyllosphere, endosphere). Analyze the mechanisms of biocontrol agents and beneficial microorganisms. Investigate microbiome engineering approaches for enhancing plant health and disease resistance. Include evaluation of field application strategies, environmental safety assessment, and regulatory considerations for microbiome-based biocontrol products."

---

## 通用研究指导原则

### 每个核心问题的深度研究应包括：

1. **技术原理与发展历程**
   - 基础科学原理和理论基础
   - 技术发展时间线和里程碑
   - 关键技术突破和创新点
   - 失败案例和经验教训

2. **当前最佳实践与标准**
   - 国际标准化组织认可的方法
   - 行业最佳实践指南
   - 质量控制和验证标准
   - 可重现性和标准化挑战

3. **计算工具与算法生态**
   - 主流软件工具的算法原理
   - 性能基准测试和比较分析
   - 开源vs商业解决方案评估
   - 新兴工具和技术评估

4. **技术挑战与解决方案**
   - 当前技术瓶颈和限制因素
   - 计算复杂度和可扩展性挑战
   - 数据质量和标准化问题
   - 跨平台兼容性和互操作性

5. **前沿发展与未来趋势**
   - 新兴技术和方法学创新
   - 学术研究热点和方向
   - 产业发展趋势和投资重点
   - 技术融合和跨学科应用

6. **实际应用与案例分析**
   - 成功应用案例和最佳实践
   - 临床转化和产业化进展
   - 成本效益分析和经济影响
   - 监管政策和伦理考量

### 研究深度与质量要求：

**时效性要求：**
- 重点关注最新3-5年的研究进展
- 包含2024年最新发表的重要研究
- 追踪技术发展的最新动态
- 识别即将到来的技术变革

**文献质量标准：**
- 优先引用Nature、Science、Cell等顶级期刊
- 包含领域专业期刊的权威研究
- 引用政府报告和行业白皮书
- 参考国际标准化组织文件

**分析深度层次：**
- **技术层面**：算法原理、实现细节、性能优化
- **方法学层面**：实验设计、统计分析、验证策略
- **应用层面**：实际案例、效果评估、推广障碍

**数据支撑要求：**
- 提供定量分析和统计数据
- 包含性能基准测试结果
- 展示成本效益分析数据
- 提供市场规模和增长预测

### 输出格式与结构建议：

**1. 执行摘要（500-800字）**
- 核心发现和主要结论
- 技术发展现状概述
- 关键挑战和机遇识别
- 未来发展趋势预测

**2. 详细分析报告（4000-6000字）**
- 技术背景和发展历程
- 当前技术状态和能力评估
- 主要挑战和解决方案分析
- 前沿发展和创新趋势
- 应用案例和效果评估

**3. 关键发现总结**
- 技术突破点和创新亮点
- 主要局限性和改进方向
- 标准化和规范化需求
- 产业化和商业化机会

**4. 推荐阅读资源**
- 权威综述文章（10-15篇）
- 重要原创研究论文（20-30篇）
- 技术标准和指南文档
- 开源工具和数据库资源

**5. 实践应用指导**
- 技术选择决策框架
- 实施步骤和注意事项
- 常见问题和解决方案
- 培训资源和学习路径

### 特殊关注领域：

**跨学科融合**
- 生物学与计算科学的结合
- 人工智能在生物信息学中的应用
- 工程技术与生命科学的交叉

**产业转化**
- 学术研究向产业应用的转化
- 商业化模式和盈利策略
- 监管审批和市场准入

**社会影响**
- 技术普及对科研民主化的影响
- 数据隐私和安全考量
- 伦理问题和社会责任

**全球视野**
- 国际合作和竞争态势
- 不同地区的技术发展水平
- 政策环境和资金支持情况
