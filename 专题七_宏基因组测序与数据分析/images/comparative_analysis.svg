<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变色定义：功能丰度色阶 -->
  <defs>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="100%" stop-color="#198754"/>
    </linearGradient>
  </defs>
  <!-- 背景 -->
  <rect width="800" height="600" rx="10" ry="10" fill="#f8f9fa"/>
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial" font-size="26" font-weight="bold" text-anchor="middle">宏基因组比较分析</text>
  <!-- 物种组成比较块 -->
  <rect x="50" y="80" width="700" height="240" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="110" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle">物种组成比较</text>
  <!-- 叠加柱状图背景 -->
  <rect x="100" y="130" width="600" height="170" rx="5" ry="5" fill="#ffffff" stroke="#6c757d"/>
  <!-- 堆叠柱状图 示例（增大间距）-->
  <!-- 可循环生成 -->
  <!-- Sample A -->
  <rect x="130" y="150" width="80" height="130" fill="#fff" stroke="#6c757d"/>
  <rect x="130" y="150" width="80" height="40" fill="#0d6efd"/>
  <rect x="130" y="190" width="80" height="30" fill="#dc3545"/>
  <rect x="130" y="220" width="80" height="20" fill="#ffc107"/>
  <rect x="130" y="240" width="80" height="15" fill="#198754"/>
  <rect x="130" y="255" width="80" height="25" fill="#6c757d"/>
  <text x="170" y="295" font-family="Arial" font-size="12" text-anchor="middle">样本A</text>
  <!-- Sample B -->
  <rect x="230" y="150" width="80" height="130" fill="#fff" stroke="#6c757d"/>
  <rect x="230" y="150" width="80" height="20" fill="#0d6efd"/>
  <rect x="230" y="170" width="80" height="50" fill="#dc3545"/>
  <rect x="230" y="220" width="80" height="25" fill="#ffc107"/>
  <rect x="230" y="245" width="80" height="20" fill="#198754"/>
  <rect x="230" y="265" width="80" height="15" fill="#6c757d"/>
  <text x="270" y="295" font-family="Arial" font-size="12" text-anchor="middle">样本B</text>
  <!-- Sample C -->
  <rect x="330" y="150" width="80" height="130" fill="#fff" stroke="#6c757d"/>
  <rect x="330" y="150" width="80" height="30" fill="#0d6efd"/>
  <rect x="330" y="180" width="80" height="40" fill="#dc3545"/>
  <rect x="330" y="220" width="80" height="30" fill="#ffc107"/>
  <rect x="330" y="250" width="80" height="15" fill="#198754"/>
  <rect x="330" y="265" width="80" height="15" fill="#6c757d"/>
  <text x="370" y="295" font-family="Arial" font-size="12" text-anchor="middle">样本C</text>
  <!-- Sample D -->
  <rect x="430" y="150" width="80" height="130" fill="#fff" stroke="#6c757d"/>
  <rect x="430" y="150" width="80" height="15" fill="#0d6efd"/>
  <rect x="430" y="165" width="80" height="60" fill="#dc3545"/>
  <rect x="430" y="225" width="80" height="20" fill="#ffc107"/>
  <rect x="430" y="245" width="80" height="25" fill="#198754"/>
  <rect x="430" y="270" width="80" height="10" fill="#6c757d"/>
  <text x="470" y="295" font-family="Arial" font-size="12" text-anchor="middle">样本D</text>
  <!-- Sample E -->
  <rect x="530" y="150" width="80" height="130" fill="#fff" stroke="#6c757d"/>
  <rect x="530" y="150" width="80" height="25" fill="#0d6efd"/>
  <rect x="530" y="175" width="80" height="45" fill="#dc3545"/>
  <rect x="530" y="220" width="80" height="35" fill="#ffc107"/>
  <rect x="530" y="255" width="80" height="15" fill="#198754"/>
  <rect x="530" y="270" width="80" height="10" fill="#6c757d"/>
  <text x="570" y="295" font-family="Arial" font-size="12" text-anchor="middle">样本E</text>
  <!-- 图例调整靠近左侧 -->
  <rect x="630" y="130" width="15" height="15" fill="#0d6efd"/><text x="650" y="142" font-family="Arial" font-size="12" text-anchor="start">Bacteroidetes</text>
  <rect x="630" y="155" width="15" height="15" fill="#dc3545"/><text x="650" y="167" font-family="Arial" font-size="12" text-anchor="start">Firmicutes</text>
  <rect x="630" y="180" width="15" height="15" fill="#ffc107"/><text x="650" y="192" font-family="Arial" font-size="12" text-anchor="start">Proteobacteria</text>
  <rect x="630" y="205" width="15" height="15" fill="#198754"/><text x="650" y="217" font-family="Arial" font-size="12" text-anchor="start">Actinobacteria</text>
  <rect x="630" y="230" width="15" height="15" fill="#6c757d"/><text x="650" y="242" font-family="Arial" font-size="12" text-anchor="start">其他</text>
  <!-- 功能组成比较块 -->
  <rect x="50" y="340" width="700" height="240" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="400" y="370" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle">功能组成比较</text>
  <!-- 热图背景 -->
  <rect x="100" y="400" width="600" height="160" rx="5" ry="5" fill="#fff" stroke="#6c757d"/>
  <!-- 热图矩阵（示例），由左至右6样本，由上至下5功能 -->
  <!-- 仅修改字体和对齐，矩阵本身保持 -->
  <!-- 行标签 -->
  <text x="140" y="445" font-family="Arial" font-size="10" text-anchor="end">碳水化合物代谢</text>
  <text x="140" y="465" font-family="Arial" font-size="10" text-anchor="end">氨基酸代谢</text>
  <text x="140" y="485" font-family="Arial" font-size="10" text-anchor="end">能量代谢</text>
  <text x="140" y="505" font-family="Arial" font-size="10" text-anchor="end">脂质代谢</text>
  <text x="140" y="525" font-family="Arial" font-size="10" text-anchor="end">核苷酸代谢</text>
  <!-- 列标签 -->
  <text x="190" y="420" font-family="Arial" font-size="12" text-anchor="middle">A</text>
  <text x="270" y="420" font-family="Arial" font-size="12" text-anchor="middle">B</text>
  <text x="350" y="420" font-family="Arial" font-size="12" text-anchor="middle">C</text>
  <text x="430" y="420" font-family="Arial" font-size="12" text-anchor="middle">D</text>
  <text x="510" y="420" font-family="Arial" font-size="12" text-anchor="middle">E</text>
  <text x="590" y="420" font-family="Arial" font-size="12" text-anchor="middle">F</text>
  <!-- 色块 -->
  <!-- repeat for six samples -->
  <!-- 行1 -->
  <rect x="150" y="430" width="80" height="20" fill="#198754" fill-opacity="0.9"/>
  <rect x="230" y="430" width="80" height="20" fill="#198754" fill-opacity="0.7"/>
  <rect x="310" y="430" width="80" height="20" fill="#198754" fill-opacity="0.3"/>
  <rect x="390" y="430" width="80" height="20" fill="#198754" fill-opacity="0.2"/>
  <rect x="470" y="430" width="80" height="20" fill="#198754" fill-opacity="0.5"/>
  <rect x="550" y="430" width="80" height="20" fill="#198754" fill-opacity="0.4"/>
  <!-- 行2 -->
  <rect x="150" y="450" width="80" height="20" fill="#198754" fill-opacity="0.5"/>
  <rect x="230" y="450" width="80" height="20" fill="#198754" fill-opacity="0.8"/>
  <rect x="310" y="450" width="80" height="20" fill="#198754" fill-opacity="0.6"/>
  <rect x="390" y="450" width="80" height="20" fill="#198754" fill-opacity="0.3"/>
  <rect x="470" y="450" width="80" height="20" fill="#198754" fill-opacity="0.2"/>
  <rect x="550" y="450" width="80" height="20" fill="#198754" fill-opacity="0.1"/>
  <!-- 行3 -->
  <rect x="150" y="470" width="80" height="20" fill="#198754" fill-opacity="0.2"/>
  <rect x="230" y="470" width="80" height="20" fill="#198754" fill-opacity="0.4"/>
  <rect x="310" y="470" width="80" height="20" fill="#198754" fill-opacity="0.9"/>
  <rect x="390" y="470" width="80" height="20" fill="#198754" fill-opacity="0.7"/>
  <rect x="470" y="470" width="80" height="20" fill="#198754" fill-opacity="0.3"/>
  <rect x="550" y="470" width="80" height="20" fill="#198754" fill-opacity="0.5"/>
  <!-- 行4 -->
  <rect x="150" y="490" width="80" height="20" fill="#198754" fill-opacity="0.7"/>
  <rect x="230" y="490" width="80" height="20" fill="#198754" fill-opacity="0.2"/>
  <rect x="310" y="490" width="80" height="20" fill="#198754" fill-opacity="0.4"/>
  <rect x="390" y="490" width="80" height="20" fill="#198754" fill-opacity="0.8"/>
  <rect x="470" y="490" width="80" height="20" fill="#198754" fill-opacity="0.6"/>
  <rect x="550" y="490" width="80" height="20" fill="#198754" fill-opacity="0.3"/>
  <!-- 行5 -->
  <rect x="150" y="510" width="80" height="20" fill="#198754" fill-opacity="0.3"/>
  <rect x="230" y="510" width="80" height="20" fill="#198754" fill-opacity="0.5"/>
  <rect x="310" y="510" width="80" height="20" fill="#198754" fill-opacity="0.1"/>
  <rect x="390" y="510" width="80" height="20" fill="#198754" fill-opacity="0.4"/>
  <rect x="470" y="510" width="80" height="20" fill="#198754" fill-opacity="0.9"/>
  <rect x="550" y="510" width="80" height="20" fill="#198754" fill-opacity="0.7"/>
  <!-- 渐变色条和文字 -->
  <rect x="660" y="450" width="100" height="20" fill="url(#greenGradient)" stroke="#6c757d" stroke-width="0.5"/>
  <text x="660" y="445" font-family="Arial" font-size="10" text-anchor="start">低</text>
  <text x="760" y="445" font-family="Arial" font-size="10" text-anchor="end">高</text>
  <text x="710" y="485" font-family="Arial" font-size="10" text-anchor="middle">相对丰度</text>
</svg>
