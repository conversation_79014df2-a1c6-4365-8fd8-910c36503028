<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">宏基因组数据可视化</text>
  
  <!-- <PERSON><PERSON> Plot -->
  <rect x="50" y="80" width="340" height="240" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="220" y="110" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">Krona图</text>
  
  <!-- Krona Visualization -->
  <circle cx="220" cy="200" r="100" fill="#ffffff" stroke="#6c757d" stroke-width="1"/>
  
  <!-- Inner Circles -->
  <path d="M 220 200 L 320 200 A 100 100 0 0 0 270 120 Z" fill="#0d6efd" fill-opacity="0.7"/>
  <path d="M 220 200 L 270 120 A 100 100 0 0 0 170 120 Z" fill="#dc3545" fill-opacity="0.7"/>
  <path d="M 220 200 L 170 120 A 100 100 0 0 0 120 200 Z" fill="#ffc107" fill-opacity="0.7"/>
  <path d="M 220 200 L 120 200 A 100 100 0 0 0 170 280 Z" fill="#198754" fill-opacity="0.7"/>
  <path d="M 220 200 L 170 280 A 100 100 0 0 0 270 280 Z" fill="#6c757d" fill-opacity="0.7"/>
  <path d="M 220 200 L 270 280 A 100 100 0 0 0 320 200 Z" fill="#0dcaf0" fill-opacity="0.7"/>
  
  <!-- Outer Segments -->
  <path d="M 270 120 L 320 200 A 100 100 0 0 0 295 160 Z" fill="#0d6efd" fill-opacity="0.9"/>
  <path d="M 170 120 L 270 120 A 100 100 0 0 0 220 100 Z" fill="#dc3545" fill-opacity="0.9"/>
  <path d="M 120 200 L 170 120 A 100 100 0 0 0 145 160 Z" fill="#ffc107" fill-opacity="0.9"/>
  <path d="M 170 280 L 120 200 A 100 100 0 0 0 145 240 Z" fill="#198754" fill-opacity="0.9"/>
  <path d="M 270 280 L 170 280 A 100 100 0 0 0 220 300 Z" fill="#6c757d" fill-opacity="0.9"/>
  <path d="M 320 200 L 270 280 A 100 100 0 0 0 295 240 Z" fill="#0dcaf0" fill-opacity="0.9"/>
  
  <text x="220" y="200" font-family="Arial" font-size="14" text-anchor="middle">细菌</text>
  <text x="295" y="160" font-family="Arial" font-size="10" text-anchor="middle">变形菌门</text>
  <text x="220" y="120" font-family="Arial" font-size="10" text-anchor="middle">厚壁菌门</text>
  <text x="145" y="160" font-family="Arial" font-size="10" text-anchor="middle">拟杆菌门</text>
  <text x="145" y="240" font-family="Arial" font-size="10" text-anchor="middle">放线菌门</text>
  <text x="220" y="280" font-family="Arial" font-size="10" text-anchor="middle">疣微菌门</text>
  <text x="295" y="240" font-family="Arial" font-size="10" text-anchor="middle">其他</text>
  
  <!-- Circos Plot -->
  <rect x="410" y="80" width="340" height="240" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="580" y="110" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">Circos图</text>
  
  <!-- Circos Visualization -->
  <circle cx="580" cy="200" r="100" fill="#ffffff" stroke="#6c757d" stroke-width="1"/>
  
  <!-- Outer Ring -->
  <path d="M 580 100 A 100 100 0 0 1 680 200 A 100 100 0 0 1 580 300 A 100 100 0 0 1 480 200 A 100 100 0 0 1 580 100" 
        fill="none" stroke="#6c757d" stroke-width="10"/>
  
  <!-- Inner Ring -->
  <path d="M 580 120 A 80 80 0 0 1 660 200 A 80 80 0 0 1 580 280 A 80 80 0 0 1 500 200 A 80 80 0 0 1 580 120" 
        fill="none" stroke="#0d6efd" stroke-width="10"/>
  
  <!-- Connections -->
  <path d="M 580 120 C 600 150, 620 180, 660 200" stroke="#dc3545" stroke-width="2" fill="none"/>
  <path d="M 580 120 C 560 150, 520 170, 500 200" stroke="#ffc107" stroke-width="2" fill="none"/>
  <path d="M 580 280 C 600 250, 630 220, 660 200" stroke="#198754" stroke-width="2" fill="none"/>
  <path d="M 580 280 C 560 250, 530 230, 500 200" stroke="#6c757d" stroke-width="2" fill="none"/>
  <path d="M 580 120 C 580 160, 580 240, 580 280" stroke="#0dcaf0" stroke-width="2" fill="none"/>
  
  <!-- Network Plot -->
  <rect x="50" y="340" width="340" height="240" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="220" y="370" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">网络图</text>
  
  <!-- Network Visualization -->
  <rect x="80" y="390" width="280" height="170" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  
  <!-- Nodes -->
  <circle cx="150" cy="450" r="20" fill="#0d6efd" fill-opacity="0.7"/>
  <circle cx="220" cy="420" r="15" fill="#dc3545" fill-opacity="0.7"/>
  <circle cx="280" cy="460" r="18" fill="#ffc107" fill-opacity="0.7"/>
  <circle cx="200" cy="500" r="12" fill="#198754" fill-opacity="0.7"/>
  <circle cx="300" cy="510" r="10" fill="#6c757d" fill-opacity="0.7"/>
  <circle cx="120" cy="510" r="15" fill="#0dcaf0" fill-opacity="0.7"/>
  
  <!-- Edges -->
  <line x1="150" y1="450" x2="220" y2="420" stroke="#6c757d" stroke-width="2"/>
  <line x1="220" y1="420" x2="280" y2="460" stroke="#6c757d" stroke-width="2"/>
  <line x1="280" y1="460" x2="200" y2="500" stroke="#6c757d" stroke-width="2"/>
  <line x1="200" y1="500" x2="150" y2="450" stroke="#6c757d" stroke-width="2"/>
  <line x1="200" y1="500" x2="300" y2="510" stroke="#6c757d" stroke-width="2"/>
  <line x1="150" y1="450" x2="120" y2="510" stroke="#6c757d" stroke-width="2"/>
  <line x1="120" y1="510" x2="200" y2="500" stroke="#6c757d" stroke-width="2"/>
  
  <!-- Heatmap -->
  <rect x="410" y="340" width="340" height="240" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="580" y="370" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">热图</text>
  
  <!-- Heatmap Visualization -->
  <rect x="440" y="390" width="280" height="170" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  
  <!-- Heatmap Grid -->
  <rect x="470" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="490" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="510" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="530" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="550" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.1"/>
  <rect x="570" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  <rect x="590" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="610" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="630" y="410" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  <rect x="650" y="410" width="20" height="20" fill="#dc3545" fill-opacity="1.0"/>
  
  <rect x="470" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  <rect x="490" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="510" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="530" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  <rect x="550" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.1"/>
  <rect x="570" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="590" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="610" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="630" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="650" y="430" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  
  <rect x="470" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="490" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="510" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="530" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.1"/>
  <rect x="550" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  <rect x="570" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="590" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="610" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  <rect x="630" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="650" y="450" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  
  <rect x="470" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="490" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="510" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  <rect x="530" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="550" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="570" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="590" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="610" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="630" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="650" y="470" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  
  <rect x="470" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="490" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="510" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="530" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="550" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  <rect x="570" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="590" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="610" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.1"/>
  <rect x="630" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="650" y="490" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  
  <rect x="470" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="490" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="510" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="530" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="550" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="570" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="590" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.2"/>
  <rect x="610" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.4"/>
  <rect x="630" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="650" y="510" width="20" height="20" fill="#dc3545" fill-opacity="0.8"/>
  
  <rect x="470" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="490" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.6"/>
  <rect x="510" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="530" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="550" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="570" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.3"/>
  <rect x="590" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.5"/>
  <rect x="610" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
  <rect x="630" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.9"/>
  <rect x="650" y="530" width="20" height="20" fill="#dc3545" fill-opacity="0.7"/>
</svg>
