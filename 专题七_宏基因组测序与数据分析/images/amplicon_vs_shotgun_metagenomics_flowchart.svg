<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 600">
  <!-- Styles -->
  <defs>
    <style>
      text {
        font-family: Arial, sans-serif;
        font-size: 12px;
      }
      .title {
        font-size: 18px;
        font-weight: bold;
      }
      .subtitle {
        font-size: 14px;
        font-weight: bold;
      }
      .step-text {
        text-anchor: middle;
      }
      .output-text {
        font-size: 14px;
        font-weight: bold;
        fill: #333;
      }
    </style>
    
    <!-- Icons -->
    <symbol id="sample-icon" viewBox="0 0 50 50">
      <circle cx="25" cy="25" r="20" fill="#e6f7ff" stroke="#0099cc" stroke-width="2"/>
      <path d="M15,25 C15,18 25,15 25,25 C25,35 35,32 35,25" stroke="#0099cc" stroke-width="2" fill="none"/>
    </symbol>
    
    <symbol id="dna-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#ffeecc" stroke="#ff9900" stroke-width="2"/>
      <path d="M15,15 L35,35 M35,15 L15,35" stroke="#ff9900" stroke-width="2"/>
    </symbol>
    
    <symbol id="pcr-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#e6ffe6" stroke="#00cc66" stroke-width="2"/>
      <path d="M15,25 L35,25 M25,15 L25,35" stroke="#00cc66" stroke-width="2"/>
    </symbol>
    
    <symbol id="library-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#ffe6f2" stroke="#cc0066" stroke-width="2"/>
      <path d="M15,20 L35,20 M15,25 L35,25 M15,30 L35,30" stroke="#cc0066" stroke-width="2"/>
    </symbol>
    
    <symbol id="sequencing-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#e6e6ff" stroke="#0000cc" stroke-width="2"/>
      <path d="M15,15 L15,35 M20,20 L20,30 M25,15 L25,35 M30,20 L30,30 M35,15 L35,35" stroke="#0000cc" stroke-width="2"/>
    </symbol>
    
    <symbol id="otu-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#f2e6ff" stroke="#6600cc" stroke-width="2"/>
      <circle cx="20" cy="20" r="5" stroke="#6600cc" stroke-width="1" fill="none"/>
      <circle cx="30" cy="20" r="5" stroke="#6600cc" stroke-width="1" fill="none"/>
      <circle cx="25" cy="30" r="5" stroke="#6600cc" stroke-width="1" fill="none"/>
    </symbol>
    
    <symbol id="qc-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#fff2e6" stroke="#cc6600" stroke-width="2"/>
      <path d="M15,30 L25,15 L35,30" stroke="#cc6600" stroke-width="2" fill="none"/>
    </symbol>
    
    <symbol id="assembly-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#e6ffff" stroke="#00cccc" stroke-width="2"/>
      <path d="M15,25 L35,25 M17,20 L33,20 M19,30 L31,30" stroke="#00cccc" stroke-width="2"/>
    </symbol>
    
    <symbol id="taxonomy-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#ffe6e6" stroke="#cc0000" stroke-width="2"/>
      <path d="M25,15 L15,25 L25,35 M25,15 L35,25 L25,35" stroke="#cc0000" stroke-width="2" fill="none"/>
    </symbol>
    
    <symbol id="functional-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#f2f2f2" stroke="#666666" stroke-width="2"/>
      <path d="M15,20 C20,15 30,15 35,20 C30,25 20,25 15,20 M15,30 C20,25 30,25 35,30" stroke="#666666" stroke-width="2" fill="none"/>
    </symbol>
    
    <symbol id="output-icon" viewBox="0 0 50 50">
      <rect x="10" y="10" width="30" height="30" rx="5" fill="#ffffe6" stroke="#cccc00" stroke-width="2"/>
      <path d="M15,15 L35,15 L35,35 L15,35 Z" stroke="#cccc00" stroke-width="1" fill="none"/>
      <path d="M20,20 L30,20 M20,25 L30,25 M20,30 L25,30" stroke="#cccc00" stroke-width="1"/>
    </symbol>
  </defs>
  
  <!-- Title -->
  <text x="500" y="40" class="title" text-anchor="middle">Amplicon Sequencing vs. Shotgun Metagenomics</text>
  
  <!-- Amplicon Sequencing -->
  <text x="250" y="80" class="subtitle" text-anchor="middle">Amplicon Sequencing (16S rRNA)</text>
  
  <!-- Shotgun Metagenomics -->
  <text x="750" y="80" class="subtitle" text-anchor="middle">Shotgun Metagenomics</text>
  
  <!-- Dividing Line -->
  <line x1="500" y1="60" x2="500" y2="550" stroke="#cccccc" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Amplicon Sequencing Flow -->
  <!-- Sample -->
  <use href="#sample-icon" x="225" y="100" width="50" height="50"/>
  <rect x="200" y="100" width="100" height="50" rx="10" fill="none" stroke="#0099cc" stroke-width="2"/>
  <text x="250" y="165" class="step-text">Sample</text>
  
  <!-- Arrow -->
  <path d="M250,160 L250,185" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- DNA Extraction -->
  <use href="#dna-icon" x="225" y="195" width="50" height="50"/>
  <rect x="200" y="195" width="100" height="50" rx="10" fill="none" stroke="#ff9900" stroke-width="2"/>
  <text x="250" y="260" class="step-text">DNA Extraction</text>
  
  <!-- Arrow -->
  <path d="M250,255 L250,280" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- PCR Amplification -->
  <use href="#pcr-icon" x="225" y="290" width="50" height="50"/>
  <rect x="200" y="290" width="100" height="50" rx="10" fill="none" stroke="#00cc66" stroke-width="2"/>
  <text x="250" y="355" class="step-text">PCR Amplification</text>
  <text x="250" y="370" class="step-text" font-style="italic">(target gene)</text>
  
  <!-- Arrow -->
  <path d="M250,375 L250,390" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Sequencing -->
  <use href="#sequencing-icon" x="225" y="400" width="50" height="50"/>
  <rect x="200" y="400" width="100" height="50" rx="10" fill="none" stroke="#0000cc" stroke-width="2"/>
  <text x="250" y="465" class="step-text">Sequencing</text>
  
  <!-- Arrow -->
  <path d="M250,465 L250,480" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- OTU/ASV Picking -->
  <use href="#otu-icon" x="225" y="490" width="50" height="50"/>
  <rect x="200" y="490" width="100" height="50" rx="10" fill="none" stroke="#6600cc" stroke-width="2"/>
  <text x="250" y="555" class="step-text">OTU/ASV Picking</text>
  
  <!-- Arrow -->
  <path d="M300,515 L350,515" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Taxonomic Profiling -->
  <use href="#taxonomy-icon" x="375" y="490" width="50" height="50"/>
  <rect x="350" y="490" width="100" height="50" rx="10" fill="none" stroke="#cc0000" stroke-width="2"/>
  <text x="400" y="555" class="step-text">Taxonomic Profiling</text>
  
  <!-- Arrow -->
  <path d="M400,540 L400,570" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Output Box -->
  <rect x="350" y="580" width="100" height="50" rx="10" fill="#ffffe6" stroke="#cccc00" stroke-width="2"/>
  <text x="400" y="605" class="output-text" text-anchor="middle">Output:</text>
  <text x="400" y="620" class="step-text" text-anchor="middle">"Who is there?"</text>
  
  <!-- Shotgun Metagenomics Flow -->
  <!-- Sample -->
  <use href="#sample-icon" x="725" y="100" width="50" height="50"/>
  <rect x="700" y="100" width="100" height="50" rx="10" fill="none" stroke="#0099cc" stroke-width="2"/>
  <text x="750" y="165" class="step-text">Sample</text>
  
  <!-- Arrow -->
  <path d="M750,160 L750,185" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- DNA Extraction -->
  <use href="#dna-icon" x="725" y="195" width="50" height="50"/>
  <rect x="700" y="195" width="100" height="50" rx="10" fill="none" stroke="#ff9900" stroke-width="2"/>
  <text x="750" y="260" class="step-text">DNA Extraction</text>
  
  <!-- Arrow -->
  <path d="M750,255 L750,280" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Library Prep -->
  <use href="#library-icon" x="725" y="290" width="50" height="50"/>
  <rect x="700" y="290" width="100" height="50" rx="10" fill="none" stroke="#cc0066" stroke-width="2"/>
  <text x="750" y="355" class="step-text">Library Prep</text>
  <text x="750" y="370" class="step-text" font-style="italic">(random fragments)</text>
  
  <!-- Arrow -->
  <path d="M750,375 L750,390" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Sequencing -->
  <use href="#sequencing-icon" x="725" y="400" width="50" height="50"/>
  <rect x="700" y="400" width="100" height="50" rx="10" fill="none" stroke="#0000cc" stroke-width="2"/>
  <text x="750" y="465" class="step-text">Sequencing</text>
  
  <!-- Arrow -->
  <path d="M750,465 L750,480" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- QC -->
  <use href="#qc-icon" x="725" y="490" width="50" height="50"/>
  <rect x="700" y="490" width="100" height="50" rx="10" fill="none" stroke="#cc6600" stroke-width="2"/>
  <text x="750" y="555" class="step-text">QC</text>
  
  <!-- Arrow -->
  <path d="M750,555 L750,570" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Assembly/Gene Prediction -->
  <use href="#assembly-icon" x="725" y="580" width="50" height="50"/>
  <rect x="700" y="580" width="100" height="50" rx="10" fill="none" stroke="#00cccc" stroke-width="2"/>
  <text x="750" y="645" class="step-text">Assembly/</text>
  <text x="750" y="660" class="step-text">Gene Prediction</text>
  
  <!-- Arrow to Taxonomic -->
  <path d="M700,605 L650,605" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow to Functional -->
  <path d="M800,605 L850,605" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Taxonomic Profiling -->
  <use href="#taxonomy-icon" x="600" y="580" width="50" height="50"/>
  <rect x="575" y="580" width="100" height="50" rx="10" fill="none" stroke="#cc0000" stroke-width="2"/>
  <text x="625" y="645" class="step-text">Taxonomic</text>
  <text x="625" y="660" class="step-text">Profiling</text>
  
  <!-- Functional Annotation -->
  <use href="#functional-icon" x="875" y="580" width="50" height="50"/>
  <rect x="850" y="580" width="100" height="50" rx="10" fill="none" stroke="#666666" stroke-width="2"/>
  <text x="900" y="645" class="step-text">Functional</text>
  <text x="900" y="660" class="step-text">Annotation</text>
  
  <!-- Arrow -->
  <path d="M625,660 L700,690" stroke="#666" stroke-width="2"/>
  <path d="M900,660 L825,690" stroke="#666" stroke-width="2"/>
  
  <!-- Output Box -->
  <rect x="700" y="700" width="150" height="70" rx="10" fill="#ffffe6" stroke="#cccc00" stroke-width="2"/>
  <text x="775" y="725" class="output-text" text-anchor="middle">Output:</text>
  <text x="775" y="745" class="step-text" text-anchor="middle">"Who is there?"</text>
  <text x="775" y="760" class="step-text" text-anchor="middle">AND</text>
  <text x="775" y="775" class="step-text" text-anchor="middle">"What can they do?"</text>
  
  <!-- Arrowhead definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
</svg>