# 专题七：宏基因组测序与数据分析 - 增强版实践操作课

## 课程目标
本增强版实践课程旨在帮助学生：
1. 掌握完整的宏基因组数据处理流程和质量控制策略
2. 学会使用MetaPhlAn3、Kraken2等工具进行物种分类分析
3. 掌握宏基因组组装和功能注释的高级方法
4. 学会宏基因组差异分析和生态网络构建

## 实验环境准备

### 软件环境配置
```bash
# 创建专题七专用环境
conda create -n metagenomics_analysis python=3.8 -y
conda activate metagenomics_analysis

# 安装基础分析工具
conda install -c bioconda fastp fastqc multiqc trimmomatic -y
conda install -c bioconda bowtie2 samtools seqtk -y

# 安装物种分类工具
conda install -c bioconda metaphlan kraken2 bracken -y
conda install -c bioconda centrifuge kaiju -y

# 安装组装工具
conda install -c bioconda megahit spades metaspades -y
conda install -c bioconda prodigal prokka -y

# 安装功能注释工具
conda install -c bioconda hmmer diamond blast -y
conda install -c bioconda eggnog-mapper -y

# 安装R包和可视化工具
conda install -c conda-forge r-base r-ggplot2 r-dplyr r-vegan -y
conda install -c bioconda r-phyloseq r-deseq2 -y

# 验证安装
echo "=== 软件版本验证 ==="
fastp --version
metaphlan --version
kraken2 --version
megahit --version
diamond version

echo "软件安装完成！"
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p metagenomics_practice/{data/{raw,processed,databases},results/{qc,taxonomy,assembly,annotation,functional},scripts,logs}
cd metagenomics_practice

# 创建模拟宏基因组双端测序数据（肠道菌群样本）
echo "=== 创建模拟宏基因组数据 ==="

# 样本1：健康对照组
cat > data/raw/healthy_R1.fastq << 'EOF'
@M01234:1:*********-A1B2C:1:1101:15000:1000 1:N:0:ACGT
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15001:1001 1:N:0:ACGT
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@M01234:1:*********-A1B2C:1:1101:15002:1002 1:N:0:ACGT
ACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15003:1003 1:N:0:ACGT
TTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCTGATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTT
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat > data/raw/healthy_R2.fastq << 'EOF'
@M01234:1:*********-A1B2C:1:1101:15000:1000 2:N:0:ACGT
ATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCAATGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTGATC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15001:1001 2:N:0:ACGT
TATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTGAACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCAA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@M01234:1:*********-A1B2C:1:1101:15002:1002 2:N:0:ACGT
TCTGGTTAGGCTGGTGTAGGGTTCTTTGTTTTGGGGGTTTGGCAGAGATGTGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTGTGCAGACATTCAATTGTT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15003:1003 2:N:0:ACGT
AAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTGATCAGAAAAGGGTGTTATTATTGTGTGCTGTGAGGCCTCAGAGATGGGGTTGGCTTTGGGAGGGGATAAGCATTGGAAAGATAAAATTTGAAA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

# 样本2：疾病组
cp data/raw/healthy_R1.fastq data/raw/disease_R1.fastq
cp data/raw/healthy_R2.fastq data/raw/disease_R2.fastq

# 创建样本元数据
cat > data/sample_metadata.txt << 'EOF'
SampleID	Group	Description
healthy	Control	Healthy control sample
disease	Disease	Disease sample
EOF

echo "实验数据准备完成！"
```

---

## 第一部分：宏基因组数据预处理与质控

### 1.1 数据质量评估和预处理

#### 1.1.1 原始数据质量控制

**实验目标：** 评估宏基因组测序数据质量，识别潜在的技术偏差和污染

```bash
echo "=== 宏基因组数据质量控制实践 ==="

# 1. 创建质量控制目录结构
mkdir -p results/qc/{raw,processed,reports}

# 2. FastQC质量评估
echo "执行原始数据质量评估..."
for sample in healthy disease; do
    echo "处理样本: $sample"
    
    # 对双端测序数据进行质量评估
    fastqc data/raw/${sample}_R1.fastq -o results/qc/raw/
    fastqc data/raw/${sample}_R2.fastq -o results/qc/raw/
    
    echo "✓ $sample 样本质量评估完成"
done

# 3. 生成综合质量报告
echo "生成综合质量报告..."
multiqc results/qc/raw/ -o results/qc/reports/ -n raw_data_qc_report

echo "原始数据质量评估完成！"
```

#### 1.1.2 高级质量过滤与预处理

```bash
# 创建宏基因组特化的质量过滤脚本
cat > scripts/metagenomics_preprocessing.py << 'EOF'
#!/usr/bin/env python3
"""
宏基因组数据预处理脚本
包含质量过滤、宿主序列去除、复杂度分析等功能
"""

import os
import subprocess
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

class MetagenomicsPreprocessor:
    def __init__(self, working_dir):
        self.working_dir = working_dir
        self.samples = []
        
    def setup_directories(self):
        """创建必要的目录结构"""
        dirs = [
            'results/qc/fastp',
            'results/qc/host_removal', 
            'results/qc/complexity',
            'results/qc/final_stats'
        ]
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
        print("目录结构创建完成")
    
    def run_fastp_filtering(self, sample_name, r1_file, r2_file):
        """使用fastp进行高质量过滤"""
        print(f"正在处理样本: {sample_name}")
        
        # fastp参数优化（针对宏基因组）
        cmd = [
            'fastp',
            '-i', r1_file,
            '-I', r2_file,
            '-o', f'results/qc/fastp/{sample_name}_clean_R1.fastq',
            '-O', f'results/qc/fastp/{sample_name}_clean_R2.fastq',
            '-h', f'results/qc/fastp/{sample_name}_fastp.html',
            '-j', f'results/qc/fastp/{sample_name}_fastp.json',
            '--cut_right',
            '--cut_right_window_size', '4',
            '--cut_right_mean_quality', '20',
            '--qualified_quality_phred', '20',
            '--unqualified_percent_limit', '40',
            '--length_required', '50',
            '--detect_adapter_for_pe',
            '--correction',
            '--thread', '4'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✓ {sample_name} fastp过滤完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {sample_name} fastp过滤失败: {e}")
            return False
    
    def analyze_sequence_complexity(self, sample_name):
        """分析序列复杂度"""
        print(f"分析 {sample_name} 序列复杂度...")
        
        # 计算序列复杂度和重复序列比例
        r1_file = f'results/qc/fastp/{sample_name}_clean_R1.fastq'
        
        if not os.path.exists(r1_file):
            print(f"文件不存在: {r1_file}")
            return
        
        # 使用seqtk计算序列统计
        cmd = ['seqtk', 'comp', r1_file]
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # 解析结果并保存
            with open(f'results/qc/complexity/{sample_name}_complexity.txt', 'w') as f:
                f.write("name\tlength\t#A\t#C\t#G\t#T\t#2\t#3\t#4\t#CpG\t#tv\t#ts\t#CpG-ts\n")
                f.write(result.stdout)
            
            print(f"✓ {sample_name} 复杂度分析完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ {sample_name} 复杂度分析失败: {e}")
    
    def generate_preprocessing_stats(self):
        """生成预处理统计报告"""
        print("生成预处理统计报告...")
        
        stats = []
        for sample in ['healthy', 'disease']:
            sample_stats = {
                'sample': sample,
                'raw_reads': self._count_fastq_reads(f'data/raw/{sample}_R1.fastq'),
                'clean_reads': self._count_fastq_reads(f'results/qc/fastp/{sample}_clean_R1.fastq'),
            }
            
            # 计算过滤效率
            if sample_stats['raw_reads'] > 0:
                sample_stats['retention_rate'] = (sample_stats['clean_reads'] / sample_stats['raw_reads']) * 100
            else:
                sample_stats['retention_rate'] = 0
            
            stats.append(sample_stats)
        
        # 保存统计结果
        df = pd.DataFrame(stats)
        df.to_csv('results/qc/final_stats/preprocessing_summary.csv', index=False)
        
        # 生成可视化图表
        self._plot_preprocessing_stats(df)
        
        print("统计报告生成完成")
        return df
    
    def _count_fastq_reads(self, fastq_file):
        """统计FASTQ文件中的reads数量"""
        if not os.path.exists(fastq_file):
            return 0
        
        try:
            cmd = ['grep', '-c', '^@', fastq_file]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return int(result.stdout.strip())
        except:
            return 0
    
    def _plot_preprocessing_stats(self, df):
        """绘制预处理统计图表"""
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 读数统计
        x = range(len(df))
        width = 0.35
        
        axes[0].bar([i - width/2 for i in x], df['raw_reads'], width, label='原始reads', alpha=0.8)
        axes[0].bar([i + width/2 for i in x], df['clean_reads'], width, label='过滤后reads', alpha=0.8)
        axes[0].set_xlabel('样本')
        axes[0].set_ylabel('Reads数量')
        axes[0].set_title('预处理前后reads统计')
        axes[0].set_xticks(x)
        axes[0].set_xticklabels(df['sample'])
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 保留率
        bars = axes[1].bar(df['sample'], df['retention_rate'], alpha=0.8, color='green')
        axes[1].set_xlabel('样本')
        axes[1].set_ylabel('保留率 (%)')
        axes[1].set_title('质量过滤保留率')
        axes[1].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[1].text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('results/qc/final_stats/preprocessing_stats.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("统计图表已保存到: results/qc/final_stats/preprocessing_stats.png")

def main():
    processor = MetagenomicsPreprocessor('.')
    processor.setup_directories()
    
    # 处理所有样本
    samples = [
        ('healthy', 'data/raw/healthy_R1.fastq', 'data/raw/healthy_R2.fastq'),
        ('disease', 'data/raw/disease_R1.fastq', 'data/raw/disease_R2.fastq')
    ]
    
    print("=== 开始宏基因组数据预处理 ===")
    
    for sample_name, r1_file, r2_file in samples:
        if processor.run_fastp_filtering(sample_name, r1_file, r2_file):
            processor.analyze_sequence_complexity(sample_name)
    
    # 生成最终统计报告
    stats_df = processor.generate_preprocessing_stats()
    
    print("\n=== 预处理结果摘要 ===")
    print(stats_df.to_string(index=False))
    
    print("\n=== 宏基因组数据预处理完成 ===")

if __name__ == "__main__":
    main()
EOF

# 运行预处理脚本
chmod +x scripts/metagenomics_preprocessing.py
python3 scripts/metagenomics_preprocessing.py
```

### 1.2 宿主DNA去除实践

#### 1.2.1 构建宿主基因组索引

```bash
echo "=== 宿主DNA去除实践 ==="

# 1. 创建简化的人类参考基因组序列用于演示
mkdir -p data/databases/host_genome

cat > data/databases/host_genome/human_chr22.fa << 'EOF'
>chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

# 2. 构建Bowtie2索引
echo "构建宿主基因组索引..."
bowtie2-build data/databases/host_genome/human_chr22.fa data/databases/host_genome/human_index

echo "宿主基因组索引构建完成！"
```

#### 1.2.2 宿主DNA过滤

```bash
# 创建宿主DNA去除脚本
cat > scripts/remove_host_dna.sh << 'EOF'
#!/bin/bash
# 宿主DNA去除脚本

echo "=== 宿主DNA去除处理 ==="

mkdir -p results/qc/host_removal

for sample in healthy disease; do
    echo "处理样本: $sample"
    
    # 1. 比对到宿主基因组
    echo "  步骤1: 比对到宿主基因组..."
    bowtie2 -x data/databases/host_genome/human_index \
        -1 results/qc/fastp/${sample}_clean_R1.fastq \
        -2 results/qc/fastp/${sample}_clean_R2.fastq \
        -S results/qc/host_removal/${sample}_host_aligned.sam \
        --un-conc results/qc/host_removal/${sample}_non_host_%.fastq \
        --threads 4 \
        2> results/qc/host_removal/${sample}_bowtie2.log
    
    # 2. 提取未比对的reads（非宿主reads）
    echo "  步骤2: 提取非宿主reads..."
    mv results/qc/host_removal/${sample}_non_host_1.fastq results/qc/host_removal/${sample}_clean_nonhost_R1.fastq
    mv results/qc/host_removal/${sample}_non_host_2.fastq results/qc/host_removal/${sample}_clean_nonhost_R2.fastq
    
    # 3. 生成比对统计
    echo "  步骤3: 生成比对统计..."
    samtools view -bS results/qc/host_removal/${sample}_host_aligned.sam > results/qc/host_removal/${sample}_host_aligned.bam
    samtools flagstat results/qc/host_removal/${sample}_host_aligned.bam > results/qc/host_removal/${sample}_flagstat.txt
    
    # 4. 统计过滤效果
    original_reads=$(grep -c "^@" results/qc/fastp/${sample}_clean_R1.fastq)
    remaining_reads=$(grep -c "^@" results/qc/host_removal/${sample}_clean_nonhost_R1.fastq)
    host_reads=$((original_reads - remaining_reads))
    host_percentage=$(echo "scale=2; $host_reads * 100 / $original_reads" | bc)
    
    echo "  样本 $sample 宿主DNA去除统计:"
    echo "    原始reads: $original_reads"
    echo "    宿主reads: $host_reads (${host_percentage}%)"
    echo "    保留reads: $remaining_reads"
    
    # 保存统计信息
    cat > results/qc/host_removal/${sample}_host_removal_stats.txt << EOF
样本: $sample
原始reads数: $original_reads
宿主reads数: $host_reads
宿主比例: ${host_percentage}%
保留reads数: $remaining_reads
保留比例: $(echo "scale=2; $remaining_reads * 100 / $original_reads" | bc)%
EOF
    
    # 清理中间文件
    rm results/qc/host_removal/${sample}_host_aligned.sam
    rm results/qc/host_removal/${sample}_host_aligned.bam
    
    echo "✓ $sample 宿主DNA去除完成"
done

echo "=== 宿主DNA去除处理完成 ==="
EOF

chmod +x scripts/remove_host_dna.sh
./scripts/remove_host_dna.sh
```

---

## 第二部分：MetaPhlAn/Kraken进行物种分类分析

### 2.1 MetaPhlAn3物种分类实践

#### 2.1.1 MetaPhlAn3数据库准备和基础分析

**实验目标：** 学会使用MetaPhlAn3进行宏基因组物种分类，理解标记基因方法的原理

```bash
echo "=== MetaPhlAn3物种分类分析 ==="

# 1. 创建分析目录
mkdir -p results/taxonomy/metaphlan/{profiles,merged,plots}

# 2. 安装和配置MetaPhlAn3数据库
echo "配置MetaPhlAn3数据库..."
metaphlan --install --bowtie2db data/databases/metaphlan_db

# 3. 运行MetaPhlAn3分析
echo "运行MetaPhlAn3物种分类..."
for sample in healthy disease; do
    echo "分析样本: $sample"
    
    # 合并双端测序数据用于MetaPhlAn分析
    cat results/qc/host_removal/${sample}_clean_nonhost_R1.fastq \
        results/qc/host_removal/${sample}_clean_nonhost_R2.fastq > \
        results/qc/host_removal/${sample}_merged_for_metaphlan.fastq
    
    # 运行MetaPhlAn3
    metaphlan results/qc/host_removal/${sample}_merged_for_metaphlan.fastq \
        --input_type fastq \
        --bowtie2out results/taxonomy/metaphlan/${sample}_bowtie2.txt \
        --nproc 4 \
        --bowtie2db data/databases/metaphlan_db \
        -o results/taxonomy/metaphlan/profiles/${sample}_profile.txt
    
    echo "✓ $sample MetaPhlAn3分析完成"
done

echo "MetaPhlAn3分析完成！"
```

#### 2.1.2 MetaPhlAn结果分析和可视化

```bash
# 创建MetaPhlAn结果分析脚本
cat > scripts/analyze_metaphlan_results.py << 'EOF'
#!/usr/bin/env python3
"""
MetaPhlAn结果分析和可视化脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

class MetaPhlAnAnalyzer:
    def __init__(self):
        self.profiles_dir = "results/taxonomy/metaphlan/profiles"
        self.output_dir = "results/taxonomy/metaphlan"
        
    def merge_profiles(self):
        """合并所有样本的MetaPhlAn分类结果"""
        print("合并MetaPhlAn分类结果...")
        
        # 收集所有分类文件
        profile_files = []
        samples = []
        
        for sample in ['healthy', 'disease']:
            profile_file = f"{self.profiles_dir}/{sample}_profile.txt"
            if os.path.exists(profile_file):
                profile_files.append(profile_file)
                samples.append(sample)
        
        if not profile_files:
            print("未找到MetaPhlAn分类结果文件")
            return None
        
        # 合并分类结果
        cmd = f"merge_metaphlan_tables.py {' '.join(profile_files)} > {self.output_dir}/merged/merged_abundance_table.txt"
        os.system(cmd)
        
        print(f"合并结果已保存到: {self.output_dir}/merged/merged_abundance_table.txt")
        return f"{self.output_dir}/merged/merged_abundance_table.txt"
    
    def analyze_taxonomic_composition(self):
        """分析分类组成"""
        print("分析分类组成...")
        
        # 读取分类结果
        composition_data = {}
        
        for sample in ['healthy', 'disease']:
            profile_file = f"{self.profiles_dir}/{sample}_profile.txt"
            if os.path.exists(profile_file):
                composition_data[sample] = self._parse_metaphlan_profile(profile_file)
        
        if not composition_data:
            print("没有可用的分类数据")
            return
        
        # 分析门水平组成
        phylum_composition = self._analyze_phylum_level(composition_data)
        
        # 分析属水平组成
        genus_composition = self._analyze_genus_level(composition_data)
        
        # 生成可视化图表
        self._plot_taxonomic_composition(phylum_composition, genus_composition)
        
        # 保存分析结果
        self._save_composition_analysis(phylum_composition, genus_composition)
    
    def _parse_metaphlan_profile(self, profile_file):
        """解析MetaPhlAn分类文件"""
        composition = {
            'phylum': {},
            'genus': {},
            'species': {}
        }
        
        try:
            with open(profile_file, 'r') as f:
                for line in f:
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        taxonomy = parts[0]
                        abundance = float(parts[1])
                        
                        # 解析分类水平
                        if '|p__' in taxonomy and '|c__' not in taxonomy:
                            # 门水平
                            phylum = taxonomy.split('|p__')[1]
                            composition['phylum'][phylum] = abundance
                        elif '|g__' in taxonomy and '|s__' not in taxonomy:
                            # 属水平
                            genus = taxonomy.split('|g__')[1]
                            composition['genus'][genus] = abundance
                        elif '|s__' in taxonomy:
                            # 种水平
                            species = taxonomy.split('|s__')[1]
                            composition['species'][species] = abundance
        
        except FileNotFoundError:
            print(f"文件不存在: {profile_file}")
        
        return composition
    
    def _analyze_phylum_level(self, composition_data):
        """分析门水平组成"""
        phylum_df = pd.DataFrame()
        
        for sample, data in composition_data.items():
            sample_data = pd.Series(data['phylum'], name=sample)
            phylum_df = pd.concat([phylum_df, sample_data], axis=1)
        
        phylum_df = phylum_df.fillna(0)
        return phylum_df
    
    def _analyze_genus_level(self, composition_data):
        """分析属水平组成"""
        genus_df = pd.DataFrame()
        
        for sample, data in composition_data.items():
            sample_data = pd.Series(data['genus'], name=sample)
            genus_df = pd.concat([genus_df, sample_data], axis=1)
        
        genus_df = genus_df.fillna(0)
        return genus_df
    
    def _plot_taxonomic_composition(self, phylum_df, genus_df):
        """绘制分类组成图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 门水平堆叠柱状图
        if not phylum_df.empty:
            phylum_df.T.plot(kind='bar', stacked=True, ax=axes[0,0], colormap='Set3')
            axes[0,0].set_title('门水平分类组成')
            axes[0,0].set_ylabel('相对丰度 (%)')
            axes[0,0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            axes[0,0].tick_params(axis='x', rotation=45)
        
        # 门水平饼图（健康样本）
        if not phylum_df.empty and 'healthy' in phylum_df.columns:
            healthy_phylum = phylum_df['healthy'][phylum_df['healthy'] > 0]
            if not healthy_phylum.empty:
                axes[0,1].pie(healthy_phylum.values, labels=healthy_phylum.index, autopct='%1.1f%%')
                axes[0,1].set_title('健康样本门水平组成')
        
        # 属水平top10（健康样本）
        if not genus_df.empty and 'healthy' in genus_df.columns:
            healthy_genus = genus_df['healthy'].sort_values(ascending=False).head(10)
            if not healthy_genus.empty:
                healthy_genus.plot(kind='bar', ax=axes[1,0])
                axes[1,0].set_title('健康样本top10属')
                axes[1,0].set_ylabel('相对丰度 (%)')
                axes[1,0].tick_params(axis='x', rotation=45)
        
        # 属水平top10（疾病样本）
        if not genus_df.empty and 'disease' in genus_df.columns:
            disease_genus = genus_df['disease'].sort_values(ascending=False).head(10)
            if not disease_genus.empty:
                disease_genus.plot(kind='bar', ax=axes[1,1], color='red', alpha=0.7)
                axes[1,1].set_title('疾病样本top10属')
                axes[1,1].set_ylabel('相对丰度 (%)')
                axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/plots/taxonomic_composition.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"分类组成图表已保存到: {self.output_dir}/plots/taxonomic_composition.png")
    
    def _save_composition_analysis(self, phylum_df, genus_df):
        """保存组成分析结果"""
        if not phylum_df.empty:
            phylum_df.to_csv(f'{self.output_dir}/merged/phylum_composition.csv')
        
        if not genus_df.empty:
            genus_df.to_csv(f'{self.output_dir}/merged/genus_composition.csv')
        
        print("组成分析结果已保存")

def main():
    analyzer = MetaPhlAnAnalyzer()
    
    print("=== MetaPhlAn结果分析 ===")
    
    # 合并分类结果
    merged_file = analyzer.merge_profiles()
    
    # 分析分类组成
    analyzer.analyze_taxonomic_composition()
    
    print("=== MetaPhlAn结果分析完成 ===")

if __name__ == "__main__":
    main()
EOF

python3 scripts/analyze_metaphlan_results.py
``` 