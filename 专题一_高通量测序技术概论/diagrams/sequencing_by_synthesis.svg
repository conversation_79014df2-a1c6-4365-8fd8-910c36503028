<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" font-weight="bold">边合成边测序原理</text>
  
  <!-- 第一步：引物结合 -->
  <rect x="50" y="80" width="700" height="100" fill="#e8f4f8" stroke="#a8d1e0" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="100" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">第一步：引物结合到模板DNA</text>
  
  <!-- 模板链 -->
  <line x1="150" y1="130" x2="650" y2="130" stroke="#3366cc" stroke-width="4" />
  <text x="400" y="120" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">模板链</text>
  
  <!-- 碱基标记 -->
  <text x="200" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#f44336">A</text>
  <text x="250" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#4caf50">C</text>
  <text x="300" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#2196f3">G</text>
  <text x="350" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#ffeb3b">T</text>
  <text x="400" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#f44336">A</text>
  <text x="450" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#2196f3">G</text>
  <text x="500" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#4caf50">C</text>
  <text x="550" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#ffeb3b">T</text>
  <text x="600" y="150" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#f44336">A</text>
  
  <!-- 引物 -->
  <line x1="150" y1="170" x2="200" y2="170" stroke="#cc3366" stroke-width="4" />
  <text x="175" y="190" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">引物</text>
  
  <!-- 第二步：加入荧光标记的核苷酸 -->
  <rect x="50" y="200" width="700" height="120" fill="#f0e8f8" stroke="#d1a8e0" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="220" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">第二步：加入荧光标记的可逆终止核苷酸</text>
  
  <!-- 模板链 -->
  <line x1="150" y1="250" x2="650" y2="250" stroke="#3366cc" stroke-width="4" />
  
  <!-- 引物+延伸的一个碱基 -->
  <line x1="150" y1="290" x2="200" y2="290" stroke="#cc3366" stroke-width="4" />
  <circle cx="250" cy="290" r="15" fill="#f44336" />
  <text x="250" y="295" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#fff">T</text>
  <line x1="200" y1="290" x2="235" y2="290" stroke="#cc3366" stroke-width="4" />
  
  <!-- DNA聚合酶 -->
  <ellipse cx="220" cy="270" rx="25" ry="15" fill="#8bc34a" stroke="#33691e" stroke-width="1" />
  <text x="220" y="275" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#fff">DNA聚合酶</text>
  
  <!-- 荧光标记的核苷酸 -->
  <circle cx="350" cy="260" r="10" fill="#f44336" />
  <text x="350" y="265" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#fff">T</text>
  <line x1="350" y1="270" x2="350" y2="280" stroke="#f44336" stroke-width="2" />
  <circle cx="350" cy="290" r="5" fill="#ff9800" />
  <text x="350" y="310" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">荧光基团</text>
  
  <circle cx="400" cy="260" r="10" fill="#4caf50" />
  <text x="400" y="265" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#fff">G</text>
  <line x1="400" y1="270" x2="400" y2="280" stroke="#4caf50" stroke-width="2" />
  <circle cx="400" cy="290" r="5" fill="#ff9800" />
  
  <circle cx="450" cy="260" r="10" fill="#2196f3" />
  <text x="450" y="265" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#fff">C</text>
  <line x1="450" y1="270" x2="450" y2="280" stroke="#2196f3" stroke-width="2" />
  <circle cx="450" cy="290" r="5" fill="#ff9800" />
  
  <circle cx="500" cy="260" r="10" fill="#ffeb3b" />
  <text x="500" y="265" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">A</text>
  <line x1="500" y1="270" x2="500" y2="280" stroke="#ffeb3b" stroke-width="2" />
  <circle cx="500" cy="290" r="5" fill="#ff9800" />
  
  <!-- 说明文字 -->
  <text x="600" y="270" font-family="Arial, sans-serif" font-size="12" text-anchor="start">每个核苷酸带有:</text>
  <text x="600" y="290" font-family="Arial, sans-serif" font-size="12" text-anchor="start">1. 特异性荧光标记</text>
  <text x="600" y="310" font-family="Arial, sans-serif" font-size="12" text-anchor="start">2. 可逆的3'-OH封闭基团</text>
  
  <!-- 第三步：检测荧光信号 -->
  <rect x="50" y="340" width="700" height="140" fill="#e8f8e8" stroke="#a8e0a8" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="360" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">第三步：检测荧光信号并去除封闭基团</text>
  
  <!-- 模板链 -->
  <line x1="150" y1="390" x2="650" y2="390" stroke="#3366cc" stroke-width="4" />
  
  <!-- 引物+延伸的一个碱基 -->
  <line x1="150" y1="430" x2="250" y2="430" stroke="#cc3366" stroke-width="4" />
  
  <!-- 激光照射 -->
  <line x1="250" y1="370" x2="250" y2="390" stroke="#4caf50" stroke-width="2" />
  <polygon points="250,390 245,380 255,380" fill="#4caf50" />
  <text x="250" y="365" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">激光</text>
  
  <!-- 荧光发射 -->
  <circle cx="250" cy="430" r="15" fill="#f44336" />
  <text x="250" y="435" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#fff">T</text>
  <path d="M 260,420 Q 280,410 300,420" stroke="#ff9800" stroke-width="1" fill="none" />
  <path d="M 260,440 Q 280,450 300,440" stroke="#ff9800" stroke-width="1" fill="none" />
  <text x="300" y="430" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">荧光信号</text>
  
  <!-- 化学处理去除封闭基团 -->
  <rect x="400" y="380" width="100" height="30" fill="#fff" stroke="#999" stroke-width="1" rx="3" ry="3" />
  <text x="450" y="400" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">化学处理</text>
  
  <line x1="400" y1="430" x2="500" y2="430" stroke="#cc3366" stroke-width="4" />
  <circle cx="450" cy="430" r="15" fill="#f44336" />
  <text x="450" y="435" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#fff">T</text>
  <text x="450" y="460" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">去除荧光基团和封闭基团</text>
  <text x="450" y="475" font-family="Arial, sans-serif" font-size="10" text-anchor="middle">准备下一轮合成</text>
  
  <!-- 说明文字 -->
  <text x="600" y="400" font-family="Arial, sans-serif" font-size="12" text-anchor="start">每个循环:</text>
  <text x="600" y="420" font-family="Arial, sans-serif" font-size="12" text-anchor="start">1. 检测荧光确定碱基</text>
  <text x="600" y="440" font-family="Arial, sans-serif" font-size="12" text-anchor="start">2. 去除封闭基团</text>
  <text x="600" y="460" font-family="Arial, sans-serif" font-size="12" text-anchor="start">3. 进行下一轮合成</text>
</svg>