# 专题一：高通量测序技术概论 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握Linux操作系统基本命令和生物信息学分析环境
2. 了解常用测序数据格式和特点
3. 学会配置生物信息学软件环境
4. 通过实际操作加深对高通量测序技术的理解

## 实验环境要求
- Linux操作系统（Ubuntu 20.04+ 或 CentOS 7+）
- 至少4GB内存，20GB可用磁盘空间
- 网络连接（用于下载软件包和测试数据）

---

## 第一部分：Linux操作系统基本命令实践

### 1.1 Linux系统基础操作

#### 1.1.1 文件系统导航练习

**实验目标：** 熟悉Linux文件系统结构，掌握基本导航命令

**操作步骤：**

```bash
# 1. 查看当前目录
pwd
# 预期输出：/home/<USER>

# 2. 查看根目录结构
ls /
# 预期输出：bin  boot  dev  etc  home  lib  lib64  ...

# 3. 详细查看当前目录内容
ls -la
# 解释：-l 显示详细信息，-a 显示隐藏文件
# 输出说明：
# drwxr-xr-x  2 <USER> <GROUP>  4096 Oct 15 10:30 Documents
# d=目录，rwx=权限，user=所有者，group=群组，4096=大小，时间=修改时间

# 4. 创建实验目录结构
mkdir -p ngs_course/data/{raw,processed,results}
mkdir -p ngs_course/scripts
mkdir -p ngs_course/logs

# 验证目录结构
tree ngs_course
# 如果没有tree命令，使用：
ls -R ngs_course
```

**实际练习任务：**
```bash
# 任务1：创建以下目录结构
# project/
# ├── data/
# │   ├── fastq/
# │   ├── reference/
# │   └── results/
# │       ├── qc/
# │       ├── alignment/
# │       └── variants/
# ├── scripts/
# └── docs/

mkdir -p project/data/{fastq,reference,results/{qc,alignment,variants}}
mkdir -p project/{scripts,docs}
```

#### 1.1.2 文件操作实践

**实验目标：** 掌握文件和目录的基本操作

```bash
# 切换到工作目录
cd ngs_course

# 1. 创建示例文件
echo "Sample_ID,Platform,Read_Length,Total_Reads" > sample_info.csv
echo "Sample_001,Illumina,150,25000000" >> sample_info.csv
echo "Sample_002,Illumina,150,27000000" >> sample_info.csv
echo "Sample_003,PacBio,15000,500000" >> sample_info.csv

# 2. 查看文件内容的不同方法
cat sample_info.csv          # 显示全部内容
head -2 sample_info.csv      # 显示前2行
tail -1 sample_info.csv      # 显示最后1行
less sample_info.csv         # 分页查看（按q退出）

# 3. 文件复制和移动
cp sample_info.csv data/sample_info_backup.csv
mv sample_info.csv data/

# 4. 文件权限管理
chmod 644 data/sample_info.csv    # 设置文件权限
chmod +x scripts/                 # 给目录添加执行权限
ls -l data/sample_info.csv        # 查看权限
```

#### 1.1.3 文本处理命令实践

**实验目标：** 学会使用grep、awk、sed等工具处理生物信息学数据

```bash
# 准备测试数据
cat > data/gene_expression.txt << 'EOF'
Gene_ID	Sample1	Sample2	Sample3	P_value
ENSG00000223972	5.2	4.8	5.1	0.001
ENSG00000227232	12.5	11.8	12.3	0.002
ENSG00000278267	0.5	0.3	0.4	0.15
ENSG00000243485	8.9	9.2	8.7	0.003
ENSG00000284332	15.2	14.8	15.5	0.0001
EOF

# 1. grep命令实践
# 查找显著差异表达基因（P值小于0.01）
grep -E "0\.(000|00[1-9])" data/gene_expression.txt
# 解释：-E使用扩展正则表达式，匹配P值模式

# 统计显著基因数量
grep -c -E "0\.(000|00[1-9])" data/gene_expression.txt

# 查找高表达基因（任一样本表达量>10）
grep -E "1[0-9]\." data/gene_expression.txt

# 2. awk命令实践
# 计算每个基因的平均表达量
awk 'NR>1 {avg=($2+$3+$4)/3; print $1, avg}' data/gene_expression.txt

# 筛选高表达且显著的基因
awk 'NR>1 && $5<0.01 && ($2>10 || $3>10 || $4>10) {print $1, $5}' data/gene_expression.txt

# 统计各个P值范围的基因数量
awk 'NR>1 {
    if($5<0.001) count1++; 
    else if($5<0.01) count2++; 
    else count3++
} 
END {
    print "P<0.001:", count1, "genes"; 
    print "0.001<=P<0.01:", count2, "genes"; 
    print "P>=0.01:", count3, "genes"
}' data/gene_expression.txt

# 3. sed命令实践
# 将制表符替换为逗号（转换为CSV格式）
sed 's/\t/,/g' data/gene_expression.txt > data/gene_expression.csv

# 删除头文件行
sed '1d' data/gene_expression.txt > data/gene_expression_no_header.txt

# 将ENSG替换为Gene
sed 's/ENSG/Gene/g' data/gene_expression.txt > data/gene_expression_modified.txt
```

#### 1.1.4 管道和重定向高级操作

**实验目标：** 掌握复杂数据处理流程

```bash
# 1. 复杂管道操作示例
# 分析sample_info.csv，统计不同平台的reads数量
cat data/sample_info.csv | 
grep -v "Sample_ID" |           # 去除头行
awk -F',' '{
    platform[$2] += $4
} 
END {
    for(p in platform) 
        print p, platform[p]
}' |
sort -k2 -nr                    # 按第二列数值降序排序

# 2. 统计基因表达数据
cat data/gene_expression.txt |
awk 'NR>1 {
    for(i=2; i<=4; i++) {
        if($i > max) max = $i;
        if(min == "" || $i < min) min = $i;
        sum += $i; count++;
    }
} 
END {
    print "Max expression:", max;
    print "Min expression:", min;
    print "Average expression:", sum/count
}'

# 3. 日志文件分析模拟
cat > logs/analysis.log << 'EOF'
2024-01-15 10:30:01 INFO Starting quality control
2024-01-15 10:35:12 INFO FastQC analysis completed
2024-01-15 10:40:23 WARNING Low quality reads detected
2024-01-15 10:45:34 INFO Trimming completed
2024-01-15 10:50:45 ERROR Alignment failed for sample_003
2024-01-15 10:55:56 INFO Retrying alignment with different parameters
2024-01-15 11:00:07 INFO Alignment completed successfully
EOF

# 分析日志：统计不同级别的消息
grep -o -E "(INFO|WARNING|ERROR)" logs/analysis.log | 
sort | 
uniq -c | 
awk '{print $2": "$1}'
```

---

## 第二部分：生物信息学分析环境配置实践

### 2.1 Conda环境管理实践

#### 2.1.1 Conda安装和配置

**实验目标：** 安装并配置Conda包管理器

```bash
# 1. 下载并安装Miniconda（如果未安装）
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b -p $HOME/miniconda3

# 2. 初始化conda环境
$HOME/miniconda3/bin/conda init bash
source ~/.bashrc

# 3. 验证安装
conda --version
# 预期输出：conda 23.x.x

# 4. 配置conda channels
conda config --add channels defaults
conda config --add channels bioconda
conda config --add channels conda-forge
conda config --set channel_priority strict

# 5. 查看配置
conda config --show channels
```

#### 2.1.2 创建专用分析环境

**实验目标：** 为NGS分析创建独立的conda环境

```bash
# 1. 创建NGS分析环境
conda create -n ngs_analysis python=3.9 -y

# 2. 激活环境
conda activate ngs_analysis

# 3. 验证环境
which python
python --version

# 4. 安装基础生物信息学工具
conda install -c bioconda fastqc trimmomatic bwa samtools bcftools -y

# 5. 验证工具安装
fastqc --version        # 预期：FastQC v0.x.x
trimmomatic -version    # 预期：0.39
bwa                     # 显示帮助信息
samtools --version      # 预期：samtools x.x.x

# 6. 创建环境信息记录
conda list > ngs_analysis_packages.txt
conda env export > ngs_analysis_environment.yml
```

#### 2.1.3 环境管理最佳实践

```bash
# 1. 列出所有环境
conda env list

# 2. 创建特定版本的环境
conda create -n old_tools python=3.7 bwa=0.7.15 samtools=1.9 -y

# 3. 克隆环境
conda create --clone ngs_analysis --name ngs_analysis_backup

# 4. 从文件创建环境
conda env create -f ngs_analysis_environment.yml

# 5. 环境清理
conda clean --all

# 6. 删除不需要的环境
# conda env remove --name old_tools  # 谨慎使用
```

### 2.2 软件安装验证实践

#### 2.2.1 工具功能测试

**实验目标：** 验证安装的生物信息学工具功能正常

```bash
# 确保在ngs_analysis环境中
conda activate ngs_analysis

# 1. 创建测试数据目录
mkdir -p test_data
cd test_data

# 2. 创建模拟FASTQ数据进行测试
cat > test_sample.fastq << 'EOF'
@SEQ_ID_1
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SEQ_ID_2
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII###
@SEQ_ID_3
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
EOF

# 3. 测试FastQC
echo "测试FastQC..."
fastqc test_sample.fastq -o .
if [ -f "test_sample_fastqc.html" ]; then
    echo "✓ FastQC测试成功"
else
    echo "✗ FastQC测试失败"
fi

# 4. 测试Trimmomatic
echo "测试Trimmomatic..."
trimmomatic SE test_sample.fastq test_sample_trimmed.fastq MINLEN:20
if [ -f "test_sample_trimmed.fastq" ]; then
    echo "✓ Trimmomatic测试成功"
    echo "原始序列数: $(grep -c "^@" test_sample.fastq)"
    echo "过滤后序列数: $(grep -c "^@" test_sample_trimmed.fastq)"
else
    echo "✗ Trimmomatic测试失败"
fi

# 5. 创建简单参考序列测试BWA
echo ">chr1" > reference.fasta
echo "GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG" >> reference.fasta

# 建立BWA索引
echo "测试BWA索引建立..."
bwa index reference.fasta
if [ -f "reference.fasta.bwt" ]; then
    echo "✓ BWA索引建立成功"
else
    echo "✗ BWA索引建立失败"
fi

# 6. 测试SAMtools
echo "测试SAMtools..."
samtools faidx reference.fasta
if [ -f "reference.fasta.fai" ]; then
    echo "✓ SAMtools测试成功"
    echo "参考序列信息:"
    cat reference.fasta.fai
else
    echo "✗ SAMtools测试失败"
fi

# 清理测试文件
cd ..
```

---

## 第三部分：测序数据格式详解与实践操作

### 3.1 FASTQ格式深度解析

#### 3.1.1 FASTQ格式结构实践

**实验目标：** 深入理解FASTQ格式的各个组成部分

```bash
mkdir -p data_formats
cd data_formats

# 1. 创建标准FASTQ示例
cat > illumina_example.fastq << 'EOF'
@HWUSI-EAS100R:6:73:941:1973#0/1
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
!''*((((***++""%%''++"++)()...+()''*(((***+"))""*(((***++""""*))(***++""
@HWUSI-EAS100R:6:73:941:1974#0/1
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII###
@HWUSI-EAS100R:6:73:941:1975#0/1
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
EOF

# 2. 解析FASTQ头部信息
echo "=== FASTQ头部信息解析 ==="
grep "^@" illumina_example.fastq | head -1 | 
awk -F':' '{
    print "仪器ID:", $1; 
    print "流通池编号:", $2; 
    print "Lane编号:", $3; 
    print "Tile编号:", $4; 
    print "X坐标:", $5; 
    print "Y坐标:", substr($6,1,index($6,"#")-1)
}'

# 3. 质量值解码实践
echo "=== 质量值解码示例 ==="
quality_line="!''*((((***++\"\"%%''++'++)()...+()''"

# 创建质量值解码脚本
cat > decode_quality.py << 'EOF'
#!/usr/bin/env python3
import sys

def decode_phred33(quality_string):
    """解码Phred+33质量值"""
    qualities = []
    for char in quality_string:
        q_score = ord(char) - 33
        error_prob = 10**(-q_score/10)
        accuracy = 1 - error_prob
        qualities.append({
            'char': char,
            'ascii': ord(char),
            'q_score': q_score,
            'error_prob': f"{error_prob:.6f}",
            'accuracy': f"{accuracy:.6f}"
        })
    return qualities

# 测试质量字符串
test_quality = "!''*((((***++\"\"%%''++'++)()...+()''"
print("位置\t字符\tASCII\tQ值\t错误概率\t准确率")
print("-" * 60)

for i, q in enumerate(decode_phred33(test_quality[:10])):  # 只显示前10个
    print(f"{i+1}\t{q['char']}\t{q['ascii']}\t{q['q_score']}\t{q['error_prob']}\t{q['accuracy']}")
EOF

python3 decode_quality.py

# 4. FASTQ统计信息计算
echo "=== FASTQ文件统计信息 ==="
cat > fastq_stats.sh << 'EOF'
#!/bin/bash
fastq_file=$1

if [ ! -f "$fastq_file" ]; then
    echo "文件不存在: $fastq_file"
    exit 1
fi

echo "文件: $fastq_file"
echo "序列总数: $(grep -c "^@" $fastq_file)"
echo "总碱基数: $(grep -v "^[@+]" $fastq_file | tr -d '\n' | wc -c)"

# 计算平均序列长度
total_length=$(grep -v "^[@+]" $fastq_file | awk '{sum += length($0)} END {print sum}')
seq_count=$(grep -c "^@" $fastq_file)
echo "平均序列长度: $(echo "scale=2; $total_length / $seq_count" | bc)"

# GC含量计算
gc_content=$(grep -v "^[@+]" $fastq_file | tr -d '\n' | 
             awk '{
                 gsub(/[^GCgc]/, "", $0); 
                 gc_count = length($0)
             } END {
                 print gc_count
             }')
echo "GC含量: $(echo "scale=2; $gc_content * 100 / $total_length" | bc)%"

# N含量统计
n_count=$(grep -v "^[@+]" $fastq_file | tr -d '\n' | tr -cd 'N' | wc -c)
echo "N碱基数: $n_count"
echo "N含量: $(echo "scale=4; $n_count * 100 / $total_length" | bc)%"
EOF

chmod +x fastq_stats.sh
./fastq_stats.sh illumina_example.fastq
```

#### 3.1.2 不同平台FASTQ差异分析

```bash
# 1. 创建不同平台的FASTQ示例
# Illumina格式
cat > illumina_reads.fastq << 'EOF'
@HWUSI-EAS100R:6:73:941:1973#0/1
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII###
@HWUSI-EAS100R:6:73:941:1974#0/1
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

# PacBio格式（长读长）
cat > pacbio_reads.fastq << 'EOF'
@m54238_180901_142851/4194304/ccs
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH