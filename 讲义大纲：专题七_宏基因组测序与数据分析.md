# 专题七：宏基因组测序与数据分析

## 理论课教学大纲 (2小时)

### 教学目标
- 理解宏基因组学的基本概念与研究问题
- 掌握宏基因组测序实验设计的关键考虑因素
- 了解宏基因组拼接与分箱(binning)的基本策略
- 掌握物种组成分析的主要方法
- 理解功能注释与代谢通路分析的基本原理
- 了解微生物组与宿主互作分析的基本方法

### 第一部分：宏基因组学基本概念与研究问题 (25分钟)
1. 宏基因组学概述
   - 宏基因组学定义与研究对象
   - 微生物组研究的意义
   - 宏基因组学与传统微生物学的区别
   - 宏基因组学研究的历史发展
   - 主要研究领域与应用

2. 微生物组研究的主要环境
   - 人体微生物组
     - 肠道微生物组
     - 口腔微生物组
     - 皮肤微生物组
     - 其他人体微生物组
   - 环境微生物组
     - 土壤微生物组
     - 水体微生物组
     - 极端环境微生物组
   - 植物相关微生物组
     - 根际微生物组
     - 叶际微生物组
     - 内生菌群
   - 动物相关微生物组

3. 宏基因组学研究方法
   - 基于扩增子的方法(16S/18S/ITS)
     - 原理与应用
     - 优势与局限性
   - 宏基因组鸟枪法测序(Shotgun Metagenomics)
     - 原理与应用
     - 优势与局限性
   - 宏转录组学(Metatranscriptomics)
   - 宏蛋白组学(Metaproteomics)
   - 宏代谢组学(Metabolomics)
   - 多组学整合研究策略

4. 宏基因组学研究的主要科学问题
   - 微生物多样性与群落结构
   - 微生物功能与代谢潜能
   - 微生物进化与适应
   - 微生物与环境互作
   - 微生物与宿主互作
   - 微生物组与疾病关系

### 第二部分：宏基因组测序实验设计与挑战 (25分钟)
1. 样本采集与处理
   - 采样策略设计
     - 空间分布考虑
     - 时间序列考虑
     - 样本量确定
   - 样本保存方法
     - 不同环境样本的保存要求
     - 核酸降解防止策略
   - DNA提取方法
     - 不同环境样本的提取方法比较
     - 提取效率与偏好性
     - 污染控制
   - 质量控制关键点

2. 宏基因组测序策略
   - 测序平台选择
     - 短读长平台(Illumina)
     - 长读长平台(PacBio, Nanopore)
     - 平台组合策略
   - 测序深度设计
     - 不同环境的深度要求
     - 稀有物种检测考虑
     - 成本效益分析
   - 文库构建策略
     - 片段大小选择
     - PCR扩增偏好性控制
     - 宿主DNA去除方法

3. 宏基因组测序的技术挑战
   - 样本复杂性
     - 物种丰度差异大
     - 基因组大小差异
     - 序列相似性高
   - 参考基因组缺乏
     - 未培养微生物占比高
     - 参考数据库不完整
   - 数据量巨大
     - 存储挑战
     - 计算挑战
   - 生物信息学分析复杂性

4. 实验设计最佳实践
   - 对照样本设计
   - 技术重复与生物学重复
   - 批次效应控制
   - 污染源监控
   - 数据质量评估策略

### 第三部分：宏基因组拼接与分箱(binning)策略 (30分钟)
1. 宏基因组数据预处理
   - 质量控制特殊考虑
   - 宿主序列去除
   - 污染序列过滤
   - 数据降噪策略
   - 序列错误校正

2. 宏基因组拼接原理与挑战
   - 单样本vs多样本拼接
   - 拼接算法适应性
     - De Bruijn图方法
     - 重叠-布局-一致性(OLC)方法
   - 宏基因组拼接的特殊挑战
     - 覆盖度不均匀
     - 种群内变异
     - 共有序列区域
     - 嵌合体形成

3. 主要宏基因组拼接工具
   - MEGAHIT
     - 算法特点
     - 内存优化策略
     - 参数设置
   - MetaSPAdes
     - 算法特点
     - 错误校正策略
     - 参数设置
   - IDBA-UD
   - 其他拼接工具比较

4. 拼接结果评估
   - 基本统计指标(N50, 总长度等)
   - 完整性评估
   - 嵌合体检测
   - 覆盖度分析

5. 宏基因组分箱(Binning)原理
   - 分箱的目标与意义
   - 基于组成的分箱方法
     - GC含量
     - k-mer频率
     - 密码子使用偏好
   - 基于覆盖度的分箱方法
     - 单样本覆盖度
     - 多样本共变模式
   - 混合分箱策略
   - 分箱算法原理

6. 主要分箱工具
   - MetaBAT
     - 算法原理
     - 参数优化
   - MaxBin
   - CONCOCT
   - 工具比较与选择

7. 基因组组装质量评估
   - CheckM评估方法
     - 单拷贝基因评估
     - 完整性与污染度计算
   - BUSCO评估
   - 分类学鉴定
   - MAG(Metagenome-Assembled Genome)质量标准

### 第四部分：物种组成分析方法 (25分钟)
1. 物种分类系统
   - 分类学层级
   - 细菌与古菌分类
   - 真菌分类
   - 病毒分类
   - 分类学数据库资源

2. 基于标记基因的物种鉴定
   - 16S rRNA基因分析
     - 变异区域选择
     - 参考数据库(Greengenes, Silva, RDP)
     - 分类算法
   - 其他标记基因
     - rpoB
     - gyrB
     - 单拷贝核心基因
   - 方法局限性

3. 基于全基因组的物种鉴定
   - 序列比对方法
     - BLAST基础
     - 比对算法优化
   - k-mer频率方法
     - k-mer索引原理
     - 快速分类算法
   - 分层分类策略

4. 主要物种分类工具
   - MetaPhlAn
     - 标记基因数据库
     - 分类算法
     - 丰度估计方法
   - Kraken/Kraken2
     - k-mer分类原理
     - 数据库构建
     - 分类速度优化
   - Centrifuge
   - CLARK
   - 工具比较与选择

5. 物种多样性分析
   - α多样性指数
     - Shannon指数
     - Simpson指数
     - Chao1丰富度
     - 稀释曲线
   - β多样性分析
     - Bray-Curtis距离
     - UniFrac距离
     - 主坐标分析(PCoA)
     - 非度量多维尺度分析(NMDS)
   - 群落结构比较方法
     - PERMANOVA
     - ANOSIM
     - 差异丰度分析

6. 物种组成可视化方法
   - 堆叠柱状图
   - 热图
   - 网络图
   - 进化树结合丰度图

### 第五部分：功能注释与代谢通路分析 (25分钟)
1. 基因预测与注释
   - 宏基因组基因预测的特殊性
   - 基因预测工具
     - Prodigal
     - MetaGeneMark
     - FragGeneScan
   - 基因冗余去除
   - 基因目录构建

2. 功能注释数据库
   - 通用功能数据库
     - KEGG
     - COG/eggNOG
     - Pfam
     - GO
   - 专业功能数据库
     - CAZy(碳水化合物活性酶)
     - CARD(抗生素抗性)
     - VFDB(毒力因子)
     - dbCAN(碳水化合物酶)

3. 功能注释方法
   - 序列相似性搜索
     - BLAST
     - DIAMOND
     - MMseqs2
   - 结构域识别
     - HMM模型
     - 保守基序搜索
   - 整合注释策略

4. 代谢通路分析
   - 通路完整性评估
   - 通路丰度计算
   - 差异通路识别
   - 通路可视化

5. 功能潜能分析工具
   - HUMAnN
     - 核心算法
     - 通路丰度计算
     - 基因家族分析
   - MEGAN
   - MG-RAST
   - 工具比较与选择

6. 功能组成可视化与解读
   - 功能热图
   - 通路图谱
   - 功能网络
   - 多样本功能比较

### 第六部分：微生物组与宿主互作分析 (10分钟)
1. 微生物-宿主互作研究策略
   - 关联分析方法
   - 因果关系推断
   - 多组学整合分析
   - 实验验证方法

2. 微生物群落间互作分析
   - 物种共现网络构建
   - 相关性分析方法
     - Pearson/Spearman相关
     - SparCC算法
     - SPIEC-EASI
   - 网络特性分析
   - 关键物种识别

3. 微生物与宿主表型关联
   - 统计关联方法
   - 机器学习预测模型
   - 生物标志物识别
   - 功能验证策略

4. 微生物组与疾病研究
   - 病例-对照研究设计
   - 纵向研究设计
   - 干预研究设计
   - 因果关系推断方法
   - 微生物组治疗策略

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握宏基因组数据预处理与质控的方法
- 学习MetaPhlAn/Kraken进行物种分类分析的流程
- 了解MEGAHIT/MetaSPAdes进行宏基因组组装的操作
- 实践HUMAnN进行功能通路分析的方法
- 学会使用多种方法进行宏基因组数据可视化与结果解读

### 第一部分：宏基因组数据预处理与质控 (30分钟)
1. 宏基因组数据特点与预处理流程
   - 数据格式与组织
   - 预处理步骤概述
   - 质控策略设计
   - 实际操作演示

2. 质量控制与过滤
   - FastQC质量评估
   - Trimmomatic/fastp质量过滤
     - 参数优化
     - 宏基因组特殊考虑
   - 宿主序列去除
     - 比对工具选择
     - 参数设置
     - 过滤效果评估
   - 实际操作演示

3. 数据降噪与错误校正
   - k-mer频率过滤
   - 数字正交化方法
   - 错误校正工具使用
   - 实际操作演示

4. 预处理结果评估
   - 过滤前后数据比较
   - 质量分布变化
   - 序列复杂度评估
   - 实际操作演示

### 第二部分：MetaPhlAn/Kraken进行物种分类分析 (30分钟)
1. MetaPhlAn使用
   - 软件安装与配置
   - 数据库准备
   - 基本命令格式
   - 参数设置
     - 分析模式选择
     - 输出格式设置
     - 并行计算设置
   - 结果文件解读
   - 实际操作演示

2. Kraken2使用
   - 软件安装与配置
   - 数据库构建/下载
   - 基本命令格式
   - 参数设置
     - 置信度阈值
     - 内存使用优化
     - 分类精度调整
   - Bracken丰度估计
   - 结果文件解读
   - 实际操作演示

3. 物种分类结果分析
   - 分类学组成统计
   - 多样本比较分析
   - 多样性指数计算
     - vegan包使用
     - α多样性计算
     - β多样性计算
   - 统计检验方法
   - 实际操作演示

4. 物种组成可视化
   - 物种组成堆叠图
     - ggplot2实现
     - 分类层级选择
     - 配色方案
   - 热图绘制
   - 多样性箱线图
   - PCoA/NMDS排序图
   - 实际操作演示

### 第三部分：MEGAHIT/MetaSPAdes进行宏基因组组装 (30分钟)
1. MEGAHIT宏基因组组装
   - 软件安装与配置
   - 基本命令格式
   - 参数设置
     - k-mer大小设置
     - 内存使用控制
     - 复杂度过滤
   - 单样本vs多样本组装
   - 输出文件解读
   - 实际操作演示

2. MetaSPAdes宏基因组组装(可选)
   - 软件安装与配置
   - 基本命令格式
   - 参数设置
   - 输出文件解读
   - 简单演示

3. 组装结果评估
   - QUAST评估工具使用
   - 基本统计指标分析
   - N50, L50计算
   - 覆盖度分析
   - 实际操作演示

4. 基因组分箱(Binning)
   - MetaBAT使用
     - 覆盖度计算
     - 分箱命令
     - 参数优化
   - CheckM质量评估
     - 完整性评估
     - 污染度评估
     - 分类学鉴定
   - 分箱结果可视化
   - 简单演示

### 第四部分：HUMAnN进行功能通路分析 (20分钟)
1. HUMAnN安装与配置
   - 软件安装
   - 数据库准备
   - 依赖工具配置
   - 运行环境设置

2. HUMAnN基本使用
   - 输入数据准备
   - 基本命令格式
   - 参数设置
     - 线程数设置
     - 中间文件处理
     - 输出选项
   - 实际操作演示

3. HUMAnN结果分析
   - 基因家族丰度表
   - 通路丰度表
   - 结果标准化
   - 多样本合并
   - 实际操作演示

4. 功能分析结果统计与可视化
   - 差异丰度分析
   - 功能组成可视化
   - 通路富集分析
   - 实际操作演示

### 第五部分：宏基因组数据可视化与结果解读 (10分钟)
1. 综合可视化策略
   - 物种-功能关联可视化
   - 多组学数据整合可视化
   - 交互式可视化工具
   - 实际操作演示

2. 结果解读与生物学意义
   - 关键发现提取
   - 假设验证方法
   - 结果解释框架
   - 案例分析

3. 宏基因组分析报告撰写
   - 方法描述规范
   - 结果展示要点
   - 图表选择建议
   - 讨论部分要点

## 课后作业
1. 使用MetaPhlAn或Kraken2对提供的宏基因组数据进行物种分类分析，并进行多样性评估
2. 使用MEGAHIT对宏基因组数据进行组装，评估组装质量
3. 使用HUMAnN对宏基因组数据进行功能通路分析，识别关键功能特征
4. 对分析结果进行可视化，并撰写分析报告解释生物学意义
5. 尝试进行物种共现网络分析，探索微生物间的相互作用关系

## 参考资料
1. Quince, C., Walker, A. W., Simpson, J. T., Loman, N. J., & Segata, N. (2017). Shotgun metagenomics, from sampling to analysis. Nature Biotechnology, 35(9), 833-844.
2. Breitwieser, F. P., Lu, J., & Salzberg, S. L. (2019). A review of methods and databases for metagenomic classification and assembly. Briefings in Bioinformatics, 20(4), 1125-1136.
3. Franzosa, E. A., McIver, L. J., Rahnavard, G., Thompson, L. R., Schirmer, M., Weingart, G., ... & Huttenhower, C. (2018). Species-level functional profiling of metagenomes and metatranscriptomes. Nature Methods, 15(11), 962-968.
4. Li, D., Liu, C. M., Luo, R., Sadakane, K., & Lam, T. W. (2015). MEGAHIT: an ultra-fast single-node solution for large and complex metagenomics assembly via succinct de Bruijn graph. Bioinformatics, 31(10), 1674-1676.
5. Nurk, S., Meleshko, D., Korobeynikov, A., & Pevzner, P. A. (2017). metaSPAdes: a new versatile metagenomic assembler. Genome Research, 27(5), 824-834.
6. Segata, N., Waldron, L., Ballarini, A., Narasimhan, V., Jousson, O., & Huttenhower, C. (2012). Metagenomic microbial community profiling using unique clade-specific marker genes. Nature Methods, 9(8), 811-814.
7. Wood, D. E., Lu, J., & Langmead, B. (2019). Improved metagenomic analysis with Kraken 2. Genome Biology, 20(1), 257.