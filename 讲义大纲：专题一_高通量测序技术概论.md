# 专题一：高通量测序技术概论

## 理论课教学大纲 (2小时)

### 教学目标
- 理解高通量测序技术的发展历程与技术演进
- 掌握不同代测序技术的基本原理与区别
- 了解主流测序平台的技术特点与应用场景
- 学习测序实验设计的关键考虑因素

### 第一部分：高通量测序技术的发展历史与技术演进 (25分钟)
1. 测序技术的发展阶段
   - 早期测序技术简介（Maxam-Gilbert法、Sanger法）
   - 人类基因组计划的推动作用
   - 从低通量到高通量的技术突破点
   
2. 高通量测序技术的发展里程碑
   - 454测序技术的出现（2005年）
   - Illumina/Solexa技术的崛起
   - Ion Torrent、PacBio等技术的发展
   - 测序成本的变化趋势分析

3. 高通量测序技术对生命科学研究的影响
   - 研究范式的转变
   - 新兴学科的产生（基因组学、转录组学等）
   - 精准医疗与个性化治疗的推动

### 第二部分：第一代、第二代和第三代测序技术原理比较 (30分钟)
1. 第一代测序技术（Sanger测序）
   - 双脱氧链终止法原理
   - 毛细管电泳技术
   - 技术优缺点与应用局限性
   
2. 第二代测序技术（NGS）
   - 边合成边测序技术原理
   - 大规模并行测序的实现方式
   - 短读长测序的特点与挑战
   - 测序错误类型与来源分析
   
3. 第三代测序技术
   - 单分子实时测序技术（SMRT）原理
   - 纳米孔测序技术原理
   - 长读长测序的优势与应用
   - 测序准确性与读长的权衡

### 第三部分：主流测序平台介绍 (30分钟)
1. Illumina测序平台
   - 技术原理：可逆终止测序法
   - 主要仪器型号与性能参数（MiSeq、NextSeq、HiSeq、NovaSeq等）
   - 数据输出特点与应用范围
   
2. Ion Torrent测序平台
   - 技术原理：半导体测序法
   - 主要仪器型号与性能参数
   - 数据特点与应用场景
   
3. PacBio测序平台
   - SMRT技术原理详解
   - Sequel系统介绍
   - 数据特点与应用优势
   
4. Oxford Nanopore测序平台
   - 纳米孔测序原理详解
   - MinION、GridION、PromethION系统介绍
   - 便携式测序的创新应用
   
5. 其他新兴测序平台简介
   - MGI/BGI测序平台
   - Element Biosciences
   - Singular Genomics

### 第四部分：各测序平台的技术特点、优缺点及适用场景 (20分钟)
1. 技术参数对比
   - 读长分布
   - 通量与成本
   - 准确率与错误模式
   - 运行时间
   
2. 应用场景分析
   - 全基因组测序的平台选择
   - 转录组测序的平台选择
   - 表观基因组学研究的平台选择
   - 宏基因组学研究的平台选择
   - 临床诊断应用的平台选择
   
3. 混合测序策略
   - 短读长与长读长测序互补
   - 多平台数据整合分析方法

### 第五部分：测序实验设计与考虑因素 (15分钟)
1. 实验设计关键要素
   - 研究目标明确化
   - 样本选择与质量控制
   - 测序深度确定
   - 技术重复与生物学重复
   
2. 文库构建策略
   - DNA文库构建方法
   - RNA文库构建方法
   - 特殊应用的文库构建（ChIP-seq、ATAC-seq等）
   
3. 测序成本与周期评估
   - 预算规划
   - 时间安排
   - 数据存储与计算资源评估

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握Linux基本命令与生物信息学分析环境配置
- 了解常见测序数据格式及其特点
- 学习使用公共数据库获取测序数据
- 实践数据传输与管理方法

### 第一部分：Linux操作系统基本命令介绍 (30分钟)
1. Linux系统基础
   - 文件系统结构
   - 终端使用方法
   - 用户权限管理
   
2. 基本命令操作
   - 文件与目录操作命令（ls, cd, mkdir, rm, cp, mv）
   - 文件查看命令（cat, less, head, tail）
   - 文本处理命令（grep, awk, sed）
   - 管道与重定向（|, >, >>）
   
3. 实用技巧
   - 命令历史与快捷键
   - 通配符使用
   - 任务管理（后台运行、进程控制）
   - 脚本编写基础

### 第二部分：生物信息学分析环境配置与测试 (30分钟)
1. 软件安装与管理
   - 包管理器使用（apt, yum, conda）
   - 生物信息学软件安装方法
   - 环境变量配置
   
2. 生物信息学分析环境搭建
   - Conda/Bioconda环境配置
   - 常用分析工具安装
   - Docker容器使用简介
   
3. 环境测试与验证
   - 软件版本检查
   - 测试数据运行
   - 常见问题排查

### 第三部分：常用测序数据格式介绍 (30分钟)
1. FASTQ格式
   - 格式结构与组成
   - 质量值编码方式
   - 格式验证与处理工具
   
2. SAM/BAM格式
   - 格式结构与字段含义
   - SAM头部信息解读
   - SAM与BAM格式转换
   - 索引创建与使用
   
3. VCF格式
   - 格式结构与字段含义
   - 变异信息解读
   - 格式处理与过滤工具
   
4. 其他常见格式
   - BED格式
   - GTF/GFF格式
   - FASTA格式
   - 格式转换工具使用

### 第四部分：公共数据库资源使用 (20分钟)
1. NCBI资源
   - SRA数据库介绍与使用
   - GEO数据库介绍与使用
   - NCBI Genome数据库
   
2. EMBL-EBI资源
   - ENA数据库
   - ArrayExpress数据库
   
3. 专项数据库
   - ENCODE项目数据库
   - GTEx项目数据库
   - 1000基因组项目数据库
   
4. 数据检索与下载
   - 检索策略与关键词选择
   - 元数据解读
   - SRA Run Selector使用
   - 批量下载方法

### 第五部分：数据传输与管理实践 (10分钟)
1. 数据传输工具
   - scp/rsync命令使用
   - FTP/SFTP工具使用
   - Aspera高速传输工具
   
2. 数据管理最佳实践
   - 目录结构组织
   - 命名规范
   - 元数据记录
   - 版本控制
   
3. 数据备份策略
   - 本地备份
   - 云存储备份
   - 备份自动化

## 课后作业
1. 完成Linux基本命令练习，提交操作记录
2. 从SRA数据库下载一个测序数据集并进行格式转换
3. 搭建个人生物信息学分析环境并验证功能
4. 撰写一份测序实验设计方案，针对特定研究问题选择合适的测序平台与参数

## 参考资料
1. Goodwin, S., McPherson, J. D., & McCombie, W. R. (2016). Coming of age: ten years of next-generation sequencing technologies. Nature Reviews Genetics, 17(6), 333-351.
2. Shendure, J., Balasubramanian, S., Church, G. M., Gilbert, W., Rogers, J., Schloss, J. A., & Waterston, R. H. (2017). DNA sequencing at 40: past, present and future. Nature, 550(7676), 345-353.
3. Buffalo, V. (2015). Bioinformatics data skills: Reproducible and robust research with open source tools. O'Reilly Media, Inc.
4. SRA Handbook: https://www.ncbi.nlm.nih.gov/sra/docs/
5. Linux Command Line Basics: https://ubuntu.com/tutorials/command-line-for-beginners