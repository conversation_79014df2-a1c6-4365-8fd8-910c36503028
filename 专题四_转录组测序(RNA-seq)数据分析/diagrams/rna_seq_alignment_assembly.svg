<svg xmlns="http://www.w3.org/2000/svg" width="600" height="400" viewBox="0 0 600 400">
  <rect width="600" height="400" fill="#f8f9fa" />
  <text x="300" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">RNA-seq 比对与转录本组装</text>
  
  <!-- DNA与RNA结构对比 -->
  <g transform="translate(50, 70)">
    <!-- DNA -->
    <rect x="0" y="0" width="200" height="30" fill="#e6f2ff" stroke="#0066cc" stroke-width="1" />
    <text x="100" y="20" font-family="Arial" font-size="12" text-anchor="middle">DNA (基因组)</text>
    
    <!-- 外显子和内含子标记 -->
    <rect x="0" y="40" width="40" height="20" fill="#ffcccc" stroke="#cc0000" stroke-width="1" />
    <rect x="40" y="40" width="60" height="20" fill="#ccffcc" stroke="#00cc00" stroke-width="1" />
    <rect x="100" y="40" width="40" height="20" fill="#ffcccc" stroke="#cc0000" stroke-width="1" />
    <rect x="140" y="40" width="60" height="20" fill="#ccffcc" stroke="#00cc00" stroke-width="1" />
    
    <text x="20" y="55" font-family="Arial" font-size="10" text-anchor="middle">外显子</text>
    <text x="70" y="55" font-family="Arial" font-size="10" text-anchor="middle">内含子</text>
    <text x="120" y="55" font-family="Arial" font-size="10" text-anchor="middle">外显子</text>
    <text x="170" y="55" font-family="Arial" font-size="10" text-anchor="middle">内含子</text>
    
    <!-- RNA (成熟mRNA) -->
    <rect x="0" y="90" width="40" height="20" fill="#ffcccc" stroke="#cc0000" stroke-width="1" />
    <rect x="40" y="90" width="40" height="20" fill="#ffcccc" stroke="#cc0000" stroke-width="1" />
    <text x="40" y="105" font-family="Arial" font-size="10" text-anchor="middle">成熟mRNA</text>
    
    <!-- 剪接连接线 -->
    <path d="M20,60 L20,90 M120,60 L60,90" stroke="#666" stroke-width="1" stroke-dasharray="3,3" />
    
    <!-- 说明文字 -->
    <text x="0" y="130" font-family="Arial" font-size="12" fill="#333">RNA-seq比对挑战: 读段需跨越内含子进行比对</text>
  </g>
  
  <!-- 比对策略 -->
  <g transform="translate(300, 70)">
    <text x="0" y="0" font-family="Arial" font-size="14" font-weight="bold" fill="#333">RNA-seq比对策略</text>
    
    <!-- HISAT2 -->
    <rect x="0" y="20" width="120" height="25" rx="5" ry="5" fill="#e6f2ff" stroke="#0066cc" stroke-width="1" />
    <text x="60" y="37" font-family="Arial" font-size="12" text-anchor="middle">HISAT2</text>
    
    <text x="130" y="37" font-family="Arial" font-size="10" fill="#333">- 分层索引结构</text>
    <text x="130" y="52" font-family="Arial" font-size="10" fill="#333">- 剪接位点检测</text>
    
    <!-- STAR -->
    <rect x="0" y="70" width="120" height="25" rx="5" ry="5" fill="#e6f2ff" stroke="#0066cc" stroke-width="1" />
    <text x="60" y="87" font-family="Arial" font-size="12" text-anchor="middle">STAR</text>
    
    <text x="130" y="87" font-family="Arial" font-size="10" fill="#333">- 后缀数组索引</text>
    <text x="130" y="102" font-family="Arial" font-size="10" fill="#333">- 二阶段比对策略</text>
    
    <!-- 伪比对 -->
    <rect x="0" y="120" width="120" height="25" rx="5" ry="5" fill="#e6ffe6" stroke="#006600" stroke-width="1" />
    <text x="60" y="137" font-family="Arial" font-size="12" text-anchor="middle">Salmon/Kallisto</text>
    
    <text x="130" y="137" font-family="Arial" font-size="10" fill="#333">- 伪比对技术</text>
    <text x="130" y="152" font-family="Arial" font-size="10" fill="#333">- k-mer匹配</text>
  </g>
  
  <!-- 转录本组装 -->
  <g transform="translate(50, 220)">
    <text x="0" y="0" font-family="Arial" font-size="14" font-weight="bold" fill="#333">转录本组装</text>
    
    <!-- 参考指导组装 -->
    <rect x="0" y="20" width="200" height="60" rx="5" ry="5" fill="#fff0e6" stroke="#cc6600" stroke-width="1" />
    <text x="100" y="40" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle">参考指导组装</text>
    <text x="100" y="60" font-family="Arial" font-size="10" text-anchor="middle">StringTie/Cufflinks</text>
    
    <!-- 从头组装 -->
    <rect x="250" y="20" width="200" height="60" rx="5" ry="5" fill="#ffe6e6" stroke="#cc0000" stroke-width="1" />
    <text x="350" y="40" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle">从头组装</text>
    <text x="350" y="60" font-family="Arial" font-size="10" text-anchor="middle">Trinity (De Bruijn图)</text>
    
    <!-- 组装原理图示 -->
    <g transform="translate(0, 100)">
      <!-- 读段覆盖 -->
      <line x1="0" y1="0" x2="200" y2="0" stroke="#999" stroke-width="1" />
      <rect x="20" y="-5" width="60" height="10" fill="#ccccff" stroke="#6666cc" stroke-width="1" />
      <rect x="50" y="-5" width="60" height="10" fill="#ccccff" stroke="#6666cc" stroke-width="1" />
      <rect x="90" y="-5" width="60" height="10" fill="#ccccff" stroke="#6666cc" stroke-width="1" />
      <rect x="120" y="-5" width="60" height="10" fill="#ccccff" stroke="#6666cc" stroke-width="1" />
      
      <!-- 组装结果 -->
      <rect x="20" y="20" width="160" height="15" fill="#ffcccc" stroke="#cc0000" stroke-width="1" />
      <text x="100" y="32" font-family="Arial" font-size="10" text-anchor="middle">组装的转录本</text>
      
      <text x="0" y="50" font-family="Arial" font-size="10" fill="#333">基于覆盖度和拓扑结构重建转录本</text>
    </g>
  </g>
  
  <!-- 注释 -->
  <rect x="50" y="360" width="500" height="30" rx="5" ry="5" fill="#f0f0f0" stroke="#999" stroke-width="1" />
  <text x="300" y="380" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">RNA-seq比对需处理跨越内含子的读段，转录本组装可发现新的转录本变体</text>
</svg>