<svg xmlns="http://www.w3.org/2000/svg" width="600" height="400" viewBox="0 0 600 400">
  <rect width="600" height="400" fill="#f8f9fa" />
  <text x="300" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">RNA-seq 表达量定量方法</text>
  
  <!-- 表达定量方法概述 -->
  <g transform="translate(50, 60)">
    <rect x="0" y="0" width="500" height="40" rx="5" ry="5" fill="#e6f2ff" stroke="#0066cc" stroke-width="1" />
    <text x="250" y="25" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle">表达定量的基本概念: 读段计数 vs 覆盖度 | 基因级别 vs 转录本级别</text>
  </g>
  
  <!-- 计数方法 -->
  <g transform="translate(50, 120)">
    <rect x="0" y="0" width="220" height="30" rx="5" ry="5" fill="#fff0e6" stroke="#cc6600" stroke-width="1" />
    <text x="110" y="20" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle">计数方法</text>
    
    <!-- 计数方法详情 -->
    <rect x="20" y="40" width="180" height="25" rx="3" ry="3" fill="#fff8f0" stroke="#cc6600" stroke-width="1" />
    <text x="110" y="57" font-family="Arial" font-size="12" text-anchor="middle">HTSeq-count</text>
    
    <rect x="20" y="75" width="180" height="25" rx="3" ry="3" fill="#fff8f0" stroke="#cc6600" stroke-width="1" />
    <text x="110" y="92" font-family="Arial" font-size="12" text-anchor="middle">featureCounts</text>
    
    <!-- 计数策略 -->
    <text x="10" y="115" font-family="Arial" font-size="10" fill="#333">- 唯一比对 vs 多重比对</text>
    <text x="10" y="130" font-family="Arial" font-size="10" fill="#333">- 特征重叠处理策略</text>
  </g>
  
  <!-- 标准化方法 -->
  <g transform="translate(330, 120)">
    <rect x="0" y="0" width="220" height="30" rx="5" ry="5" fill="#e6ffe6" stroke="#006600" stroke-width="1" />
    <text x="110" y="20" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle">标准化方法</text>
    
    <!-- 标准化方法详情 -->
    <rect x="20" y="40" width="180" height="25" rx="3" ry="3" fill="#f0fff0" stroke="#006600" stroke-width="1" />
    <text x="110" y="57" font-family="Arial" font-size="12" text-anchor="middle">RPKM/FPKM</text>
    
    <rect x="20" y="75" width="180" height="25" rx="3" ry="3" fill="#f0fff0" stroke="#006600" stroke-width="1" />
    <text x="110" y="92" font-family="Arial" font-size="12" text-anchor="middle">TPM</text>
    
    <!-- 标准化特点 -->
    <text x="10" y="115" font-family="Arial" font-size="10" fill="#333">- 校正测序深度差异</text>
    <text x="10" y="130" font-family="Arial" font-size="10" fill="#333">- 校正基因长度差异</text>
  </g>
  
  <!-- 转录本定量方法 -->
  <g transform="translate(50, 180)">
    <rect x="0" y="0" width="500" height="30" rx="5" ry="5" fill="#f0e6ff" stroke="#6600cc" stroke-width="1" />
    <text x="250" y="20" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle">转录本级别定量方法</text>
    
    <!-- 基于比对的方法 -->
    <g transform="translate(20, 40)">
      <rect x="0" y="0" width="200" height="80" rx="5" ry="5" fill="#f8f0ff" stroke="#6600cc" stroke-width="1" />
      <text x="100" y="20" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle">基于比对的方法</text>
      <text x="100" y="40" font-family="Arial" font-size="12" text-anchor="middle">Cufflinks / StringTie</text>
      <text x="10" y="60" font-family="Arial" font-size="10" fill="#333">- 基于比对结果进行定量</text>
      <text x="10" y="75" font-family="Arial" font-size="10" fill="#333">- 可同时进行转录本组装</text>
    </g>
    
    <!-- 基于k-mer的方法 -->
    <g transform="translate(280, 40)">
      <rect x="0" y="0" width="200" height="80" rx="5" ry="5" fill="#f8f0ff" stroke="#6600cc" stroke-width="1" />
      <text x="100" y="20" font-family="Arial" font-size="12" font-weight="bold" text-anchor="middle">基于k-mer的方法</text>
      <text x="100" y="40" font-family="Arial" font-size="12" text-anchor="middle">Salmon / Kallisto</text>
      <text x="10" y="60" font-family="Arial" font-size="10" fill="#333">- 伪比对技术，无需全比对</text>
      <text x="10" y="75" font-family="Arial" font-size="10" fill="#333">- EM算法解决多重映射</text>
    </g>
  </g>
  
  <!-- 表达定量方法比较 -->
  <g transform="translate(50, 310)">
    <rect x="0" y="0" width="500" height="80" rx="5" ry="5" fill="#f0f0f0" stroke="#999" stroke-width="1" />
    <text x="250" y="20" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle">表达定量方法比较</text>
    
    <line x1="20" y1="30" x2="480" y2="30" stroke="#999" stroke-width="1" />
    
    <text x="100" y="45" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">基因级别计数</text>
    <text x="100" y="60" font-family="Arial" font-size="10" text-anchor="middle">简单、计算效率高</text>
    <text x="100" y="75" font-family="Arial" font-size="10" text-anchor="middle">无法区分转录本变体</text>
    
    <text x="250" y="45" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">基于比对转录本定量</text>
    <text x="250" y="60" font-family="Arial" font-size="10" text-anchor="middle">可发现新转录本</text>
    <text x="250" y="75" font-family="Arial" font-size="10" text-anchor="middle">计算资源需求大</text>
    
    <text x="400" y="45" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">伪比对转录本定量</text>
    <text x="400" y="60" font-family="Arial" font-size="10" text-anchor="middle">速度快、资源需求少</text>
    <text x="400" y="75" font-family="Arial" font-size="10" text-anchor="middle">需要已知转录本参考</text>
  </g>
</svg>