<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">RNA-seq数据分析流程</text>
  
  <!-- Workflow boxes -->
  <!-- Step 1: Quality Control -->
  <rect x="250" y="80" width="300" height="60" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="115" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">数据质控与预处理</text>
  
  <!-- Step 2: Alignment -->
  <rect x="250" y="160" width="300" height="60" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="400" y="195" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">序列比对或直接定量</text>
  
  <!-- Step 3: Transcript Assembly -->
  <rect x="250" y="240" width="300" height="60" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="400" y="275" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">转录本组装(可选)</text>
  
  <!-- Step 4: Expression Quantification -->
  <rect x="250" y="320" width="300" height="60" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="400" y="355" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">表达量定量</text>
  
  <!-- Step 5: Differential Expression -->
  <rect x="250" y="400" width="300" height="60" fill="#e2e3e5" rx="10" ry="10" stroke="#6c757d" stroke-width="2"/>
  <text x="400" y="435" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">差异表达分析</text>
  
  <!-- Step 6: Functional Annotation -->
  <rect x="250" y="480" width="300" height="60" fill="#d3d3d3" rx="10" ry="10" stroke="#495057" stroke-width="2"/>
  <text x="400" y="515" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">功能注释与富集分析</text>
  
  <!-- Connecting arrows -->
  <line x1="400" y1="140" x2="400" y2="160" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="220" x2="400" y2="240" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="300" x2="400" y2="320" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="380" x2="400" y2="400" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="460" x2="400" y2="480" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Tools for each step -->
  <text x="580" y="115" font-family="Arial" font-size="14" text-anchor="start">FastQC, Trimmomatic, Cutadapt</text>
  <text x="580" y="195" font-family="Arial" font-size="14" text-anchor="start">HISAT2, STAR, Salmon, Kallisto</text>
  <text x="580" y="275" font-family="Arial" font-size="14" text-anchor="start">StringTie, Trinity</text>
  <text x="580" y="355" font-family="Arial" font-size="14" text-anchor="start">HTSeq-count, featureCounts, Salmon</text>
  <text x="580" y="435" font-family="Arial" font-size="14" text-anchor="start">DESeq2, edgeR, limma-voom</text>
  <text x="580" y="515" font-family="Arial" font-size="14" text-anchor="start">GO, KEGG, clusterProfiler</text>
  
  <!-- Input/Output for each step -->
  <text x="150" y="115" font-family="Arial" font-size="14" text-anchor="end">FASTQ文件</text>
  <text x="150" y="195" font-family="Arial" font-size="14" text-anchor="end">清洗后的FASTQ文件</text>
  <text x="150" y="275" font-family="Arial" font-size="14" text-anchor="end">比对结果(BAM文件)</text>
  <text x="150" y="355" font-family="Arial" font-size="14" text-anchor="end">比对结果/转录本</text>
  <text x="150" y="435" font-family="Arial" font-size="14" text-anchor="end">表达矩阵</text>
  <text x="150" y="515" font-family="Arial" font-size="14" text-anchor="end">差异表达基因列表</text>
  
  <!-- Arrow definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d"/>
    </marker>
  </defs>
  
  <!-- Legend -->
  <text x="400" y="570" font-family="Arial" font-size="14" text-anchor="middle">* 虚线表示可选步骤</text>
</svg>
