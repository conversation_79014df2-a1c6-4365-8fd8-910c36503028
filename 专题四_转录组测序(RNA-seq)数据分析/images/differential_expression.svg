<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">差异表达分析</text>
  
  <!-- Statistical models -->
  <rect x="100" y="80" width="600" height="120" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="110" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">统计模型</text>
  
  <!-- Poisson distribution -->
  <rect x="150" y="130" width="200" height="50" fill="#e9ecef" rx="5" ry="5"/>
  <text x="250" y="160" font-family="Arial" font-size="16" text-anchor="middle">泊松分布</text>
  
  <!-- Negative binomial distribution -->
  <rect x="450" y="130" width="200" height="50" fill="#e9ecef" rx="5" ry="5"/>
  <text x="550" y="160" font-family="Arial" font-size="16" text-anchor="middle">负二项分布</text>
  
  <!-- DESeq2 -->
  <rect x="100" y="220" width="280" height="160" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="240" y="250" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">DESeq2</text>
  
  <text x="120" y="280" font-family="Arial" font-size="14" text-anchor="start">• 负二项分布模型</text>
  <text x="120" y="310" font-family="Arial" font-size="14" text-anchor="start">• 经验贝叶斯方法估计离散度</text>
  <text x="120" y="340" font-family="Arial" font-size="14" text-anchor="start">• 收缩估计策略</text>
  <text x="120" y="370" font-family="Arial" font-size="14" text-anchor="start">• 异常值处理</text>
  
  <!-- edgeR -->
  <rect x="420" y="220" width="280" height="160" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="560" y="250" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">edgeR</text>
  
  <text x="440" y="280" font-family="Arial" font-size="14" text-anchor="start">• 负二项分布模型</text>
  <text x="440" y="310" font-family="Arial" font-size="14" text-anchor="start">• 经验贝叶斯框架</text>
  <text x="440" y="340" font-family="Arial" font-size="14" text-anchor="start">• 精确检验vs似然比检验</text>
  <text x="440" y="370" font-family="Arial" font-size="14" text-anchor="start">• 标签加权策略</text>
  
  <!-- Visualization -->
  <rect x="100" y="400" width="600" height="180" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="400" y="430" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">结果可视化</text>
  
  <!-- Volcano plot -->
  <rect x="150" y="460" width="200" height="100" fill="#e9ecef" rx="5" ry="5"/>
  <text x="250" y="480" font-family="Arial" font-size="16" text-anchor="middle">火山图</text>
  
  <!-- Volcano plot simplified -->
  <line x1="170" y1="540" x2="330" y2="540" stroke="#6c757d" stroke-width="1"/>
  <line x1="250" y1="490" x2="250" y2="550" stroke="#6c757d" stroke-width="1"/>
  <text x="250" y="560" font-family="Arial" font-size="12" text-anchor="middle">log2FoldChange</text>
  <text x="160" y="515" font-family="Arial" font-size="12" text-anchor="middle" transform="rotate(-90, 160, 515)">-log10(P-value)</text>
  
  <!-- Points -->
  <circle cx="200" cy="520" r="3" fill="#198754"/>
  <circle cx="220" cy="510" r="3" fill="#198754"/>
  <circle cx="230" cy="530" r="3" fill="#6c757d"/>
  <circle cx="240" cy="520" r="3" fill="#6c757d"/>
  <circle cx="250" cy="510" r="3" fill="#6c757d"/>
  <circle cx="260" cy="530" r="3" fill="#6c757d"/>
  <circle cx="270" cy="520" r="3" fill="#6c757d"/>
  <circle cx="280" cy="510" r="3" fill="#dc3545"/>
  <circle cx="300" cy="520" r="3" fill="#dc3545"/>
  
  <!-- MA plot -->
  <rect x="450" y="460" width="200" height="100" fill="#e9ecef" rx="5" ry="5"/>
  <text x="550" y="480" font-family="Arial" font-size="16" text-anchor="middle">MA图</text>
  
  <!-- MA plot simplified -->
  <line x1="470" y1="540" x2="630" y2="540" stroke="#6c757d" stroke-width="1"/>
  <line x1="550" y1="490" x2="550" y2="550" stroke="#6c757d" stroke-width="1"/>
  <text x="550" y="560" font-family="Arial" font-size="12" text-anchor="middle">平均表达量</text>
  <text x="460" y="515" font-family="Arial" font-size="12" text-anchor="middle" transform="rotate(-90, 460, 515)">log2FoldChange</text>
  
  <!-- Points -->
  <circle cx="500" cy="520" r="3" fill="#198754"/>
  <circle cx="520" cy="530" r="3" fill="#198754"/>
  <circle cx="530" cy="540" r="3" fill="#6c757d"/>
  <circle cx="540" cy="535" r="3" fill="#6c757d"/>
  <circle cx="550" cy="540" r="3" fill="#6c757d"/>
  <circle cx="560" cy="545" r="3" fill="#6c757d"/>
  <circle cx="570" cy="540" r="3" fill="#6c757d"/>
  <circle cx="580" cy="550" r="3" fill="#dc3545"/>
  <circle cx="600" cy="560" r="3" fill="#dc3545"/>
</svg>
