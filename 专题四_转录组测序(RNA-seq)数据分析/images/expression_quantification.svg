<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">基因表达定量方法</text>
  
  <!-- Gene model -->
  <rect x="100" y="80" width="600" height="80" fill="#e9ecef" rx="5" ry="5"/>
  <text x="400" y="110" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">基因模型</text>
  
  <rect x="150" y="130" width="100" height="20" fill="#0d6efd" rx="5" ry="5"/>
  <rect x="300" y="130" width="80" height="20" fill="#0d6efd" rx="5" ry="5"/>
  <rect x="430" y="130" width="120" height="20" fill="#0d6efd" rx="5" ry="5"/>
  <rect x="600" y="130" width="50" height="20" fill="#0d6efd" rx="5" ry="5"/>
  
  <text x="200" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="white">外显子1</text>
  <text x="340" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="white">外显子2</text>
  <text x="490" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="white">外显子3</text>
  <text x="625" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="white">外显子4</text>
  
  <!-- Read counting methods -->
  <rect x="100" y="180" width="600" height="120" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="210" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">读段计数方法</text>
  
  <!-- Reads -->
  <rect x="150" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="220" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="300" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="380" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="450" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="520" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  <rect x="600" y="230" width="50" height="15" fill="#dc3545" rx="5" ry="5"/>
  
  <text x="150" y="270" font-family="Arial" font-size="14" text-anchor="start">• 唯一比对: 只计算唯一比对到基因的读段</text>
  <text x="150" y="290" font-family="Arial" font-size="14" text-anchor="start">• 多重比对: 将多重比对的读段按一定规则分配</text>
  
  <!-- Normalization methods -->
  <rect x="100" y="320" width="600" height="240" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="400" y="350" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">表达量标准化方法</text>
  
  <!-- RPM -->
  <rect x="150" y="380" width="500" height="40" fill="#e9ecef" rx="5" ry="5"/>
  <text x="170" y="405" font-family="Arial" font-size="16" font-weight="bold">RPM</text>
  <text x="400" y="405" font-family="Arial" font-size="14" text-anchor="middle">RPM = (读段计数 / 总读段数) × 10^6</text>
  
  <!-- RPKM/FPKM -->
  <rect x="150" y="430" width="500" height="40" fill="#e9ecef" rx="5" ry="5"/>
  <text x="170" y="455" font-family="Arial" font-size="16" font-weight="bold">RPKM/FPKM</text>
  <text x="400" y="455" font-family="Arial" font-size="14" text-anchor="middle">RPKM = (读段计数 / 基因长度 / 总读段数) × 10^9</text>
  
  <!-- TPM -->
  <rect x="150" y="480" width="500" height="40" fill="#e9ecef" rx="5" ry="5"/>
  <text x="170" y="505" font-family="Arial" font-size="16" font-weight="bold">TPM</text>
  <text x="400" y="505" font-family="Arial" font-size="14" text-anchor="middle">TPM = (RPK / 总RPK) × 10^6</text>
  
  <!-- Comparison -->
  <text x="150" y="540" font-family="Arial" font-size="14" text-anchor="start">• TPM优势: 样本间可比性更好，总和恒定为10^6</text>
</svg>
