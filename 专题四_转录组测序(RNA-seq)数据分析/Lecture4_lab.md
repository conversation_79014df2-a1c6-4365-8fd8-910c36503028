# 专题四：转录组测序(RNA-seq)数据分析 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握HISAT2和STAR转录组比对工具的使用
2. 学会StringTie和featureCounts进行转录本定量
3. 掌握DESeq2和edgeR进行差异表达分析
4. 学会功能富集分析和可视化方法

## 实验环境准备

### 软件环境配置
```bash
# 创建专题四专用环境
conda create -n rna_seq_analysis python=3.8 -y
conda activate rna_seq_analysis

# 安装核心软件
conda install -c bioconda hisat2 star stringtie subread samtools bcftools -y
conda install -c bioconda rseqc gffutils -y
conda install -c r r-deseq2 r-edger r-ggplot2 r-clusterProfiler -y

# 验证安装
echo "=== 软件版本验证 ==="
hisat2 --version
STAR --version
stringtie --version
featureCounts -v
samtools --version

echo "软件安装完成！"
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p rna_seq_practice/{data/{reference,raw,processed},results/{alignment,assembly,quantification,diff_analysis},scripts,logs}
cd rna_seq_practice

# 创建简化的参考基因组（用于教学演示）
cat > data/reference/chr22_mini.fa << 'EOF'
>chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

# 创建简化的基因注释文件
cat > data/reference/chr22_mini.gtf << 'EOF'
chr22	ensembl	gene	100	400	.	+	.	gene_id "ENSG00000001"; gene_name "GENE1"; gene_biotype "protein_coding";
chr22	ensembl	transcript	100	400	.	+	.	gene_id "ENSG00000001"; transcript_id "ENST00000001"; gene_name "GENE1";
chr22	ensembl	exon	100	200	.	+	.	gene_id "ENSG00000001"; transcript_id "ENST00000001"; exon_number "1";
chr22	ensembl	exon	300	400	.	+	.	gene_id "ENSG00000001"; transcript_id "ENST00000001"; exon_number "2";
chr22	ensembl	gene	500	800	.	-	.	gene_id "ENSG00000002"; gene_name "GENE2"; gene_biotype "protein_coding";
chr22	ensembl	transcript	500	800	.	-	.	gene_id "ENSG00000002"; transcript_id "ENST00000002"; gene_name "GENE2";
chr22	ensembl	exon	500	600	.	-	.	gene_id "ENSG00000002"; transcript_id "ENST00000002"; exon_number "1";
chr22	ensembl	exon	700	800	.	-	.	gene_id "ENSG00000002"; transcript_id "ENST00000002"; exon_number "2";
EOF

# 创建模拟RNA-seq双端测序数据（正常表达样本）
cat > data/raw/control_R1.fastq << 'EOF'
@SRR_control_1/1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_2/1
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR_control_3/1
TCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_4/1
CAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat > data/raw/control_R2.fastq << 'EOF'
@SRR_control_1/2
TGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_2/2
ACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR_control_3/2
TGCAGACATTCAATTGTTATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_4/2
TGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

# 创建处理组样本（模拟差异表达）
cp data/raw/control_R1.fastq data/raw/treated_R1.fastq
cp data/raw/control_R2.fastq data/raw/treated_R2.fastq

echo "实验数据准备完成！"
```

---

## 第一部分：HISAT2/STAR转录组比对实践

### 1.1 参考基因组准备与索引构建

#### 1.1.1 HISAT2索引构建实践

**实验目标：** 学会构建HISAT2索引，理解剪接位点提取的重要性

```bash
echo "=== HISAT2索引构建实践 ==="

# 1. 检查参考基因组文件
echo "检查参考基因组文件格式..."
head -5 data/reference/chr22_mini.fa
echo "序列长度: $(grep -v "^>" data/reference/chr22_mini.fa | wc -c)"

# 2. 检查GTF注释文件
echo -e "\n检查GTF注释文件..."
head -5 data/reference/chr22_mini.gtf
echo "注释条目数: $(wc -l < data/reference/chr22_mini.gtf)"

# 3. 提取剪接位点信息
echo -e "\n=== 提取剪接位点信息 ==="
extract_splice_sites.py data/reference/chr22_mini.gtf > data/reference/splice_sites.txt

# 检查剪接位点文件
echo "已知剪接位点:"
cat data/reference/splice_sites.txt

# 4. 提取外显子信息
echo -e "\n=== 提取外显子信息 ==="
extract_exons.py data/reference/chr22_mini.gtf > data/reference/exons.txt

# 检查外显子文件
echo "外显子信息:"
cat data/reference/exons.txt

# 5. 构建HISAT2索引
echo -e "\n=== 构建HISAT2索引 ==="
hisat2-build \
    data/reference/chr22_mini.fa \
    data/reference/chr22_hisat2_index \
    --ss data/reference/splice_sites.txt \
    --exon data/reference/exons.txt \
    -p 2

# 验证索引文件
echo -e "\n索引文件列表:"
ls -la data/reference/chr22_hisat2_index*

echo "HISAT2索引构建完成！"
```

#### 1.1.2 STAR索引构建实践

```bash
echo "=== STAR索引构建实践 ==="

# 1. 创建STAR索引目录
mkdir -p data/reference/star_index

# 2. 构建STAR索引（针对小基因组调整参数）
echo "构建STAR索引..."
STAR --runMode genomeGenerate \
    --genomeDir data/reference/star_index \
    --genomeFastaFiles data/reference/chr22_mini.fa \
    --sjdbGTFfile data/reference/chr22_mini.gtf \
    --genomeSAindexNbases 4 \
    --runThreadN 2

# 参数解释：
echo -e "\n=== STAR索引参数解释 ==="
cat << 'EOF'
--runMode genomeGenerate: 索引构建模式
--genomeDir: 索引输出目录
--genomeFastaFiles: 参考基因组FASTA文件
--sjdbGTFfile: 基因注释GTF文件
--genomeSAindexNbases: 索引参数（小基因组设为4）
--runThreadN: 线程数
EOF

# 3. 验证索引文件
echo -e "\n索引文件列表:"
ls -la data/reference/star_index/

echo "STAR索引构建完成！"
```

### 1.2 HISAT2比对详细操作

#### 1.2.1 基本比对流程

```bash
echo "=== HISAT2比对实践 ==="

# 1. 单样本双端比对
echo "正在比对control样本..."
hisat2 -x data/reference/chr22_hisat2_index \
    -1 data/raw/control_R1.fastq \
    -2 data/raw/control_R2.fastq \
    -S results/alignment/control_hisat2.sam \
    --summary-file results/alignment/control_hisat2_summary.txt \
    -p 2

# 2. 比对treated样本
echo "正在比对treated样本..."
hisat2 -x data/reference/chr22_hisat2_index \
    -1 data/raw/treated_R1.fastq \
    -2 data/raw/treated_R2.fastq \
    -S results/alignment/treated_hisat2.sam \
    --summary-file results/alignment/treated_hisat2_summary.txt \
    -p 2

echo "HISAT2比对完成！"
```

#### 1.2.2 比对结果处理和统计

```bash
# 创建比对结果处理脚本
cat > scripts/process_alignment.sh << 'EOF'
#!/bin/bash
# RNA-seq比对结果处理脚本

sample_name=$1
input_sam=$2

echo "=== 处理样本: $sample_name ==="

# 1. SAM转BAM
echo "SAM文件转换为BAM..."
samtools view -bS $input_sam > results/alignment/${sample_name}.bam

# 2. 排序
echo "BAM文件排序..."
samtools sort results/alignment/${sample_name}.bam -o results/alignment/${sample_name}.sorted.bam

# 3. 建立索引
echo "建立BAM索引..."
samtools index results/alignment/${sample_name}.sorted.bam

# 4. 比对统计
echo "生成比对统计信息..."
samtools flagstat results/alignment/${sample_name}.sorted.bam > results/alignment/${sample_name}_flagstat.txt

# 5. 基本统计信息
echo "样本: $sample_name" > results/alignment/${sample_name}_stats.txt
echo "总reads数: $(samtools view -c results/alignment/${sample_name}.sorted.bam)" >> results/alignment/${sample_name}_stats.txt
echo "比对上的reads数: $(samtools view -c -F 4 results/alignment/${sample_name}.sorted.bam)" >> results/alignment/${sample_name}_stats.txt

# 计算比对率
total_reads=$(samtools view -c results/alignment/${sample_name}.sorted.bam)
mapped_reads=$(samtools view -c -F 4 results/alignment/${sample_name}.sorted.bam)
if [ $total_reads -gt 0 ]; then
    mapping_rate=$(echo "scale=2; $mapped_reads * 100 / $total_reads" | bc)
    echo "比对率: ${mapping_rate}%" >> results/alignment/${sample_name}_stats.txt
fi

echo "✓ $sample_name 处理完成"
EOF

chmod +x scripts/process_alignment.sh

# 处理比对结果
echo "=== 处理HISAT2比对结果 ==="
./scripts/process_alignment.sh control_hisat2 results/alignment/control_hisat2.sam
./scripts/process_alignment.sh treated_hisat2 results/alignment/treated_hisat2.sam

# 显示比对统计结果
echo -e "\n=== 比对结果统计 ==="
echo "Control样本统计:"
cat results/alignment/control_hisat2_stats.txt

echo -e "\nTreated样本统计:"
cat results/alignment/treated_hisat2_stats.txt
```

### 1.3 STAR比对详细操作

#### 1.3.1 STAR比对实践

```bash
echo "=== STAR比对实践 ==="

# 1. Control样本STAR比对
echo "STAR比对control样本..."
STAR --genomeDir data/reference/star_index \
    --readFilesIn data/raw/control_R1.fastq data/raw/control_R2.fastq \
    --outFileNamePrefix results/alignment/control_star_ \
    --outSAMtype BAM SortedByCoordinate \
    --runThreadN 2

# 2. Treated样本STAR比对
echo "STAR比对treated样本..."
STAR --genomeDir data/reference/star_index \
    --readFilesIn data/raw/treated_R1.fastq data/raw/treated_R2.fastq \
    --outFileNamePrefix results/alignment/treated_star_ \
    --outSAMtype BAM SortedByCoordinate \
    --runThreadN 2

echo "STAR比对完成！"
```

#### 1.3.2 STAR结果分析

```bash
# 创建STAR结果分析脚本
cat > scripts/analyze_star_results.py << 'EOF'
#!/usr/bin/env python3
"""
STAR比对结果分析脚本
分析STAR输出的日志文件，提取关键比对统计信息
"""

import os
import re

def parse_star_log(log_file):
    """解析STAR日志文件"""
    stats = {}
    
    if not os.path.exists(log_file):
        print(f"警告: 日志文件 {log_file} 不存在")
        return stats
    
    with open(log_file, 'r') as f:
        content = f.read()
        
        # 提取关键统计信息
        patterns = {
            'total_reads': r'Number of input reads \|\s+(\d+)',
            'mapped_reads': r'Uniquely mapped reads number \|\s+(\d+)',
            'mapped_percent': r'Uniquely mapped reads % \|\s+([\d.]+)%',
            'splice_junctions': r'Number of splices: Total \|\s+(\d+)',
            'novel_junctions': r'Number of splices: Novel \|\s+(\d+)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                stats[key] = match.group(1)
    
    return stats

def generate_report(sample_name, stats):
    """生成比对报告"""
    print(f"\n=== STAR比对报告: {sample_name} ===")
    
    if not stats:
        print("无法获取统计信息")
        return
    
    print(f"总输入reads数: {stats.get('total_reads', 'N/A')}")
    print(f"唯一比对reads数: {stats.get('mapped_reads', 'N/A')}")
    print(f"唯一比对率: {stats.get('mapped_percent', 'N/A')}%")
    print(f"剪接位点总数: {stats.get('splice_junctions', 'N/A')}")
    print(f"新发现剪接位点: {stats.get('novel_junctions', 'N/A')}")

if __name__ == "__main__":
    # 分析control样本
    control_stats = parse_star_log("results/alignment/control_star_Log.final.out")
    generate_report("Control", control_stats)
    
    # 分析treated样本  
    treated_stats = parse_star_log("results/alignment/treated_star_Log.final.out")
    generate_report("Treated", treated_stats)
    
    # 比较分析
    print(f"\n=== 样本比较 ===")
    if control_stats and treated_stats:
        print("样本\t总reads\t比对reads\t比对率\t剪接位点")
        print(f"Control\t{control_stats.get('total_reads', 'N/A')}\t{control_stats.get('mapped_reads', 'N/A')}\t{control_stats.get('mapped_percent', 'N/A')}%\t{control_stats.get('splice_junctions', 'N/A')}")
        print(f"Treated\t{treated_stats.get('total_reads', 'N/A')}\t{treated_stats.get('mapped_reads', 'N/A')}\t{treated_stats.get('mapped_percent', 'N/A')}%\t{treated_stats.get('splice_junctions', 'N/A')}")
EOF

chmod +x scripts/analyze_star_results.py

# 运行STAR结果分析
echo "=== 分析STAR比对结果 ==="
python3 scripts/analyze_star_results.py

# 为STAR输出的BAM文件建立索引
echo -e "\n=== 为STAR输出建立索引 ==="
for bam in results/alignment/*_star_Aligned.sortedByCoord.out.bam; do
    if [ -f "$bam" ]; then
        echo "为 $(basename $bam) 建立索引..."
        samtools index "$bam"
    fi
done
```

### 1.4 比对工具性能比较

```bash
# 创建比对工具比较脚本
cat > scripts/compare_aligners.sh << 'EOF'
#!/bin/bash
# 比较HISAT2和STAR比对结果

echo "=== 比对工具性能比较 ==="
echo -e "工具\t样本\t总reads\t比对reads\t比对率"

# HISAT2结果分析
for sample in control treated; do
    if [ -f "results/alignment/${sample}_hisat2.sorted.bam" ]; then
        total=$(samtools view -c results/alignment/${sample}_hisat2.sorted.bam)
        mapped=$(samtools view -c -F 4 results/alignment/${sample}_hisat2.sorted.bam)
        if [ $total -gt 0 ]; then
            rate=$(echo "scale=2; $mapped * 100 / $total" | bc)
        else
            rate="0"
        fi
        echo -e "HISAT2\t$sample\t$total\t$mapped\t${rate}%"
    fi
done

# STAR结果分析
for sample in control treated; do
    bam_file="results/alignment/${sample}_star_Aligned.sortedByCoord.out.bam"
    if [ -f "$bam_file" ]; then
        total=$(samtools view -c "$bam_file")
        mapped=$(samtools view -c -F 4 "$bam_file")
        if [ $total -gt 0 ]; then
            rate=$(echo "scale=2; $mapped * 100 / $total" | bc)
        else
            rate="0"
        fi
        echo -e "STAR\t$sample\t$total\t$mapped\t${rate}%"
    fi
done

echo -e "\n=== 建议 ==="
echo "1. HISAT2: 内存需求低，适合标准RNA-seq分析"
echo "2. STAR: 速度快，剪接位点检测精确，适合大规模数据"
echo "3. 选择标准: 根据计算资源和精度要求进行选择"
EOF

chmod +x scripts/compare_aligners.sh
./scripts/compare_aligners.sh > results/alignment/aligner_comparison.txt

echo "比对工具比较结果:"
cat results/alignment/aligner_comparison.txt
```

## StringTie/Cufflinks进行转录本组装与定量

### 1. StringTie转录本组装与定量

*   **StringTie安装与配置**
    *   使用conda安装StringTie：
        ```bash
        conda install -c bioconda stringtie
        ```

*   **基本命令格式**
    *   `stringtie [options] <input.bam>`

*   **参考指导组装**
    *   使用参考基因组和基因注释文件进行转录本组装。
    *   **参数设置**
        *   `-G <reference.gtf>`: 指定参考基因组注释文件
        *   `-o <output.gtf>`: 指定输出GTF文件
        *   `-p <threads>`: 指定线程数
    *   **GTF文件使用**
        *   GTF文件包含基因、转录本、外显子等信息。
    *   例如：
        ```bash
        stringtie -G Homo_sapiens.GRCh38.104.gtf -o transcripts.gtf Aligned.sortedByCoord.out.bam -p 8
        ```

*   **从头组装模式**
    *   在没有参考基因组的情况下，进行转录本组装。
    *   需要使用`-l`参数指定平均片段长度。
    *   例如：
        ```bash
        stringtie -l 200 -o transcripts.gtf Aligned.sortedByCoord.out.bam -p 8
        ```

*   **多样本合并策略**
    *   将多个样本的转录本组装结果合并成一个文件。
    *   **stringtie --merge使用**
        ```bash
        stringtie --merge -G Homo_sapiens.GRCh38.104.gtf -o merged_transcripts.gtf list.txt
        ```
        *   `list.txt` 文件包含所有样本的GTF文件路径。
    *   **参考转录本整合**
        *   将组装结果与参考转录本进行整合，提高组装的准确性。

*   **表达量估计**
    *   StringTie可以同时进行转录本组装和表达量估计。
    *   **TPM/FPKM输出**
        *   StringTie输出TPM (Transcripts Per Million) 和 FPKM (Fragments Per Kilobase per Million) 两种表达量。
    *   **覆盖度文件生成**
        *   StringTie可以生成覆盖度文件，用于可视化转录本结构。

*   **实际操作演示**
    1.  下载BAM文件。
    2.  下载参考基因组和基因注释文件。
    3.  使用StringTie进行转录本组装和表达量估计。
    4.  将多个样本的转录本组装结果合并成一个文件。

### 2. Cufflinks转录本组装与定量

*   **Cufflinks安装与配置**
    *   使用conda安装Cufflinks：
        ```bash
        conda install -c bioconda cufflinks
        ```

*   **基本命令格式**
    *   `cufflinks [options] <input.bam>`

*   **关键参数设置**
    *   `-g <reference.gtf>`: 指定参考基因组注释文件
    *   `-o <output_dir>`: 指定输出目录
    *   `-p <threads>`: 指定线程数

*   **转录本组装**
    *   使用Cufflinks进行转录本组装：
        ```bash
        cufflinks -g Homo_sapiens.GRCh38.104.gtf -o cufflinks_out Aligned.sortedByCoord.out.bam -p 8
        ```

*   **表达量估计**
    *   Cufflinks可以同时进行转录本组装和表达量估计。
    *   Cufflinks输出FPKM (Fragments Per Kilobase per Million) 表达量。

*   **Cuffmerge使用**
    *   将多个样本的转录本组装结果合并成一个文件：
        ```bash
        cuffmerge -g Homo_sapiens.GRCh38.104.gtf -s Homo_sapiens.GRCh38.dna.primary_assembly.fa assembly_list.txt
        ```
        *   `assembly_list.txt` 文件包含所有样本的GTF文件路径。

*   **实际操作演示**
    1.  下载BAM文件。
    2.  下载参考基因组和基因注释文件。
    3.  使用Cufflinks进行转录本组装和表达量估计。
    4.  将多个样本的转录本组装结果合并成一个文件。

### 3. 转录本组装质量评估

*   **与参考注释比较**
    *   将组装结果与参考注释进行比较，评估组装的准确性。

*   **新转录本评估**
    *   评估新发现的转录本的可靠性。

*   **GffCompare工具使用**
    *   GffCompare 是一种常用的转录本组装质量评估工具。
    *   GffCompare可以将组装结果与参考注释进行比较，并生成评估报告。

*   **组装可视化检查**
    *   使用IGV等可视化工具检查组装结果，评估组装的准确性。

### 4. 表达矩阵生成

*   **StringTie表达矩阵提取**
    *   使用StringTie自带的`prepDE.py`脚本提取表达矩阵。
        ```bash
        prepDE.py -i gtf_file_list.txt
        ```
        *   `gtf_file_list.txt` 文件包含所有样本的GTF文件路径。

*   **表达矩阵格式转换**
    *   将表达矩阵转换为适合后续分析的格式。

*   **表达数据初步探索**
    *   对表达数据进行初步探索，例如：
        *   绘制表达量分布图
        *   进行PCA分析

*   **实际操作演示**
    1.  使用StringTie或Cufflinks进行转录本组装和表达量估计。
    2.  使用`prepDE.py`脚本提取表达矩阵。
    3.  对表达数据进行初步探索。

**总结**

转录本组装和定量是RNA-seq数据分析的重要步骤。掌握StringTie和Cufflinks的使用方法，并对组装结果进行质量评估，可以为后续的差异表达分析奠定基础。 

## DESeq2/edgeR进行差异表达分析

### 1. R环境准备

*   **R/RStudio安装**
    *   从R官网下载R安装包，并安装。
    *   从RStudio官网下载RStudio安装包，并安装。

*   **Bioconductor配置**
    *   Bioconductor是一个R包的集合，专门用于生物信息学分析。
    *   使用以下命令安装Bioconductor：
        ```R
        if (!requireNamespace("BiocManager", quietly = TRUE))
            install.packages("BiocManager")
        BiocManager::install(version = "3.14")
        ```

*   **必要包安装**
    *   安装DESeq2和edgeR等R包：
        ```R
        BiocManager::install("DESeq2")
        BiocManager::install("edgeR")
        BiocManager::install("ggplot2")
        ```

*   **工作环境设置**
    *   设置R的工作目录，方便管理文件。
        ```R
        setwd("/path/to/your/working/directory")
        ```

### 2. DESeq2差异表达分析

*   **数据导入**
    *   **计数矩阵格式要求**
        *   计数矩阵是一个表格，行表示基因或转录本，列表示样本，每个元素表示该基因或转录本在该样本中的读段计数。
    *   **样本信息表格式**
        *   样本信息表是一个表格，包含样本的各种信息，如组别、处理方式等。

*   **DESeqDataSet对象创建**
    *   使用`DESeqDataSetFromMatrix`函数创建DESeqDataSet对象：
        ```R
        dds <- DESeqDataSetFromMatrix(countData = countData,
                                      colData = sampleInfo,
                                      design = ~ condition)
        ```
        *   `countData`: 计数矩阵
        *   `sampleInfo`: 样本信息表
        *   `design`: 实验设计公式

*   **数据预处理**
    *   **低表达基因过滤**
        *   去除低表达基因，减少假阳性。
        ```R
        keep <- rowSums(counts(dds)) >= 10
        dds <- dds[keep,]
        ```
    *   **数据转换**
        *   使用`vst`函数或`rlog`函数对数据进行转换，使其满足线性模型的假设。
        ```R
        vsd <- vst(dds, blind = FALSE)
        ```
    *   **异常值检测**
        *   使用PCA等方法检测异常值。

*   **差异表达分析**
    *   **设计矩阵构建**
        *   根据实验设计，构建设计矩阵。
        ```R
        design(dds) <- ~ condition
        ```
    *   **DESeq函数运行**
        *   使用`DESeq`函数进行差异表达分析。
        ```R
        dds <- DESeq(dds)
        ```
    *   **结果提取与过滤**
        *   使用`results`函数提取差异表达分析结果。
        ```R
        res <- results(dds)
        res <- res[order(res$padj),]
        ```
        *   根据P值和log2FoldChange等指标，对结果进行过滤。

*   **批次效应校正**
    *   如果存在批次效应，可以使用`removeBatchEffect`函数进行校正。

*   **结果可视化**
    *   **MA图**
        ```R
        plotMA(res, ylim=c(-5,5))
        ```
    *   **火山图**
        ```R
        EnhancedVolcano(res,
                        lab = rownames(res),
                        x = 'log2FoldChange',
                        y = 'padj',
                        pCutoff = 0.05,
                        FCcutoff = 1.0)
        ```
    *   **热图**
        ```R
        pheatmap(assay(vsd)[head(rownames(res),20),], cluster_rows=TRUE, show_rownames=TRUE,
                 cluster_cols=TRUE, annotation_col=as.data.frame(colData(dds)[,"condition"]))
        ```

*   **实际操作演示**
    1.  准备计数矩阵和样本信息表。
    2.  使用DESeq2进行差异表达分析。
    3.  对结果进行过滤和可视化。

### 3. edgeR差异表达分析

*   **数据导入与预处理**
    *   使用`readDGE`函数导入计数矩阵和样本信息表。
        ```R
        dge <- readDGE(countData, group=factor(sampleInfo$condition))
        ```

*   **DGEList对象创建**
    *   创建DGEList对象。

*   **数据标准化**
    *   使用`calcNormFactors`函数进行数据标准化。
        ```R
        dge <- calcNormFactors(dge)
        ```

*   **离散度估计**
    *   使用`estimateDisp`函数估计离散度。
        ```R
        dge <- estimateDisp(dge, design)
        ```

*   **差异表达分析**
    *   **精确检验**
        ```R
        et <- exactTest(dge)
        ```
    *   **广义线性模型**
        ```R
        fit <- glmQLFit(dge, design)
        qlf <- glmQLFTest(fit, coef=2)
        ```

*   **结果提取与过滤**
    *   使用`topTags`函数提取差异表达分析结果。
        ```R
        topTags(et)
        ```
    *   根据P值和logFC等指标，对结果进行过滤。

*   **结果可视化**
    *   使用`plotMD`函数绘制MA图。

*   **实际操作演示**
    1.  准备计数矩阵和样本信息表。
    2.  使用edgeR进行差异表达分析。
    3.  对结果进行过滤和可视化。

### 4. 差异表达结果解读

*   **结果表格字段解释**
    *   logFC：log2倍数变化
    *   logCPM：log2 counts per million
    *   LR：似然比统计量
    *   PValue：P值
    *   FDR：FDR校正后的P值

*   **显著性阈值设定**
    *   常用的显著性阈值包括：
        *   P值 < 0.05
        *   FDR < 0.05
        *   log2FoldChange > 1 或 log2FoldChange < -1

*   **上调/下调基因筛选**
    *   根据log2FoldChange的值，筛选出上调基因和下调基因。

*   **结果导出与保存**
    *   将差异表达分析结果导出为文件，方便后续分析。

*   **与DESeq2结果比较**
    *   比较DESeq2和edgeR的差异表达分析结果，评估结果的一致性。

**总结**

差异表达分析是RNA-seq数据分析的核心内容。掌握DESeq2和edgeR的使用方法，并对结果进行解读和验证, 可以获得准确可靠的差异表达基因信息. 

## ClusterProfiler进行功能富集分析

### 1. ClusterProfiler安装与配置

*   **包安装**
    *   ClusterProfiler是一个R包，用于进行功能富集分析。
    *   使用BiocManager安装ClusterProfiler：
        ```R
        BiocManager::install("clusterProfiler")
        ```

*   **注释数据库准备**
    *   ClusterProfiler需要使用注释数据库进行功能富集分析。
    *   常用的注释数据库包括：
        *   GO (Gene Ontology)
        *   KEGG (Kyoto Encyclopedia of Genes and Genomes)
        *   Reactome

*   **物种设置**
    *   根据研究物种，设置相应的物种信息。
    *   例如，对于人类基因组，可以使用`org.Hs.eg.db`包：
        ```R
        BiocManager::install("org.Hs.eg.db")
        library(org.Hs.eg.db)
        ```

### 2. GO富集分析

*   **基因ID转换**
    *   将基因ID转换为Entrez ID，用于GO富集分析。
        ```R
        gene <- bitr(geneList, fromType="SYMBOL",
                      toType="ENTREZID",
                      Orgdb="org.Hs.eg.db")
        ```

*   **enrichGO函数使用**
    *   使用`enrichGO`函数进行GO富集分析。
        ```R
        ego <- enrichGO(gene          = gene$ENTREZID,
                        Orgdb         = org.Hs.eg.db,
                        ont           = "BP",
                        pAdjustMethod = "BH",
                        pvalueCutoff  = 0.05,
                        qvalueCutoff  = 0.05,
                        readable      = TRUE)
        ```
    *   **参数设置与优化**
        *   `gene`: 基因ID列表
        *   `Orgdb`: 物种信息
        *   `ont`: GO类别（BP, CC, MF）
        *   `pAdjustMethod`: 多重检验校正方法
        *   `pvalueCutoff`: P值阈值
        *   `qvalueCutoff`: Q值阈值
        *   `readable`: 是否显示基因名称

*   **结果解读**
    *   `ID`: GO term ID
    *   `Description`: GO term描述
    *   `GeneRatio`: 差异表达基因中，属于该GO term的基因比例
    *   `BgRatio`: 所有基因中，属于该GO term的基因比例
    *   `pvalue`: P值
    *   `p.adjust`: 校正后的P值
    *   `qvalue`: Q值

*   **可视化方法**
    *   **条形图**
        ```R
        barplot(ego, showCategory=20)
        ```
    *   **点图**
        ```R
        dotplot(ego, showCategory=20)
        ```
    *   **网络图**
        ```R
        plotGOgraph(ego)
        ```

*   **实际操作演示**
    1.  准备差异表达基因列表。
    2.  将基因ID转换为Entrez ID。
    3.  使用`enrichGO`函数进行GO富集分析。
    4.  解读GO富集分析结果。
    5.  使用条形图、点图等可视化方法展示富集结果。

### 3. KEGG通路富集分析

*   **enrichKEGG函数使用**
    *   使用`enrichKEGG`函数进行KEGG通路富集分析。
        ```R
        kk <- enrichKEGG(gene          = gene$ENTREZID,
                         organism      = 'hsa',
                         pvalueCutoff  = 0.05,
                         qvalueCutoff  = 0.05)
        ```
    *   **参数设置与优化**
        *   `gene`: 基因ID列表
        *   `organism`: 物种信息（hsa：人类）
        *   `pvalueCutoff`: P值阈值
        *   `qvalueCutoff`: Q值阈值

*   **结果解读**
    *   `ID`: KEGG通路ID
    *   `Description`: KEGG通路描述
    *   `GeneRatio`: 差异表达基因中，属于该KEGG通路的基因比例
    *   `BgRatio`: 所有基因中，属于该KEGG通路的基因比例
    *   `pvalue`: P值
    *   `p.adjust`: 校正后的P值
    *   `qvalue`: Q值

*   **通路图可视化**
    *   使用`pathview`包可视化KEGG通路。
        ```R
        pathview(gene.data  = geneList,
                 pathway.id = "hsa04110",
                 species    = "hsa",
                 limit      = list(gene=max(abs(geneList)), cpd=1))
        ```

*   **实际操作演示**
    1.  准备差异表达基因列表。
    2.  将基因ID转换为Entrez ID。
    3.  使用`enrichKEGG`函数进行KEGG通路富集分析。
    4.  解读KEGG通路富集分析结果。
    5.  使用`pathview`包可视化KEGG通路。

### 4. 基因集富集分析(GSEA)

*   **排序列表准备**
    *   准备一个包含所有基因的排序列表，按照差异表达倍数排序。

*   **GSEA函数使用**
    *   使用`gseGO`函数进行GSEA分析。
        ```R
        gse <- gseGO(geneList     = geneList,
                     Orgdb        = org.Hs.eg.db,
                     ont          = "BP",
                     nPerm        = 10000,
                     pvalueCutoff = 0.05,
                     verbose      = FALSE)
        ```
    *   **结果解读**
        *   NES (Normalized Enrichment Score)：标准化富集分数
        *   pvalue：P值
        *   FDR：FDR校正后的P值

*   **富集图可视化**
    *   使用`gseaplot2`函数可视化GSEA结果。

*   **实际操作演示**
    1.  准备差异表达基因列表。
    2.  将基因ID转换为Entrez ID。
    3.  使用`gseGO`函数进行GSEA分析。
    4.  解读GSEA结果。
    5.  使用`gseaplot2`函数可视化GSEA结果。

### 5. 富集结果整合与解读

*   **多种富集结果比较**
    *   比较GO富集分析、KEGG通路富集分析和GSEA的结果，寻找一致的结论。

*   **生物学意义解释**
    *   结合文献资料和实验数据，对富集结果进行生物学解释。

*   **结果导出与报告**
    *   将富集结果导出为文件，方便分享和交流。

**总结**

功能富集分析是RNA-seq数据分析的重要步骤。掌握ClusterProfiler的使用方法，并对富集结果进行解读和验证，可以帮助我们了解差异表达基因的功能和参与的通路，为后续的实验验证提供依据. 

## 表达数据可视化

### 1. 热图绘制

*   **pheatmap包使用**
    *   pheatmap是一个R包，用于绘制热图。
    *   使用BiocManager安装pheatmap：
        ```R
        BiocManager::install("pheatmap")
        library(pheatmap)
        ```

*   **数据准备与转换**
    *   准备表达矩阵，行表示基因或转录本，列表示样本。
    *   对数据进行log2转换。

*   **聚类参数设置**
    *   设置聚类参数，例如：
        *   `cluster_rows`: 是否对行进行聚类
        *   `cluster_cols`: 是否对列进行聚类
        *   `clustering_distance_rows`: 行聚类距离计算方法
        *   `clustering_method`: 聚类方法

*   **注释信息添加**
    *   添加注释信息，例如：
        *   样本组别
        *   基因功能

*   **配色方案选择**
    *   选择合适的配色方案，使热图更易于解读。

*   **实际操作演示**
    ```R
    # 准备数据
    data <- read.table("expression_matrix.txt", header=TRUE, row.names=1)
    data <- log2(data + 1)

    # 准备样本信息
    annotation <- data.frame(condition = factor(sampleInfo$condition))
    rownames(annotation) <- colnames(data)

    # 绘制热图
    pheatmap(data,
             cluster_rows = TRUE,
             cluster_cols = TRUE,
             annotation_col = annotation,
             color = colorRampPalette(c("blue", "white", "red"))(100))
    ```

### 2. 火山图绘制

*   **EnhancedVolcano包使用**
    *   EnhancedVolcano是一个R包，用于绘制火山图。
    *   使用BiocManager安装EnhancedVolcano：
        ```R
        BiocManager::install("EnhancedVolcano")
        library(EnhancedVolcano)
        ```

*   **数据准备**
    *   准备差异表达分析结果，包括log2FoldChange和P值。

*   **参数设置**
    *   设置火山图参数，例如：
        *   `x`: log2FoldChange
        *   `y`: P值
        *   `pCutoff`: P值阈值
        *   `FCcutoff`: log2FoldChange阈值
        *   `lab`: 基因名称

*   **标签添加**
    *   添加基因名称标签，突出显示重要的基因。

*   **实际操作演示**
    ```R
    # 准备数据
    res <- read.table("deseq2_results.txt", header=TRUE, row.names=1)

    # 绘制火山图
    EnhancedVolcano(res,
                    lab = rownames(res),
                    x = 'log2FoldChange',
                    y = 'padj',
                    pCutoff = 0.05,
                    FCcutoff = 1.0)
    ```

### 3. PCA分析与可视化

*   **PCA原理简介**
    *   PCA (Principal Component Analysis) 是一种常用的降维方法。
    *   PCA可以将高维数据转换为低维数据，同时保留数据的主要特征。

*   **prcomp函数使用**
    *   使用R语言的`prcomp`函数进行PCA分析。
        ```R
        pca <- prcomp(t(data), scale. = TRUE)
        ```

*   **ggplot2绘图**
    *   使用ggplot2包绘制PCA散点图。
        ```R
        library(ggplot2)
        pcaData <- data.frame(PC1=pca$x[,1], PC2=pca$x[,2], condition=sampleInfo$condition)
        ggplot(pcaData, aes(PC1, PC2, color=condition)) +
          geom_point(size=3) +
          xlab(paste0("PC1: ",percentVar[1],"% variance")) +
          ylab(paste0("PC2: ",percentVar[2],"% variance")) +
          coord_fixed()
        ```

*   **结果解读**
    *   PCA散点图可以展示样本之间的相似性和差异性。
    *   PC1和PC2分别表示第一主成分和第二主成分，解释了数据中最大的变异。

*   **实际操作演示**
    1.  准备表达矩阵。
    2.  使用`prcomp`函数进行PCA分析。
    3.  使用ggplot2绘制PCA散点图。

### 4. 其他可视化方法

*   **箱线图**
    *   用于展示基因在不同组别之间的表达水平分布。

*   **小提琴图**
    *   用于展示基因在不同组别之间的表达水平分布，比箱线图更详细。

*   **MA图**
    *   用于展示差异表达分析结果，以平均表达量为横坐标，以log2FoldChange为纵坐标。

*   **基因表达趋势图**
    *   用于展示基因在不同时间点或不同处理条件下的表达趋势。

*   **实际操作演示**
    *   使用ggplot2等R包绘制各种可视化图表。

**总结**

表达数据可视化是RNA-seq数据分析的重要手段。掌握各种可视化方法，可以帮助我们更好地理解和解释RNA-seq数据。 

---

## 第二部分：StringTie转录本组装与定量实践

### 2.1 StringTie基础操作

#### 2.1.1 参考指导的转录本组装

**实验目标：** 使用参考注释进行转录本组装，理解已知转录本的定量

```bash
echo "=== StringTie参考指导组装实践 ==="

# 1. 使用HISAT2比对结果进行StringTie组装
echo "Control样本转录本组装..."
stringtie results/alignment/control_hisat2.sorted.bam \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/control_stringtie.gtf \
    -A results/assembly/control_gene_abundances.tab \
    -e \
    -B \
    -p 2

echo "Treated样本转录本组装..."
stringtie results/alignment/treated_hisat2.sorted.bam \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/treated_stringtie.gtf \
    -A results/assembly/treated_gene_abundances.tab \
    -e \
    -B \
    -p 2

# 参数解释：
cat << 'EOF'
=== StringTie参数详解 ===
-G: 参考注释GTF文件
-o: 输出GTF文件
-A: 基因丰度输出文件
-e: 只估计参考转录本的表达量（不组装新转录本）
-B: 输出Ballgown格式文件用于下游分析
-p: 线程数
EOF

echo "StringTie组装完成！"
```

#### 2.1.2 转录本组装结果分析

```bash
# 创建StringTie结果分析脚本
cat > scripts/analyze_stringtie_results.py << 'EOF'
#!/usr/bin/env python3
"""
StringTie结果分析脚本
分析转录本组装和基因表达定量结果
"""

import pandas as pd
import numpy as np

def analyze_gene_abundances(file_path, sample_name):
    """分析基因丰度文件"""
    print(f"\n=== {sample_name} 基因表达分析 ===")
    
    try:
        # 读取基因丰度文件
        df = pd.read_csv(file_path, sep='\t')
        
        print(f"检测到基因数量: {len(df)}")
        print(f"表达基因数量 (FPKM>0): {len(df[df['FPKM'] > 0])}")
        print(f"高表达基因数量 (FPKM>1): {len(df[df['FPKM'] > 1])}")
        
        # 基本统计
        print(f"\nFPKM统计:")
        print(f"最大值: {df['FPKM'].max():.2f}")
        print(f"最小值: {df['FPKM'].min():.2f}")
        print(f"平均值: {df['FPKM'].mean():.2f}")
        print(f"中位数: {df['FPKM'].median():.2f}")
        
        # 显示表达量最高的基因
        print(f"\n表达量最高的基因:")
        top_genes = df.nlargest(5, 'FPKM')[['Gene Name', 'FPKM', 'TPM']]
        print(top_genes.to_string(index=False))
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在")
        return None
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None

def compare_samples(control_df, treated_df):
    """比较两个样本的表达情况"""
    if control_df is None or treated_df is None:
        print("无法进行样本比较：缺少数据")
        return
    
    print(f"\n=== 样本比较分析 ===")
    
    # 合并数据进行比较
    merged = pd.merge(control_df, treated_df, on='Gene ID', suffixes=('_control', '_treated'))
    
    print(f"共同检测到的基因: {len(merged)}")
    
    # 计算差异表达基因数量（简单倍数变化）
    merged['fold_change'] = np.log2((merged['FPKM_treated'] + 0.1) / (merged['FPKM_control'] + 0.1))
    
    # 统计差异表达基因
    upregulated = len(merged[merged['fold_change'] > 1])  # >2倍上调
    downregulated = len(merged[merged['fold_change'] < -1])  # >2倍下调
    
    print(f"上调基因数量 (>2倍): {upregulated}")
    print(f"下调基因数量 (>2倍): {downregulated}")
    
    # 显示变化最大的基因
    print(f"\n上调最显著的基因:")
    top_up = merged.nlargest(3, 'fold_change')[['Gene Name_control', 'FPKM_control', 'FPKM_treated', 'fold_change']]
    print(top_up.to_string(index=False))
    
    print(f"\n下调最显著的基因:")
    top_down = merged.nsmallest(3, 'fold_change')[['Gene Name_control', 'FPKM_control', 'FPKM_treated', 'fold_change']]
    print(top_down.to_string(index=False))

def analyze_gtf_assembly(gtf_file, sample_name):
    """分析GTF组装文件"""
    print(f"\n=== {sample_name} 转录本组装分析 ===")
    
    try:
        with open(gtf_file, 'r') as f:
            lines = f.readlines()
        
        # 统计各种特征类型
        feature_counts = {}
        for line in lines:
            if line.startswith('#'):
                continue
            fields = line.strip().split('\t')
            if len(fields) >= 3:
                feature = fields[2]
                feature_counts[feature] = feature_counts.get(feature, 0) + 1
        
        print("组装特征统计:")
        for feature, count in feature_counts.items():
            print(f"{feature}: {count}")
            
    except FileNotFoundError:
        print(f"错误: GTF文件 {gtf_file} 不存在")
    except Exception as e:
        print(f"分析GTF文件时出错: {e}")

if __name__ == "__main__":
    # 分析基因表达结果
    control_df = analyze_gene_abundances("results/assembly/control_gene_abundances.tab", "Control")
    treated_df = analyze_gene_abundances("results/assembly/treated_gene_abundances.tab", "Treated")
    
    # 比较样本
    compare_samples(control_df, treated_df)
    
    # 分析转录本组装结果
    analyze_gtf_assembly("results/assembly/control_stringtie.gtf", "Control")
    analyze_gtf_assembly("results/assembly/treated_stringtie.gtf", "Treated")
EOF

chmod +x scripts/analyze_stringtie_results.py
python3 scripts/analyze_stringtie_results.py
```

### 2.2 featureCounts定量分析

#### 2.2.1 基于基因的读数计数

```bash
echo "=== featureCounts基因计数分析 ==="

# 1. 对HISAT2比对结果进行基因计数
echo "使用featureCounts进行基因计数..."
featureCounts -a data/reference/chr22_mini.gtf \
    -o results/quantification/gene_counts.txt \
    -g gene_id \
    -t exon \
    -T 2 \
    results/alignment/control_hisat2.sorted.bam \
    results/alignment/treated_hisat2.sorted.bam

# 2. 检查计数结果
echo -e "\n=== featureCounts结果概览 ==="
echo "计数文件前10行:"
head -10 results/quantification/gene_counts.txt

# 3. 提取计数统计信息
echo -e "\n计数统计信息:"
tail -n +2 results/quantification/gene_counts.txt.summary

# 4. 创建清理后的计数矩阵
echo -e "\n=== 创建表达矩阵 ==="
cat > scripts/process_counts.py << 'EOF'
#!/usr/bin/env python3
"""
处理featureCounts输出，创建表达矩阵
"""

import pandas as pd

def process_feature_counts(input_file, output_file):
    """处理featureCounts输出文件"""
    
    # 读取featureCounts输出
    df = pd.read_csv(input_file, sep='\t', comment='#')
    
    # 提取基因ID和计数列
    gene_counts = df[['Geneid'] + [col for col in df.columns if col.endswith('.bam')]]
    
    # 重命名列
    new_columns = ['gene_id']
    for col in gene_counts.columns[1:]:
        # 从路径中提取样本名称
        sample_name = col.split('/')[-1].replace('_hisat2.sorted.bam', '')
        new_columns.append(sample_name)
    
    gene_counts.columns = new_columns
    
    # 保存处理后的矩阵
    gene_counts.to_csv(output_file, sep='\t', index=False)
    
    print(f"表达矩阵已保存到: {output_file}")
    print(f"基因数量: {len(gene_counts)}")
    print(f"样本数量: {len(gene_counts.columns) - 1}")
    
    # 显示矩阵概览
    print(f"\n表达矩阵概览:")
    print(gene_counts.head())
    
    # 基本统计
    print(f"\n表达统计:")
    for col in gene_counts.columns[1:]:
        total_reads = gene_counts[col].sum()
        expressed_genes = len(gene_counts[gene_counts[col] > 0])
        print(f"{col}: 总reads={total_reads}, 表达基因={expressed_genes}")

if __name__ == "__main__":
    process_feature_counts("results/quantification/gene_counts.txt", 
                          "results/quantification/gene_expression_matrix.txt")
EOF

python3 scripts/process_counts.py
```

### 2.3 多样本转录本合并

#### 2.3.1 创建样本列表和合并转录本

```bash
echo "=== 多样本转录本合并 ==="

# 1. 创建样本列表文件
echo "创建样本列表..."
cat > results/assembly/sample_list.txt << 'EOF'
results/assembly/control_stringtie.gtf
results/assembly/treated_stringtie.gtf
EOF

# 2. 合并所有样本的转录本
echo "合并转录本..."
stringtie --merge \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/merged_transcripts.gtf \
    results/assembly/sample_list.txt

# 3. 检查合并结果
echo -e "\n=== 合并转录本分析 ==="
echo "合并前后转录本统计:"
echo "参考注释转录本数: $(grep -c "transcript" data/reference/chr22_mini.gtf)"
echo "Control组装转录本数: $(grep -c "transcript" results/assembly/control_stringtie.gtf)"
echo "Treated组装转录本数: $(grep -c "transcript" results/assembly/treated_stringtie.gtf)"
echo "合并后转录本数: $(grep -c "transcript" results/assembly/merged_transcripts.gtf)"

# 4. 使用合并转录本重新定量
echo -e "\n=== 使用合并转录本重新定量 ==="
for sample in control treated; do
    echo "重新定量样本: $sample"
    stringtie results/alignment/${sample}_hisat2.sorted.bam \
        -G results/assembly/merged_transcripts.gtf \
        -o results/assembly/${sample}_reprocessed.gtf \
        -A results/assembly/${sample}_reprocessed_abundances.tab \
        -e \
        -B \
        -p 2
done

echo "转录本合并和重新定量完成！"
```

### 2.4 表达量标准化方法比较

```bash
# 创建表达量标准化比较脚本
cat > scripts/compare_normalization.py << 'EOF'
#!/usr/bin/env python3
"""
比较不同表达量标准化方法
比较Raw counts, FPKM, TPM等不同标准化方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_fpkm(counts, gene_lengths, total_mapped_reads):
    """计算FPKM值"""
    # FPKM = (reads * 1000 * 1,000,000) / (gene_length * total_mapped_reads)
    fpkm = (counts * 1000 * 1000000) / (gene_lengths * total_mapped_reads)
    return fpkm

def calculate_tpm(counts, gene_lengths):
    """计算TPM值"""
    # 第一步：计算每个基因的RPK (Reads Per Kilobase)
    rpk = counts / (gene_lengths / 1000)
    
    # 第二步：计算每个样本的scaling factor
    scaling_factor = rpk.sum() / 1000000
    
    # 第三步：计算TPM
    tpm = rpk / scaling_factor
    return tpm

def analyze_normalization_methods():
    """分析不同标准化方法的效果"""
    
    # 读取原始计数矩阵
    try:
        counts_df = pd.read_csv("results/quantification/gene_expression_matrix.txt", sep='\t')
        
        print("=== 表达量标准化方法比较 ===")
        print(f"基因数量: {len(counts_df)}")
        print(f"样本数量: {len(counts_df.columns) - 1}")
        
        # 模拟基因长度（实际应用中从GTF文件获取）
        np.random.seed(42)  # 保证结果可重复
        gene_lengths = np.random.normal(2000, 500, len(counts_df))
        gene_lengths = np.maximum(gene_lengths, 500)  # 最小长度500bp
        
        # 为每个样本计算不同标准化方法
        for col in counts_df.columns[1:]:
            print(f"\n--- 样本: {col} ---")
            
            raw_counts = counts_df[col]
            total_mapped = raw_counts.sum()
            
            print(f"原始计数总和: {total_mapped}")
            print(f"表达基因数 (counts>0): {len(raw_counts[raw_counts > 0])}")
            
            # 计算FPKM
            fpkm = calculate_fpkm(raw_counts, gene_lengths, total_mapped)
            print(f"FPKM平均值: {fpkm.mean():.2f}")
            print(f"FPKM中位数: {fpkm.median():.2f}")
            
            # 计算TPM
            tpm = calculate_tpm(raw_counts, gene_lengths)
            print(f"TPM总和: {tpm.sum():.0f} (理论值应为1,000,000)")
            print(f"TPM平均值: {tpm.mean():.2f}")
            
            # 添加到结果DataFrame
            counts_df[f'{col}_FPKM'] = fpkm
            counts_df[f'{col}_TPM'] = tpm
        
        # 保存标准化结果
        output_file = "results/quantification/normalized_expression.txt"
        counts_df.to_csv(output_file, sep='\t', index=False)
        print(f"\n标准化结果已保存到: {output_file}")
        
        # 比较标准化前后的相关性
        print(f"\n=== 标准化方法相关性分析 ===")
        control_raw = counts_df['control']
        treated_raw = counts_df['treated']
        
        # 过滤掉零值进行相关性计算
        mask = (control_raw > 0) & (treated_raw > 0)
        
        if mask.sum() > 1:
            # Raw counts相关性
            raw_corr = np.corrcoef(control_raw[mask], treated_raw[mask])[0,1]
            print(f"Raw counts相关性: {raw_corr:.3f}")
            
            # FPKM相关性
            fpkm_corr = np.corrcoef(counts_df['control_FPKM'][mask], 
                                   counts_df['treated_FPKM'][mask])[0,1]
            print(f"FPKM相关性: {fpkm_corr:.3f}")
            
            # TPM相关性
            tpm_corr = np.corrcoef(counts_df['control_TPM'][mask], 
                                  counts_df['treated_TPM'][mask])[0,1]
            print(f"TPM相关性: {tpm_corr:.3f}")
        
        return counts_df
        
    except FileNotFoundError:
        print("错误: 找不到基因表达矩阵文件")
        return None
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return None

def summarize_normalization_recommendations():
    """总结标准化方法选择建议"""
    print(f"\n=== 标准化方法选择建议 ===")
    print("1. Raw Counts:")
    print("   - 用途: 差异表达分析（DESeq2, edgeR）")
    print("   - 优点: 保留原始统计分布，适合统计模型")
    print("   - 缺点: 不能直接比较不同基因或样本")
    
    print("\n2. FPKM (Fragments Per Kilobase per Million):")
    print("   - 用途: 样本内基因表达比较")
    print("   - 优点: 校正基因长度和测序深度")
    print("   - 缺点: 样本间比较存在偏差")
    
    print("\n3. TPM (Transcripts Per Million):")
    print("   - 用途: 样本间基因表达比较")
    print("   - 优点: 样本间总和一致，便于比较")
    print("   - 缺点: 不适合差异表达统计分析")
    
    print("\n4. 使用建议:")
    print("   - 可视化和比较: 使用TPM")
    print("   - 差异表达分析: 使用Raw counts")
    print("   - 功能富集分析: 可使用FPKM或TPM")

if __name__ == "__main__":
    result_df = analyze_normalization_methods()
    summarize_normalization_recommendations()
EOF

python3 scripts/compare_normalization.py
```
