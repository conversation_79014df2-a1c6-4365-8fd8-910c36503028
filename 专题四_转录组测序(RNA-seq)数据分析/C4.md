# **Chapter 4: Analysis of Transcriptome Sequencing Data (RNA-seq)**

**Chapter Goals:** This chapter introduces the fundamental principles and applications of RNA sequencing (RNA-seq), a cornerstone technology in modern molecular biology. We will delve into the intricacies of experimental design, explore the diverse types of RNA-seq technologies, and provide a comprehensive overview of the standard bioinformatics workflow for processing and analyzing RNA-seq data. Key analysis steps, including read alignment, transcript assembly, expression quantification, differential expression analysis, and functional interpretation, will be discussed in detail, equipping students with the theoretical knowledge required to understand and critically evaluate RNA-seq studies.

## **Section 4.1: Principles of Transcriptome Sequencing: Experimental Design and Applications**

### **4.1.1 The Transcriptome: A Dynamic Window into Gene Expression**

The central dogma of molecular biology describes the flow of genetic information from DNA to RNA to protein. While the genome represents the relatively static blueprint of an organism, the **transcriptome** captures the dynamic functional state by encompassing the complete set of RNA transcripts produced by the genome under specific conditions.

*   **Definition and Characteristics:**
    *   The **transcriptome** is formally defined as the totality of RNA molecules present in a specific cell type, tissue, or organism at a given time point or under defined conditions. This includes messenger RNA (mRNA), ribosomal RNA (rRNA), transfer RNA (tRNA), and a diverse array of non-coding RNAs (ncRNAs), such as long non-coding RNAs (lncRNAs), microRNAs (miRNAs), circular RNAs (circRNAs), and others.
    *   **Key Characteristics:**
        *   **Dynamic Nature:** Unlike the mostly stable genome, the transcriptome is highly dynamic. Gene expression levels fluctuate rapidly in response to developmental cues, environmental stimuli, disease states, and cellular signals. This dynamism makes the transcriptome a sensitive indicator of cellular activity and physiological state.
        *   **Complexity:** The transcriptome exhibits significant complexity arising from:
            *   **Multiple RNA Types:** Different RNA classes possess distinct structures and functions.
            *   **Vast Dynamic Range:** Transcript abundance can vary by several orders of magnitude, from highly abundant rRNAs and housekeeping mRNAs to rare regulatory transcripts.
            *   **Alternative Splicing:** A single gene can generate multiple mRNA isoforms through alternative splicing, significantly expanding the coding potential and regulatory possibilities.
            *   **Post-transcriptional Modifications:** RNA editing and other modifications further diversify the transcriptome.

*   **Transcriptome vs. Genome: Function in Flux**
    *   The **genome** contains the complete genetic information, essentially the potential of an organism. It is largely identical across different cell types within an individual (somatic mutations notwithstanding).
    *   The **transcriptome** reflects the *active* portion of the genome being expressed under specific circumstances. It varies dramatically between cell types, developmental stages, and conditions, directly dictating cellular function and phenotype. Studying the transcriptome provides insights into which genes are turned 'on' or 'off' and to what extent.

![转录组vs基因组](images/transcriptome_vs_genome.svg)
    *   *Figure 4.1: Conceptual comparison of the genome and transcriptome. The genome represents the static DNA blueprint, while the transcriptome is the dynamic set of RNA molecules actively transcribed, reflecting the current functional state.*

*   **Elaborating on Dynamics and Complexity:**
    *   **Temporal Dynamics:** Consider embryonic development – precise temporal regulation of gene expression orchestrates cell differentiation and morphogenesis, leading to vastly different transcriptomes at different stages.
    *   **Spatial Dynamics (Cell-type Specificity):** A neuron and a hepatocyte share the same genome but possess unique transcriptomes tailored to their specialized functions, determined by cell-type-specific transcription factors and regulatory networks.
    *   **Conditional Dynamics:** Exposure to heat shock, pathogens, or therapeutic drugs triggers rapid transcriptional reprogramming, altering the expression of stress-response genes, metabolic pathways, or drug targets.
    *   **Isoform Complexity:** Alternative splicing allows a limited number of genes (~20,000 in humans) to produce a much larger repertoire of protein isoforms, contributing significantly to biological complexity. Non-coding RNAs add another layer of regulatory complexity.

*   **Significance of Transcriptome Research:**
    *   **Understanding Gene Regulation:** Identifying co-expressed genes and inferring regulatory networks (e.g., transcription factor targets).
    *   **Gene Discovery and Annotation:** Identifying novel genes, transcript isoforms, and non-coding RNAs, particularly in non-model organisms or poorly annotated genomes.
    *   **Elucidating Disease Mechanisms:** Comparing transcriptomes of healthy versus diseased tissues to identify dysregulated genes and pathways, providing clues to pathogenesis and potential biomarkers.
    *   **Drug Discovery and Development:** Identifying potential drug targets, evaluating drug efficacy and toxicity by monitoring transcriptional responses, and stratifying patients for personalized medicine based on expression signatures.

### **4.1.2 Major Types of RNA Sequencing Technologies**

The choice of RNA-seq technology depends heavily on the specific research question. Several strategies exist, each targeting different subsets of the transcriptome or offering unique capabilities.

*   **Total RNA Sequencing (Total RNA-seq):**
    *   **Principle:** Aims to capture the broadest range of RNA species. Typically involves depleting the highly abundant ribosomal RNA (rRNA) prior to library construction, as rRNA can constitute 80-90% of total RNA mass, overwhelming the signal from other transcripts. Methods like Ribo-Zero or RNase H-based depletion are common.
    *   **Application:** Ideal for comprehensive transcriptome surveys, studying both coding (mRNA) and non-coding RNAs (e.g., lncRNAs, circRNAs), and analyzing overall transcriptional landscapes.

*   **mRNA Sequencing (mRNA-seq / Poly(A)-selected RNA-seq):**
    *   **Principle:** Exploits the polyadenylated (poly(A)) tail present on most mature eukaryotic mRNAs. Oligo(dT) probes (sequences of thymidines) are used to selectively capture and enrich for poly(A)+ RNA molecules.
    *   **Application:** The most common approach for studying protein-coding gene expression levels and identifying differentially expressed genes (DEGs). Cost-effective for focusing on the coding portion of the transcriptome. Cannot detect non-polyadenylated RNAs (e.g., most ncRNAs, histone mRNAs).

*   **Small RNA Sequencing (small RNA-seq):**
    *   **Principle:** Employs size selection steps (e.g., gel electrophoresis or magnetic beads) to isolate RNA molecules typically shorter than 200 nucleotides. Specialized library preparation protocols are required due to the small size and specific end structures of small RNAs.
    *   **Application:** Specifically designed for studying microRNAs (miRNAs), small interfering RNAs (siRNAs), PIWI-interacting RNAs (piRNAs), and other small regulatory RNAs, focusing on their expression profiles and discovery.

*   **Long Non-coding RNA Sequencing (lncRNA-seq):**
    *   **Methodology:** Often synonymous with Total RNA-seq (after rRNA depletion), as lncRNAs are captured alongside mRNAs. Bioinformatics analysis then focuses specifically on identifying and quantifying lncRNA transcripts. Targeted capture methods for specific lncRNAs also exist.
    *   **Application:** Dedicated to the study of lncRNA expression, isoform diversity, and potential regulatory roles.

*   **Targeted RNA Sequencing (Targeted RNA-seq):**
    *   **Principle:** Uses custom-designed probes (oligonucleotides) to capture a specific, predefined set of RNA transcripts of interest (e.g., genes within a particular pathway, a panel of cancer-related genes). Capture can occur before or after cDNA synthesis.
    *   **Application:** Provides very high sequencing depth for the targeted regions, enabling highly sensitive detection of expression changes, variant calling within transcripts, or analysis of low-abundance transcripts within the target set. Cost-effective for hypothesis validation or clinical panels. Limits discovery to the pre-selected targets.

*   **Single-Cell RNA Sequencing (scRNA-seq):**
    *   **Principle:** Physically isolates individual cells (e.g., using microfluidics, FACS) and prepares barcoded sequencing libraries for each cell's transcriptome separately. This allows gene expression profiles to be resolved at the single-cell level.
    *   **Application:** Revolutionizing studies of cellular heterogeneity, identifying rare cell populations, reconstructing developmental trajectories, and dissecting complex tissue microenvironments. Technically demanding and computationally intensive, dealing with data sparsity (dropout events).

*   **Spatial Transcriptomics:**
    *   **Principle:** A rapidly evolving field aiming to measure gene expression while retaining information about the spatial location of the RNA molecules within the original tissue context. Techniques vary, including microdissection followed by RNA-seq, in situ sequencing, or array-based capture on slides followed by imaging and sequencing.
    *   **Application:** Enables mapping of gene expression patterns onto tissue architecture, studying cell-cell interactions in their native environment, and understanding spatial organization of biological processes. Resolution and throughput are key areas of ongoing development.

### **4.1.3 Key Factors in RNA-seq Experimental Design**

A robust experimental design is paramount for generating high-quality, interpretable RNA-seq data and achieving statistically sound conclusions. Careful consideration of the following factors is crucial *before* starting the experiment.

*   **Clear Research Objectives:**
    *   **Hypothesis First:** What specific biological question is being addressed? Is the goal to compare expression between conditions, discover novel isoforms, analyze splicing, or characterize non-coding RNAs?
    *   **Technology Alignment:** The research goal dictates the most appropriate RNA-seq technology (Section 4.1.2) and analysis strategy. Mismatched technology can lead to wasted resources or inability to answer the research question.

*   **Sample Selection and Replication Strategy:**
    *   **Representative Samples:** Ensure samples accurately represent the biological conditions under investigation. Define clear inclusion/exclusion criteria. Proper control groups are essential.
    *   **Biological Replicates: The Cornerstone:**
        *   **Definition:** Independent biological units (e.g., different individuals, distinct cell cultures, separate tissue dissections) treated under the same condition.
        *   **Purpose:** **Absolutely essential** for assessing biological variability within a group. This variability is required for statistical inference (e.g., differential expression testing) to distinguish true biological effects from random fluctuation.
        *   **Recommendation:** A minimum of **three biological replicates per condition** is generally recommended for differential expression studies. More replicates increase statistical power, especially for detecting subtle changes or in systems with high inherent variability. Power analysis can help determine optimal sample size.
    *   **Technical Replicates:**
        *   **Definition:** Multiple library preparations and sequencing runs from the *same* initial RNA sample.
        *   **Purpose:** Assess technical variability introduced during library prep and sequencing.
        *   **Recommendation:** With current stable sequencing technologies, technical variability is usually low compared to biological variability. Therefore, **prioritizing biological replicates over technical replicates is strongly advised** for most applications, especially differential expression. Technical replicates might be useful for troubleshooting new protocols or assessing platform noise.
    *   **Controlling Batch Effects:**
        *   **Definition:** Systematic, non-biological variations introduced by processing samples in different batches (e.g., different days, reagents, operators, sequencers).
        *   **Impact:** Batch effects can confound biological comparisons, leading to false positives or masking true differences.
        *   **Mitigation Strategies:**
            *   **Randomization:** Randomly assign samples from different biological groups across processing batches.
            *   **Balanced Design:** Ensure each batch contains a similar mix of samples from all experimental groups. Avoid processing all samples of one condition in one batch and all samples of another condition in a separate batch.
            *   **Record Keeping:** Meticulously document batch information for potential downstream computational correction.

*   **RNA Extraction and Quality Control (QC): A Critical Foundation**
    *   **Extraction Method:** Choose a method optimized for the sample type that yields high-quality RNA with minimal bias.
    *   **RNA Integrity Assessment:**
        *   **Method:** Electrophoretic methods (e.g., Agilent Bioanalyzer, TapeStation) generate an RNA Integrity Number (RIN) or equivalent score (e.g., RQN).
        *   **Interpretation:** RIN ranges from 1 (completely degraded) to 10 (intact). Degraded RNA (low RIN) leads to 3'-bias in poly(A)-selected libraries and inaccurate quantification.
        *   **Requirement:** For standard RNA-seq (especially mRNA-seq), **RIN ≥ 7 is strongly recommended**, with RIN ≥ 8 being preferable for sensitive applications like isoform analysis. Requirements might be relaxed for highly degraded samples (e.g., FFPE) but require specialized protocols.
    *   **Purity Assessment:** Spectrophotometry (e.g., NanoDrop) measures absorbance ratios (A260/A280 ≈ 1.8-2.1; A260/A230 > 1.8) to check for protein and chemical (e.g., phenol, salts) contamination.
    *   **Contamination Control:**
        *   **RNase Prevention:** Use RNase-free reagents, consumables, and techniques throughout the process.
        *   **Genomic DNA (gDNA) Removal:** Residual gDNA can be amplified and sequenced, leading to erroneous results. Treatment with DNase is a standard and crucial step. Verify removal via qPCR or gel electrophoresis if necessary.

*   **Library Construction Strategy:**
    *   **Strand-Specific (Stranded) Libraries:**
        *   **Principle:** Incorporates methods (e.g., dUTP method, ligation of specific adapters) that preserve the original strand orientation of the RNA molecule during library preparation.
        *   **Advantage:** Allows distinguishing reads originating from the sense versus antisense strand of a gene. **Crucial** for accurately quantifying overlapping genes, identifying antisense transcripts, discovering novel ncRNAs, and improving transcript assembly. **Stranded library preparation is now considered standard practice and is highly recommended.**
    *   **Starting Material Choice:**
        *   **Poly(A) Selection vs. rRNA Depletion:** As discussed in Section 4.1.2, choose based on whether the focus is solely on mRNA or includes ncRNAs. rRNA depletion is necessary for Total RNA-seq.
    *   **Fragmentation:** RNA or cDNA is fragmented (enzymatically or mechanically) to a size range suitable for the sequencing platform (e.g., 200-500 bp).
    *   **Adapter Ligation & PCR Amplification:** Sequencing adapters are ligated to fragment ends. PCR amplification increases library yield but should be minimized (e.g., 10-15 cycles) to reduce amplification bias and duplicate reads.

*   **Sequencing Depth and Read Length:**
    *   **Sequencing Depth (Number of Reads):**
        *   **Definition:** Total number of reads generated per sample.
        *   **Considerations:** Deeper sequencing increases the likelihood of detecting low-abundance transcripts and improves the statistical power for differential expression and splicing analysis. Required depth depends on genome size, transcriptome complexity, and research goals.
        *   **General Guidelines (Paired-End Reads):**
            *   Differential Gene Expression: ~20-50 million read pairs per sample.
            *   Isoform discovery/quantification, splicing analysis: ~50-100+ million read pairs per sample.
            *   Single-cell RNA-seq: Highly variable, often 10,000-100,000 reads per cell (after filtering).
    *   **Saturation Analysis:** Assess if sufficient depth has been reached by plotting the number of detected genes/transcripts as a function of increasing (subsampled) read depth. Saturation occurs when the curve plateaus.
    *   **Read Length:**
        *   **Definition:** The length of each individual sequence read (e.g., 50bp, 100bp, 150bp).
        *   **Considerations:** Longer reads (e.g., 100bp, 150bp) generally improve mapping accuracy, especially across splice junctions and in repetitive regions, facilitate isoform discrimination, and aid de novo assembly. Shorter reads might suffice for basic gene counting if the reference genome is good.
    *   **Paired-End (PE) vs. Single-End (SE) Reads:**
        *   **PE Sequencing:** Sequences both ends of a fragment. Provides more information (fragment length, better mapping in repeats, detection of insertions/deletions, fusion gene identification). **PE sequencing is highly recommended for most RNA-seq applications.**
    *   **Cost-Benefit Analysis:** Balance the desired depth and read length against budget constraints. Deeper sequencing yields diminishing returns beyond a certain point.

### **4.1.4 Major Applications of RNA Sequencing**

RNA-seq has become a versatile tool applicable to a wide range of biological investigations:

*   **Gene Expression Profiling:** Quantifying the abundance of thousands of transcripts simultaneously to generate a snapshot of cellular activity.
*   **Differential Gene Expression (DGE) Analysis:** Identifying genes whose expression levels change significantly between experimental conditions or groups. This is arguably the most common application.
*   **Alternative Splicing Analysis:** Detecting and quantifying different transcript isoforms produced from a single gene and identifying changes in splicing patterns between samples.
*   **Novel Transcript Discovery and Annotation:** Identifying previously uncharacterized transcripts, including new protein-coding genes, non-coding RNAs, or novel isoforms of known genes, particularly in non-model organisms or complex disease states.
*   **Fusion Gene Detection:** Identifying chimeric transcripts resulting from genomic rearrangements (e.g., chromosomal translocations), often implicated in cancer.
*   **Allele-Specific Expression (ASE) Analysis:** Quantifying expression specifically from the maternal or paternal allele by leveraging heterozygous single nucleotide polymorphisms (SNPs) within transcript sequences. Useful for studying genomic imprinting or cis-regulatory effects.
*   **RNA Editing Site Identification:** Detecting sites where the RNA sequence has been altered post-transcriptionally (e.g., Adenosine-to-Inosine editing).
*   **Non-coding RNA (ncRNA) Profiling:** Characterizing the expression patterns and potential functions of various classes of ncRNAs (lncRNAs, miRNAs, circRNAs).
*   **Co-expression Network Analysis:** Grouping genes with similar expression patterns across samples to build networks, inferring potential functional modules or regulatory relationships (e.g., using WGCNA).
*   **Integration with Other Omics Data:** Combining transcriptomic data with genomic (DNA-seq, ChIP-seq), proteomic, or metabolomic data for a more holistic, systems-level understanding of biological processes.

### **Section 4.1 Summary**

The transcriptome provides a dynamic view of cellular function, distinct from the static genome. RNA-seq technologies allow for comprehensive or targeted interrogation of this dynamic landscape. Careful experimental design, particularly regarding replication, batch control, and rigorous quality control of RNA and library preparation, is critical for obtaining meaningful results. The versatility of RNA-seq supports a wide array of applications, from basic gene expression profiling to complex analyses of splicing, non-coding RNAs, and single-cell heterogeneity, making it an indispensable tool in modern biology.

## **Section 4.2: The RNA-seq Data Analysis Workflow**

Once sequencing is complete, raw RNA-seq data must undergo a series of computational steps to extract biological insights. While specific tools may vary, a standard workflow generally involves quality control, read mapping or pseudo-alignment, expression quantification, statistical analysis for differential expression, and downstream functional interpretation.

### **4.2.1 Overview of the Standard Analysis Pipeline**

A typical RNA-seq analysis workflow can be conceptualized as follows:

![RNA-seq数据分析流程](images/rnaseq_workflow.svg)
    *   *Figure 4.2: A schematic representation of a standard RNA-seq data analysis workflow. It begins with raw sequencing reads and proceeds through quality control, alignment/quantification, differential expression analysis, and concludes with functional enrichment and interpretation.*

1.  **Raw Data Quality Control (QC) & Preprocessing:**
    *   **Initial QC:** Assess the quality of the raw sequencing reads (FASTQ format) using tools like FastQC or Fastp. Key metrics include per-base quality scores, GC content distribution, adapter content, sequence duplication levels, and k-mer frequencies. This step identifies potential issues with the sequencing run.
    *   **Read Trimming & Filtering:** Remove low-quality bases (typically from the 3' ends), sequencing adapters, and potentially very short reads using tools like Trimmomatic, Cutadapt, or Fastp. For total RNA-seq data, rRNA reads might be computationally filtered if depletion was incomplete.
    *   **Post-Preprocessing QC:** Re-run QC tools on the cleaned reads to confirm the effectiveness of trimming and filtering.

2.  **Read Alignment / Pseudo-alignment:**
    *   **Goal:** Determine the genomic origin of each sequencing read.
    *   **Alignment-based approach:** Use splice-aware aligners (e.g., STAR, HISAT2) to map reads to a reference genome. These tools can handle reads spanning exon-intron boundaries. The output is typically a Sequence Alignment/Map (SAM) or its binary version (BAM) file, containing detailed alignment information for each read. Alignment quality metrics (mapping rate, unique mapping rate) should be assessed.
    *   **Alignment-free (Pseudo-alignment) approach:** Use tools like Salmon or Kallisto, which employ k-mer hashing and other algorithms to rapidly associate reads with reference *transcript* sequences (not the genome) without performing computationally expensive base-to-base alignment. This directly leads to quantification.

3.  **Expression Quantification:**
    *   **Goal:** Estimate the abundance of each gene or transcript in each sample.
    *   **Alignment-based Counting:** If reads were aligned to the genome (BAM file), use tools like featureCounts (from Subread package) or HTSeq-count, along with a gene annotation file (GTF/GFF), to count how many reads overlap the defined features (exons or genes). This generates a **raw count matrix**.
    *   **Pseudo-alignment based Quantification:** Salmon and Kallisto directly output estimated counts and normalized abundance values (like TPM) as part of their process.
    *   **Transcript-level Quantification:** Tools like Salmon, Kallisto, RSEM, or StringTie can estimate the abundance of individual transcript isoforms, often employing statistical models (like the EM algorithm) to resolve reads compatible with multiple isoforms.

4.  **Transcriptome Assembly (Optional):**
    *   **De Novo Assembly:** For organisms without a reference genome, tools like Trinity or SPAdes (in rna mode) assemble reads into putative transcripts based on read overlaps (often using De Bruijn graphs). Computationally intensive and requires careful quality assessment (e.g., using BUSCO).
    *   **Reference-Guided Assembly:** Even with a reference genome, tools like StringTie or Cufflinks can use alignment information (BAM files) to assemble transcripts, potentially identifying novel isoforms or improving existing annotations.

5.  **Data Normalization & Differential Expression Analysis:**
    *   **Normalization:** Adjust raw counts to account for differences in library size (sequencing depth) and RNA composition bias between samples, making expression levels comparable. Methods like TMM (edgeR), RLE/median-of-ratios (DESeq2), CPM, or TPM are used (see Section 4.4.3). **Crucially, statistical packages like DESeq2 and edgeR perform normalization internally based on raw counts.**
    *   **Differential Expression Testing:** Employ statistical methods specifically designed for count data (accounting for overdispersion) to identify genes or transcripts showing statistically significant expression changes between experimental groups. Popular R packages include DESeq2, edgeR, and limma (with voom transformation). Output typically includes log2 fold changes, p-values, and adjusted p-values (FDR).

6.  **Downstream Analysis: Functional Annotation & Enrichment:**
    *   **Functional Annotation:** Assign biological context to differentially expressed genes using databases like Gene Ontology (GO), KEGG pathways, Reactome, etc.
    *   **Enrichment Analysis:** Determine if specific biological pathways, functions, or cellular components are statistically over-represented (using ORA methods) or coordinately changed (using GSEA methods) within the set of differentially expressed genes (see Section 4.6).

7.  **Visualization and Interpretation:**
    *   **Visualization:** Use plots like volcano plots, MA plots, heatmaps, PCA plots, and pathway diagrams to explore, present, and interpret the results.
    *   **Biological Interpretation:** Integrate the findings with existing knowledge, formulate hypotheses, and design follow-up experiments.

### **4.2.2 Reference Genome-Based vs. De Novo Assembly Strategies**

A fundamental strategic decision in RNA-seq analysis is whether to rely on a reference genome or perform de novo assembly.

*   **Reference Genome-Based Analysis:**
    *   **Workflow:** Map reads to the existing reference genome -> Quantify expression based on gene annotations -> Perform downstream analyses. Optionally includes reference-guided assembly to refine annotations.
    *   **Advantages:**
        *   Computationally less demanding than de novo assembly.
        *   Leverages existing knowledge encoded in annotations.
        *   Results are generally more accurate and easier to interpret and compare across studies (assuming a high-quality reference).
        *   Mature and well-established pipelines and tools.
    *   **Disadvantages:**
        *   Heavily dependent on the quality, completeness, and accuracy of the reference genome and its annotation. Poor references lead to mapping biases and inaccurate results.
        *   May miss transcripts from genes entirely absent in the reference or highly divergent alleles/strains.
        *   Cannot be applied if no reference genome is available.

*   **De Novo Transcriptome Assembly:**
    *   **Workflow:** Assemble reads directly into contigs (putative transcripts) -> (Optional) Annotate contigs -> Quantify contig abundance -> Perform downstream analyses using the assembled transcriptome as a reference.
    *   **Advantages:**
        *   Does not require a reference genome; essential for non-model organisms.
        *   Potential to discover truly novel genes and transcripts absent from any reference.
    *   **Disadvantages:**
        *   Computationally very intensive (high RAM and CPU time requirements).
        *   Assembly is challenging; algorithms may produce fragmented, incomplete, chimeric, or redundant contigs. Requires rigorous filtering and quality assessment (e.g., BUSCO).
        *   Annotation of assembled contigs can be difficult.
        *   Can be harder to compare results across different de novo assemblies or studies.

*   **Hybrid Approaches:** Combine elements of both strategies. For instance, mapping reads to a reference first, then assembling the unmapped reads de novo. Or using a de novo assembly to improve or supplement a reference annotation.

*   **Guidance on Choice:**
    *   **High-Quality Reference Available:** The reference-based approach is almost always preferred due to its efficiency, accuracy, and interpretability. Consider adding reference-guided assembly (e.g., StringTie) if novel isoform discovery is a goal.
    *   **No Reference Available:** De novo assembly is necessary. Be prepared for computational challenges and the need for thorough quality control.
    *   **Poor-Quality Reference Available:** A hybrid approach or careful evaluation of reference-based mapping quality versus de novo assembly might be warranted.

### **4.2.3 Understanding RNA-seq Data Matrices: Counts vs. Normalized Expression**

RNA-seq quantification results are typically represented in matrices, but it's crucial to distinguish between raw counts and normalized expression values, as they serve different purposes.

*   **Raw Count Matrix:**
    *   **Structure:** Rows represent genes (or transcripts), columns represent samples. Each cell `(i, j)` contains the raw number of reads/fragments unambiguously assigned to gene `i` in sample `j`.
    *   **Properties:**
        *   Integer values.
        *   Directly reflects sequencing depth (column sums vary).
        *   Contains untransformed biological and technical variance.
        *   Exhibits heteroscedasticity (variance depends on the mean).
    *   **Primary Use:** **Essential input for statistical differential expression analysis tools like DESeq2 and edgeR.** These tools have built-in methods to handle the specific statistical properties (e.g., discrete nature, overdispersion) and normalization requirements of raw counts.

*   **Normalized Expression Matrix:**
    *   **Purpose:** To adjust raw counts for technical artifacts, primarily library size (sequencing depth) and sometimes gene length or RNA composition bias, making expression levels comparable across samples or genes.
    *   **Common Units/Methods:**
        *   **CPM (Counts Per Million):** Normalizes for library size only. `CPM = (Raw Count / Total Mapped Reads) * 1e6`. Useful for comparing expression of the *same gene* across samples when gene length isn't a factor. Does not correct for RNA composition bias effectively.
        *   **RPKM/FPKM (Reads/Fragments Per Kilobase of exon per Million mapped reads):** Normalizes for both library size and gene/transcript length. `FPKM = (Raw Fragment Count / (Feature Length in kb * Total Mapped Fragments in Millions))`. **Flawed for between-sample comparisons** because the total FPKM sum can differ across samples due to RNA composition effects. Largely superseded by TPM. Primarily useful for comparing expression of *different genes* within the *same sample*.
        *   **TPM (Transcripts Per Million):** Also normalizes for library size and gene/transcript length, but in a way that the sum of all TPM values in a sample is always one million. `TPM_i = ( (Raw Count_i / Length_i) / sum(Raw Count_j / Length_j for all j) ) * 1e6`. **Considered the best unit for comparing the relative abundance of genes both within and between samples.** Reflects the proportion of transcripts in a sample attributable to a given gene.
        *   **Internal Normalization Factors (e.g., TMM, RLE):** Methods used by DESeq2/edgeR to calculate robust size factors that account for library size and composition bias, used internally within their statistical models rather than directly transforming counts into a normalized matrix for DE testing.
    *   **Primary Use:** Data exploration, visualization (e.g., heatmaps, PCA plots after transformation), sample clustering, correlation analyses, and input for certain downstream analyses (e.g., network analysis) that require normalized values. **Not suitable as direct input for DESeq2/edgeR.**

*   **Data Transformations for Visualization and Downstream Analysis:**
    *   **Purpose:** Raw counts or TPMs often have skewed distributions and variance dependent on the mean, violating assumptions of methods like PCA or clustering. Transformations aim to stabilize variance and make distributions more symmetric.
    *   **Common Transformations:**
        *   **Logarithmic Transformation:** `log2(x + c)`, where `x` is CPM, TPM, or count, and `c` is a small pseudocount (e.g., 1) to avoid log(0). Compresses high values, expands low values. Widely used.
        *   **Variance Stabilizing Transformation (VST):** Implemented in DESeq2. Aims to make variance approximately independent of the mean.
        *   **Regularized Logarithm (rlog):** Also in DESeq2. Similar goal to VST, particularly effective at stabilizing variance for low-count genes. Recommended for visualization and clustering when sample size is moderate to large.
        *   **voom Transformation:** Implemented in limma. Transforms counts to log2(CPM) while calculating precision weights for each observation to be used in linear modeling.

*   **Data Selection Summary:**
    *   **Differential Expression (DESeq2, edgeR):** Use **Raw Counts**.
    *   **Visualization (PCA, Heatmap), Clustering:** Use **Transformed, Normalized Data** (e.g., `log2(TPM+1)`, VST, rlog).
    *   **Comparing Gene Proportions:** Use **TPM**.

### **4.2.4 Computational Resources and Quality Control Checkpoints**

RNA-seq analysis is computationally intensive and requires careful monitoring of quality throughout the pipeline.

*   **Computational Resource Requirements:**
    *   **Storage:** Raw FASTQ files are large (tens to hundreds of GB per sample). BAM files are also large. Intermediate files can accumulate. Projects often require hundreds of GB to several TB of storage. Plan for long-term storage and backup.
    *   **Memory (RAM):** Often the bottleneck. Genome indexing and alignment (especially with STAR) can require significant RAM (e.g., >30-60GB for human genome). De novo assembly (e.g., Trinity) is extremely RAM-intensive (potentially hundreds of GB). Quantification and DE analysis memory needs depend on sample size and complexity.
    *   **CPU:** Most analysis steps benefit from multiple CPU cores. Tools are often multi-threaded. High-performance computing (HPC) clusters or cloud computing platforms are frequently used.
*   **Parallelization Strategies:** Utilize multi-threading options within tools, task parallelization frameworks (GNU Parallel, Snakemake, Nextflow), or cluster job schedulers (SLURM, SGE) to speed up analysis, especially when processing many samples.
*   **Cloud Computing:** Platforms like AWS, GCP, and Azure offer scalable computational resources and storage, often with pre-configured bioinformatics environments, facilitating large-scale analyses. Requires cost management.
*   **Quality Control (QC) Checkpoints:** QC is not a single step but an ongoing process integrated throughout the workflow:
    *   **Raw Read QC (FastQC):** Assess initial data quality before any processing. Guides cleaning strategy.
    *   **Post-Trimming QC (FastQC):** Verify effectiveness of adapter removal and quality trimming.
    *   **Alignment QC (STAR logs, Qualimap, Picard):** Check mapping rates (total, unique), feature overlap (exonic, intronic, intergenic rates), insert size distribution (for PE), strand specificity, and gene body coverage. Identifies potential issues like gDNA contamination, library quality problems, or incorrect strandness parameters.
    *   **Post-Quantification / Pre-DE QC (PCA, Clustering, Correlation):** Examine sample relationships using normalized/transformed data. Biological replicates should cluster together. Identify potential outliers or batch effects. Assesses overall data coherence before statistical testing.
    *   **DE Results QC (MA Plot, Volcano Plot, P-value distribution):** Evaluate the outcome of differential expression testing for potential biases or unexpected patterns.

### **Section 4.2 Summary**

The RNA-seq analysis workflow transforms raw sequence reads into biological insights through a series of steps involving quality control, read mapping or pseudo-alignment, expression quantification, normalization, differential expression testing, and functional interpretation. Key decisions include choosing between reference-based and de novo strategies and understanding the appropriate use of raw count versus normalized expression matrices. The process is computationally demanding and requires diligent quality control at multiple checkpoints to ensure the reliability and validity of the final results.

## **Section 4.3: Read Alignment and Transcript Assembly**

Mapping RNA-seq reads to their genomic origin and, in some cases, assembling them into full-length transcripts are crucial intermediate steps in the analysis workflow. These processes face unique challenges due to the nature of RNA processing.

### **4.3.1 The Unique Challenges of RNA-seq Alignment**

Aligning RNA-seq reads is more complex than aligning DNA sequencing reads primarily because of RNA splicing.

![RNA-seq比对的特殊挑战](images/alignment_challenges.svg)
    *   *Figure 4.3: Illustration of spliced alignment. A read originating from a mature mRNA may span an exon-exon junction. A splice-aware aligner must map the segments of the read to the corresponding, non-contiguous exon regions in the reference genome.*

*   **Spliced Alignment:** Mature mRNAs have introns removed. Reads spanning exon-exon junctions will not map contiguously to the reference genome. Aligners must be "splice-aware," capable of splitting a single read and aligning its segments to potentially distant exonic regions, correctly identifying the intervening intron.
*   **Alternative Splicing:** A single gene locus can produce multiple isoforms with different exon combinations. Aligners need to accurately identify reads supporting various known and potentially novel splice junctions.
*   **Distinguishing Isoforms:** Accurately assigning reads to specific isoforms, especially when they share exons, is challenging and crucial for transcript-level quantification and splicing analysis.
*   **Gene Fusions/Chimeric Transcripts:** Reads originating from transcripts formed by the fusion of two distinct genes (due to chromosomal rearrangements) require specialized detection capabilities within the aligner or downstream tools.
*   **Homology and Repeats:** Reads originating from gene families or repetitive elements may map to multiple genomic locations (multi-mappers). Aligners need strategies to handle these ambiguities (e.g., reporting all locations, choosing the best hit, or allowing downstream tools to resolve them probabilistically).
*   **RNA Editing and Polymorphisms:** Aligners must tolerate mismatches and indels arising from sequence variants (SNPs) between the sequenced sample and the reference genome, or from post-transcriptional RNA editing events.

### **4.3.2 HISAT2: Hierarchical Indexing for Speed and Efficiency**

HISAT2 is a popular splice-aware aligner known for its speed and relatively low memory footprint, succeeding the earlier TopHat2 aligner.

*   **Core Algorithm: Hierarchical Graph FM Index (HGFM):** HISAT2 employs a sophisticated indexing strategy combining global and local indexes.
    *   **Global FM Index:** Covers the entire genome for fast initial mapping of reads or read segments that align contiguously.
    *   **Local FM Indexes:** Thousands of small indexes, each covering a small genomic region (~64 kbp). These can be loaded quickly into memory as needed.
    *   **Graph Augmentation:** The indexes incorporate information about tens of thousands of known splice sites and exons from annotations. This graph structure helps guide the alignment process across potential junctions.
*   **Alignment Strategy:** It first attempts alignment using the global index. If a read cannot be fully aligned (suggesting a splice junction), HISAT2 uses the graph information and potential anchor points (short exact matches) to identify relevant local regions. It then loads the corresponding small local indexes to perform more sensitive alignment searches within those regions to find the spliced alignment solution.
*   **Splice Site Detection:** Leverages both its built-in database of known splice sites and pattern matching (e.g., canonical GT-AG signals) to identify known and novel junctions.
*   **Performance:** Generally faster and more memory-efficient than TopHat2 and competitive with other modern aligners like STAR, especially on systems with moderate memory.
*   **Key Parameters:** `--rna-strandness` (specifying library strandedness: RF or FR), `--known-splicesite-infile` (providing known splice sites from a GTF file to improve accuracy), `--dta` (output format suitable for downstream assemblers like StringTie).

### **4.3.3 STAR: Ultrafast Alignment via Maximal Mappable Seeds**

STAR (Spliced Transcripts Alignment to a Reference) is renowned for its exceptional speed, particularly on large genomes, although it typically requires more RAM than HISAT2.

*   **Core Algorithm: Maximal Mappable Seed Search:**
    *   **Seed Identification:** STAR identifies the longest possible subsequence within a read (Maximal Mappable Seed, MMS) that exactly matches a genomic region.
    *   **Suffix Array Index:** Utilizes a highly efficient suffix array index of the genome to rapidly locate all occurrences of these seeds.
*   **Spliced Alignment Mechanism:** If a seed doesn't cover the entire read, STAR searches for additional seeds in the unmapped portions. If these seeds map to the genome in a manner consistent with splicing (i.e., co-linear, separated by a plausible intron length, and potentially matching splice junction motifs), it stitches them together to form a spliced alignment. This sequential seed search allows for the identification of multiple introns within a single read.
*   **Two-Pass Mode (Recommended):**
    *   **1st Pass:** Performs an initial alignment for each sample individually, discovering potential novel splice junctions.
    *   **2nd Pass:** Collects the high-confidence novel junctions discovered across all samples in the first pass, incorporates them into a revised genome index (or junction database), and then re-aligns all samples using this enhanced index. This leverages information across the entire cohort to improve sensitivity for detecting lowly expressed or sample-specific junctions.
*   **Additional Features:** Capable of detecting non-canonical splice junctions and has built-in functionality for identifying chimeric (fusion) reads.
*   **Performance:** Extremely fast, especially with multi-threading. Requires substantial RAM for genome indexing (~30GB for human) and alignment runs.
*   **Key Parameters:** `--runMode genomeGenerate` (for indexing), `--genomeDir` (index location), `--readFilesIn` (input FASTQ), `--outSAMtype BAM SortedByCoordinate` (output format), `--twopassMode Basic` (enable two-pass), `--chimSegmentMin` (for fusion detection).

### **4.3.4 Other Alignment Approaches**

While STAR and HISAT2 are widely used, other tools exist:

*   **GSNAP:** Known for its sensitivity in detecting complex variants like SNPs, indels, and long-distance splicing.
*   **BBMap:** A versatile aligner known for speed and accuracy across various data types.
*   **Pseudo-alignment (Salmon, Kallisto):** As mentioned previously, these are *not* aligners in the traditional sense. They bypass base-level alignment for rapid quantification by matching k-mers to reference *transcripts*. They do not produce BAM files suitable for variant calling or visualization in a genome browser context.

*   **Tool Selection Guidance:**
    *   **General Purpose:** STAR (if >30-60GB RAM available) or HISAT2 are excellent choices. STAR is often faster; HISAT2 uses less memory.
    *   **Quantification Only:** Salmon or Kallisto offer the fastest route if only expression estimates (TPM, counts) are needed, requiring reference transcript sequences (FASTA).
    *   **Complex Variants/Long Reads:** Consider GSNAP.

### **4.3.5 Principles of Transcriptome Assembly**

Transcriptome assembly aims to reconstruct full-length transcript sequences from short RNA-seq reads. This is essential for non-model organisms or for discovering novel isoforms.

*   **Reference-Guided vs. De Novo Assembly:**
    *   **Reference-Guided:** Uses read alignments (BAM file) against a reference genome as a guide. Tools like StringTie and Cufflinks build splicing graphs based on aligned reads and traverse these graphs to reconstruct transcripts. Benefits from genome context but limited by reference quality.
    *   **De Novo:** Assembles transcripts directly from reads without a reference genome. Tools like Trinity, SOAPdenovo-Trans, and SPAdes (rna mode) typically use De Bruijn graphs based on k-mer overlaps. Essential for non-model organisms but computationally demanding and prone to assembly errors.

*   **Graph-Based Assembly Algorithms:**
    *   **De Bruijn Graph (DBG):** Nodes represent k-mers (short subsequences of length k); edges connect k-mers that overlap by k-1 bases. Transcript reconstruction involves finding paths through the graph. Widely used in de novo assembly. Choice of k is critical. Handles high coverage well but struggles with repeats and complex splicing, which create bubbles and spurs in the graph.
    *   **Overlap/Splicing Graph:** Nodes represent reads, contigs, or exons; edges represent overlaps or splice junctions inferred from read alignments. Used primarily in reference-guided assembly where genomic coordinates provide structure.

*   **Challenges in Transcript Reconstruction:**
    *   **Resolving Isoforms:** Distinguishing highly similar transcripts resulting from alternative splicing, especially when based on subtle differences or low read coverage.
    *   **Expression Level Variation:** High-abundance transcripts are easier to assemble; low-abundance transcripts may be fragmented or missed.
    *   **Repeats and Homology:** Genomic repeats or members of gene families create ambiguities in the assembly graph.
    *   **Sequencing Errors:** Introduce false k-mers or paths.
    *   **Chimeras:** Mis-assembly can erroneously join segments from different transcripts.

### **4.3.6 StringTie: Flow Network-Based Assembly and Quantification**

StringTie is a leading tool for reference-guided transcriptome assembly and quantification.

*   **Core Algorithm: Network Flow Model:**
    *   **Splicing Graph Construction:** Builds a graph where nodes represent contiguous regions of read coverage or exons, and edges represent splice junctions inferred from the input BAM file.
    *   **Flow Network Transformation:** Treats the splicing graph as a flow network. Edge capacities are determined by the number of reads supporting that connection or region.
    *   **Max-Flow Algorithm:** Iteratively finds the path from potential transcription start sites (sources) to termination sites (sinks) that carries the maximum flow (representing the most highly supported transcript path).
    *   **Transcript Assembly & Quantification:** Each max-flow path identified corresponds to an assembled transcript. The amount of flow assigned to that path is used to estimate the transcript's abundance (FPKM, TPM). The flow is then removed from the network, and the process repeats to identify lower-abundance isoforms.
*   **Comparison with Cufflinks:** StringTie generally offers improved speed, memory efficiency, and assembly accuracy compared to its predecessor, Cufflinks.
*   **Key Functions:** Assembling known and novel transcripts (optionally guided by a reference GTF), estimating transcript and gene abundances, and merging assemblies from multiple samples (`stringtie --merge`) to create a unified annotation set.

### **4.3.7 De Novo Transcriptome Assembly with Trinity**

Trinity is arguably the most widely adopted software for de novo RNA-seq assembly.

*   **Workflow Principle:** Employs a three-stage process:
    1.  **Inchworm:** Assembles reads into longer linear contigs by greedily extending high-frequency k-mers. Creates initial transcript "skeletons".
    2.  **Chrysalis:** Clusters Inchworm contigs likely originating from the same gene or splicing variants. Builds independent De Bruijn graphs for each cluster using the original reads associated with those contigs.
    3.  **Butterfly:** Processes each De Bruijn graph in parallel. Analyzes the graph topology (bubbles, spurs) using read and paired-end information to resolve alternative splicing events and reconstruct full-length transcript isoforms.
*   **Strengths:** Specifically designed to handle the complexity of alternative splicing and recover multiple isoforms per gene locus.
*   **Resource Needs:** Highly computationally intensive, requiring significant RAM and CPU time.
*   **Post-Assembly Processing and Quality Assessment:**
    *   **Filtering/Clustering:** De novo assemblies often contain redundancy and artifacts. Tools like CD-HIT-EST remove highly similar sequences. Filtering based on length or estimated abundance (e.g., using RSEM or Kallisto on the assembly) is common.
    *   **Coding Potential Prediction:** Tools like TransDecoder identify likely protein-coding regions within assembled transcripts.
    *   **Quality Metrics:** Essential to evaluate assembly quality. Key metrics include:
        *   **Contiguity Statistics:** N50, mean/max contig length.
        *   **Completeness:** BUSCO analysis (Benchmarking Universal Single-Copy Orthologs) assesses the presence and completeness of expected conserved genes. **Considered a gold standard metric.**
        *   **Read Representation:** Percentage of input reads that map back to the assembled transcriptome.
        *   **Comparison to References (if possible):** Aligning assembly to known proteins or related species' genomes.

### **Section 4.3 Summary**

Aligning RNA-seq reads requires specialized splice-aware algorithms like HISAT2 and STAR to handle exon-intron boundaries. STAR is generally faster but requires more memory. Transcriptome assembly reconstructs transcript sequences, either guided by a reference genome (e.g., StringTie) or de novo (e.g., Trinity). De novo assembly is crucial for non-model organisms but presents significant computational and validation challenges, requiring rigorous quality assessment using metrics like BUSCO. Careful selection of alignment and assembly tools based on experimental goals and available resources is essential.

## **Section 4.4: Methods for Gene Expression Quantification**

A primary goal of many RNA-seq experiments is to quantify the expression level of each gene or transcript. This involves counting the reads associated with each feature and then normalizing these counts for meaningful comparisons.

### **4.4.1 Fundamental Concepts in Expression Quantification**

*   **Objective:** To estimate the relative abundance of each gene or transcript within each sample's RNA population.
*   **Basis:** Typically based on the number of sequencing reads originating from a given gene or transcript. The assumption is that higher abundance leads to more reads being sequenced.
*   **Levels of Quantification:**
    *   **Gene Level:** Summarizes the expression of all isoforms of a gene into a single value. More robust and often sufficient for standard differential expression analysis.
    *   **Transcript (Isoform) Level:** Estimates the expression of each individual transcript isoform separately. Provides finer resolution for studying alternative splicing but is inherently more complex due to sequence similarity between isoforms.
*   **Challenges in Quantification:**
    *   **Read Ambiguity:** Reads mapping to multiple genes (multi-mappers) or compatible with multiple isoforms of the same gene pose a significant challenge. Simple counting methods might discard these reads, while more sophisticated methods attempt to probabilistically assign them.
    *   **Biases:** Technical biases during library preparation (e.g., fragmentation, PCR amplification) and sequencing can affect read counts non-uniformly. Normalization aims to correct for some biases.
    *   **Mapping Accuracy:** Errors in read alignment directly impact quantification accuracy.

### **4.4.2 Counting Methods: Assigning Reads to Features**

These methods generate the **raw count matrix**, typically used as input for differential expression analysis packages. They require read alignments (BAM format) and gene annotations (GTF/GFF format).

*   **Handling Ambiguous Reads (Multi-mapping & Overlapping Features):**
    *   **Unique Mappers Only:** The simplest approach is to count only reads that map uniquely to a single gene/feature. This is unambiguous but potentially discards considerable data, underestimating expression for genes in repetitive regions or gene families.
    *   **Strategies for Multi-mappers (More Advanced):** Some aligners or downstream tools offer options like reporting one 'best' hit, reporting all hits, or providing alignment scores that can be used for probabilistic assignment (though often handled better by transcript-level quantifiers). Counting tools like featureCounts have options but often default to unique mappers for gene-level counts.
    *   **Handling Reads Overlapping Multiple Features:** When a read alignment overlaps exons from more than one annotated gene (e.g., overlapping genes), counting tools need a rule:
        *   **Discard:** Mark the read as ambiguous and do not count it towards any feature (e.g., default `union` mode in HTSeq-count). Conservative.
        *   **Count for All:** Count the read for every feature it overlaps (can inflate counts).
        *   **Count Based on Overlap Fraction:** More complex, rarely used in standard counting tools.
*   **Popular Counting Tools:**
    *   **featureCounts (from Subread package):**
        *   **Advantages:** Extremely fast (written in C), accurate, flexible options for handling paired-end reads, strand specificity, multi-mapping reads (though often used in unique mode for gene counts), and feature overlaps. **Currently a widely recommended standard.**
        *   **Typical Usage:** Counts reads uniquely overlapping the exons of a gene.
    *   **HTSeq-count:**
        *   **Advantages:** Written in Python, easy to use, widely cited in older literature.
        *   **Disadvantages:** Significantly slower than featureCounts. Its default 'union' mode for overlap handling is quite conservative.
    *   **Aligner-Integrated Counting:** Some aligners like STAR can perform simple gene counting during the alignment step (`--quantMode GeneCounts`), providing convenience but potentially less flexibility than dedicated counters.

### **4.4.3 Normalization Methods: Making Counts Comparable**

Raw read counts are confounded by technical factors like sequencing depth (library size) and potentially gene length and RNA composition. Normalization adjusts for these factors to allow meaningful comparisons of expression levels across samples and/or genes.

*   **Rationale for Normalization:**
    *   **Library Size:** Samples sequenced to different depths will have systematically different total counts.
    *   **Gene Length:** Longer genes naturally tend to produce more reads than shorter genes at the same actual expression level. (Relevant when comparing *different* genes).
    *   **RNA Composition:** Changes in the expression of a few highly abundant genes can alter the *proportion* of reads available for all other genes, even if their absolute expression remains constant.
*   **Common Normalization Units and Methods:** (See also Section 4.2.3 for definitions)
    *   **CPM (Counts Per Million):** Adjusts for library size. `CPM = (Raw Count / Total Reads) * 1e6`. Suitable for comparing the same gene across samples if composition bias is low.
    *   **RPKM/FPKM (Reads/Fragments Per Kilobase per Million):** Adjusts for library size and gene length. **Not recommended for between-sample comparisons due to susceptibility to composition bias.**
    *   **TPM (Transcripts Per Million):** Adjusts for library size and gene length, ensuring the sum of TPMs per sample is constant. **Preferred method for comparing relative expression proportions across samples and genes.** `TPM_i = (RPK_i / sum(RPKs)) * 1e6`, where `RPK_i = Raw Count_i / Length_i`.
    *   **Size Factor Methods (TMM, RLE/Median-of-Ratios):** Used internally by edgeR and DESeq2. These methods compute robust scaling factors for each library based on the assumption that most genes are *not* differentially expressed. They effectively normalize for library size and RNA composition bias simultaneously, providing the basis for statistical modeling on the original count scale. **These are the standard normalization procedures performed *within* DESeq2/edgeR before DE testing.**
*   **Absolute vs. Relative Quantification:** Standard RNA-seq provides relative quantification (e.g., TPM reflects proportions). Achieving absolute quantification (mRNA molecules per cell) typically requires the addition of known quantities of exogenous RNA spike-in controls during library preparation, which presents its own technical challenges.

### **4.4.4 Transcript-Level Quantification Methods**

Estimating the abundance of individual isoforms is more complex due to shared sequences. Methods often rely on statistical inference.

*   **Alignment-Based Methods (e.g., Cufflinks, StringTie's quant mode):**
    *   These tools use the genomic alignments (BAM file) and often pair-end information (fragment length distribution) to build statistical models (often using Expectation-Maximization, EM) that infer the most likely abundance of each annotated isoform contributing to the observed read coverage pattern.
*   **Alignment-Free (Pseudo-alignment) Methods (e.g., Salmon, Kallisto):**
    *   **Principle:** They bypass full genomic alignment. Reads are rapidly mapped (pseudo-aligned) to a reference *transcriptome* index (FASTA file of transcript sequences). Sophisticated statistical models (EM or Variational Bayes) are then used to resolve read ambiguity (reads compatible with multiple isoforms) and estimate transcript abundances (TPM and estimated counts). These models often incorporate bias correction (e.g., sequence-specific or GC bias, fragment length distribution).
    *   **Advantages:** Extremely fast and memory-efficient; accuracy is comparable or superior to alignment-based methods for quantification.
*   **Method Selection Guidance:**
    *   **For rapid and accurate transcript-level quantification:** Salmon or Kallisto are the current state-of-the-art.
    *   **If simultaneous reference-guided assembly and quantification are desired:** StringTie is suitable.
    *   **If primarily gene-level counts for DGE analysis are needed:** A fast aligner (STAR) followed by a fast counter (featureCounts) is a robust and efficient pipeline.

### **4.4.5 Quality Assessment for Quantification Results**

After quantification, it's crucial to perform checks to ensure the results are reliable and consistent.

*   **Consistency of Replicates:**
    *   **Technical Replicates:** Should show very high correlation (e.g., Pearson r > 0.95) in their expression profiles (e.g., log-TPM values).
    *   **Biological Replicates:** Should be more similar to each other than to samples from different experimental groups. Assessed using:
        *   **Correlation Heatmaps:** Visualize pairwise correlations between all samples. Expect blocks of high correlation for biological replicates.
        *   **Principal Component Analysis (PCA) / Multidimensional Scaling (MDS):** Dimension reduction techniques to visualize sample relationships in 2D or 3D. Replicates should cluster together. These plots are also powerful tools for identifying outliers and batch effects.
*   **Expression Distribution:** Boxplots or density plots of normalized expression values (e.g., log2(CPM+1) or log2(TPM+1)) across samples. Distributions should look reasonably similar after normalization. Significant deviations might indicate problematic samples or normalization issues.
*   **Comparison with Expectations:** Check the expression levels of known housekeeping genes (should be relatively stable) or genes expected to be affected by the experimental treatment.

### **Section 4.4 Summary**

Expression quantification translates aligned reads into abundance estimates for genes or transcripts. Raw read counts, generated by tools like featureCounts, are the required input for robust differential expression analysis using packages like DESeq2 or edgeR. Normalization methods like TPM are essential for comparing relative expression levels across samples and genes, particularly for visualization and exploratory analysis. Transcript-level quantification, often best performed by rapid pseudo-alignment tools like Salmon or Kallisto, resolves expression at the isoform level using statistical inference. Thorough quality control, including assessment of replicate consistency and expression distributions using correlation analysis and PCA, is vital for validating quantification results.

## **Section 4.5: Statistical Principles of Differential Expression Analysis**

Differential Expression (DE) analysis is often the central goal of RNA-seq experiments, aiming to identify genes or transcripts whose expression levels show statistically significant differences between experimental conditions. This requires specialized statistical methods tailored to the unique properties of RNA-seq count data.

### **4.5.1 The Framework of Hypothesis Testing for DE**

DE analysis is fundamentally based on statistical hypothesis testing, performed independently for each gene.

![差异表达分析](images/differential_expression.svg)
    *   *Figure 4.4: Conceptual illustration of differential expression analysis comparing two conditions. Genes significantly upregulated (red) or downregulated (blue) are identified based on statistical tests.*

*   **Hypotheses per Gene:**
    *   **Null Hypothesis (H₀):** There is no difference in the true average expression level of the gene between the conditions being compared. Any observed difference is due to random chance (technical noise + biological variability).
    *   **Alternative Hypothesis (H₁):** There is a real difference in the true average expression level of the gene between conditions.
*   **Statistical Testing:** A test statistic is calculated for each gene, quantifying the evidence against H₀ based on the observed counts, sample variability, and experimental design. This statistic is compared to its expected distribution under H₀ to obtain a **p-value**.
*   **P-value:** The probability of observing a test statistic as extreme as, or more extreme than, the one calculated, assuming the null hypothesis (H₀) is true. A small p-value suggests the observed data is unlikely under H₀.
*   **Significance Level (α):** A pre-defined threshold (commonly 0.05) for the p-value. If p < α, H₀ is rejected, and the gene is declared "differentially expressed."

*   **The Multiple Testing Problem:**
    *   When testing thousands of genes simultaneously, using a fixed α like 0.05 will lead to a large number of false positives purely by chance (Type I errors). If testing 20,000 genes where none are truly DE, we expect 20,000 * 0.05 = 1,000 false positives.
    *   **Correction is Mandatory:** Raw p-values must be adjusted to control the overall error rate across all tests.

*   **Controlling the False Discovery Rate (FDR):**
    *   **Definition:** The FDR is the expected proportion of false positives among all features declared significant. For example, an FDR threshold of 0.05 means that, on average, we expect no more than 5% of the genes called significant to be false positives.
    *   **Benjamini-Hochberg (BH) procedure:** The most common method for controlling the FDR. It adjusts the original p-values to generate "adjusted p-values" (often denoted as q-values or padj). Genes are typically declared differentially expressed if their **adjusted p-value < FDR threshold** (e.g., padj < 0.05). The BH method is generally more powerful (finds more true positives) than stricter methods like the Bonferroni correction.

*   **Statistical Power and Sample Size:**
    *   **Power:** The probability of correctly rejecting H₀ when it is false (i.e., detecting a truly differentially expressed gene). Power = 1 - β (where β is the Type II error rate, or false negative rate).
    *   **Determinants:** Power depends on the true effect size (magnitude of fold change), biological variability (variance within groups), sample size (number of biological replicates), and the chosen significance threshold (α or FDR).
    *   **Importance of Replicates:** **Increasing the number of biological replicates is the most effective way to increase statistical power.** Underpowered studies (too few replicates) risk failing to detect real biological differences. Power analysis should ideally inform the experimental design to determine the necessary sample size.

### **4.5.2 Statistical Properties of RNA-seq Count Data**

Standard statistical tests assuming normality (like t-tests or ANOVA) are inappropriate for raw RNA-seq counts due to their specific characteristics:

*   **Discrete Nature:** Counts are non-negative integers.
*   **Mean-Variance Relationship:** The variance of counts is typically related to the mean. Higher expressed genes exhibit greater absolute variance.
*   **Overdispersion:** The observed variance is often significantly larger than the mean. While technical variation might follow a Poisson distribution (where variance equals mean), biological variation between replicates adds extra variance, leading to overdispersion relative to the Poisson model.
    *   **Negative Binomial (NB) Distribution:** This discrete probability distribution is widely used to model RNA-seq counts because it can explicitly account for overdispersion. It has two parameters: the mean (μ) and a dispersion parameter (α or φ) that quantifies the extra-Poisson variation. The variance in the NB model is typically modeled as `Var = μ + αμ²`. As α approaches 0, the NB distribution converges to the Poisson.
*   **Low Count Issues:** Genes with very low counts have poor statistical properties, high relative noise, and low power for DE detection. They are often filtered out before statistical testing to improve reliability and reduce the multiple testing burden.

### **4.5.3 DESeq2: Modeling Counts with Negative Binomial GLMs and Shrinkage**

DESeq2 is a widely used R/Bioconductor package for DE analysis.

*   **Model:** Uses a Negative Binomial Generalized Linear Model (NB-GLM) for each gene:
    *   `Counts ~ NB(mean, dispersion)`
    *   The mean `μ` is modeled on the log scale as a function of experimental variables (e.g., condition, batch) via a design matrix and includes a sample-specific normalization factor (size factor) estimated using the median-of-ratios method. `log(μ) = log(size_factor) + design_matrix * coefficients`.
*   **Dispersion Estimation:** A key feature. DESeq2 employs an empirical Bayes approach:
    1.  Calculates gene-wise dispersion estimates.
    2.  Fits a trend between mean expression and dispersion across all genes.
    3.  Shrinks the gene-wise estimates towards the fitted trend. This "borrows" information across genes, stabilizing estimates, especially for low-count genes, leading to more reliable inference.
*   **Log2 Fold Change (LFC) Shrinkage:** Applies another empirical Bayes shrinkage procedure specifically to the estimated LFCs. This moderates LFCs for low-count or high-dispersion genes, reducing noise and improving ranking for downstream analyses. The `lfcShrink` function implements this.
*   **Hypothesis Testing:** Primarily uses the Wald test to assess if a specific coefficient (typically the LFC between conditions) is significantly different from zero. Offers Likelihood Ratio Tests (LRT) for comparing nested models, useful for multi-level factors or testing multiple coefficients simultaneously.
*   **Outlier Handling:** Automatically flags and down-weights potential count outliers based on Cook's distance.
*   **Batch Effect Handling:** Can incorporate batch information directly into the design formula (e.g., `~ batch + condition`) to account for known batch effects during model fitting.

### **4.5.4 edgeR: Empirical Bayes and GLM/QLF Approaches**

edgeR is another cornerstone R/Bioconductor package for DE analysis of count data.

*   **Model:** Also based on the Negative Binomial distribution. `Counts ~ NB(mean, dispersion)`. The mean `μ` is related to library size and group means.
*   **Dispersion Estimation:** Employs an empirical Bayes framework using a weighted conditional likelihood approach to estimate:
    1.  **Common Dispersion:** A single value across all genes.
    2.  **Trended Dispersion:** A relationship between mean expression and dispersion.
    3.  **Tagwise (Gene-wise) Dispersion:** Shrinks gene-wise estimates towards the common or trended dispersion, moderated by prior beliefs about variance.
*   **Hypothesis Testing Methods:**
    *   **Exact Test:** Based on a method similar to Fisher's exact test, adapted for overdispersed data. Suitable for simple two-group comparisons.
    *   **GLM Framework:** Allows fitting models for complex experimental designs. Can use:
        *   **Likelihood Ratio Test (LRT):** Compares nested GLMs.
        *   **Quasi-Likelihood F-test (QLF):** **Currently recommended method.** Models uncertainty in dispersion estimation more accurately, providing potentially better FDR control, especially with fewer replicates.
*   **Normalization:** Uses the Trimmed Mean of M-values (TMM) method to calculate normalization factors robust to RNA composition bias.

### **4.5.5 limma-voom: Linear Modeling after Variance Transformation**

limma is a powerful R package originally developed for microarray analysis, extended to RNA-seq via the `voom` function.

*   **Core Idea:** Transforms RNA-seq count data so that the robust linear modeling and empirical Bayes methods of limma can be applied.
*   **`voom` Transformation:**
    1.  Converts raw counts to log2-Counts Per Million (logCPM).
    2.  Models the mean-variance relationship of the logCPM values across all genes.
    3.  Uses this relationship to compute precision weights for each *individual observation* (each gene in each sample). Observations with higher precision (lower variance) get higher weights.
*   **Linear Modeling:** Fits standard linear models to the logCPM data using the `lmFit` function, incorporating the voom-generated weights.
*   **Empirical Bayes Moderation:** Applies the `eBayes` function to the fitted model. This shrinks the estimated variances (standard errors) for each gene towards a common prior value, borrowing information across genes to stabilize inference and increase power, especially with small sample sizes. Calculates moderated t-statistics and p-values.
*   **Strengths:** Excellent flexibility in handling complex experimental designs (multi-factor, interactions, random effects via `duplicateCorrelation`) inherited from limma. Often computationally faster than NB-based methods.

### **4.5.6 Interpreting Differential Expression Results**

The output of DE analysis is typically a table ranking genes by statistical significance. Effective interpretation involves more than just filtering by p-value.

*   **Key Metrics:** Adjusted p-value (FDR), log2 Fold Change (LFC), base mean expression.
*   **Filtering Criteria:** Common practice is to combine an FDR threshold (e.g., `padj < 0.05`) with an LFC threshold (e.g., `abs(log2FoldChange) > 1` for a minimum 2-fold change, or `> 0.58` for 1.5-fold) to focus on statistically significant *and* biologically meaningful changes.
*   **Volcano Plot:**
    *   **Axes:** X-axis = log2 Fold Change; Y-axis = -log10(adjusted p-value).
    *   **Interpretation:** Genes in the top-left (high -log10(padj), negative LFC) are significantly downregulated. Genes in the top-right (high -log10(padj), positive LFC) are significantly upregulated. Helps visualize the relationship between statistical significance and magnitude of change.
*   **MA Plot:**
    *   **Axes:** X-axis = Average expression (A, e.g., log2 baseMean); Y-axis = log2 Fold Change (M).
    *   **Interpretation:** Useful for diagnosing potential biases. Most genes (non-DE) should cluster around M=0. DE genes appear above or below M=0. Helps visualize LFC shrinkage effects (low-count genes pulled towards M=0) and assess if DE status depends on overall expression level.
*   **Clustering and Heatmaps:** Visualizing the expression patterns of DE genes across samples using heatmaps (often after clustering genes and/or samples) can reveal groups of co-regulated genes and highlight sample relationships.
*   **Biological Validation is Crucial:** RNA-seq identifies candidate DE genes. Experimental validation using independent methods on the same or new samples is essential to confirm findings.
    *   **RT-qPCR:** The gold standard for validating expression changes of selected genes.
    *   **Western Blot / ELISA:** Validate changes at the protein level.
    *   **Functional Assays:** Test the biological consequence of the gene's altered expression (e.g., via knockdown or overexpression).

### **Section 4.5 Summary**

Differential expression analysis identifies genes with statistically significant changes in expression between conditions, employing methods like DESeq2, edgeR, or limma-voom that model the unique count-based, overdispersed nature of RNA-seq data. Proper control for multiple testing using FDR is mandatory. Interpretation requires considering both statistical significance (adjusted p-value) and biological relevance (fold change), aided by visualizations like volcano and MA plots. Findings should ideally be validated experimentally. The choice of statistical package depends on the experimental design complexity and user preference, with all three mentioned being robust and widely accepted options.

## **Section 4.6: Functional Enrichment Analysis: From Gene Lists to Biological Insights**

Identifying a list of differentially expressed genes (DEGs) is often just the first step. Functional enrichment analysis aims to interpret these gene lists by determining whether they are significantly enriched for genes associated with specific biological pathways, molecular functions, or cellular locations, thus providing insights into the underlying biological processes affected by the experimental conditions.

### **4.6.1 Functional Annotation Databases: The Knowledge Base**

Enrichment analysis relies on comprehensive databases that systematically link genes to functional annotations.

![功能富集分析](images/functional_enrichment.svg)
    *   *Figure 4.5: Conceptual overview of functional enrichment analysis. A list of differentially expressed genes is tested for over-representation within predefined functional gene sets (e.g., pathways, GO terms).*

*   **Gene Ontology (GO): A Standardized Vocabulary**
    *   **Structure:** A hierarchical, controlled vocabulary describing gene product attributes across three domains, organized as Directed Acyclic Graphs (DAGs).
    *   **Domains:**
        *   **Molecular Function (MF):** Elemental activities, tasks, or binding potentials (e.g., 'protein kinase activity', 'DNA binding').
        *   **Biological Process (BP):** Larger biological programs accomplished by ordered assemblies of molecular functions (e.g., 'signal transduction', 'cell cycle', 'immune response'). Often the most informative domain for interpreting experiments.
        *   **Cellular Component (CC):** Locations in the cell where a gene product is active (e.g., 'nucleus', 'mitochondrial membrane', 'cytosol').
    *   **Utility:** Provides a standardized way to describe gene functions across different species and databases.

*   **KEGG (Kyoto Encyclopedia of Genes and Genomes) Pathway Database:**
    *   **Focus:** Manually curated pathway maps representing molecular interaction and reaction networks.
    *   **Content:** Covers metabolism, genetic information processing, cellular processes, organismal systems, human diseases, and drug development.
    *   **Utility:** Excellent for understanding how DEGs fit into known metabolic or signaling pathways. Pathway diagrams allow visualization of affected steps.

*   **Reactome Pathway Database:**
    *   **Focus:** Curated human biological pathways and processes, presented as series of molecular reactions.
    *   **Content:** Detailed, peer-reviewed modules covering a wide range of biological events. Hierarchical organization allows browsing from high-level processes to detailed molecular steps.
    *   **Utility:** Provides high-quality, detailed pathway information, particularly strong for human studies.

*   **Other Relevant Databases and Gene Set Collections:**
    *   **MSigDB (Molecular Signatures Database):** A large, crucial collection of annotated gene sets suitable for GSEA, including Hallmark (well-defined biological states), curated pathways (from KEGG, Reactome, etc.), regulatory motifs (transcription factor/miRNA targets), GO sets, oncogenic, and immunologic signatures.
    *   **WikiPathways, BioCarta:** Other pathway database resources.
    *   **Disease Databases (e.g., DisGeNET, DO):** Link genes to specific diseases.
    *   **Drug Databases (e.g., DrugBank):** Link genes to drug targets.

### **4.6.2 Over-Representation Analysis (ORA): Is My Gene List Enriched?**

ORA is a widely used method to determine if a predefined list of interesting genes (e.g., DEGs passing a certain threshold) has a statistically significant overlap with genes belonging to a specific functional category (gene set).

*   **Input:** A list of 'interesting' genes (e.g., DEGs with padj < 0.05 and |LFC| > 1) and a 'background' or 'universe' list (e.g., all genes detected or expressed in the experiment).
*   **Principle:** For each functional category (e.g., a specific GO term or KEGG pathway), it compares the proportion of interesting genes belonging to that category against the proportion of background genes belonging to the same category.
*   **Statistical Test:** Typically uses the **Hypergeometric Test** or the equivalent **Fisher's Exact Test**. These tests calculate the probability of observing an overlap of size `k` (or greater) purely by chance, given the size of the interesting list (`n`), the size of the functional category within the background (`K`), and the total size of the background (`N`).
*   **Output:** For each functional category tested:
    *   Raw p-value (from Hypergeometric/Fisher's test).
    *   **Adjusted p-value (FDR):** Essential due to testing many categories simultaneously.
    *   Fold Enrichment: Ratio of the proportion of interesting genes in the category to the proportion of background genes in the category (`(k/n) / (K/N)`). Indicates the strength of enrichment.
    *   List of interesting genes belonging to the category.
*   **Advantages:** Conceptually simple, easy to implement and interpret.
*   **Disadvantages:**
    *   Requires setting an arbitrary threshold to define the 'interesting' gene list, discarding potentially relevant information from genes near the threshold.
    *   Treats all genes in the list equally, ignoring the magnitude or direction of expression change.
    *   Assumes independence between genes.

### **4.6.3 Gene Set Enrichment Analysis (GSEA): Detecting Coordinated Changes**

GSEA is a powerful alternative approach that does not require a predefined threshold for differential expression. It assesses whether genes within a functional set show statistically significant, concordant differences between two biological states.

*   **Input:** A ranked list of *all* genes (or all expressed genes) ordered by a metric reflecting their association with the phenotype difference (e.g., signal-to-noise ratio, signed LFC, t-statistic) and a collection of predefined gene sets (e.g., from MSigDB).
*   **Principle:** Determines whether the members of a gene set tend to cluster towards the top (e.g., consistently upregulated) or bottom (e.g., consistently downregulated) of the ranked list more than expected by chance.
*   **Algorithm:**
    1.  **Calculate Enrichment Score (ES):** Walk down the ranked gene list. Increase a running-sum statistic when encountering a gene in the set ('hit') and decrease it for genes not in the set ('miss'). The magnitude of increase/decrease can be weighted by the gene's ranking metric. The maximum deviation of this running sum from zero is the ES.
    2.  **Estimate Significance (Permutation Test):** Repeat the ES calculation many times using randomly permuted sample labels (phenotypes) to generate a null distribution of ES values for that gene set. The nominal p-value reflects the probability of observing the actual ES (or a more extreme one) under the null distribution.
    3.  **Normalize and Correct:** Calculate a Normalized Enrichment Score (NES) by normalizing the ES for gene set size and correlating it with the null distribution. Calculate an FDR q-value to correct for multiple hypothesis testing across all gene sets.
*   **Output:** For each gene set: NES, nominal p-value, FDR q-value, and the "Leading Edge Subset" (the core members of the gene set contributing most to the ES).
*   **Advantages:**
    *   Threshold-free: Uses information from all genes.
    *   Sensitive to subtle but coordinated changes within a gene set.
    *   Considers the rank/magnitude of expression change.
*   **Disadvantages:** Can be computationally more intensive than ORA; interpretation of results requires understanding the NES and enrichment plots.

### **4.6.4 Tools for Functional Enrichment Analysis**

A variety of software tools, including standalone programs, web servers, and R packages, are available.

*   **R/Bioconductor Packages:**
    *   **clusterProfiler:** Highly versatile and widely recommended package. Performs ORA and GSEA for GO, KEGG, Reactome, DO, MSigDB, and more. Supports numerous organisms and offers extensive visualization capabilities (dot plots, enrichment maps, pathway views via Pathview).
    *   **fgsea:** Provides a fast implementation of the GSEA algorithm.
    *   **goseq:** Specialized ORA tool for RNA-seq data that accounts for potential gene length bias in DEG identification.
*   **Web-Based Tools:**
    *   **DAVID:** Classic tool for ORA and functional annotation clustering. User-friendly interface. (Note: Database updates might lag).
    *   **Enrichr:** Offers ORA against an exceptionally broad range of gene set libraries. Provides diverse visualization options.
    *   **Metascape:** Focuses on streamlined analysis and deep biological interpretation, integrating multiple databases and providing network visualization.
    *   **g:Profiler:** Comprehensive tool for ORA, pathway analysis, and variant interpretation.
*   **Standalone Software:**
    *   **GSEA Java Desktop Application (Broad Institute):** The original implementation of GSEA, offering standard analysis workflows and visualizations.

*   **Tool Selection:** For R users, `clusterProfiler` is often the preferred choice due to its integration, flexibility, and visualization power. Web tools like Enrichr or Metascape are convenient for quick analyses or accessing specific gene set libraries.

### **4.6.5 Interpretation and Visualization of Enrichment Results**

Interpreting enrichment results requires careful consideration beyond just statistical significance.

*   **Beyond P-values:** Consider the effect size (Fold Enrichment in ORA, NES in GSEA), the number of genes involved, and the biological plausibility in the context of the experiment.
*   **Handling Redundancy (Especially with GO):** Many GO terms are related hierarchically or semantically. Enrichment results often include redundant terms.
    *   **Visualization:** Use tools like `enrichplot` (associated with clusterProfiler) to create Enrichment Maps or GO DAGs, visually grouping related terms.
    *   **Simplification:** Some tools (like clusterProfiler's `simplify` function) use semantic similarity measures to remove redundant GO terms, focusing on the most informative ones.
*   **Visualizing Results:**
    *   **Bar plots/Dot plots:** Effective for showing top enriched terms ranked by significance or enrichment score. Dot plots can additionally encode gene count and fold enrichment.
    *   **Network plots (Enrichment Maps):** Show relationships between enriched terms (nodes), where edges represent gene overlap. Helps identify functional modules.
    *   **Pathway Diagrams:** Map DEGs onto KEGG or Reactome pathway diagrams, highlighting affected components (using tools like Pathview or KEGG mapper).
*   **Biological Context:** Always interpret results within the framework of the experimental design and known biology. Are the enriched pathways consistent with the observed phenotype or treatment? Do the results generate testable hypotheses for follow-up studies? Focus on patterns and converging evidence rather than isolated terms.

### **Section 4.6 Summary**

Functional enrichment analysis bridges the gap between lists of differentially expressed genes and biological understanding. Leveraging annotation databases like GO, KEGG, and Reactome, methods like Over-Representation Analysis (ORA) and Gene Set Enrichment Analysis (GSEA) identify biological themes significantly associated with the observed expression changes. ORA tests for significant overlap between a predefined DEG list and functional categories, while the threshold-free GSEA detects coordinated changes within gene sets across a ranked list of all genes. Tools like `clusterProfiler` provide powerful frameworks for performing these analyses. Careful interpretation, considering statistical significance, effect size, redundancy, and biological context, aided by effective visualization, is crucial for extracting meaningful insights.