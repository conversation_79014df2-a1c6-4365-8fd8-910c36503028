# 专题四：转录组测序(RNA-seq)数据分析 - 理论课 第一部分

**课程目标：** 理解转录组测序的基本原理、实验设计考量、主要技术类型、标准分析流程及其核心步骤的生物信息学方法。

## 转录组测序实验设计与应用场景

### 1. 转录组测序基本概念

*   **转录组定义与特点**
    *   **转录组（Transcriptome）**：严格来说，指特定细胞、组织或生物体在特定生理状态下，由基因组转录产生的所有RNA分子的总和，主要包括信使RNA (mRNA)、核糖体RNA (rRNA)、转运RNA (tRNA) 以及多种非编码RNA (ncRNA) 如长链非编码RNA (lncRNA)、微小RNA (miRNA)、环状RNA (circRNA) 等。
    *   **特点**：
        *   **动态性（Dynamic）**：转录组是细胞状态的直接反映，对内外部信号响应迅速，随时间（发育阶段、细胞周期）、空间（组织、细胞类型）和环境条件（刺激、疾病状态）发生显著变化。它是连接基因型和表型的关键环节。
        *   **复杂性（Complex）**：包含多种类型、丰度差异巨大的RNA分子。同一基因可能通过可变剪接（Alternative Splicing）产生多种转录本（Isoforms），以及存在转录后修饰（如RNA编辑）。

*   **转录组 vs 基因组**
    *   **基因组（Genome）**：一套完整的遗传物质（通常是DNA），包含了一个生物体所有基因和其他非编码序列。在个体的大多数细胞中，基因组是相对稳定和相同的（体细胞突变除外）。
    *   **转录组（Transcriptome）**：是基因组功能的动态体现，代表了在特定条件下哪些基因被激活（转录）以及它们的活跃程度（表达水平）。基因组是“蓝图”，转录组是“正在执行的指令”。

![转录组vs基因组](images/transcriptome_vs_genome.svg)
    *   *图示说明：该图形象地展示了基因组是相对静态的DNA序列，而转录组则是动态变化的RNA分子集合，是基因表达的产物。*

*   **转录组的动态性与复杂性（进一步阐述）**
    *   **动态性示例**：
        *   **发育调控**：胚胎发育不同阶段，特定基因的开关决定了细胞分化和组织形成。
        *   **细胞特异性**：神经元和肝细胞拥有相同的基因组，但其转录组差异巨大，决定了它们不同的结构和功能。
        *   **应激响应**：细胞受到热休克或药物处理时，会快速上调或下调相关基因的转录。
    *   **复杂性来源**：
        *   **多样的RNA种类**：除了编码蛋白质的mRNA，大量非编码RNA在基因调控、蛋白质合成等方面扮演重要角色。
        *   **可变剪接**：一个基因可以通过不同的剪接方式产生多种mRNA异构体，极大增加了蛋白质多样性。
        *   **丰度差异**：不同RNA分子的丰度可能相差数个数量级（如rRNA占比极高，而某些转录因子mRNA则非常稀少）。

*   **转录组研究的意义**
    *   **揭示基因功能与调控网络**：通过比较不同条件下的转录组，识别受调控的基因及其潜在功能。
    *   **发现新的基因、转录本和调控元件**：尤其是在非模式生物或复杂疾病研究中。
    *   **理解疾病分子机制**：识别与疾病发生、发展相关的异常表达基因和通路，寻找潜在的生物标志物（Biomarkers）。
    *   **药物研发与精准医疗**：评估药物效果、筛选药物靶点、根据个体转录组特征指导治疗。

### 2. 转录组测序技术类型

*   **全转录组测序 (Total RNA-seq)**
    *   **原理**：通常先去除rRNA（占总RNA 80-90%），然后对剩余的所有RNA（包括mRNA、lncRNA、部分小RNA等）进行测序。
    *   **优点**：能够捕获最全面的RNA信息，尤其适合研究非编码RNA和整体转录景观。
    *   **缺点**：成本相对较高，对低丰度转录本的覆盖可能不如特异性富集方法。

*   **mRNA 测序 (mRNA-seq / Poly(A)-selected RNA-seq)**
    *   **原理**：利用真核生物mRNA 3'端的Poly(A)尾巴，通过Oligo(dT)磁珠富集mRNA，然后进行测序。
    *   **优点**：成本效益高，专注于编码基因的表达谱分析，是研究差异基因表达最常用的方法。
    *   **缺点**：无法检测无Poly(A)尾的RNA（如大部分非编码RNA、组蛋白mRNA、部分降解的mRNA），可能受RNA完整性影响较大。

*   **小 RNA 测序 (small RNA-seq)**
    *   **原理**：特异性地分离和富集小分子RNA（通常<200 nt），如miRNA、siRNA、piRNA，然后进行测序。
    *   **优点**：专门研究小RNA的表达、发现和功能。
    *   **缺点**：需要专门的文库构建方法，无法覆盖长RNA。

*   **长非编码 RNA 测序 (lncRNA-seq)**
    *   **方法**：通常采用Total RNA-seq（去除rRNA后）的数据，通过生物信息学分析重点关注lncRNA；或采用特异性捕获方法。
    *   **目标**：研究lncRNA的表达、结构和潜在功能。

*   **靶向 RNA 测序 (Targeted RNA-seq)**
    *   **原理**：使用特异性探针捕获感兴趣的特定基因或转录本集合，然后进行测序。
    *   **优点**：极大地提高目标区域的测序深度，灵敏度高，成本相对较低（对于特定目标），适合验证或临床应用。
    *   **缺点**：只能分析预先选定的目标，无法发现新转录本。

*   **单细胞 RNA 测序 (scRNA-seq)**
    *   **原理**：将组织解离成单个细胞，并对每个细胞的转录组进行独立标记和测序。
    *   **优点**：能够解析细胞间的异质性，识别稀有细胞类型，重建细胞分化轨迹。
    *   **缺点**：技术复杂，成本高，数据稀疏性（dropout）问题。

*   **空间转录组测序 (Spatial Transcriptomics)**
    *   **原理**：在保留组织空间位置信息的前提下，对原位（in situ）或组织切片上的RNA进行测序或成像。
    *   **优点**：能够将基因表达谱映射回组织结构，研究细胞微环境和空间互作。
    *   **缺点**：技术仍在快速发展中，分辨率和通量有待提高。

### 3. 转录组测序实验设计关键因素

*   **研究目标明确化 (Crucial First Step)**
    *   **问题驱动**：研究目的是什么？是寻找差异表达基因？发现新转录本？研究可变剪接？还是分析非编码RNA？
    *   **技术匹配**：根据研究目标选择最合适的测序技术（如上所述）。例如，研究lncRNA表达最好用Total RNA-seq，研究miRNA必须用small RNA-seq。

*   **样本选择与处理**
    *   **样本代表性**：样本是否能真实反映所研究的生物学问题？（例如，疾病样本的选择、对照组的设定）
    *   **样本数量与统计功效 (Power)**：
        *   **生物学重复 (Biological Replicates)**：**至关重要！** 指的是来自不同独立个体或独立培养体系的样本。用于评估生物学固有的变异，是进行可靠统计推断（如差异表达分析）的基础。**推荐至少3个生物学重复，复杂系统或效应微弱时需要更多。**
        *   **技术重复 (Technical Replicates)**：对同一个RNA样本进行多次文库构建和测序。主要用于评估实验操作和测序本身的技术噪音。**在当前测序技术稳定性下，通常不是必需的，除非是评估新流程或特定技术问题。** 优先保证生物学重复。
        *   样本量不足会导致假阴性（漏掉真实差异）或假阳性（误报差异）增高。
    *   **批次效应 (Batch Effect) 控制**：
        *   **定义**：由非生物学因素（如不同的实验日期、操作人员、试剂批次、测序仪）引入的系统性偏差。
        *   **危害**：可能掩盖真实的生物学差异或产生虚假的差异信号。
        *   **控制策略**：
            *   **实验设计阶段**：样本随机化（将不同组的样本随机分配到不同批次处理）、平衡设计（各组样本在各批次中分布均匀）。
            *   **数据分析阶段**：在统计模型中将批次作为协变量进行校正。

*   **RNA提取与质量控制 (QC)**
    *   **RNA提取方法**：根据样本类型选择合适的提取试剂盒，确保高效、无偏好性地提取所有目标RNA。
    *   **RNA完整性评估 (RIN值)**：
        *   使用Agilent Bioanalyzer或类似仪器检测。RIN (RNA Integrity Number) 范围1-10，值越高代表RNA完整性越好。
        *   **重要性**：降解的RNA会导致文库构建偏向3'端，影响基因表达定量的准确性，尤其对mRNA-seq。
        *   **推荐**：高质量的总RNA应具有RIN ≥ 7，对于要求更高的应用（如研究可变剪接），RIN ≥ 8 更佳。
    *   **纯度评估**：通过NanoDrop等测定A260/A280（~1.8-2.1）和A260/A230（>1.8）比值，评估蛋白质和盐类污染。
    *   **污染控制**：
        *   **RNase污染**：整个过程需在无RNase环境中操作（使用DEPC处理水、专用耗材、佩戴手套）。
        *   **基因组DNA (gDNA)污染**：残留的gDNA会被测序，干扰转录本定量。通常需要进行DNase处理步骤，并通过qPCR或凝胶电泳检查。

*   **文库构建策略**
    *   **链特异性文库 (Strand-specific Library)**：
        *   **原理**：通过特殊方法（如dUTP法）保留转录本的链来源信息。
        *   **优势**：能够区分正义链和反义链的转录本，对于准确注释基因、研究反义转录、lncRNA等至关重要。**强烈推荐，已成为标准实践。**
    *   **起始材料选择：**
        *   **Poly(A)选择**：富集mRNA，适用于研究编码基因表达。
        *   **rRNA去除 (Ribosomal RNA Depletion)**：去除高丰度的rRNA，保留mRNA和大部分非编码RNA，适用于Total RNA-seq。选择哪种取决于研究目标。
        *   **随机引物 (Random Primers) vs Oligo(dT) 引物**：在反转录步骤中使用。Oligo(dT)主要用于Poly(A)+ RNA；随机引物可用于所有类型的RNA（包括无PolyA尾或降解的RNA），但可能增加rRNA比例（若未去除）。
    *   **片段化 (Fragmentation)**：将RNA或cDNA打断成适合测序平台读长的大小。
    *   **接头连接 (Adapter Ligation)**：连接测序平台所需的接头序列。
    *   **PCR扩增 (PCR Amplification)**：增加文库量，但也可能引入偏好性（bias）。应尽量减少PCR循环数。

*   **测序深度与读长 (Sequencing Depth & Read Length)**
    *   **测序深度 (Depth)**：指产生的总reads数量或覆盖基因组/转录组的平均次数。
        *   **影响因素**：研究目标、基因组大小与复杂性、表达谱动态范围。
        *   **一般推荐 (Illumina平台, paired-end reads)**：
            *   基因表达谱分析（差异表达）：~20-30 Million reads/sample
            *   可变剪接分析、新转录本发现：~50-100+ Million reads/sample
            *   单细胞RNA-seq：~50,000-100,000 reads/cell
    *   **饱和度分析 (Saturation Analysis)**：评估当前测序深度是否足以检测到大部分表达的基因。可以通过抽样不同比例的reads，观察检测到的基因数量是否趋于平稳。
    *   **读长 (Read Length)**：单个测序读段的长度。
        *   **常见读长**：PE100 (Paired-end 100bp), PE150。
        *   **选择**：长读长（如PE150）更有利于跨越剪接位点、区分异构体和进行转录本组装。短读长（如SE50）可能足够用于简单的基因计数。**推荐Paired-end测序**，可提供片段长度信息，改善比对和组装效果。
    *   **成本效益评估**：在满足研究需求的前提下，平衡测序深度、读长与预算。

### 4. 转录组测序主要应用场景

*   **基因表达谱分析 (Gene Expression Profiling)**：量化基因在不同样本中的表达水平，构建表达图谱。
*   **差异表达基因鉴定 (Differential Gene Expression Analysis)**：比较不同条件（如疾病vs健康，处理vs对照）下的转录组，找出表达水平有统计学显著差异的基因。**这是RNA-seq最常见的应用。**
*   **可变剪接分析 (Alternative Splicing Analysis)**：研究同一基因如何通过不同剪接方式产生多种转录本异构体，及其在不同条件下的变化。
*   **新转录本发现与注释 (Novel Transcript Discovery & Annotation)**：识别和鉴定基因组中尚未注释的转录本，包括新的编码基因、非编码RNA或已知基因的新异构体。
*   **融合基因检测 (Fusion Gene Detection)**：识别由两个或多个基因通过染色体重排连接而成的异常融合转录本，常见于癌症研究。
*   **RNA 编辑位点鉴定 (RNA Editing Site Identification)**：发现转录后RNA序列发生改变的位点（如A-to-I编辑）。
*   **非编码 RNA (ncRNA) 研究**：系统地鉴定和定量各类非编码RNA（lncRNA, miRNA, circRNA等）的表达，探索其调控功能。
*   **等位基因特异性表达分析 (Allele-Specific Expression Analysis)**：利用SNP信息，区分和定量来自父本和母本等位基因的表达量，研究印记基因、显性效应等。
*   **共表达网络构建 (Co-expression Network Construction)**：分析基因表达模式的相似性，构建基因网络，推断基因模块和调控关系。
*   **与其他组学数据整合分析 (Integration with other Omics)**：结合基因组、蛋白质组、代谢组等数据，更全面地理解生物系统。

**本节总结**

转录组测序是研究基因表达调控的强大工具。成功的RNA-seq研究始于清晰的研究目标和周密的实验设计。理解不同技术类型的特点，严格控制样本质量和实验流程中的关键环节（如重复设置、批次效应、文库构建策略、测序深度），是获取高质量、可信数据的基石。

## RNA-seq数据分析流程概述

### 1. RNA-seq数据分析标准流程

![RNA-seq数据分析流程](images/rnaseq_workflow.svg)
    *   *图示说明：该图展示了一个典型的RNA-seq数据分析流程，从原始测序数据开始，经过质量控制、比对/定量、差异分析，最终到功能解释。*

*   **数据质控与预处理 (Quality Control & Preprocessing)**
    *   **原始数据质控 (Raw Read QC)**：使用FastQC等工具评估原始测序reads的质量，检查指标如碱基质量分数、GC含量分布、接头(adapter)污染、序列重复水平等。
    *   **数据清洗 (Trimming & Filtering)**：使用Trimmomatic, Cutadapt等工具去除低质量碱基、测序接头序列。根据需要过滤掉过短的reads。对于Total RNA-seq，可能需要过滤rRNA序列（如果文库构建未完全去除）。
    *   **质控后检查 (Post-QC Check)**：再次运行FastQC确认清洗效果。

*   **序列比对或直接定量 (Alignment or Pseudo-alignment & Quantification)**
    *   **选项一：基于比对 (Alignment-based)**
        *   **比对 (Mapping/Alignment)**：使用专门的RNA-seq比对工具（如HISAT2, STAR）将清洗后的reads比对到参考基因组 (Reference Genome) 或参考转录组 (Reference Transcriptome) 上。这些工具能够处理跨越内含子的剪接读段 (Spliced Reads)。输出通常是BAM/SAM格式文件。
        *   **比对后质控**：检查比对率、唯一比对率、片段长度分布等。
    *   **选项二：基于伪比对/直接定量 (Alignment-free/Pseudo-alignment)**
        *   **原理**：使用Salmon, Kallisto等工具，基于k-mer匹配，快速地将reads映射（或称“伪比对”）到参考转录本序列上并进行定量，无需生成完整的碱基级别的比对文件。
        *   **优势**：速度极快，内存占用相对较小。

*   **转录本组装 (Transcriptome Assembly) (可选)**
    *   **场景**：
        *   **无参考基因组 (De Novo Assembly)**：对于没有高质量参考基因组的物种。使用Trinity等工具。
        *   **基于参考基因组的组装 (Reference-guided Assembly)**：即使有参考基因组，也可用于发现新的转录本异构体或完善现有注释。使用StringTie, Cufflinks等工具，通常以比对产生的BAM文件为输入。
    *   **挑战**：计算量大，结果可能产生冗余或嵌合体转录本，需要仔细过滤和评估。

*   **表达量定量 (Expression Quantification)**
    *   **目标**：计算每个基因或转录本的表达丰度。
    *   **方法**：
        *   **基于计数的工具 (Count-based)**：使用HTSeq-count, featureCounts, StringTie (-e 模式) 等工具，根据比对结果（BAM文件）和基因注释文件（GTF/GFF），统计比对到每个基因/外显子区域的reads数目，得到原始计数矩阵 (Raw Count Matrix)。
        *   **基于模型的工具**：Salmon, Kallisto 在伪比对的同时直接输出估计的表达量（如TPM）和计数。StringTie/Cufflinks 在组装的同时也会估计表达量。

*   **数据标准化与差异表达分析 (Normalization & Differential Expression Analysis)**
    *   **标准化 (Normalization)**：校正测序深度、基因长度（对于RPKM/FPKM/TPM）和RNA组成偏差等因素，使得样本间和基因间的表达量具有可比性。常用方法包括CPM, TPM, TMM (edgeR), RLE (DESeq2)等。**注意：差异表达分析工具（DESeq2, edgeR）通常以内建的标准化方法处理原始计数矩阵。**
    *   **差异表达分析 (DEA)**：使用DESeq2, edgeR, limma-voom等统计包，基于合适的统计模型（如负二项分布）分析计数数据，识别在不同实验组间表达水平具有统计学显著差异的基因或转录本。输出结果包括log2 Fold Change, p-value, adjusted p-value (FDR)。

*   **下游功能注释与富集分析 (Functional Annotation & Enrichment Analysis)**
    *   **功能注释 (Annotation)**：为差异表达基因关联生物学功能信息，如GO (Gene Ontology) 条目、KEGG通路、Reactome通路等。
    *   **富集分析 (Enrichment Analysis)**：分析差异表达基因在哪些功能类别或通路中显著富集，从而推断实验处理可能影响的关键生物学过程。常用方法有ORA (Over-Representation Analysis) 和 GSEA (Gene Set Enrichment Analysis)。

*   **结果可视化与解释 (Visualization & Interpretation)**
    *   **可视化**：使用火山图 (Volcano Plot)、热图 (Heatmap)、MA图、表达谱聚类图、通路图等多种图形展示分析结果。
    *   **生物学解释**：结合已有的生物学知识、文献和实验设计，对分析结果进行深入的生物学意义解读。

### 2. 参考基因组依赖 vs 从头组装策略

*   **参考基因组依赖 (Reference-based) 方法**
    *   **流程**：Reads比对到已知参考基因组 -> 定量已知基因/转录本 -> 差异分析。可结合参考指导的组装（如StringTie）发现新异构体。
    *   **优点**：
        *   分析流程成熟，工具众多。
        *   结果准确性通常较高（依赖于参考基因组和注释的质量）。
        *   结果易于解释和与其他研究比较。
        *   计算效率相对较高（相比De Novo）。
    *   **缺点**：
        *   严重依赖高质量、完整且注释良好的参考基因组。
        *   对于基因组与参考差异较大的样本（如不同品系、远缘物种），比对率可能降低。
        *   难以发现参考基因组中完全不存在的新基因。

*   **从头组装 (De Novo Assembly) 方法**
    *   **流程**：Reads拼接成转录本contigs -> 构建新的转录本集合 -> (可选)功能注释 -> 定量 -> 差异分析。
    *   **优点**：
        *   无需参考基因组，适用于非模式生物或基因组信息缺乏的物种。
        *   能够发现全新的基因和转录本。
    *   **缺点**：
        *   计算资源需求巨大（内存、时间）。
        *   组装过程复杂，易产生嵌合体、冗余、片段化的转录本，结果质量不易保证。
        *   后续注释和解释难度较大。
        *   定量和差异分析可能更复杂。

*   **混合策略 (Hybrid Strategy)**
    *   **思路**：结合两者的优势。例如，先进行De Novo组装获得转录本集合，然后将其作为参考进行比对和定量；或者，先进行参考比对，未比对上的reads再进行De Novo组装。
    *   **应用场景**：参考基因组质量不高、注释不全，或者希望在利用参考信息的同时最大程度发现新转录本。

*   **方法选择建议**
    *   **首选参考基因组依赖方法**：当研究物种有高质量参考基因组和注释时。这是最常用、最可靠的策略。
    *   **考虑De Novo组装**：当研究非模式生物且无参考基因组，或者研究目标是发现大量新基因/转录本时。需要配备强大的计算资源和仔细的质量评估。
    *   **参考指导的组装（如StringTie）**：作为参考基因组依赖方法的补充，用于发现新的异构体和完善注释，是兼顾效率和发现能力的好选择。

### 3. 计数矩阵 vs 表达矩阵 (标准化)

*   **原始计数矩阵 (Raw Count Matrix)**
    *   **内容**：行代表基因（或转录本），列代表样本，矩阵中的数值(i, j)表示基因i在样本j中被检测到的reads（或fragments）数目。
    *   **特点**：
        *   **整数**：是计数值。
        *   **反映测序深度**：总计数与测序文库大小直接相关。
        *   **包含技术噪音和生物学变异**。
        *   **异方差性 (Heteroscedasticity)**：表达量越高的基因，其计数的绝对变异通常也越大。
    *   **用途**：**是进行差异表达分析（如使用DESeq2, edgeR）的直接输入。** 这些工具内置了考虑计数数据特性的统计模型和标准化方法。

*   **标准化表达矩阵 (Normalized Expression Matrix)**
    *   **目标**：消除技术偏差（如测序深度、基因长度、RNA组成），使表达量在样本间和/或基因间具有可比性。
    *   **常用标准化方法**：
        *   **RPM/CPM (Reads/Counts Per Million mapped reads)**：校正测序深度。(CPM = RawCount / TotalMappedReads * 1,000,000)。简单直观，但未校正基因长度和RNA组成偏差。
        *   **RPKM/FPKM (Reads/Fragments Per Kilobase of transcript per Million mapped reads)**：同时校正测序深度和基因/转录本长度。RPKM用于单端，FPKM用于双端。**缺点：在样本间比较时存在偏差，现已不推荐用于差异分析或样本间比较。**
        *   **TPM (Transcripts Per Million)**：也同时校正测序深度和基因/转录本长度，但计算方式不同，使得**同一〃本内所有基因/转录本的TPM总和相同**。**优点：样本内和样本间的基因表达比例更具可比性。是目前推荐的用于比较基因相对表达丰度的单位。**
        *   **TMM (Trimmed Mean of M-values, edgeR) / RLE (Relative Log Expression, DESeq2) / MRN (Median Ratio Normalization)**：这些是差异表达工具内部使用的更稳健的标准化方法，主要校正测序深度和RNA组成偏差，生成用于模型拟合的标准化因子 (Size Factors)。它们通常不直接输出替换原始计数的标准化矩阵，而是作用于统计模型中。
    *   **用途**：主要用于数据可视化（如热图、PCA）、样本聚类、基因聚类、以及某些需要标准化输入的下游分析（如WGCNA）。

*   **数据变换方法 (Data Transformation)**
    *   **目的**：对于某些需要满足特定分布假设（如正态分布）的下游分析（如PCA、聚类、线性模型），需要对计数或标准化后的数据（如TPM）进行变换。
    *   **常用方法**：
        *   **log2(count + pseudocount)**：如log2(count+1) 或 log2(TPM+1)。对数变换可以压缩高表达值，拉伸低表达值，使数据分布更接近对称，稳定方差。加伪计数是为了避免log(0)。
        *   **VST (Variance Stabilizing Transformation, DESeq2)**：方差稳定变换，使得变换后数据的方差大致独立于均值。
        *   **rlog (Regularized Logarithm, DESeq2)**：正则化对数变换，对低计数值的基因具有更好的方差稳定效果，特别适合可视化和聚类。
        *   **voom (Variance modeling at the observational level, limma)**：不仅进行log2(CPM)变换，还为每个观测值估计一个权重，以适应线性模型的假设，常用于limma包进行差异分析。

*   **下游分析的数据选择总结**：
    *   **差异表达分析 (DESeq2, edgeR)**：**必须使用原始计数矩阵 (Raw Counts)。**
    *   **可视化 (Heatmap, PCA, Clustering)**：通常使用**log2变换后的标准化数据**（如log2(TPM+1), VST, rlog）。
    *   **基因相关性/共表达网络分析 (WGCNA)**：通常使用**标准化后的表达矩阵**（如TPM）。
    *   **机器学习/分类预测**：根据模型要求，可能使用标准化矩阵（TPM）或变换后的数据（log-transformed, VST, rlog）。

### 4. RNA-seq分析的计算资源需求

*   **存储需求评估**
    *   **原始数据 (FASTQ)**：最大头，通常每个样本几十到上百 GB (取决于测序深度)。压缩后(fastq.gz)会小很多，但仍是主要部分。
    *   **比对结果 (BAM)**：也很大，通常几十 GB 每个样本。排序和索引后会更大。
    *   **中间文件**：组装、定量等过程可能产生大量临时或中间文件。
    *   **结果文件 (计数矩阵, 差异表达列表等)**：相对较小，通常 MB 级别。
    *   **总体**：一个典型的RNA-seq项目可能需要几百 GB 到几 TB 的存储空间。需要考虑长期存储和备份策略。

*   **计算需求评估 (CPU & RAM)**
    *   **RAM (内存)**：**关键瓶颈！**
        *   **比对 (STAR)**：需要大量内存，基因组越大越复杂，所需内存越多。人类基因组通常需要 >30GB，建议 64GB 或更高。HISAT2 内存需求相对较低。
        *   **De Novo 组装 (Trinity)**：内存消耗巨大，可能需要数百 GB。
        *   **定量 (Salmon/Kallisto)**：内存需求相对较低。
        *   **差异表达分析**：对于样本量大的项目，也可能需要较多内存。
    *   **CPU (处理器)**：
        *   大部分步骤（比对、组装、定量）都支持多线程并行计算。
        *   核心数越多，分析速度越快。建议使用多核服务器（如 16核、32核或更多）。

*   **并行计算策略**
    *   **多线程 (Multithreading)**：大多数生物信息学工具都提供 `-p` 或 `-t` 或 `--threads` 参数，利用单台机器的多个CPU核心。
    *   **任务并行 (Task Parallelism)**：使用 `GNU Parallel` 或工作流管理系统（如 Snakemake, Nextflow）将多个样本或独立任务分配到不同的计算节点或核心上同时处理。
    *   **集群计算 (Cluster Computing)**：利用高性能计算集群 (HPC) 的资源调度系统（如 SLURM, SGE, PBS）提交和管理大量计算任务。

*   **云计算资源利用**
    *   **平台**：Amazon Web Services (AWS), Google Cloud Platform (GCP), Microsoft Azure 等。
    *   **优势**：
        *   **弹性伸缩**：按需获取计算资源（CPU, RAM, GPU）和存储空间，无需前期硬件投入。
        *   **预配置环境**：提供包含常用生物信息学工具的镜像或容器。
        *   **数据共享与协作**：方便团队成员共享数据和分析流程。
    *   **考虑因素**：成本控制（按使用量付费），数据传输速度和安全。

### 5. RNA-seq分析的质量控制点 (贯穿始终)

*   **原始数据质控 (Pre-alignment QC)**：
    *   **工具**：FastQC, MultiQC (聚合多个FastQC报告)。
    *   **检查点**：碱基质量、GC含量、接头污染、序列重复度、K-mer分布等。**目的是了解原始数据状况，决定是否需要以及如何进行数据清洗。**
*   **数据清洗后质控 (Post-trimming QC)**：
    *   **工具**：再次运行FastQC/MultiQC。
    *   **检查点**：确认低质量碱基和接头已被有效去除，reads长度分布是否符合预期。
*   **比对质量评估 (Post-alignment QC)**：
    *   **工具**：比对软件自身的报告 (如STAR log文件), Qualimap, Picard Tools (CollectRnaSeqMetrics), samtools stats。
    *   **检查点**：总比对率、唯一比对率、多重比对率、跨接位点比对情况、片段长度分布、基因体覆盖度 (gene body coverage)、链特异性检查等。**目的是评估比对效果是否理想，是否存在潜在问题（如gDNA污染、链特异性文库构建失败）。**
*   **表达量分布与样本关系检查 (Post-quantification QC)**：
    *   **方法**：箱线图 (Boxplot) 展示样本表达量分布、主成分分析 (PCA)、多维尺度分析 (MDS)、样本间相关性热图 (Correlation Heatmap)、层次聚类 (Hierarchical Clustering)。
    *   **检查点**：
        *   样本表达量分布是否一致（标准化后应大致相似）。
        *   生物学重复是否聚在一起？不同实验组是否能分开？
        *   是否存在明显的离群样本 (Outliers)？
        *   是否存在明显的批次效应？（如来自不同批次的样本在PCA图上分开）。**目的是评估数据整体质量、样本关系是否符合预期，识别异常样本和批次效应。**
*   **差异表达分析结果检查**：
    *   **方法**：MA图、火山图、P值分布直方图。
    *   **检查点**：MA图是否对称（经过良好标准化）、火山图差异基因分布是否合理、P值分布是否均匀（在零假设下，大量非差异基因的p值应均匀分布，接近0处有富集表示存在差异信号）。**目的是评估差异分析结果的可靠性。**

**本节总结**

RNA-seq数据分析是一个多步骤、迭代的过程，每一步都伴随着质量控制。理解标准流程、区分不同策略（参考依赖 vs De Novo）、掌握数据类型（计数 vs 标准化矩阵）及其适用场景、评估计算资源需求，并严格执行各阶段的质量控制，是确保最终分析结果准确可靠的关键。

## 转录本比对和拼接

### 1. RNA-seq比对的特殊挑战

![RNA-seq比对的特殊挑战](images/alignment_challenges.svg)
    *   *图示说明：该图形象地展示了RNA-seq比对需要处理的主要难点：跨越内含子的剪接读段。与DNA比对不同，RNA reads可能来源于成熟mRNA，其序列在基因组上是不连续的。*

*   **跨越内含子的比对 (Spliced Alignment)**：
    *   **核心挑战**：RNA-seq reads来源于经过剪接去除内含子(introns)的成熟mRNA（或其他RNA）。当一个read跨越一个或多个外显子-外显子连接点(exon-exon junction)时，它无法直接连续地比对到参考基因组上。
    *   **解决方案**：RNA-seq比对工具必须具备“剪接感知”(splice-aware)能力，能够将一个read切分成几部分，分别比对到基因组上不同的外显子区域，并识别中间的内含子。

*   **可变剪接处理 (Handling Alternative Splicing)**：
    *   **挑战**：同一个基因可能产生多种不同的剪接异构体(isoforms)，导致存在多种不同的剪接方式和外显子组合。比对工具需要能准确识别这些不同的剪接事件。
    *   **要求**：不仅要识别已知的剪接位点（基于注释），还要能发现新的、未注释的剪接位点。

*   **基因融合检测 (Detecting Gene Fusions)**：
    *   **挑战**：染色体重排可能导致两个基因融合在一起，产生嵌合转录本(chimeric transcript)。比对工具需要能识别那些一部分比对到一个基因，另一部分比对到另一个基因（可能在不同染色体上）的reads。

*   **同源基因/重复序列区分 (Distinguishing Homologous Genes/Repeats)**：
    *   **挑战**：基因组中存在高度相似的基因家族成员或重复序列。一个read可能同时比对到多个位置（多重比对，multi-mapping reads）。
    *   **处理**：比对工具需要有策略来报告或处理这些多重比对的reads（例如，报告所有可能位置，或选择最佳位置，或进行下游概率分配）。

*   **RNA编辑/多态性 (RNA Editing/Polymorphisms)**：
    *   **挑战**：RNA编辑或个体SNP位点会导致read序列与参考基因组存在少量碱基不匹配。比对算法需要容忍一定程度的错配(mismatches)和插入缺失(indels)。

### 2. HISAT2算法原理

*   **定位**：HISAT2 (Hierarchical Indexing for Spliced Alignment of Transcripts 2) 是TopHat2的后继者，以速度快、内存占用相对较低著称。
*   **核心技术：分层图FM索引 (Hierarchical Graph FM index, HGFM)**：
    *   **全局索引 (Global Index)**：一个覆盖整个基因组的FM索引，用于快速定位大部分可以直接比对的reads或read片段。
    *   **局部索引 (Local Indexes)**：数万个小的、覆盖基因组特定区域（约64kbp）的FM索引。这些局部索引非常小，可以快速加载到内存中。
    *   **分层策略**：首先尝试使用全局索引进行比对。如果失败（例如，因为跨越内含子），HISAT2会利用其内置的包含大量已知剪接位点和外显子的索引信息，识别潜在的锚点（anchor points），然后加载覆盖这些锚点区域的局部索引，进行更精细、更耗时的局部比对，以找到跨内含子的比对方案。
*   **剪接位点检测 (Splice Site Detection)**：
    *   利用内置的已知剪接位点数据库（来自注释文件）。
    *   也能通过比对模式（如寻找GT-AG等经典剪接信号）发现新的剪接位点。
*   **比对策略**：结合了全局搜索和局部精细搜索，利用多种索引策略（包括图索引）来加速剪接比对。
*   **与TopHat2的比较**：显著提高了比对速度和准确性，内存效率更高。
*   **参数优化策略**：
    *   `--known-splicesite-infile`：提供已知的剪接位点文件（通常从注释GTF提取），可以提高已知剪接位点的识别准确性和速度。
    *   `--rna-strandness`：指定链特异性文库类型（RF, FR），对准确定量和注释非常重要。
    *   `--dta`：输出适用于下游StringTie/Cufflinks组装的比对信息。

### 3. STAR算法原理

*   **定位**：STAR (Spliced Transcripts Alignment to a Reference) 是目前最快、最流行的RNA-seq比对工具之一，尤其在大型基因组（如人类）上表现优异，但内存需求较高。
*   **核心技术：最高可比对种子搜索 (Maximal Mappable Seed Search)**：
    *   **种子 (Seed)**：Read中的一个子序列。
    *   **策略**：STAR首先在read中寻找能够无错配地比对到基因组上的最长子序列（即Maximum Mappable Seed, MMS）。
    *   **索引**：使用后缀数组 (Suffix Array) 构建基因组索引，能够非常快速地查找种子的所有匹配位置。
*   **两阶段比对策略 (Two-pass Mode) (可选但推荐)**：
    *   **第一阶段 (1st Pass)**：对每个样本进行初步比对，同时发现新的、高置信度的剪接位点。
    *   **第二阶段 (2nd Pass)**：将所有样本在第一阶段发现的新剪接位点汇总起来，重新构建基因组索引（或仅更新剪接点数据库），然后再次进行比对。这样可以利用整个数据集的信息来提高对低丰度或样本特异性剪接位点的检测能力。
*   **剪接比对机制**：当一个read的种子不能覆盖整个read时（暗示可能存在剪接），STAR会尝试将剩余部分作为新的种子进行搜索，如果这些种子比对到基因组上相隔一段距离（符合内含子长度范围）且位置合理（符合剪接信号），则认为是一个剪接比对。
*   **非规范剪接检测 (Non-canonical Splicing)**：STAR对剪接位点的信号序列要求不严格，能够检测非经典的剪接位点（如GC-AG）。
*   **嵌合读段检测 (Chimeric Read Detection)**：STAR内置了检测嵌合（融合）转录本来源的reads的功能，可以输出专门的嵌合体检测文件。
*   **计算资源需求**：**内存（RAM）需求大**。构建人类基因组索引约需30GB RAM，运行时也需要相似量级的内存。速度极快，尤其利用多线程时。
*   **关键参数**：
    *   `--runMode genomeGenerate`：构建基因组索引。
    *   `--genomeDir`：指定索引目录。
    *   `--readFilesIn`：输入FASTQ文件。
    *   `--outSAMtype BAM SortedByCoordinate`：输出排序后的BAM文件。
    *   `--twopassMode Basic`：启用两阶段模式。
    *   `--chimSegmentMin`：控制嵌合体检测的最小片段长度。

### 4. 其他RNA-seq比对工具

*   **GSNAP (Genomic Short-read Nucleotide Alignment Program)**：
    *   特点：擅长处理复杂的变异，如SNP、Indel和长距离剪接。对长reads支持较好。
*   **BBMap**：
    *   特点：速度快，内存效率高，准确性好。对多种测序平台和数据类型支持良好。由JGI开发。
*   **Salmon / Kallisto (伪比对 / Alignment-free)**：
    *   **注意**：它们不是传统意义上的比对工具，不生成BAM文件。它们的目标是**快速定量**。
    *   **原理**：基于k-mer匹配和期望最大化(EM)算法，直接将reads概率性地分配给参考转录本序列。
    *   **优点**：极快，计算资源需求低。
    *   **缺点**：不提供碱基级别的比对信息，不适用于需要比对细节的分析（如SNP calling, RNA编辑分析）。

*   **工具选择建议**
    *   **通用首选**：**STAR** (如果内存足够 >30-60GB) 或 **HISAT2** (内存受限时)。两者都是目前广泛使用且性能优良的选择。STAR通常更快，尤其在多线程下，且对新剪接位点检测可能更敏感。
    *   **仅需定量**：**Salmon** 或 **Kallisto** 是最快的选择，尤其适合大规模数据集或快速预览。需要提供参考转录本序列（通常是CDS或所有转录本的fasta文件）。
    *   **复杂变异/长读段**：可以考虑GSNAP。
    *   **特定需求/偏好**：BBMap也是一个可靠的选择。

### 5. 转录本组装原理 (Transcriptome Assembly)

*   **目标**：利用RNA-seq的短读段(short reads)拼接(assemble)出尽可能完整的转录本序列(transcripts)。
*   **两大策略**：
    *   **参考指导组装 (Reference-Guided Assembly)**：
        *   **输入**：RNA-seq reads比对到参考基因组的BAM文件。
        *   **原理**：利用比对信息构建剪接图(splicing graph)或重叠群图(overlap graph)，图中的节点代表外显子或基因组区域，边代表剪接连接或reads覆盖的连续区域。通过寻找图中的路径来重建转录本。
        *   **代表工具**：StringTie, Cufflinks。
    *   **从头组装 (De Novo Assembly)**：
        *   **输入**：原始的RNA-seq reads (FASTQ文件)。
        *   **原理**：不依赖参考基因组。核心思想是将reads打碎成固定长度的k-mers，然后构建De Bruijn图。图中的节点是k-mers，边表示k-mers之间存在k-1的重叠。通过寻找图中的路径来推断出转录本序列。
        *   **代表工具**：Trinity, SOAPdenovo-Trans, SPAdes (RNA模式)。

*   **基于图的组装算法 (Graph-based Assembly)**
    *   **De Bruijn 图 (De Bruijn Graph, DBG)**：
        *   **核心思想**：关注k-mers之间的重叠关系。
        *   **优点**：对测序错误相对鲁棒，能有效处理高覆盖度数据。
        *   **挑战**：重复序列（基因家族、低复杂度区域）会在图中形成复杂的分支和气泡，难以解析；可变剪接也会导致图结构复杂化。k-mer大小的选择很关键。
        *   **主要用于**：De Novo组装。
    *   **重叠图/剪接图 (Overlap Graph / Splicing Graph)**：
        *   **核心思想**：利用reads的比对位置或reads之间的重叠信息构建图。
        *   **优点**：能更直观地利用比对信息来指导组装，尤其是在参考指导组装中。
        *   **主要用于**：参考指导组装。

*   **转录本重建挑战**
    *   **可变剪接**：同一基因座可能存在多条路径，需要算法能区分并重建不同的异构体。
    *   **表达水平差异巨大**：高表达转录本易于组装，低表达转录本可能因覆盖度不足而断裂或丢失。
    *   **基因家族/重复序列**：导致组装图模糊不清，难以区分相似转录本。
    *   **测序错误和偏好性**：引入错误的k-mer或路径。
    *   **嵌合体转录本**：组装算法可能错误地将来自不同基因的片段连接起来。

### 6. StringTie算法原理

*   **定位**：目前最流行的**参考指导**转录本组装和定量工具之一，通常接在HISAT2或STAR比对之后。
*   **核心技术：流网络模型 (Network Flow Model)**：
    *   **构建剪接图**：首先，基于BAM文件中reads的比对信息（尤其是跨越剪接点的reads）构建一个剪接图。图的节点大致对应外显子或连续覆盖区域，边代表剪接或连续覆盖。
    *   **转化为流网络**：将剪接图看作一个流网络。每条边赋予一个容量(capacity)，这个容量与覆盖该区域/连接的reads数量相关。
    *   **最大流算法 (Max-Flow Algorithm)**：通过求解从源点（转录起始区域）到汇点（转录终止区域）的最大流问题，来找到网络中最可能代表真实转录本的路径。每找到一条最大流路径，就对应组装出一个转录本，并从网络中移除相应的流（reads覆盖度）。
    *   **迭代过程**：重复寻找最大流路径，直到网络中没有足够的流（reads）来支持新的路径，从而组装出多个异构体。
*   **表达量估计整合**：在组装的同时，利用最大流算法中计算的流量（flow）来估计每个组装出的转录本的表达丰度（如FPKM, TPM）。
*   **与Cufflinks的比较**：StringTie在速度和内存效率上通常优于其前身Cufflinks，并且在某些情况下组装准确性更高。目前更推荐使用StringTie。
*   **主要功能**：
    *   组装新的和已知的转录本（需要提供参考注释GTF文件以辅助）。
    *   估计基因和转录本的表达量。
    *   合并多个样本的组装结果，生成统一的转录本集合 (`stringtie --merge`)。

### 7. 从头转录本组装 (De Novo Transcriptome Assembly)

*   **Trinity工作原理**：
    *   **定位**：目前最广泛使用的**De Novo** RNA-seq组装软件。
    *   **三个模块**：
        1.  **Inchworm**：通过贪婪地延伸高频k-mers来组装出线性的contigs（代表转录本的片段或完整转录本的骨架）。
        2.  **Chrysalis**：将Inchworm产生的contigs聚类，构建多个独立的De Bruijn图。每个图代表一个基因或一组来自可变剪接的相似转录本。同时读取原始reads并将其分配到相应的图上。
        3.  **Butterfly**：并行地处理每个Chrysalis图。在每个图中，利用reads信息（包括paired-end信息）解析复杂的图结构（如气泡、分支），最终输出全长的转录本异构体序列。
    *   **优势**：设计上能够很好地处理可变剪接，并尝试重建尽可能多的全长异构体。
    *   **资源需求**：计算密集，需要大量内存和CPU时间。

*   **De Bruijn图在转录本组装中的应用**：
    *   **核心**：将RNA-seq reads转化为k-mer集合，再用k-mer间的重叠关系构建图。
    *   **挑战**：如何选择最优的k-mer大小？（小k对低覆盖敏感但易受重复影响，大k区分重复能力强但对测序错误和低覆盖敏感）。Trinity内部可能使用固定k（如25或31）。如何有效地遍历图并解析复杂区域（分支、循环）以重建转录本。

*   **组装后处理与过滤 (Post-assembly Processing & Filtering)**：
    *   **必要性**：De Novo组装通常会产生大量冗余、片段化、甚至可能是错误的转录本。
    *   **常用步骤**：
        *   **去除冗余**：使用CD-HIT-EST等工具去除高度相似或完全相同的序列。
        *   **过滤低表达/短序列**：根据覆盖度或长度阈值去除可能不可靠的转录本。
        *   **评估编码潜能**：使用TransDecoder等工具预测ORF（开放阅读框），区分编码和非编码转录本。
        *   **功能注释**：使用BLAST, InterProScan等对组装的转录本进行注释。
        *   **嵌合体检测**：使用特定工具或检查比对模式。

*   **组装质量评估 (Assembly Quality Assessment)**：
    *   **指标**：
        *   **N50/Nx**：衡量组装连续性的指标。N50是指，将所有组装出的contigs按长度从长到短排序，累加长度达到总长度50%时的那个contigs的长度。N50越大通常表示组装效果越好。
        *   **Contig数量**：总共组装出多少条转录本。
        *   **平均/最长 Contig长度**。
        *   **BUSCO (Benchmarking Universal Single-Copy Orthologs)**：通过检查组装结果中保守的单拷贝基因的完整性来评估组装的完整度和准确性。**是目前最推荐的评估指标之一。**
        *   **比对回参考基因组/转录组**（如果可能）：检查组装出的转录本与已知基因的匹配程度。
        *   **原始Reads比对回组装结果的比率**：看有多少比例的原始数据能被组装结果所解释。

**本节总结**

RNA-seq比对需克服剪接带来的挑战，STAR和HISAT2是当前主流的高效比对工具。转录本组装旨在重建完整的转录本序列，分为参考指导（StringTie）和从头组装（Trinity）两大策略。De Novo组装是研究无参考基因组物种的关键，但计算要求高且需仔细进行质量评估。理解这些工具的原理和适用场景，有助于选择合适的方法并正确解读结果。

## 基因表达定量方法

### 1. 表达定量的基本概念

![基因表达定量方法](images/expression_quantification.svg)
    *   *图示说明：该图可能示意了将测序读段（reads）分配到基因或转录本上进行计数的过程，突出了表达定量的核心任务。*

*   **核心目标**：估计每个基因或转录本在样本中的相对丰度(relative abundance)。
*   **基本单位**：
    *   **读段计数 (Read Count)**：最直接的衡量方式，指比对到某个基因或转录本区域的测序读段（reads）或片段（fragments，对于双端测序）的数量。这是许多下游统计分析（尤其是差异表达）的基础。
    *   **覆盖度 (Coverage)**：指基因或转录本被测序读段覆盖的程度或深度。虽然与表达量相关，但通常不直接用作定量单位，更多用于可视化或评估测序均匀性。
*   **定量层级 (Quantification Level)**：
    *   **基因级别定量 (Gene-level Quantification)**：估计整个基因（所有其转录本异构体的总和）的表达水平。通常通过汇总比对到该基因所有外显子的reads来计算。**更常用，尤其是在差异表达分析中，结果更稳定。**
    *   **转录本级别定量 (Transcript-level / Isoform-level Quantification)**：估计基因的每个不同剪接异构体的表达水平。**更精细，能研究可变剪接调控，但定量难度更大，不确定性更高。**
*   **定量的挑战**：
    *   **读段模糊性 (Read Ambiguity)**：
        *   **多重比对 (Multi-mapping Reads)**：一个read可以比对到基因组的多个位置（如重复序列、同源基因）。如何分配这些reads的计数？
        *   **基因重叠 (Overlapping Genes)**：基因在基因组上可能存在重叠区域。比对到重叠区的read应计入哪个基因？
        *   **共享外显子 (Shared Exons)**：一个基因的不同转录本可能共享某些外显子。比对到共享外显子的read属于哪个异构体？这是转录本级别定量的核心难题。
    *   **技术偏好性 (Technical Biases)**：文库制备（如PCR扩增偏好、片段化偏好）和测序过程可能引入系统性偏差，影响定量准确性。需要通过标准化方法校正。
    *   **RNA组成变化 (RNA Composition Effects)**：少数极高表达的基因发生剧烈变化时，可能导致其他基因的相对比例发生变化，即使它们的绝对表达量未变。标准化方法（如TMM, RLE）旨在解决此问题。

### 2. 计数方法 (Generating Raw Counts)

*   **核心任务**：将比对好的reads（通常在BAM文件中）分配给基因组特征（通常是基因或外显子），并进行计数。需要一个基因注释文件 (GTF或GFF格式) 来定义这些特征的位置。
*   **处理读段模糊性的策略**：
    *   **唯一比对 (Uniquely Mapped Reads)**：最简单的方法，只统计那些能够唯一比对到某个基因特征的reads。**缺点：丢失了多重比对reads的信息，可能低估基因家族成员或位于重复区域基因的表达量。**
    *   **多重比对处理**：
        *   **全部分配 (Count all mappings)**：将一个多重比对read计入所有它比对上的特征。**缺点：可能夸大表达量。**
        *   **分数分配 (Fractional Allocation)**：将一个比对到N个位置的read，给每个位置分配1/N的计数值。**更合理，但实现复杂。**
        *   **期望最大化 (Expectation-Maximization, EM) 算法**：一些更高级的定量工具（尤其是在转录本级别）使用EM算法，根据唯一比对reads提供的证据，迭代地估计多重比对reads最可能来源于哪个转录本，并据此进行概率性分配。
*   **特征重叠处理 (Handling Feature Overlap)**：
    *   **HTSeq-count 模式**：
        *   `union` (默认模式)：如果一个read与至少一个基因的任何外显子重叠，就计数。如果它与多个基因重叠，则标记为模糊(ambiguous)，不计数。**保守，但可能丢失重叠基因信息。**
        *   `intersection-strict`：只有当一个read完全包含在某个基因的一个外显子内，并且不与其他基因重叠时，才计数。**非常严格，可能丢失跨外显子或部分重叠的reads。**
        *   `intersection-nonempty`：只要read与某个基因的任何外显子有非空交集，就计数。如果read与多个基因有交集，标记为模糊。
    *   **featureCounts 策略**：提供了更灵活的选项来处理重叠，例如可以将read分配给它重叠的所有特征，或者只分配给一个（可能基于重叠度）。
*   **常用计数工具**：
    *   **HTSeq-count**：
        *   Python脚本，易于使用，被广泛引用。
        *   相对较慢。
        *   提供上述几种处理重叠的模式。
    *   **featureCounts**：
        *   包含在Subread软件包中，用C语言编写，**速度非常快**。
        *   功能强大，选项丰富，包括处理多重比对、双端reads、链特异性等。
        *   **是目前推荐的主流计数工具。**
    *   **STAR**：在比对时可以直接输出基因计数 (`--quantMode GeneCounts`)。
    *   **Salmon / Kallisto**：虽然是伪比对工具，但也能输出估计的计数。

### 3. 表达量标准化方法 (Normalization)

*   **目的**：消除非生物学因素（主要是测序深度和RNA组成）对原始计数的影响，使得表达量可以在样本之间进行有意义的比较。
*   **总读段数标准化 (基于库大小的标准化)**：
    *   **RPM (Reads Per Million mapped reads)**：`RPM = (Raw Count / Total Mapped Reads in Sample) * 1,000,000`
    *   **CPM (Counts Per Million mapped reads)**：同RPM，有时用于区分原始计数。
    *   **优点**：计算简单，直观理解。
    *   **缺点**：
        *   未校正基因长度差异（比较不同基因表达量时有问题）。
        *   易受极高表达基因的影响（RNA组成偏差）。例如，如果一个基因在某个样本中表达量极高，会使其他所有基因的RPM/CPM值相对降低，即使它们的真实表达未变。

*   **RPKM/FPKM标准化 (校正库大小和基因长度)**：
    *   **RPKM (Reads Per Kilobase of transcript per Million mapped reads)**： `RPKM = (Raw Count / (Gene Length in kb * Total Mapped Reads in Sample in Millions))`
    *   **FPKM (Fragments Per Kilobase of transcript per Million mapped reads)**：用于双端测序，概念类似RPKM，但基于Fragments计数。
    *   **计算原理**：先按基因长度标准化（除以长度），再按测序深度标准化（除以总reads数）。
    *   **适用场景**：主要用于**比较同一个样本内不同基因的表达水平**。
    *   **局限性**：**不适合用于样本间的比较**，因为不同样本的RPKM/FPKM总和可能不同，且同样受RNA组成偏差影响。**目前已不推荐用于差异表达分析或样本间比较。**

*   **TPM标准化 (Transcripts Per Million)**：
    *   **计算原理**：
        1.  **长度标准化**：先将每个基因的原始计数除以其长度（单位kb），得到 RPK (Reads Per Kilobase)。
        2.  **库大小标准化 (按比例)**：计算样本中所有基因的RPK之和 (Total RPKs)。
        3.  **计算TPM**： `TPM = (RPK / Total RPKs) * 1,000,000`
    *   **与FPKM的区别**：计算顺序不同（TPM先长度标准化，再库大小标准化）。关键在于，**一个样本内所有基因的TPM值加起来等于一百万**。
    *   **优势与适用场景**：
        *   **样本内和样本间比较**：由于每个样本的TPM总和固定，TPM值可以更好地反映基因在该样本总mRNA中所占的比例，因此更适合样本间的比较。
        *   **是目前推荐的用于展示和比较基因相对表达丰度的单位。**
    *   **注意**：虽然TPM比FPKM更适合比较，但它仍然是标准化的值，不应用作差异表达统计模型的直接输入（应使用原始计数）。

*   **用于差异表达的内部标准化方法**：
    *   **TMM (Trimmed Mean of M-values, in edgeR)**
    *   **RLE (Relative Log Expression, in DESeq2)**
    *   **MRN (Median Ratio Normalization, DESeq2的底层方法)**
    *   **原理**：这些方法旨在计算更稳健的“标准化因子”(Size Factors)来校正库大小和RNA组成偏差，而不是直接修改计数值。它们假设大部分基因在样本间是非差异表达的，并基于这些基因来估计标准化因子。
    *   **用途**：这些方法产生的Size Factors被DESeq2/edgeR等工具内部用于其统计模型，以进行准确的差异表达分析。**用户通常不需要手动应用这些方法，只需提供原始计数矩阵给工具即可。**

*   **相对表达量 vs 绝对表达量**
    *   **相对表达量 (Relative Expression)**：RNA-seq测量的是基因表达的相对水平（相对于总RNA量或相对于其他基因）。RPM, CPM, FPKM, TPM 都是相对表达量。
    *   **绝对表达量 (Absolute Expression)**：指每个细胞中某个基因的mRNA分子数。RNA-seq通常**不能**直接测量绝对表达量，除非加入已知浓度的内参RNA（Spike-ins）。

### 4. 转录本定量方法 (Transcript/Isoform Quantification)

*   **挑战**：由于不同异构体共享外显子，导致大量reads比对到多个异构体上，需要精确地将这些模糊的reads分配到各自来源的异构体。
*   **基于比对的方法 (Alignment-based)**：
    *   **工具**：Cufflinks, StringTie (在其组装功能中包含定量)
    *   **原理**：首先将reads比对到基因组。然后，基于比对结果（尤其是跨越异构体特异性剪接点的reads和片段长度分布信息）构建一个似然模型，通常使用期望最大化(EM)算法来迭代估计每个异构体的相对丰度。
    *   **优点**：可以同时进行组装和定量。
    *   **缺点**：比对步骤耗时；准确性依赖于比对质量和模型的假设。
*   **基于k-mer的方法 (Alignment-free / Pseudo-alignment)**：
    *   **工具**：Salmon, Kallisto, Sailfish (早期代表)
    *   **原理**：
        1.  **索引构建**：对**参考转录本序列 (fasta格式)** 构建索引（通常是基于k-mer的哈希表或De Bruijn图）。
        2.  **伪比对 (Pseudo-alignment)**：将每个RNA-seq read打碎成k-mers。快速查找这些k-mers出现在哪些转录本中，确定read与哪些转录本“兼容”(compatible)。**注意：这里不进行精确的碱基比对。**
        3.  **表达量估计 (Quantification)**：构建一个似然模型，其中每个read的来源是一个潜在变量。使用**期望最大化 (EM) 算法**或**变分贝叶斯 (Variational Bayesian, VB)** 方法，基于所有reads的兼容性信息，迭代地估计每个转录本的相对丰度(通常输出TPM和估计计数)。模型会考虑转录本长度、序列相似性（导致k-mer共享）以及可能的测序偏好（如GC偏好、片段长度分布）。
    *   **计算效率优势**：**极快**，因为跳过了耗时的比对步骤。内存占用也相对较低。
    *   **准确性**：在准确性上通常与基于比对的方法相当，甚至有时更好。
*   **方法选择建议**：
    *   **如果主要目标是转录本级别的快速、准确定量**：**Salmon** 或 **Kallisto** 是目前的首选。它们速度快，资源占用少，准确性高。
    *   **如果需要同时进行参考指导的组装和定量**：**StringTie** 是好的选择。
    *   **如果需要基因组比对文件(BAM)用于其他分析 (如SNP calling, 可视化)**：需要先运行比对工具（如STAR, HISAT2），然后可以选择基于BAM文件的定量工具（如featureCounts用于基因计数，或将BAM输入给后续的StringTie进行转录本定量）。

### 5. 表达定量质量评估 (QC for Quantification)

*   **目的**：检查定量结果的合理性和一致性。
*   **技术重复一致性 (Consistency of Technical Replicates)**：
    *   如果做了技术重复，它们之间的表达量（如TPM或log-counts）应该高度相关。
    *   **方法**：计算相关系数（Pearson或Spearman），绘制散点图。相关性应非常高（如 R > 0.95）。
*   **生物学重复变异性 (Variability of Biological Replicates)**：
    *   生物学重复之间的变异性应该大于技术重复，但同组内的生物学重复应该比不同组之间的更相似。
    *   **方法**：PCA图、MDS图、样本相关性热图。同组样本应在图中聚类在一起。
*   **表达分布检查 (Expression Distribution Check)**：
    *   **方法**：绘制每个样本表达量（如log2(TPM+1) 或 log2(CPM+1)）的箱线图或密度图。
    *   **预期**：经过标准化后，不同样本的表达量分布应大致相似。异常的分布可能提示样本质量问题或标准化失败。
*   **与已知知识比较**：检查一些已知高表达（如管家基因）或低表达基因的定量结果是否符合预期。
*   **批次效应评估 (Batch Effect Assessment)**：
    *   **方法**：在PCA图或聚类图中，用不同颜色或形状标记来自不同批次的样本，观察是否存在按批次聚集的现象。
    *   **处理**：如果存在显著批次效应，应在下游差异表达分析的模型中加以校正。

**本节总结**

基因表达定量是RNA-seq分析的核心步骤，旨在估计基因或转录本的丰度。理解计数（用于差异分析）和标准化（用于比较和可视化）的区别至关重要。TPM是目前推荐的用于比较相对表达量的单位。快速的k-mer定量工具（Salmon, Kallisto）在转录本定量方面表现优异。定量的准确性依赖于上游的比对/伪比对质量，并需要通过多方面的QC来评估结果的可靠性。

## 差异表达分析统计原理

### 1. 差异表达分析基本概念

![差异表达分析](images/differential_expression.svg)
    *   *图示说明：该图可能展示了比较两组（如处理组 vs 对照组）样本的基因表达水平，并识别出表达量有显著变化的基因（上调或下调）。*

*   **核心目标**：识别在不同实验条件（如疾病vs健康、药物处理vs对照、不同时间点）下，哪些基因的表达水平发生了统计学上显著的变化。
*   **统计假设检验框架 (Statistical Hypothesis Testing Framework)**：
    *   对于**每个基因**，我们进行一次假设检验：
        *   **零假设 (H₀, Null Hypothesis)**：该基因在不同组间的平均表达水平**没有差异**（或者说，差异为零）。
        *   **备择假设 (H₁, Alternative Hypothesis)**：该基因在不同组间的平均表达水平**存在差异**（差异不为零）。
    *   **检验统计量 (Test Statistic)**：基于样本数据计算一个值，用来衡量观测到的差异相对于随机波动的程度。
    *   **P 值 (P-value)**：在零假设成立的前提下，观测到当前检验统计量或更极端值的概率。P值越小，表示观测到的差异越不可能是由随机因素造成的。
    *   **显著性水平 (Significance Level, α)**：预先设定的阈值（通常为 α = 0.05 或 0.01）。如果计算出的 P 值 < α，我们**拒绝零假设 (Reject H₀)**，认为该基因是差异表达的。否则，我们**不能拒绝零假设 (Fail to reject H₀)**。

*   **多重检验校正 (Multiple Testing Correction)**：
    *   **问题**：在RNA-seq分析中，我们通常同时对成千上万个基因（比如20,000个）进行假设检验。如果每次检验都使用 α = 0.05 的阈值，即使所有基因都没有真实差异（所有H₀都为真），我们预计也会有 20000 * 0.05 = 1000 个基因因为随机性而被错误地判定为差异表达（即**假阳性, False Positives**）。这就是多重检验问题。
    *   **目标**：控制整体的错误发现率，而不是单次检验的错误率。
    *   **常用方法**：
        *   **Bonferroni 校正**：最简单但过于保守的方法。将单次检验的显著性水平 α 除以检验的总次数 m (α_corrected = α / m)。会导致很高的假阴性率（漏掉真实的差异基因）。
        *   **Benjamini-Hochberg (BH) 校正 / FDR 控制**：**目前最常用的方法。** 控制的是**错误发现率 (False Discovery Rate, FDR)**，即在所有被宣布为显著的发现中，假阳性的比例。通常将校正后的P值（称为 q-value 或 adjusted p-value）与一个预设的FDR阈值（如 0.05 或 0.1）进行比较。**比Bonferroni更强大（能发现更多真实差异）。**

*   **假阳性率 (FDR) 控制**：
    *   FDR = E[V / R | R > 0]，其中 V 是假阳性数，R 是总阳性数（被宣布为差异的基因数）。FDR控制的目标是使得这个期望比例低于设定的阈值（如5%）。
    *   **实际操作**：我们通常看差异表达分析软件输出的 `adjusted p-value` 或 `FDR` 列，并使用一个阈值（如 `FDR < 0.05`）来筛选差异基因。

*   **统计功效与样本量 (Statistical Power & Sample Size)**：
    *   **统计功效 (Power)**：正确拒绝错误零假设的概率（即，当基因确实存在差异时，我们能够成功检测到它的概率，1 - β，β是假阴性率）。
    *   **影响因素**：
        *   **效应大小 (Effect Size)**：基因真实的表达差异倍数 (Fold Change)。差异越大，越容易检测到。
        *   **样本量 (Sample Size)**：**生物学重复数是决定功效的关键因素。** 样本量越大，功效越高。
        *   **样本内变异 (Within-group Variability)**：生物学重复之间的变异越小，功效越高。
        *   **显著性水平 (α / FDR)**：α 或 FDR 阈值设得越宽松，功效越高，但假阳性风险也越高。
    *   **重要性**：在实验设计阶段，应进行功效分析（Power Analysis）来估计需要多少样本量才能以足够的概率检测到预期大小的差异。样本量不足是导致实验失败（无法发现真实差异）的常见原因。**通常建议每组至少3个生物学重复，对于期望差异较小或变异较大的系统，需要更多（如5个或更多）。**

### 2. RNA-seq计数数据的统计特性

*   **离散计数数据特点 (Discrete Count Data)**：
    *   RNA-seq的原始输出是reads计数，是非负整数。
    *   这决定了不能直接使用适用于连续数据（如芯片数据）的传统统计方法（如t检验、ANOVA，它们通常假设数据服从正态分布且方差齐性）。
*   **均值-方差关系 (Mean-Variance Relationship)**：
    *   对于计数数据，方差通常与均值相关。表达量越高（均值越大）的基因，其计数的绝对波动（方差）也越大。
    *   **泊松分布 (Poisson Distribution)**：一个简单的计数模型，其特点是**方差等于均值**。在某些理想情况下（如只有技术噪音），可能适用。
    *   **过度离散现象 (Overdispersion)**：在实际RNA-seq数据中，观测到的方差通常**大于**均值。这是因为除了泊松分布所描述的抽样变异（技术噪音）外，还存在**生物学变异**（不同生物重复之间的真实表达水平差异）。
    *   **负二项分布 (Negative Binomial Distribution, NB)**：**是模拟RNA-seq计数数据最常用的模型。** NB分布有两个参数（均值 μ 和离散度 α），其方差为 `Var = μ + αμ²`。当离散度 α > 0 时，方差大于均值，可以很好地拟合过度离散的数据。当 α → 0 时，NB分布近似于泊松分布。
*   **低表达基因的挑战 (Challenges with Low Counts)**：
    *   低表达基因的计数绝对值小，相对变异大，信号噪音比低。
    *   对其差异表达的检测功效较低，结果可能不稳定。
    *   **处理**：通常在分析前会进行过滤，移除那些在所有（或几乎所有）样本中表达量都极低的基因（如基于平均计数、CPM或在多少样本中计数大于某个阈值）。
*   **异方差性 (Heteroscedasticity)**：
    *   如上所述，方差随均值变化。这是RNA-seq数据的一个重要特征，差异表达分析的统计模型必须能妥善处理这一点。NB模型天然地考虑了这种关系。基于线性模型的方法（如limma-voom）则需要通过数据变换或加权来处理。

### 3. DESeq2原理

*   **定位**：一个非常流行且广泛使用的R Bioconductor包，专门用于RNA-seq数据的差异表达分析。
*   **核心模型：负二项分布广义线性模型 (Negative Binomial Generalized Linear Model, NB-GLM)**：
    *   假设基因 `i` 在样本 `j` 的计数 `Kᵢⱼ` 服从负二项分布： `Kᵢⱼ ~ NB(μᵢⱼ, αᵢ)`
    *   均值 `μᵢⱼ` 与实验设计相关，通过对数连接函数 (log link) 建模： `log₂(μᵢⱼ) = xⱼ * βᵢ`，其中 `xⱼ` 是样本 `j` 的设计矩阵行（包含分组信息、批次信息等协变量），`βᵢ` 是基因 `i` 的系数向量（包括表达差异的对数倍数 Log2 Fold Change）。
    *   `sⱼ` 是样本 `j` 的标准化因子 (Size Factor)，用于校正测序深度和RNA组成偏差。DESeq2使用中位数比例法 (Median Ratio Normalization) 估计 `sⱼ`。`μᵢⱼ = sⱼ * qᵢⱼ`，其中 `qᵢⱼ` 是与样本条件相关的期望计数值。
*   **离散度估计方法 (Dispersion Estimation)**：
    *   **关键步骤**：准确估计每个基因的离散度 `αᵢ` 对于可靠的差异分析至关重要。
    *   **策略**：
        1.  **基因별估计 (Gene-wise Estimation)**：为每个基因独立估计一个离散度。对于低计数的基因，这种估计可能不稳定。
        2.  **均值-离散度关系拟合 (Fit Mean-Dispersion Trend)**：假设具有相似表达水平的基因具有相似的离散度，拟合一个表达均值与离散度的关系曲线。
        3.  **经验贝叶斯收缩 (Empirical Bayes Shrinkage)**：**DESeq2的核心优势之一。** 将每个基因独立估计的离散度向拟合的趋势线进行收缩(shrinkage)。表达量高、信息量足的基因，其离散度估计更多地依赖自身数据；表达量低、信息量少的基因，其估计则更多地借鉴表达水平相似的其他基因的信息。这使得离散度估计更稳定、更可靠，尤其对于低表达基因。
*   **收缩估计策略 (Shrinkage Estimation for Log2 Fold Changes)**：
    *   **目的**：为了改善差异倍数(LFC)的估计，特别是对于低计数基因（其LFC估计噪音大）和高离散度基因。
    *   **方法**：使用经验贝叶斯方法，将基因的LFC估计值向零值进行收缩。收缩的强度取决于基因的表达量和离散度。低计数或高变异基因的LFC会被更强地收缩。
    *   **优点**：使得LFC排名更可靠，减少因噪音导致的极端LFC值，有助于下游排序和功能分析。提供了 `lfcShrink` 函数来实现。
*   **假设检验：Wald 检验 (Wald Test)**：
    *   默认方法，用于检验模型中某个系数（通常是代表组间差异的LFC）是否显著不为零。速度快。
*   **假设检验：似然比检验 (Likelihood Ratio Test, LRT)**：
    *   用于比较两个嵌套的模型（一个包含某项因素，一个不包含）。例如，检验某个因素（如处理效果、时间效应）是否整体上对基因表达有显著影响，或者用于多组比较。比Wald检验更适合复杂模型或检验多个系数。
*   **异常值处理 (Outlier Handling)**：
    *   内置Cook's distance方法检测对模型拟合影响过大的异常计数值，并将其标记和自动处理（在LFC收缩和p值计算时降低其权重或排除）。
*   **批次效应校正 (Batch Effect Correction)**：
    *   可以在设计矩阵中直接包含批次信息作为协变量 (`design = ~ batch + condition`)，模型会在估计处理效应时校正批次效应。

### 4. edgeR原理

*   **定位**：另一个非常流行且历史悠久的R Bioconductor包，用于差异表达分析，尤其适用于计数数据。
*   **核心模型：负二项分布模型**：
    *   与DESeq2类似，也使用负二项分布来模拟计数数据 `yᵢⱼ ~ NB(μᵢⱼ, φᵢ)`，其中 `μᵢⱼ = lib.sizeⱼ * pᵢⱼ` (库大小 * 相对丰度)，方差 `Var(yᵢⱼ) = μᵢⱼ + φᵢ * μᵢⱼ²`。这里的 `φᵢ` 是离散度参数 (dispersion)。
*   **经验贝叶斯框架 (Empirical Bayes Framework)**：
    *   **离散度估计**：edgeR也使用经验贝叶斯思想来稳定离散度估计。
        *   **共同离散度 (Common Dispersion)**：假设所有基因共享一个离散度值，通过最大化所有基因的联合似然函数来估计。
        *   **趋势离散度 (Trended Dispersion)**：拟合离散度与基因平均表达量的关系。
        *   **标签/基因特异性离散度 (Tagwise/Gene-wise Dispersion)**：以共同或趋势离散度为先验，结合每个基因自身的数据，使用加权似然方法估计每个基因的离散度。类似于DESeq2的收缩，但实现方式不同。
*   **假设检验方法**：
    *   **精确检验 (Exact Test)**：基于超几何分布的精确检验的变种，适用于NB分布。主要用于两组比较，不需要复杂的模型拟合。**是edgeR早期版本的经典方法，简单、快速。**
    *   **广义线性模型 (GLM) + 似然比检验 (Likelihood Ratio Test, LRT) 或 准似然F检验 (Quasi-Likelihood F-test, QLF)**：
        *   **GLM框架**：允许拟合复杂的设计矩阵（多因素、批次效应、配对设计等），类似于DESeq2。
        *   **LRT**：类似于DESeq2的LRT。
        *   **QLF (推荐)**：准似然框架考虑了离散度估计的不确定性，对离散度估计误差更鲁棒，通常能更好地控制FDR，尤其是在生物学重复较少时。**是edgeR目前推荐的方法。**
*   **标准化**：使用TMM (Trimmed Mean of M-values) 方法计算标准化因子，校正库大小和RNA组成偏差。

### 5. limma-voom原理

*   **定位**：limma最初是为微阵列数据设计的著名R包，后来通过voom方法扩展应用于RNA-seq数据。它利用线性模型框架，非常灵活，特别擅长处理复杂实验设计。
*   **核心思想**：将RNA-seq计数数据**转换**，使其能够适用于基于正态分布假设的线性模型（即limma原有的分析框架）。
*   **voom转换步骤**：
    1.  **标准化**：将原始计数转换为**log2(Counts Per Million)**，即 `log2(CPM)`。通常会加入一个小的伪计数（offset）。
    2.  **均值-方差关系建模**：对每个基因的 `log2(CPM)` 值拟合一个线性模型，得到残差。然后，基于残差的平方根（standard deviation）与平均表达量（average log2CPM）的关系，使用LOWESS（局部加权回归）拟合一个均值-方差趋势。
    3.  **计算观测级别权重 (Observation-level Weights)**：利用拟合的均值-方差趋势，预测每个观测值（即每个基因在每个样本中的log2CPM值）应有的方差。计算每个观测值的权重，权重与预测方差的倒数成正比（即，方差小的观测值获得更高权重）。
    4.  **输出**：voom函数输出一个包含 `log2(CPM)` 值的表达矩阵 `E` 和一个包含相应权重的矩阵 `weights`。
*   **线性模型框架 (Linear Modeling)**：
    *   将voom转换后的 `log2(CPM)` 数据和权重矩阵输入到limma的线性模型函数 `lmFit` 中。
    *   模型形式：`E[yᵢⱼ] = xⱼ * βᵢ`，其中 `yᵢⱼ` 是基因 `i` 在样本 `j` 的 `log2(CPM)` 值，`xⱼ` 是设计矩阵行，`βᵢ` 是系数。模型拟合时会考虑 `weights`。
*   **经验贝叶斯调节 (Empirical Bayes Moderation)**：
    *   使用limma的 `eBayes` 函数对线性模型拟合得到的标准误进行经验贝叶斯收缩。它会假设不同基因的方差服从一个先验分布，并利用所有基因的信息来调整（“缓和”）每个基因的方差估计。
    *   **优点**：增加了自由度，提高了对差异表达检测的功效，尤其是在样本量较小时。计算出修正后的t统计量 (moderated t-statistic) 和相应的p值。
*   **复杂实验设计处理**：
    *   limma的线性模型框架非常灵活，可以轻松处理多因素设计、交互效应、时间序列实验、配对样本、批次效应校正等复杂情况。通过构建合适的设计矩阵 (design matrix) 和对比矩阵 (contrast matrix) 来实现。
*   **优势**：
    *   继承了limma在线性模型和处理复杂设计方面的强大能力。
    *   voom转换考虑了RNA-seq数据的均值-方差关系。
    *   通常计算速度较快。

### 6. 差异表达分析结果解读

*   **关键输出信息**：通常是一个包含每个基因信息的表格，主要列包括：
    *   `log2FoldChange` (LFC): 组间表达水平差异的log2值。正值表示上调，负值表示下调。
    *   `pvalue`: 原始的p值。
    *   `padj` / `FDR`: 经过多重检验校正后的p值。**这是判断显著性的主要依据。**
    *   `baseMean` / `logCPM`: 基因的平均表达水平。
    *   检验统计量 (如 Wald statistic, F-statistic)。
*   **筛选标准**：通常结合 `padj` 阈值和 `log2FoldChange` 阈值来筛选显著差异表达基因。例如：`padj < 0.05` 且 `abs(log2FoldChange) > 1` （表示差异至少2倍）。
*   **火山图 (Volcano Plot)**：
    *   **X轴**: `log2FoldChange`
    *   **Y轴**: `-log10(padj)` 或 `-log10(pvalue)`
    *   **解读**: 每个点代表一个基因。Y轴越高，表示差异越显著（p值越小）。X轴离0越远，表示差异倍数越大。通常在图上标出 `padj` 和 `LFC` 的阈值线，位于阈值线之外（通常是左上角和右上角）的点代表显著上调或下调的基因。可以快速概览差异基因的分布和显著性。
*   **MA 图 (MA Plot)**：
    *   **X轴**: 平均表达量 (`A = log2(baseMean)` 或 `log2(average CPM)`)
    *   **Y轴**: `log2FoldChange` (`M = log2(Fold Change)`)
    *   **解读**: 每个点代表一个基因。用于检查差异表达结果是否存在系统性偏差。理想情况下，非差异表达的基因（M接近0）应该在所有表达水平（A值）上均匀分布。差异表达的基因（M偏离0）会显示出来。可以帮助检查标准化效果和LFC收缩效果。
*   **差异表达基因聚类分析 (Clustering of DEGs)**：
    *   **方法**: 提取显著差异表达基因的表达谱（如log-transformed normalized counts 或 TPM），使用层次聚类 (Hierarchical Clustering) 或 K-means 聚类。
    *   **目的**: 观察这些基因在不同样本或条件下的表达模式，将具有相似模式的基因分组，可能揭示共调控机制或相关功能。结果常用热图 (Heatmap) 展示。
*   **结果验证策略 (Result Validation)**：
    *   **重要性**：生物信息学分析结果需要实验验证。
    *   **常用方法**：
        *   **定量 PCR (qPCR / RT-qPCR)**：最常用的验证方法。选择一部分差异表达基因（包括上调、下调、不同表达水平、重要功能基因等），以及一些非差异表达的管家基因作为内参，在相同的样本或新的生物学重复上进行qPCR实验，看其表达变化趋势是否与RNA-seq结果一致。
        *   **Western Blot / ELISA**：如果关心蛋白质水平的变化，可以检测差异基因对应蛋白质的表达量。
        *   **功能实验 (Functional Assays)**：通过基因敲除/敲低、过表达等实验，验证差异基因在相关生物学过程中的功能作用。

**本节总结**

差异表达分析是RNA-seq的核心目标之一，其统计基础在于处理离散、过度离散且具有均值-方差依赖性的计数数据。DESeq2, edgeR和limma-voom是三大主流工具，它们都基于负二项分布或其变种/转换，并采用经验贝叶斯等策略来提高统计功效和结果的可靠性。理解多重检验校正（FDR）和统计功效的重要性，以及如何解读火山图、MA图等结果，并进行后续实验验证，是完成一个严谨的差异表达分析的关键环节。

## 功能富集分析方法

### 1. 功能注释数据库 (Functional Annotation Databases)

*   **目的**：为基因或蛋白质关联已知的生物学功能、参与的通路或调控信息。是理解差异基因列表生物学意义的基础。
*   **基因本体论 (Gene Ontology, GO)**
    *   **定位**：目前最广泛使用的基因功能注释系统，旨在用标准化的词汇（GO terms）描述基因产物的功能。
    *   **结构与组织**：GO terms被组织在一个有向无环图 (Directed Acyclic Graph, DAG) 中。每个term都有一个唯一的ID、名称和定义。Terms之间存在层级关系（如 'is_a', 'part_of', 'regulates'），从非常笼统的概念（如 'metabolic process'）到非常具体的概念（如 'glucose transmembrane transport'）。
    *   **三大类别 (Three Domains/Ontologies)**：
        *   **分子功能 (Molecular Function, MF)**：描述基因产物在分子水平上的活动，如“催化活性 (catalytic activity)”、“转录因子结合 (transcription factor binding)”。
        *   **生物学过程 (Biological Process, BP)**：描述由多个分子功能组成的、更大尺度的生物学目标或事件，如“信号转导 (signal transduction)”、“细胞凋亡 (apoptosis)”、“DNA修复 (DNA repair)”。**通常是研究者最关心的部分。**
        *   **细胞组分 (Cellular Component, CC)**：描述基因产物在细胞中作用的场所，如“细胞核 (nucleus)”、“线粒体 (mitochondrion)”、“细胞质 (cytoplasm)”。
    *   **注释来源**：GO注释由数据库（如UniProt, Ensembl）和研究社区通过文献挖掘、计算预测等方式提供，并关联到具体的基因或蛋白质。

*   **KEGG 通路数据库 (Kyoto Encyclopedia of Genes and Genomes)**
    *   **定位**：一个整合了基因组、化学和系统功能信息的数据库，其核心是**KEGG PATHWAY**数据库，收录了大量人工绘制的、代表分子间相互作用和反应网络的通路图。
    *   **通路分类**：包括新陈代谢 (Metabolism)、遗传信息处理 (Genetic Information Processing)、环境信息处理 (Environmental Information Processing)、细胞过程 (Cellular Processes)、生物体系统 (Organismal Systems)、人类疾病 (Human Diseases) 以及药物开发 (Drug Development) 等类别。
    *   **通路图解读**：KEGG通路图以图形化的方式展示了基因（或蛋白质、酶）在特定通路中的位置、相互作用关系（激活、抑制、磷酸化等）以及与小分子化合物的联系。差异表达的基因可以在通路图上高亮显示，直观地看出它们可能影响了哪些生物学通路。

*   **Reactome 数据库**
    *   **定位**：一个开放获取、人工注释、同行评议的通路数据库，专注于人类生物学通路和过程。
    *   **特点**：以“反应 (reaction)”为基本单位构建通路，层级结构清晰，注释质量高，覆盖广泛的生物学事件，从基础代谢到复杂的信号转导和疾病通路。也提供通路图和分析工具。

*   **其他专业数据库/基因集集合 (Gene Set Collections)**
    *   **MsigDB (Molecular Signatures Database)**：由Broad研究所维护，收集了大量注释好的基因集，来源广泛，包括：
        *   **Hallmark gene sets**: 代表明确定义的生物状态或过程的核心基因集合。
        *   **Positional gene sets**: 染色体位置相关的基因集。
        *   **Curated gene sets**: 来自KEGG, Reactome, BioCarta等通路数据库以及文献挖掘的基因集。
        *   **Motif gene sets**: 包含特定调控基序（如转录因子结合位点、miRNA靶点）的基因集。
        *   **GO gene sets**: 基于GO注释构建的基因集。
        *   **Oncogenic signatures**: 与癌症相关的基因表达特征。
        *   **Immunologic signatures**: 与免疫相关的基因表达特征。
        *   **非常适合用于GSEA分析。**
    *   **WikiPathways**: 一个开放、协作的通路数据库平台。
    *   **BioCarta**: 另一个通路数据库（更新可能不如KEGG/Reactome频繁）。
    *   **Disease Ontology (DO)** / **DisGeNET**: 关联基因与疾病的数据库。
    *   **DrugBank**: 关联基因与药物靶点信息的数据库。

### 2. 过表示分析 (Over-Representation Analysis, ORA)

*   **也称为**：富集分析 (Enrichment Analysis) 的一种经典方法，有时直接称为GO富集分析或KEGG富集分析。
*   **核心思想**：检验一个**预先定义的基因列表**（通常是差异表达基因列表，DEG list）中，属于某个特定功能类别（如一个GO term或一个KEGG通路）的基因比例，是否**显著高于**其在背景基因列表（通常是芯片上所有检测到的基因或RNA-seq中所有表达的基因）中的比例。
*   **统计原理：基于2x2列联表 (Contingency Table)**
    *   对于某个特定的功能类别（例如，GO term "Apoptosis"）：

        |                     | Genes in Category "Apoptosis" | Genes NOT in Category "Apoptosis" | Total      |
        | :------------------ | :---------------------------- | :-------------------------------- | :--------- |
        | **In DEG list**     | `k`                           | `n - k`                           | `n`        |
        | **NOT in DEG list** | `K - k`                       | `(N - K) - (n - k)`               | `N - n`    |
        | **Total (Background)** | `K`                           | `N - K`                           | `N`        |

    *   `N`: 背景基因总数。
    *   `K`: 背景基因中属于该功能类别的基因数。
    *   `n`: 输入的差异表达基因列表中的基因总数。
    *   `k`: 输入的差异表达基因列表中，同时也属于该功能类به的基因数。
*   **检验方法**：
    *   **超几何检验 (Hypergeometric Test)**：精确计算在`N`个基因中随机抽取`n`个基因，恰好抽到`k`个或更多属于该类别（总共`K`个）的基因的概率。这是最常用的方法。
        *   P 值 = P(X ≥ k) = Σ [ C(K, x) * C(N-K, n-x) / C(N, n) ] for x from k to min(n, K)
        （其中 C(a, b) 是组合数 "a choose b"）
    *   **Fisher 精确检验 (Fisher's Exact Test)**：与超几何检验等价，也是基于上述2x2列联表计算概率。
    *   **卡方检验 (Chi-squared Test)**：当各类别的期望计数值较大时（如>5），可以用卡方检验作为近似，计算速度更快。但在基因集通常较小的情况下，精确检验更优。
*   **多重检验校正**：**必须进行！** 因为同时对成百上千个GO terms或KEGG pathways进行了检验。通常使用**FDR (如BH方法)** 进行校正。
*   **结果解读与可视化**：
    *   **关键输出**：每个功能类别的 P 值、校正后的 P 值 (FDR/q-value)、富集倍数 (Fold Enrichment = (k/n) / (K/N))、属于该类别的差异基因列表。
    *   **筛选**：通常选择 FDR < 0.05 或 0.1 的功能类别作为显著富集的结果。
    *   **可视化**：
        *   **条形图 (Bar Plot)**：展示富集最显著的功能类别，按 P 值或富集倍数排序。
        *   **点图 (Dot Plot)**：用点的大小表示该类别下的差异基因数量，点的颜色表示 P 值或富集倍数，横轴是富集倍数，纵轴是功能类别。
        *   **富集网络图/树状图**：展示富集到的GO terms之间的层级关系或相似性。

*   **优点**：简单直观，易于理解和实现。
*   **缺点**：
    *   需要预先设定一个差异基因的阈值（如 padj < 0.05），丢失了阈值边缘基因的信息。
    *   只考虑了基因是否“差异”，未考虑其差异的程度（Fold Change）和方向（上调/下调）。
    *   假设所有基因独立，忽略了基因间的相互作用。

### 3. 基因集富集分析 (Gene Set Enrichment Analysis, GSEA)

*   **定位**：一种更强大的富集分析方法，旨在克服ORA的主要缺点。由Broad研究所开发。
*   **核心思想**：不再需要设定硬性的差异基因阈值。而是检验一个**预先定义的基因集**（Gene Set，如某个GO term下的所有基因，或某个KEGG通路的所有基因）中的成员，是否**倾向于聚集**在**整个基因排序列表 (ranked list) 的顶部或底部**。这个排序列表是基于所有基因的差异表达信息（如Fold Change, t-statistic, 或 signal-to-noise ratio）生成的。
*   **基本步骤**：
    1.  **基因排序 (Rank Genes)**：计算所有基因的差异表达指标（如 signal-to-noise ratio 或直接用 LFC），并将所有基因（通常是所有检测到的基因）按此指标从高到低（或从强上调到强下调）进行排序，得到一个**排序基因列表 L**。
    2.  **计算富集分数 (Enrichment Score, ES)**：
        *   对于一个给定的基因集 S（如 "Apoptosis" 相关的基因），从列表 L 的顶端开始向下扫描。
        *   遇到属于基因集 S 的基因时，增加一个“命中得分”(hit score)，通常与该基因的排序指标相关（表达差异越大，得分越高）。
        *   遇到不属于基因集 S 的基因时，减少一个“未命中得分”(miss score)。
        *   记录下这个过程中累积得分的最大值（正向峰值或负向谷值），这个值就是该基因集 S 的（原始）富集分数 ES。
        *   **直观理解**：如果一个基因集的成员主要集中在排序列表的顶部（如强上调基因中），ES会是一个大的正值；如果集中在底部（强下调基因中），ES会是一个大的负值；如果随机分布，ES会接近零。
    3.  **评估显著性 (Assess Significance)**：
        *   **随机排列检验 (Permutation Test)**：通过多次（如1000次）随机打乱样本的表型标签（如 处理组/对照组），重新计算每个基因的差异表达指标并排序，然后为基因集 S 计算一个随机的 ES'。得到一个 ES' 的经验分布（零分布）。
        *   **计算 P 值**：将原始数据计算得到的 ES 与这个零分布进行比较，计算出 ES 比随机情况下更极端的概率，即为名义 P 值 (Nominal P-value)。
    4.  **校正与标准化 (Correction & Normalization)**：
        *   **标准化 ES (Normalized Enrichment Score, NES)**：将原始 ES 根据基因集大小和在不同随机排列中的 ES 分布进行标准化，得到 NES。NES考虑了不同基因集大小的影响，使得不同基因集之间的富集结果具有可比性。`NES > 0` 表示富集在排序列表顶部（通常对应第一个表型，如上调），`NES < 0` 表示富集在底部（通常对应第二个表型，如下调）。
        *   **计算 FDR q-value**：对所有基因集的 NES 进行多重检验校正，计算 FDR q-value。

*   **优势**：
    *   **无需设定阈值**：利用了所有基因的表达信息，对微弱但协调的表达变化更敏感。
    *   **考虑表达变化方向和大小**：通过排序指标体现。
    *   **更侧重于生物学通路/过程的整体行为**。
*   **适用场景**：当预期处理会引起某个通路中多个基因发生幅度不大但方向一致的变化时，GSEA可能比ORA更有效。
*   **结果解读**：
    *   **关键输出**：每个基因集的 NES, Nominal P-value, FDR q-value, 以及导致富集的核心基因 (Leading Edge Subset)。
    *   **筛选**：通常选择 `FDR q-value < 0.25` （GSEA官方推荐，比较宽松）或更严格的阈值（如<0.1或<0.05），并结合 NES 的绝对值大小来判断富集强度。
    *   **可视化**：GSEA软件会生成富集图 (Enrichment Plot)，展示ES曲线、基因在排序列表中的位置（hit标记）以及leading edge基因。

### 4. 功能富集分析工具

*   **基于ORA的工具**：
    *   **clusterProfiler (R Bioconductor包)**：**强烈推荐！** 功能非常强大且灵活的R包。
        *   支持 GO, KEGG, Reactome, DO, DisGeNET 等多种数据库的 ORA 分析。
        *   支持多种物种。
        *   提供丰富的可视化功能（条形图、点图、网络图、通路图高亮等）。
        *   也集成了 GSEA 功能。
    *   **DAVID (Database for Annotation, Visualization and Integrated Discovery)**：
        *   经典的**在线网页工具**。
        *   用户友好，提供基因列表输入，输出富集的 GO terms, KEGG pathways 等。
        *   提供功能聚类 (Functional Clustering) 功能。
        *   注意：数据库更新可能不及时，背景基因选择可能不灵活。
    *   **Enrichr**：
        *   另一个流行的**在线网页工具**。
        *   整合了极其丰富的基因集库（通路、转录因子靶点、药物、疾病、组织表达等）。
        *   提供多种可视化方式。
    *   **Metascape**:
        *   功能强大的在线工具，整合多种数据库，侧重于提供更深入的生物学解释和可视化，如蛋白质互作网络富集。
    *   **g:Profiler**:
        *   提供网页版和R包，支持多种生物，功能全面。
*   **基于GSEA的工具**：
    *   **GSEA 软件 (Broad Institute)**：
        *   **官方的Java桌面应用程序或命令行工具**。
        *   提供标准化的GSEA分析流程。
        *   需要下载软件和基因集文件(gmt格式)。
    *   **fgsea (R Bioconductor包)**：
        *   快速实现GSEA算法的R包，比官方Java版快很多。
    *   **clusterProfiler (R Bioconductor包)**：
        *   也提供了 `GSEA` 函数，可以直接调用，方便在R环境中完成分析。
*   **工具选择建议**：
    *   **R 用户首选**：**clusterProfiler**，因为它整合了ORA和GSEA，支持多种数据库，并且与R的生态系统无缝衔接，便于定制化分析和可视化。fgsea 可作为快速GSEA的选择。
    *   **非R用户/快速在线分析**：**DAVID**, **Enrichr**, **Metascape**, **g:Profiler** 都是不错的网页工具，各有侧重。
    *   **进行标准GSEA分析**：可以使用 **Broad GSEA 软件** 或 **fgsea/clusterProfiler** 中的GSEA功能。

### 5. 富集结果解读与可视化

*   **超越 P 值**：不能只看P值或FDR。需要结合富集倍数(ORA)、NES(GSEA)、涉及的基因数量、生物学背景知识来综合判断富集结果的重要性。
*   **处理冗余 (Handling Redundancy)**：尤其是GO分析，由于其层级结构，可能富集到很多语义相似或包含关系的terms。
    *   **方法**：
        *   **功能聚类 (Functional Clustering)**：如DAVID的工具，将相似的terms聚类。
        *   **语义相似性计算**：如clusterProfiler中的 `simplify` 函数，去除冗余的GO terms。
        *   **可视化网络图 (Enrichment Map / GO DAG)**：用网络图展示富集到的terms及其关系，相似的terms会聚集在一起，有助于识别核心功能模块。clusterProfiler提供相关功能。
*   **热图展示 (Heatmap Visualization)**：
    *   将显著富集的功能类别（如通路）作为行，样本作为列，用颜色展示该通路下核心基因的（平均）表达水平变化。可以直观看到不同通路在不同样本组中的整体激活或抑制状态。
*   **通路可视化 (Pathway Visualization)**：
    *   将差异表达基因映射到KEGG通路图上，并用不同颜色标记上调和下调。
    *   **工具**：KEGG官网的工具, Pathview (R包), clusterProfiler 内部函数。有助于理解差异基因在通路中的具体位置和潜在影响。
*   **结果整合与解释 (Integration and Interpretation)**：
    *   **综合多个数据库结果**：GO, KEGG, Reactome 等可能提供互补的信息。
    *   **结合差异倍数和方向**：不仅看哪些通路富集，还要看通路中的基因是上调还是下调为主。
    *   **关联生物学问题**：富集结果是否与实验设计、预期表型或已有文献知识一致？能否提出新的假说？
    *   **关注核心基因 (Leading Edge Genes in GSEA)**：GSEA会给出对富集分数贡献最大的基因子集，这些基因可能是驱动通路变化的关键成员，值得重点关注。

**本节总结**

功能富集分析是将基因列表（如差异表达基因）转化为生物学见解的关键步骤。理解常用的功能数据库（GO, KEGG, Reactome等）的特点，掌握ORA和GSEA两种主要富集方法的原理、优缺点和适用场景，并熟练使用至少一种分析工具（如clusterProfiler）至关重要。最后，需要结合多种可视化手段和生物学背景知识，批判性地解读富集结果，避免过度解读，并为后续实验验证提供方向。