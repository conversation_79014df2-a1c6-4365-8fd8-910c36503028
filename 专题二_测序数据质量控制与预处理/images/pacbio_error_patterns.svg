<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">PacBio平台错误模式</text>
  
  <!-- Section 1: CLR Mode Errors -->
  <rect x="50" y="80" width="700" height="220" fill="#e9ecef" rx="10" ry="10" stroke="#6c757d" stroke-width="1"/>
  <text x="400" y="110" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">CLR模式随机Indel错误</text>
  
  <!-- DNA Sequence with Random Indel Errors -->
  <rect x="100" y="130" width="600" height="150" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  
  <!-- Reference Sequence -->
  <text x="120" y="155" font-family="Courier New" font-size="16" text-anchor="start" font-weight="bold">参考序列: </text>
  <text x="230" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="250" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="270" y="155" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="290" y="155" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="310" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="330" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="350" y="155" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="370" y="155" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="390" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="410" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="430" y="155" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="450" y="155" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="470" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="490" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="510" y="155" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="530" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="550" y="155" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="570" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="590" y="155" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="610" y="155" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="630" y="155" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="650" y="155" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  
  <!-- CLR Read with Random Errors -->
  <text x="120" y="185" font-family="Courier New" font-size="16" text-anchor="start" font-weight="bold">CLR读段: </text>
  <text x="230" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="250" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="270" y="185" font-family="Courier New" font-size="16" text-anchor="start" fill="#dc3545">-</text>
  <text x="290" y="185" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="310" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="330" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="350" y="185" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="370" y="185" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="390" y="185" font-family="Courier New" font-size="16" text-anchor="start" fill="#dc3545">C</text>
  <text x="410" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="430" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="450" y="185" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="470" y="185" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="490" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="510" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="530" y="185" font-family="Courier New" font-size="16" text-anchor="start">G</text>
  <text x="550" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="570" y="185" font-family="Courier New" font-size="16" text-anchor="start">C</text>
  <text x="590" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  <text x="610" y="185" font-family="Courier New" font-size="16" text-anchor="start">A</text>
  <text x="630" y="185" font-family="Courier New" font-size="16" text-anchor="start" fill="#dc3545">-</text>
  <text x="650" y="185" font-family="Courier New" font-size="16" text-anchor="start">T</text>
  
  <!-- Error Labels -->
  <text x="270" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#dc3545">缺失</text>
  <text x="390" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#dc3545">插入</text>
  <text x="630" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#dc3545">缺失</text>
  
  <!-- Error Rate Info -->
  <text x="400" y="235" font-family="Arial" font-size="16" text-anchor="middle">CLR模式错误率: ~10-15% (主要为随机分布的插入和缺失错误)</text>
  <text x="400" y="260" font-family="Arial" font-size="14" text-anchor="middle">错误分布无明显位置或序列偏好性</text>
  
  <!-- Section 2: CCS/HiFi Mode Error Reduction -->
  <rect x="50" y="320" width="700" height="260" fill="#e9ecef" rx="10" ry="10" stroke="#6c757d" stroke-width="1"/>
  <text x="400" y="350" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">CCS/HiFi模式错误率降低原理</text>
  
  <!-- Circular Template Illustration -->
  <ellipse cx="200" cy="450" rx="100" ry="60" fill="#ffffff" stroke="#6c757d" stroke-width="2" fill-opacity="0.7"/>
  <text x="200" y="450" font-family="Arial" font-size="16" text-anchor="middle">环形模板</text>
  
  <!-- Multiple Pass Arrows -->
  <path d="M 200 390 C 250 370, 300 370, 300 450 C 300 530, 250 530, 200 510" 
        stroke="#0d6efd" stroke-width="3" fill="none" marker-end="url(#arrowhead1)"/>
  <text x="300" y="450" font-family="Arial" font-size="14" text-anchor="middle" fill="#0d6efd">第1次通过</text>
  
  <path d="M 200 510 C 150 530, 100 530, 100 450 C 100 370, 150 370, 200 390" 
        stroke="#198754" stroke-width="3" fill="none" marker-end="url(#arrowhead2)"/>
  <text x="100" y="450" font-family="Arial" font-size="14" text-anchor="middle" fill="#198754">第2次通过</text>
  
  <path d="M 230 390 C 280 380, 320 390, 320 430 C 320 470, 280 480, 230 470" 
        stroke="#dc3545" stroke-width="3" fill="none" marker-end="url(#arrowhead3)"/>
  <text x="320" y="430" font-family="Arial" font-size="14" text-anchor="middle" fill="#dc3545">第3次通过</text>
  
  <path d="M 170 510 C 120 500, 80 490, 80 430 C 80 370, 120 380, 170 390" 
        stroke="#ffc107" stroke-width="3" fill="none" marker-end="url(#arrowhead4)"/>
  <text x="80" y="430" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffc107">第N次通过</text>
  
  <!-- Consensus Generation -->
  <rect x="400" y="400" width="300" height="120" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="550" y="420" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">一致性序列生成</text>
  
  <!-- Consensus Sequence Example -->
  <text x="420" y="450" font-family="Courier New" font-size="14" text-anchor="start">Pass 1: A T G C A T G C T A G C</text>
  <text x="420" y="470" font-family="Courier New" font-size="14" text-anchor="start">Pass 2: A T - C A T G C C A G C</text>
  <text x="420" y="490" font-family="Courier New" font-size="14" text-anchor="start">Pass 3: A T G C A T G C T A - C</text>
  <text x="420" y="510" font-family="Courier New" font-size="14" text-anchor="start" font-weight="bold">共识: A T G C A T G C T A G C</text>
  
  <!-- Error Rate Comparison -->
  <rect x="100" y="530" width="600" height="30" fill="#ffffff" rx="5" ry="5" stroke="#6c757d" stroke-width="1"/>
  <text x="400" y="550" font-family="Arial" font-size="16" text-anchor="middle">CCS/HiFi模式错误率: ~0.1-1% (多次通过消除随机错误)</text>
  
  <!-- Arrow definitions -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0d6efd"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#198754"/>
    </marker>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#dc3545"/>
    </marker>
    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ffc107"/>
    </marker>
  </defs>
</svg>
