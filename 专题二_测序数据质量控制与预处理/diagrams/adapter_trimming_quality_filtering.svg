<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <text x="400" y="30" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">接头去除和低质量序列过滤原理</text>
  
  <!-- 接头去除部分 -->
  <g transform="translate(50, 70)">
    <rect width="700" height="240" rx="10" fill="#e6f2ff" stroke="#0066cc" stroke-width="2" />
    <text x="350" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">接头去除原理</text>
    
    <!-- 原始序列 -->
    <text x="20" y="60" font-family="Arial" font-size="14" font-weight="bold">原始序列:</text>
    
    <!-- 5'端接头 -->
    <rect x="150" y="50" width="100" height="25" fill="#ff9999" stroke="#cc0000" />
    <text x="200" y="67" font-family="Arial" font-size="12" text-anchor="middle">5'端接头</text>
    
    <!-- 目标序列 -->
    <rect x="250" y="50" width="200" height="25" fill="#99ccff" stroke="#0066cc" />
    <text x="350" y="67" font-family="Arial" font-size="12" text-anchor="middle">目标序列</text>
    
    <!-- 3'端接头 -->
    <rect x="450" y="50" width="100" height="25" fill="#ff9999" stroke="#cc0000" />
    <text x="500" y="67" font-family="Arial" font-size="12" text-anchor="middle">3'端接头</text>
    
    <!-- 接头识别算法 -->
    <text x="20" y="100" font-family="Arial" font-size="14" font-weight="bold">接头识别算法:</text>
    
    <!-- 精确匹配 -->
    <rect x="150" y="90" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
    <text x="225" y="110" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">精确匹配法</text>
    <text x="225" y="130" font-family="Arial" font-size="10" text-anchor="middle">寻找与已知接头序列</text>
    <text x="225" y="145" font-family="Arial" font-size="10" text-anchor="middle">完全匹配的序列</text>
    
    <!-- 模糊匹配 -->
    <rect x="320" y="90" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
    <text x="395" y="110" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">模糊匹配法</text>
    <text x="395" y="130" font-family="Arial" font-size="10" text-anchor="middle">容忍少量错误的匹配</text>
    <text x="395" y="145" font-family="Arial" font-size="10" text-anchor="middle">(Smith-Waterman算法)</text>
    
    <!-- 局部比对 -->
    <rect x="490" y="90" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
    <text x="565" y="110" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">局部比对算法</text>
    <text x="565" y="130" font-family="Arial" font-size="10" text-anchor="middle">寻找局部匹配的序列</text>
    <text x="565" y="145" font-family="Arial" font-size="10" text-anchor="middle">适用于嵌合体序列</text>
    
    <!-- 接头去除策略 -->
    <text x="20" y="180" font-family="Arial" font-size="14" font-weight="bold">接头去除策略:</text>
    
    <!-- 5'端接头去除 -->
    <g transform="translate(150, 170)">
      <rect x="0" y="0" width="150" height="50" rx="5" fill="#fff2e6" stroke="#ff8c1a" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">5'端接头去除</text>
      
      <rect x="10" y="30" width="30" height="10" fill="#ff9999" />
      <rect x="40" y="30" width="60" height="10" fill="#99ccff" />
      <path d="M10,25 L40,25" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
      <path d="M10,45 L40,45" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
    </g>
    
    <!-- 3'端接头去除 -->
    <g transform="translate(320, 170)">
      <rect x="0" y="0" width="150" height="50" rx="5" fill="#fff2e6" stroke="#ff8c1a" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">3'端接头去除</text>
      
      <rect x="50" y="30" width="60" height="10" fill="#99ccff" />
      <rect x="110" y="30" width="30" height="10" fill="#ff9999" />
      <path d="M110,25 L140,25" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
      <path d="M110,45 L140,45" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
    </g>
    
    <!-- 内部接头识别 -->
    <g transform="translate(490, 170)">
      <rect x="0" y="0" width="150" height="50" rx="5" fill="#fff2e6" stroke="#ff8c1a" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">内部接头识别</text>
      
      <rect x="20" y="30" width="30" height="10" fill="#99ccff" />
      <rect x="50" y="30" width="30" height="10" fill="#ff9999" />
      <rect x="80" y="30" width="30" height="10" fill="#99ccff" />
      <path d="M50,25 L80,25" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
      <path d="M50,45 L80,45" stroke="#cc0000" stroke-width="1" stroke-dasharray="2,2" />
    </g>
  </g>
  
  <!-- 低质量序列过滤部分 -->
  <g transform="translate(50, 330)">
    <rect width="700" height="240" rx="10" fill="#ffe6e6" stroke="#cc0000" stroke-width="2" />
    <text x="350" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">低质量序列过滤原理</text>
    
    <!-- 原始序列 -->
    <text x="20" y="60" font-family="Arial" font-size="14" font-weight="bold">原始序列质量:</text>
    
    <!-- 序列质量示意 -->
    <g transform="translate(150, 50)">
      <rect x="0" y="0" width="400" height="25" fill="#f2f2f2" stroke="#666666" />
      
      <!-- 高质量区域 -->
      <rect x="0" y="0" width="200" height="25" fill="#99ff99" stroke="none" />
      <text x="100" y="17" font-family="Arial" font-size="12" text-anchor="middle">高质量区域 (Q30+)</text>
      
      <!-- 中等质量区域 -->
      <rect x="200" y="0" width="100" height="25" fill="#ffff99" stroke="none" />
      <text x="250" y="17" font-family="Arial" font-size="12" text-anchor="middle">中等 (Q20)</text>
      
      <!-- 低质量区域 -->
      <rect x="300" y="0" width="100" height="25" fill="#ff9999" stroke="none" />
      <text x="350" y="17" font-family="Arial" font-size="12" text-anchor="middle">低质量 (<Q10)</text>
    </g>
    
    <!-- 过滤算法 -->
    <text x="20" y="110" font-family="Arial" font-size="14" font-weight="bold">过滤算法:</text>
    
    <!-- 硬截断法 -->
    <g transform="translate(150, 90)">
      <rect x="0" y="0" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">硬截断法</text>
      
      <rect x="10" y="30" width="130" height="10" fill="#f2f2f2" />
      <rect x="10" y="30" width="90" height="10" fill="#99ff99" />
      <rect x="100" y="30" width="40" height="10" fill="#ff9999" />
      <path d="M100,25 L100,45" stroke="#000" stroke-width="1" />
      <text x="75" y="50" font-family="Arial" font-size="10" text-anchor="middle">设定质量阈值，去除低于阈值的碱基</text>
    </g>
    
    <!-- 滑动窗口法 -->
    <g transform="translate(320, 90)">
      <rect x="0" y="0" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">滑动窗口法</text>
      
      <rect x="10" y="30" width="130" height="10" fill="#f2f2f2" />
      <rect x="10" y="30" width="90" height="10" fill="#99ff99" />
      <rect x="100" y="30" width="40" height="10" fill="#ff9999" />
      
      <!-- 窗口 -->
      <rect x="70" y="25" width="40" height="20" fill="none" stroke="#0066cc" stroke-width="1" stroke-dasharray="2,2" />
      <text x="75" y="50" font-family="Arial" font-size="10" text-anchor="middle">计算窗口内平均质量，动态调整</text>
    </g>
    
    <!-- 动态质量阈值法 -->
    <g transform="translate(490, 90)">
      <rect x="0" y="0" width="150" height="60" rx="5" fill="#e6ffe6" stroke="#009900" />
      <text x="75" y="20" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">动态质量阈值法</text>
      
      <rect x="10" y="30" width="130" height="10" fill="#f2f2f2" />
      <rect x="10" y="30" width="90" height="10" fill="#99ff99" />
      <rect x="100" y="30" width="40" height="10" fill="#ff9999" />
      
      <!-- 动态阈值线 -->
      <path d="M10,35 Q50,25 90,35 Q110,40 140,30" stroke="#0066cc" stroke-width="1" stroke-dasharray="2,2" fill="none" />
      <text x="75" y="50" font-family="Arial" font-size="10" text-anchor="middle">根据整体质量动态调整阈值</text>
    </g>
    
    <!-- 过滤结果 -->
    <text x="20" y="180" font-family="Arial" font-size="14" font-weight="bold">过滤结果:</text>
    
    <!-- 过滤前 -->
    <g transform="translate(150, 170)">
      <text x="0" y="10" font-family="Arial" font-size="12" font-weight="bold">过滤前:</text>
      <rect x="60" y="0" width="400" height="20" fill="#f2f2f2" stroke="#666666" />
      
      <!-- 高质量区域 -->
      <rect x="60" y="0" width="200" height="20" fill="#99ff99" stroke="none" />
      
      <!-- 中等质量区域 -->
      <rect x="260" y="0" width="100" height="20" fill="#ffff99" stroke="none" />
      
      <!-- 低质量区域 -->
      <rect x="360" y="0" width="100" height="20" fill="#ff9999" stroke="none" />
    </g>
    
    <!-- 过滤后 -->
    <g transform="translate(150, 200)">
      <text x="0" y="10" font-family="Arial" font-size="12" font-weight="bold">过滤后:</text>
      <rect x="60" y="0" width="300" height="20" fill="#f2f2f2" stroke="#666666" />
      
      <!-- 高质量区域 -->
      <rect x="60" y="0" width="200" height="20" fill="#99ff99" stroke="none" />
      
      <!-- 中等质量区域 -->
      <rect x="260" y="0" width="100" height="20" fill="#ffff99" stroke="none" />
    </g>
  </g>
  
  <!-- 底部说明 -->
  <text x="400" y="580" font-family="Arial" font-size="14" text-anchor="middle" font-style="italic">接头去除和低质量序列过滤是测序数据预处理的关键步骤</text>
</svg>