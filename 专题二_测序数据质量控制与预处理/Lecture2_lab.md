# 专题二：测序数据质量控制与预处理 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握FastQC工具的使用和质量报告解读
2. 学会使用Trimmomatic、Cutadapt等工具进行数据过滤
3. 理解测序数据质量控制的原理和最佳实践
4. 建立完整的数据预处理自动化流程

## 实验环境准备

### 软件安装验证
```bash
# 激活分析环境
conda activate ngs_analysis

# 验证软件安装
fastqc --version          # 应显示 FastQC v0.x.x
trimmomatic -version      # 应显示 0.39
cutadapt --version        # 应显示版本信息
multiqc --version         # 应显示版本信息

# 如果未安装，执行以下命令
conda install -c bioconda fastqc trimmomatic cutadapt multiqc -y
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p qc_practice/{data/{raw,processed},results/{fastqc,trimmed,reports},scripts}
cd qc_practice

# 下载示例数据（如果网络允许）
# wget ftp://ftp.sra.ebi.ac.uk/vol1/fastq/SRR000/SRR000001/SRR000001.fastq.gz
# 或创建模拟数据用于练习

# 创建模拟FASTQ数据
cat > data/raw/sample1_R1.fastq << 'EOF'
@SRR000001.1 length=36
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+SRR000001.1 length=36
!''*((((***++""%%''++"++)()...+()''*(((***+"))""*(((***++""""*))(***++""
@SRR000001.2 length=36
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+SRR000001.2 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII###
@SRR000001.3 length=36
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+SRR000001.3 length=36
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
@SRR000001.4 length=36
AGATCGGAAGAGCACACGTCTGAACTCCAGTCACNNNNNNATCTCGTATGCCGTCTTCTGCTTGAAAAA
+SRR000001.4 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII!!!IIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR000001.5 length=36
GATCGGAAGAGCACACGTCTGAACTCCAGTCACNNNNNNATCTCGTATGCCGTCTTCTGCTTGAAAAAA
+SRR000001.5 length=36
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ!!!JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

# 创建配对的R2文件
cat > data/raw/sample1_R2.fastq << 'EOF'
@SRR000001.1 length=36
CATCATCATCATCATCATCATCATCATCATTATTTACGATATGCTGTTTGAACCCCAAATCGATTTGGGGTT
+SRR000001.1 length=36
!''*((((***++""%%''++"++)()...+()''*(((***+"))""*(((***++""""*))(***++""
@SRR000001.2 length=36
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+SRR000001.2 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII###
@SRR000001.3 length=36
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+SRR000001.3 length=36
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
@SRR000001.4 length=36
TTTTTTCAAGCAGAACGGCATACGAGATNNNNNGTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
+SRR000001.4 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIII!!!IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR000001.5 length=36
TTTTTTTCAAGCAGAACGGCATACGAGATNNNNNGTGACTGGAGTTCAGACGTGTGCTCTTCCGATC
+SRR000001.5 length=36
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ!!!JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

# 压缩文件（模拟真实数据）
gzip data/raw/sample1_R*.fastq

echo "实验数据准备完成！"
```

---

## 第一部分：FastQC工具使用与质量报告解读

### 1.1 FastQC基础操作

#### 1.1.1 单文件质量控制分析

**实验目标：** 学会使用FastQC进行单个FASTQ文件的质量分析

```bash
# 1. 基本FastQC分析
echo "=== 执行FastQC质量控制分析 ==="
fastqc data/raw/sample1_R1.fastq.gz -o results/fastqc/

# 检查输出文件
ls -la results/fastqc/
# 应该看到：
# sample1_R1_fastqc.html  - HTML报告文件
# sample1_R1_fastqc.zip   - 压缩的详细结果

# 2. 带参数的FastQC分析
fastqc data/raw/sample1_R1.fastq.gz \
    -o results/fastqc/ \
    -t 2 \
    --extract \
    --nogroup

# 参数说明：
# -o: 指定输出目录
# -t: 指定线程数
# --extract: 自动解压结果文件
# --nogroup: 不将碱基位置分组

echo "FastQC分析完成，请查看results/fastqc/目录"
```

#### 1.1.2 批量处理多个文件

```bash
# 1. 简单批量处理
echo "=== 批量处理多个FASTQ文件 ==="
fastqc data/raw/*.fastq.gz -o results/fastqc/ -t 4

# 2. 使用循环进行更精确的控制
for file in data/raw/*.fastq.gz; do
    echo "正在处理: $file"
    filename=$(basename "$file" .fastq.gz)
    
    fastqc "$file" \
        -o results/fastqc/ \
        -t 2 \
        --extract
    
    if [ $? -eq 0 ]; then
        echo "✓ $filename 分析成功"
    else
        echo "✗ $filename 分析失败"
    fi
done

# 3. 并行处理（如果数据量大）
echo "=== 并行处理示例 ==="
find data/raw/ -name "*.fastq.gz" | \
parallel -j 4 fastqc {} -o results/fastqc/ -t 1

echo "批量处理完成！"
```

### 1.2 FastQC质量报告深度解读

#### 1.2.1 创建报告解读脚本

```bash
# 创建FastQC报告解读脚本
cat > scripts/parse_fastqc.py << 'EOF'
#!/usr/bin/env python3
"""
FastQC报告解读脚本
解析FastQC输出文件，提取关键质量指标
"""

import os
import re
import sys
from pathlib import Path

def parse_fastqc_data(fastqc_dir):
    """解析FastQC数据文件"""
    data_file = Path(fastqc_dir) / "fastqc_data.txt"
    
    if not data_file.exists():
        print(f"错误: 找不到文件 {data_file}")
        return None
    
    results = {}
    current_module = None
    
    with open(data_file, 'r') as f:
        for line in f:
            line = line.strip()
            
            # 基本统计信息
            if line.startswith("Filename"):
                results['filename'] = line.split('\t')[1]
            elif line.startswith("Total Sequences"):
                results['total_sequences'] = int(line.split('\t')[1])
            elif line.startswith("Sequence length"):
                results['sequence_length'] = line.split('\t')[1]
            elif line.startswith("%GC"):
                results['gc_content'] = int(line.split('\t')[1])
            
            # 模块信息
            elif line.startswith(">>") and not line.startswith(">>END"):
                parts = line.split('\t')
                module_name = parts[0][2:]
                status = parts[1] if len(parts) > 1 else "UNKNOWN"
                
                if 'modules' not in results:
                    results['modules'] = {}
                results['modules'][module_name] = status
                current_module = module_name
    
    return results

def analyze_quality_scores(fastqc_dir):
    """分析质量分数分布"""
    data_file = Path(fastqc_dir) / "fastqc_data.txt"
    
    quality_scores = []
    in_quality_section = False
    
    with open(data_file, 'r') as f:
        for line in f:
            line = line.strip()
            
            if line == ">>Per base sequence quality":
                in_quality_section = True
                continue
            elif line.startswith(">>END_MODULE"):
                in_quality_section = False
                continue
            elif in_quality_section and line and not line.startswith("#"):
                parts = line.split('\t')
                if len(parts) >= 2:
                    position = parts[0]
                    mean_quality = float(parts[1])
                    quality_scores.append((position, mean_quality))
    
    return quality_scores

def generate_report(sample_name, results, quality_scores):
    """生成质量报告"""
    print(f"\n=== FastQC质量报告: {sample_name} ===")
    print(f"文件名: {results.get('filename', 'N/A')}")
    print(f"总序列数: {results.get('total_sequences', 'N/A'):,}")
    print(f"序列长度: {results.get('sequence_length', 'N/A')}")
    print(f"GC含量: {results.get('gc_content', 'N/A')}%")
    
    # 模块状态统计
    if 'modules' in results:
        print(f"\n模块状态统计:")
        status_count = {'PASS': 0, 'WARN': 0, 'FAIL': 0}
        
        for module, status in results['modules'].items():
            if status in status_count:
                status_count[status] += 1
            print(f"  {module}: {status}")
        
        print(f"\n状态汇总:")
        print(f"  通过 (PASS): {status_count['PASS']}")
        print(f"  警告 (WARN): {status_count['WARN']}")
        print(f"  失败 (FAIL): {status_count['FAIL']}")
    
    # 质量分数分析
    if quality_scores:
        print(f"\n质量分数分析:")
        avg_quality = sum(score[1] for score in quality_scores) / len(quality_scores)
        min_quality = min(score[1] for score in quality_scores)
        
        print(f"  平均质量分数: {avg_quality:.2f}")
        print(f"  最低质量分数: {min_quality:.2f}")
        
        # 识别低质量区域
        low_quality_positions = [pos for pos, qual in quality_scores if qual < 20]
        if low_quality_positions:
            print(f"  低质量区域 (Q<20): {len(low_quality_positions)} 个位置")
            print(f"  低质量位置: {', '.join(low_quality_positions[:10])}" + 
                  ("..." if len(low_quality_positions) > 10 else ""))
    
    # 质量建议
    print(f"\n质量评估建议:")
    if 'modules' in results:
        fail_modules = [m for m, s in results['modules'].items() if s == 'FAIL']
        warn_modules = [m for m, s in results['modules'].items() if s == 'WARN']
        
        if fail_modules:
            print(f"  ❌ 严重问题: {', '.join(fail_modules)}")
            if 'Adapter Content' in fail_modules:
                print(f"     建议: 使用Trimmomatic或Cutadapt去除接头序列")
            if 'Per base sequence quality' in fail_modules:
                print(f"     建议: 使用质量过滤，建议SLIDINGWINDOW:4:20")
        
        if warn_modules:
            print(f"  ⚠️  需要注意: {', '.join(warn_modules)}")
        
        if not fail_modules and not warn_modules:
            print(f"  ✅ 数据质量良好，可以直接用于下游分析")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python parse_fastqc.py <fastqc_output_directory>")
        sys.exit(1)
    
    fastqc_dir = sys.argv[1]
    sample_name = os.path.basename(fastqc_dir).replace('_fastqc', '')
    
    results = parse_fastqc_data(fastqc_dir)
    if results:
        quality_scores = analyze_quality_scores(fastqc_dir)
        generate_report(sample_name, results, quality_scores)
EOF

chmod +x scripts/parse_fastqc.py

# 运行报告解读
echo "=== 运行FastQC报告解读 ==="
for dir in results/fastqc/*_fastqc/; do
    if [ -d "$dir" ]; then
        python3 scripts/parse_fastqc.py "$dir"
    fi
done
```

#### 1.2.2 质量指标详细分析

```bash
# 创建质量指标提取脚本
cat > scripts/extract_quality_metrics.sh << 'EOF'
#!/bin/bash
# 从FastQC结果中提取关键质量指标

echo "=== 提取FastQC质量指标 ==="
echo -e "样本名\t总序列数\t序列长度\tGC含量\t通过模块数\t警告模块数\t失败模块数"

for fastqc_dir in results/fastqc/*_fastqc/; do
    if [ -d "$fastqc_dir" ]; then
        data_file="$fastqc_dir/fastqc_data.txt"
        sample_name=$(basename "$fastqc_dir" _fastqc)
        
        if [ -f "$data_file" ]; then
            # 提取基本信息
            total_seq=$(grep "Total Sequences" "$data_file" | cut -f2)
            seq_length=$(grep "Sequence length" "$data_file" | cut -f2)
            gc_content=$(grep "%GC" "$data_file" | cut -f2)
            
            # 统计模块状态
            pass_count=$(grep -E "^>>.*\tPASS" "$data_file" | wc -l)
            warn_count=$(grep -E "^>>.*\tWARN" "$data_file" | wc -l)
            fail_count=$(grep -E "^>>.*\tFAIL" "$data_file" | wc -l)
            
            echo -e "$sample_name\t$total_seq\t$seq_length\t$gc_content\t$pass_count\t$warn_count\t$fail_count"
        fi
    fi
done
EOF

chmod +x scripts/extract_quality_metrics.sh
./scripts/extract_quality_metrics.sh > results/quality_summary.txt

echo "质量指标汇总保存到: results/quality_summary.txt"
cat results/quality_summary.txt
```

---

## 第二部分：Trimmomatic/Cutadapt数据过滤实践

### 2.1 Trimmomatic实际操作

#### 2.1.1 单端数据处理

```bash
# 准备Illumina通用接头文件
mkdir -p adapters
cat > adapters/TruSeq3-SE.fa << 'EOF'
>TruSeq3_IndexedAdapter
AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC
>TruSeq3_UniversalAdapter
AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA
EOF

# 单端数据过滤示例
echo "=== Trimmomatic单端数据处理 ==="

# 解压文件用于演示
gunzip -c data/raw/sample1_R1.fastq.gz > data/raw/sample1_R1_temp.fastq

trimmomatic SE \
    data/raw/sample1_R1_temp.fastq \
    data/processed/sample1_R1_trimmed.fastq \
    ILLUMINACLIP:adapters/TruSeq3-SE.fa:2:30:10 \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:15 \
    MINLEN:36 \
    -phred33 \
    -trimlog data/processed/sample1_trim.log

# 参数详解：
echo "=== Trimmomatic参数详解 ==="
cat << 'EOF'
ILLUMINACLIP:adapters/TruSeq3-SE.fa:2:30:10
- 接头文件路径
- 2: 最大mismatch数
- 30: palindrome clip threshold
- 10: simple clip threshold

LEADING:3
- 去除5'端质量值低于3的碱基

TRAILING:3  
- 去除3'端质量值低于3的碱基

SLIDINGWINDOW:4:15
- 滑动窗口大小4bp
- 平均质量值阈值15

MINLEN:36
- 最小序列长度36bp

-phred33
- 质量值编码格式
EOF

# 检查处理结果
echo "=== 处理结果统计 ==="
echo "原始序列数: $(grep -c '^@' data/raw/sample1_R1_temp.fastq)"
echo "过滤后序列数: $(grep -c '^@' data/processed/sample1_R1_trimmed.fastq)"

# 清理临时文件
rm data/raw/sample1_R1_temp.fastq
```

#### 2.1.2 双端数据处理详解

```bash
echo "=== Trimmomatic双端数据处理 ==="

# 准备双端接头文件
cat > adapters/TruSeq3-PE.fa << 'EOF'
>PrefixPE/1
TACACTCTTTCCCTACACGACGCTCTTCCGATCT
>PrefixPE/2
GTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
>PE1
TACACTCTTTCCCTACACGACGCTCTTCCGATCT
>PE1_rc
AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA
>PE2
GTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
>PE2_rc
AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC
EOF

# 解压双端数据
gunzip -c data/raw/sample1_R1.fastq.gz > data/raw/sample1_R1_temp.fastq
gunzip -c data/raw/sample1_R2.fastq.gz > data/raw/sample1_R2_temp.fastq

# 执行双端数据过滤
trimmomatic PE \
    data/raw/sample1_R1_temp.fastq data/raw/sample1_R2_temp.fastq \
    data/processed/sample1_R1_paired.fastq data/processed/sample1_R1_unpaired.fastq \
    data/processed/sample1_R2_paired.fastq data/processed/sample1_R2_unpaired.fastq \
    ILLUMINACLIP:adapters/TruSeq3-PE.fa:2:30:10:2:keepBothReads \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:36 \
    -threads 4 \
    -trimlog data/processed/sample1_PE_trim.log \
    -summary data/processed/sample1_PE_summary.txt

# 分析处理结果
echo "=== 双端数据处理结果 ==="
echo "原始R1序列数: $(grep -c '^@' data/raw/sample1_R1_temp.fastq)"
echo "原始R2序列数: $(grep -c '^@' data/raw/sample1_R2_temp.fastq)"
echo "配对保留R1: $(grep -c '^@' data/processed/sample1_R1_paired.fastq)"
echo "配对保留R2: $(grep -c '^@' data/processed/sample1_R2_paired.fastq)"
echo "未配对R1: $(grep -c '^@' data/processed/sample1_R1_unpaired.fastq)"
echo "未配对R2: $(grep -c '^@' data/processed/sample1_R2_unpaired.fastq)"

# 显示汇总信息
if [ -f "data/processed/sample1_PE_summary.txt" ]; then
    echo -e "\n处理汇总信息:"
    cat data/processed/sample1_PE_summary.txt
fi

# 清理临时文件
rm data/raw/sample1_R*_temp.fastq
```

### 2.2 Cutadapt实际操作

#### 2.2.1 接头去除实践

```bash
echo "=== Cutadapt接头去除实践 ==="

# 重新解压文件用于cutadapt演示
gunzip -c data/raw/sample1_R1.fastq.gz > data/raw/sample1_R1_temp.fastq

# 单端接头去除
cutadapt \
    -a AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC \
    -q 20 \
    -m 30 \
    --trim-n \
    -o data/processed/sample1_R1_cutadapt.fastq \
    data/raw/sample1_R1_temp.fastq \
    > data/processed/cutadapt_report.txt

# 参数说明：
cat << 'EOF'
=== Cutadapt参数详解 ===
-a: 3'端接头序列
-q: 质量值修剪阈值
-m: 最小序列长度
--trim-n: 去除N碱基
-o: 输出文件
EOF

# 显示处理报告
echo "=== Cutadapt处理报告 ==="
cat data/processed/cutadapt_report.txt

# 双端数据处理
echo -e "\n=== Cutadapt双端数据处理 ==="
gunzip -c data/raw/sample1_R2.fastq.gz > data/raw/sample1_R2_temp.fastq

cutadapt \
    -a AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC \
    -A AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA \
    -q 20,20 \
    -m 30 \
    --trim-n \
    -o data/processed/sample1_R1_cutadapt_PE.fastq \
    -p data/processed/sample1_R2_cutadapt_PE.fastq \
    data/raw/sample1_R1_temp.fastq \
    data/raw/sample1_R2_temp.fastq \
    > data/processed/cutadapt_PE_report.txt

echo "双端处理报告:"
cat data/processed/cutadapt_PE_report.txt

# 清理临时文件
rm data/raw/sample1_R*_temp.fastq
```

### 2.3 质量过滤效果对比

```bash
# 创建质量过滤效果对比脚本
cat > scripts/compare_filtering_results.py << 'EOF'
#!/usr/bin/env python3
"""
比较不同过滤工具的效果
"""

def count_sequences(fastq_file):
    """统计FASTQ文件中的序列数量"""
    try:
        with open(fastq_file, 'r') as f:
            count = 0
            for line in f:
                if line.startswith('@'):
                    count += 1
            return count
    except FileNotFoundError:
        return 0

def calculate_average_length(fastq_file):
    """计算平均序列长度"""
    try:
        total_length = 0
        count = 0
        with open(fastq_file, 'r') as f:
            lines = f.readlines()
            for i in range(1, len(lines), 4):  # 序列行
                total_length += len(lines[i].strip())
                count += 1
        return total_length / count if count > 0 else 0
    except FileNotFoundError:
        return 0

def calculate_gc_content(fastq_file):
    """计算GC含量"""
    try:
        gc_count = 0
        total_bases = 0
        with open(fastq_file, 'r') as f:
            lines = f.readlines()
            for i in range(1, len(lines), 4):  # 序列行
                seq = lines[i].strip()
                gc_count += seq.count('G') + seq.count('C')
                total_bases += len(seq)
        return (gc_count / total_bases * 100) if total_bases > 0 else 0
    except FileNotFoundError:
        return 0

# 分析文件列表
files_to_analyze = [
    ("原始R1", "data/raw/sample1_R1.fastq.gz"),
    ("Trimmomatic-SE", "data/processed/sample1_R1_trimmed.fastq"),
    ("Trimmomatic-PE-R1", "data/processed/sample1_R1_paired.fastq"),
    ("Cutadapt-SE", "data/processed/sample1_R1_cutadapt.fastq"),
    ("Cutadapt-PE-R1", "data/processed/sample1_R1_cutadapt_PE.fastq")
]

print("=== 质量过滤效果对比 ===")
print(f"{'文件类型':<20} {'序列数':<10} {'平均长度':<10} {'GC含量%':<10} {'保留率%':<10}")
print("-" * 70)

original_count = None

for file_type, file_path in files_to_analyze:
    if file_path.endswith('.gz'):
        import gzip
        # 处理压缩文件
        try:
            with gzip.open(file_path, 'rt') as f:
                count = sum(1 for line in f if line.startswith('@'))
                
            # 重新打开计算其他统计信息
            with gzip.open(file_path, 'rt') as f:
                lines = f.readlines()
                total_length = sum(len(lines[i].strip()) for i in range(1, len(lines), 4))
                avg_length = total_length / count if count > 0 else 0
                
                gc_count = sum(lines[i].strip().count('G') + lines[i].strip().count('C') 
                              for i in range(1, len(lines), 4))
                gc_content = (gc_count / total_length * 100) if total_length > 0 else 0
        except:
            count = avg_length = gc_content = 0
    else:
        count = count_sequences(file_path)
        avg_length = calculate_average_length(file_path)
        gc_content = calculate_gc_content(file_path)
    
    if original_count is None:
        original_count = count
    
    retention_rate = (count / original_count * 100) if original_count > 0 else 0
    
    print(f"{file_type:<20} {count:<10} {avg_length:<10.1f} {gc_content:<10.1f} {retention_rate:<10.1f}")

print("\n=== 结论和建议 ===")
print("1. 比较不同工具的序列保留率")
print("2. 观察平均序列长度的变化")
print("3. 检查GC含量是否有显著变化")
print("4. 根据下游分析需求选择合适的过滤策略")
EOF

python3 scripts/compare_filtering_results.py
```

---

## 第三部分：MultiQC结果汇总与可视化

### 3.1 MultiQC基础使用

```bash
echo "=== MultiQC结果汇总 ==="

# 首先对过滤后的数据运行FastQC
echo "对过滤后数据运行FastQC..."
fastqc data/processed/*.fastq -o results/fastqc/ -t 4

# 运行MultiQC汇总所有结果
multiqc results/fastqc/ -o results/reports/ --title "QC Practice Report" --filename "qc_summary_report"

echo "MultiQC报告生成完成: results/reports/qc_summary_report.html"

# 检查输出文件
ls -la results/reports/
```

### 3.2 自定义MultiQC配置

```bash
# 创建MultiQC配置文件
cat > multiqc_config.yaml << 'EOF'
title: "NGS数据质量控制报告"
subtitle: "专题二实践课程"
intro_text: "本报告汇总了FastQC和数据过滤工具的结果"

report_header_info:
    - Contact E-mail: '<EMAIL>'
    - Application Type: 'NGS Quality Control'
    - Project Type: 'Training Exercise'

# 自定义样本名称
sample_names_rename:
    - ['_R1', ' R1']
    - ['_R2', ' R2']
    - ['_trimmed', ' (Trimmed)']
    - ['_cutadapt', ' (Cutadapt)']

# 模块顺序
module_order:
    - fastqc
    - trimmomatic
    - cutadapt

# 图表配置
plots_flat: false
plots_force_flat: false

# 输出设置
make_data_dir: true
zip_data_dir: true
EOF

# 使用自定义配置运行MultiQC
multiqc results/fastqc/ data/processed/ \
    -o results/reports/ \
    -c multiqc_config.yaml \
    --title "定制化QC报告" \
    --filename "custom_qc_report" \
    --force

echo "定制化MultiQC报告生成完成!"
```
