<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">ChIP-seq数据分析流程</text>
  
  <!-- Step 1: Quality Control -->
  <rect x="250" y="80" width="300" height="60" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="115" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">数据质控与预处理</text>
  
  <!-- Step 2: Alignment -->
  <rect x="250" y="160" width="300" height="60" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="400" y="195" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">参考基因组比对</text>
  
  <!-- Step 3: Peak Calling -->
  <rect x="250" y="240" width="300" height="60" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="400" y="275" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">峰检测(Peak Calling)</text>
  
  <!-- Step 4: Differential Binding Analysis -->
  <rect x="250" y="320" width="300" height="60" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="400" y="355" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">差异结合分析</text>
  
  <!-- Step 5: Peak Annotation -->
  <rect x="250" y="400" width="300" height="60" fill="#e2e3e5" rx="10" ry="10" stroke="#6c757d" stroke-width="2"/>
  <text x="400" y="435" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">结合位点注释</text>
  
  <!-- Step 6: Motif Analysis -->
  <rect x="250" y="480" width="300" height="60" fill="#d3d3d3" rx="10" ry="10" stroke="#495057" stroke-width="2"/>
  <text x="400" y="515" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">结合模式分析</text>
  
  <!-- Connecting arrows -->
  <line x1="400" y1="140" x2="400" y2="160" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="220" x2="400" y2="240" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="300" x2="400" y2="320" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="380" x2="400" y2="400" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="460" x2="400" y2="480" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Tools for each step -->
  <text x="580" y="115" font-family="Arial" font-size="14" text-anchor="start">FastQC, Trimmomatic</text>
  <text x="580" y="195" font-family="Arial" font-size="14" text-anchor="start">Bowtie2, BWA</text>
  <text x="580" y="275" font-family="Arial" font-size="14" text-anchor="start">MACS2, HOMER, SICER</text>
  <text x="580" y="355" font-family="Arial" font-size="14" text-anchor="start">DiffBind, MAnorm</text>
  <text x="580" y="435" font-family="Arial" font-size="14" text-anchor="start">ChIPseeker, HOMER</text>
  <text x="580" y="515" font-family="Arial" font-size="14" text-anchor="start">MEME, HOMER</text>
  
  <!-- Input/Output for each step -->
  <text x="150" y="115" font-family="Arial" font-size="14" text-anchor="end">FASTQ文件</text>
  <text x="150" y="195" font-family="Arial" font-size="14" text-anchor="end">清洗后的FASTQ文件</text>
  <text x="150" y="275" font-family="Arial" font-size="14" text-anchor="end">比对结果(BAM文件)</text>
  <text x="150" y="355" font-family="Arial" font-size="14" text-anchor="end">峰文件(BED/narrowPeak)</text>
  <text x="150" y="435" font-family="Arial" font-size="14" text-anchor="end">差异结合区域</text>
  <text x="150" y="515" font-family="Arial" font-size="14" text-anchor="end">注释后的峰文件</text>
  
  <!-- Peak Calling Visualization -->
  <rect x="50" y="240" width="180" height="60" fill="#fff3cd" rx="5" ry="5" stroke="#ffc107" stroke-width="1"/>
  
  <!-- ChIP-seq signal -->
  <polyline points="60,280 70,280 80,270 90,260 100,250 110,260 120,270 130,280 140,280" 
           stroke="#dc3545" stroke-width="2" fill="none"/>
  
  <!-- Input signal -->
  <polyline points="60,280 70,280 80,278 90,276 100,275 110,276 120,278 130,280 140,280" 
           stroke="#6c757d" stroke-width="2" fill="none"/>
  
  <!-- Peak region -->
  <rect x="90" y="245" width="20" height="5" fill="#dc3545" opacity="0.5"/>
  <text x="170" y="265" font-family="Arial" font-size="12" text-anchor="end">峰区域</text>
  <line x1="150" y1="265" x2="100" y2="250" stroke="#dc3545" stroke-width="1" stroke-dasharray="2,2"/>
  
  <!-- Arrow definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d"/>
    </marker>
  </defs>
</svg>
