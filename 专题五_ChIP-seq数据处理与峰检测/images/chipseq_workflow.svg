<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">ChIP-seq实验原理与流程</text>
  
  <!-- Step 1: Cross-linking -->
  <rect x="100" y="80" width="600" height="80" fill="#d1e7dd" rx="10" ry="10" stroke="#198754" stroke-width="2"/>
  <text x="400" y="110" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">1. 交联固定</text>
  <text x="400" y="140" font-family="Arial" font-size="14" text-anchor="middle">使用甲醛将蛋白质与DNA交联固定</text>
  
  <!-- DNA and Protein -->
  <line x1="200" y1="110" x2="300" y2="110" stroke="#0d6efd" stroke-width="3"/>
  <circle cx="250" cy="95" r="15" fill="#dc3545"/>
  <text x="250" y="100" font-family="Arial" font-size="14" text-anchor="middle" fill="white">P</text>
  
  <!-- Step 2: Fragmentation -->
  <rect x="100" y="180" width="600" height="80" fill="#cfe2ff" rx="10" ry="10" stroke="#0d6efd" stroke-width="2"/>
  <text x="400" y="210" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">2. 染色质片段化</text>
  <text x="400" y="240" font-family="Arial" font-size="14" text-anchor="middle">使用超声或酶切方法将染色质片段化</text>
  
  <!-- Fragmented DNA -->
  <line x1="200" y1="210" x2="230" y2="210" stroke="#0d6efd" stroke-width="3"/>
  <circle cx="245" cy="195" r="15" fill="#dc3545"/>
  <text x="245" y="200" font-family="Arial" font-size="14" text-anchor="middle" fill="white">P</text>
  
  <line x1="260" y1="210" x2="290" y2="210" stroke="#0d6efd" stroke-width="3"/>
  <circle cx="275" cy="195" r="15" fill="#dc3545"/>
  <text x="275" y="200" font-family="Arial" font-size="14" text-anchor="middle" fill="white">P</text>
  
  <line x1="320" y1="210" x2="350" y2="210" stroke="#0d6efd" stroke-width="3"/>
  
  <line x1="380" y1="210" x2="410" y2="210" stroke="#0d6efd" stroke-width="3"/>
  <circle cx="395" cy="195" r="15" fill="#dc3545"/>
  <text x="395" y="200" font-family="Arial" font-size="14" text-anchor="middle" fill="white">P</text>
  
  <!-- Step 3: Immunoprecipitation -->
  <rect x="100" y="280" width="600" height="80" fill="#fff3cd" rx="10" ry="10" stroke="#ffc107" stroke-width="2"/>
  <text x="400" y="310" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">3. 免疫沉淀</text>
  <text x="400" y="340" font-family="Arial" font-size="14" text-anchor="middle">使用特异性抗体结合目标蛋白，沉淀抗体-蛋白-DNA复合物</text>
  
  <!-- Antibody and Beads -->
  <path d="M 250 310 L 230 290 L 270 290 Z" fill="#198754"/>
  <text x="250" y="300" font-family="Arial" font-size="12" text-anchor="middle" fill="white">Ab</text>
  
  <line x1="250" y1="310" x2="250" y2="320" stroke="#198754" stroke-width="2"/>
  <circle cx="250" cy="330" r="10" fill="#6c757d"/>
  <line x1="250" y1="320" x2="270" y2="320" stroke="#0d6efd" stroke-width="3"/>
  <circle cx="260" cy="310" r="10" fill="#dc3545"/>
  <text x="260" y="314" font-family="Arial" font-size="10" text-anchor="middle" fill="white">P</text>
  
  <!-- Step 4: Reverse Cross-linking -->
  <rect x="100" y="380" width="600" height="80" fill="#f8d7da" rx="10" ry="10" stroke="#dc3545" stroke-width="2"/>
  <text x="400" y="410" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">4. 交联逆转</text>
  <text x="400" y="440" font-family="Arial" font-size="14" text-anchor="middle">去除交联剂，释放DNA片段</text>
  
  <!-- Released DNA -->
  <line x1="250" y1="410" x2="300" y2="410" stroke="#0d6efd" stroke-width="3"/>
  
  <!-- Step 5: Library Construction -->
  <rect x="100" y="480" width="600" height="80" fill="#e2e3e5" rx="10" ry="10" stroke="#6c757d" stroke-width="2"/>
  <text x="400" y="510" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">5. 文库构建与测序</text>
  <text x="400" y="540" font-family="Arial" font-size="14" text-anchor="middle">末端修复、加接头、PCR扩增、高通量测序</text>
  
  <!-- Library and Sequencing -->
  <rect x="200" y="510" width="20" height="10" fill="#0d6efd"/>
  <rect x="220" y="510" width="60" height="10" fill="#6c757d"/>
  <rect x="280" y="510" width="20" height="10" fill="#dc3545"/>
  
  <rect x="320" y="510" width="20" height="10" fill="#0d6efd"/>
  <rect x="340" y="510" width="60" height="10" fill="#6c757d"/>
  <rect x="400" y="510" width="20" height="10" fill="#dc3545"/>
  
  <rect x="440" y="510" width="20" height="10" fill="#0d6efd"/>
  <rect x="460" y="510" width="60" height="10" fill="#6c757d"/>
  <rect x="520" y="510" width="20" height="10" fill="#dc3545"/>
  
  <!-- Connecting arrows -->
  <line x1="400" y1="160" x2="400" y2="180" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="260" x2="400" y2="280" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="360" x2="400" y2="380" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="460" x2="400" y2="480" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d"/>
    </marker>
  </defs>
</svg>
