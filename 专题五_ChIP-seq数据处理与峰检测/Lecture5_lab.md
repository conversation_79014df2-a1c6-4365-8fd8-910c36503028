# 专题五：表观基因组测序数据分析 - 实践操作课

## 课程目标
本实践课程旨在帮助学生：
1. 掌握ChIP-seq数据处理的完整流程
2. 学会使用MACS2进行峰检测和参数优化
3. 掌握ATAC-seq数据分析和开放染色质区域鉴定
4. 学会差异结合分析和功能注释方法

## 实验环境准备

### 软件环境配置
```bash
# 创建专题五专用环境
conda create -n chipseq_analysis python=3.8 -y
conda activate chipseq_analysis

# 安装核心软件
conda install -c bioconda bowtie2 samtools bedtools macs2 -y
conda install -c bioconda picard fastqc trimmomatic -y
conda install -c bioconda deeptools homer -y
conda install -c r r-base r-diffbind r-chipseeker r-clusterProfiler -y

# 验证安装
echo "=== 软件版本验证 ==="
bowtie2 --version
samtools --version
macs2 --version
bedtools --version

echo "软件安装完成！"
```

### 实验数据准备
```bash
# 创建工作目录
mkdir -p chipseq_practice/{data/{reference,raw,processed},results/{alignment,peaks,qc,diff_analysis},scripts,logs}
cd chipseq_practice

# 创建简化的参考基因组（用于教学演示）
cat > data/reference/chr22_mini.fa << 'EOF'
>chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

# 创建基因注释文件
cat > data/reference/chr22_mini.bed << 'EOF'
chr22	100	300	GENE1	0	+
chr22	500	700	GENE2	0	-
chr22	800	1000	GENE3	0	+
EOF

# 创建模拟ChIP-seq数据（转录因子结合）
cat > data/raw/treatment.fastq << 'EOF'
@chipseq_read_1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@chipseq_read_2
CGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTAT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@chipseq_read_3
TTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@chipseq_read_4
AATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCC
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

# 创建对照样本（Input DNA）
cat > data/raw/control.fastq << 'EOF'
@input_read_1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@input_read_2
CGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTAT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@input_read_3
TTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
EOF

echo "实验数据准备完成！"
```

---

## 第一部分：ChIP-seq数据处理完整流程

### 1.1 数据质控与预处理

#### 1.1.1 FastQC质量控制

**实验目标：** 评估原始ChIP-seq数据质量，识别潜在问题

```bash
echo "=== ChIP-seq数据质量控制 ==="

# 1. 对原始数据进行质量控制
echo "执行FastQC质量控制..."
fastqc data/raw/treatment.fastq -o results/qc/
fastqc data/raw/control.fastq -o results/qc/

# 2. 检查质量控制结果
echo -e "\n=== 质量控制结果概览 ==="
ls -la results/qc/

# 3. 创建质量评估脚本
cat > scripts/assess_chip_quality.py << 'EOF'
#!/usr/bin/env python3
"""
ChIP-seq数据质量评估脚本
评估测序数据的基本统计信息和质量分布
"""

def count_fastq_reads(fastq_file):
    """统计FASTQ文件中的reads数量"""
    try:
        with open(fastq_file, 'r') as f:
            count = 0
            for line in f:
                if line.startswith('@'):
                    count += 1
            return count
    except FileNotFoundError:
        return 0

def calculate_sequence_stats(fastq_file):
    """计算序列基本统计信息"""
    try:
        total_length = 0
        gc_count = 0
        total_bases = 0
        read_count = 0
        
        with open(fastq_file, 'r') as f:
            lines = f.readlines()
            
        for i in range(1, len(lines), 4):  # 序列行
            if i < len(lines):
                seq = lines[i].strip()
                total_length += len(seq)
                gc_count += seq.count('G') + seq.count('C')
                total_bases += len(seq)
                read_count += 1
        
        return {
            'read_count': read_count,
            'avg_length': total_length / read_count if read_count > 0 else 0,
            'gc_content': (gc_count / total_bases * 100) if total_bases > 0 else 0,
            'total_bases': total_bases
        }
    except:
        return None

def generate_quality_report(sample_name, fastq_file):
    """生成质量报告"""
    print(f"\n=== {sample_name} 质量报告 ===")
    
    stats = calculate_sequence_stats(fastq_file)
    if stats:
        print(f"reads数量: {stats['read_count']:,}")
        print(f"平均序列长度: {stats['avg_length']:.1f} bp")
        print(f"GC含量: {stats['gc_content']:.1f}%")
        print(f"总碱基数: {stats['total_bases']:,}")
        
        # 质量评估建议
        print(f"\n质量评估:")
        if stats['read_count'] < 1000000:
            print("⚠️  reads数量较少，可能影响后续分析")
        else:
            print("✅ reads数量充足")
            
        if 40 <= stats['gc_content'] <= 60:
            print("✅ GC含量正常")
        else:
            print("⚠️  GC含量异常，可能存在污染或偏好性")
    else:
        print("❌ 无法读取文件")

# 分析样本
generate_quality_report("Treatment (ChIP)", "data/raw/treatment.fastq")
generate_quality_report("Control (Input)", "data/raw/control.fastq")

print(f"\n=== ChIP-seq特殊质控建议 ===")
print("1. 检查片段长度分布：应该反映转录因子保护的DNA片段")
print("2. 检查重复序列比例：ChIP-seq中重复序列比例应该较低")
print("3. 比较ChIP和Input样本：ChIP样本应在特定区域有富集")
print("4. 评估文库复杂度：避免PCR过度扩增")
EOF

python3 scripts/assess_chip_quality.py
```

#### 1.1.2 序列过滤和修剪

```bash
echo "=== 序列质量过滤 ==="

# 1. 使用Trimmomatic进行序列修剪
echo "执行序列修剪..."
trimmomatic SE \
    data/raw/treatment.fastq \
    data/processed/treatment_trimmed.fastq \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:30

trimmomatic SE \
    data/raw/control.fastq \
    data/processed/control_trimmed.fastq \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:30

# 2. 比较修剪前后的统计信息
echo -e "\n=== 修剪效果对比 ==="
echo "Treatment样本:"
echo "原始reads: $(grep -c '^@' data/raw/treatment.fastq)"
echo "修剪后reads: $(grep -c '^@' data/processed/treatment_trimmed.fastq)"

echo -e "\nControl样本:"
echo "原始reads: $(grep -c '^@' data/raw/control.fastq)"
echo "修剪后reads: $(grep -c '^@' data/processed/control_trimmed.fastq)"

echo "序列过滤完成！"
```

### 1.2 参考基因组比对

#### 1.2.1 构建Bowtie2索引

```bash
echo "=== 构建Bowtie2索引 ==="

# 1. 为参考基因组构建索引
echo "构建参考基因组索引..."
bowtie2-build data/reference/chr22_mini.fa data/reference/chr22_bowtie2_index

# 2. 验证索引文件
echo -e "\n索引文件列表:"
ls -la data/reference/chr22_bowtie2_index*

echo "Bowtie2索引构建完成！"
```

#### 1.2.2 序列比对实践

```bash
echo "=== ChIP-seq序列比对 ==="

# 1. Treatment样本比对
echo "比对Treatment样本..."
bowtie2 -x data/reference/chr22_bowtie2_index \
    -U data/processed/treatment_trimmed.fastq \
    -S results/alignment/treatment.sam \
    -p 2 \
    --very-sensitive

# 2. Control样本比对
echo "比对Control样本..."
bowtie2 -x data/reference/chr22_bowtie2_index \
    -U data/processed/control_trimmed.fastq \
    -S results/alignment/control.sam \
    -p 2 \
    --very-sensitive

# 3. SAM转BAM并排序
echo -e "\n=== 处理比对结果 ==="
for sample in treatment control; do
    echo "处理${sample}样本..."
    
    # SAM转BAM
    samtools view -bS results/alignment/${sample}.sam > results/alignment/${sample}.bam
    
    # 排序
    samtools sort results/alignment/${sample}.bam -o results/alignment/${sample}.sorted.bam
    
    # 建立索引
    samtools index results/alignment/${sample}.sorted.bam
    
    # 生成比对统计
    samtools flagstat results/alignment/${sample}.sorted.bam > results/alignment/${sample}_flagstat.txt
    
    echo "✓ ${sample}样本处理完成"
done

echo "序列比对完成！"
```

### 1.3 PCR重复去除与质量过滤

#### 1.3.1 PCR重复标记

```bash
echo "=== PCR重复去除 ==="

# 1. 使用Picard标记重复序列
echo "标记PCR重复序列..."
for sample in treatment control; do
    echo "处理${sample}样本..."
    
    picard MarkDuplicates \
        I=results/alignment/${sample}.sorted.bam \
        O=results/alignment/${sample}.marked.bam \
        M=results/alignment/${sample}_dup_metrics.txt \
        REMOVE_DUPLICATES=true
    
    # 重新排序和索引
    samtools sort results/alignment/${sample}.marked.bam -o results/alignment/${sample}.final.bam
    samtools index results/alignment/${sample}.final.bam
    
    echo "✓ ${sample}重复去除完成"
done

# 2. 质量过滤
echo -e "\n=== 质量过滤 ==="
for sample in treatment control; do
    echo "质量过滤${sample}样本..."
    
    samtools view -b -q 20 -F 1028 \
        results/alignment/${sample}.final.bam > \
        results/alignment/${sample}.filtered.bam
    
    samtools index results/alignment/${sample}.filtered.bam
    
    echo "✓ ${sample}质量过滤完成"
done

# 3. 比对质量统计对比
echo -e "\n=== 处理效果统计 ==="
cat > scripts/alignment_stats.sh << 'EOF'
#!/bin/bash
echo -e "样本\t阶段\t总reads\t比对reads\t比对率\t唯一比对"

for sample in treatment control; do
    # 原始比对结果
    if [ -f "results/alignment/${sample}.sorted.bam" ]; then
        total=$(samtools view -c results/alignment/${sample}.sorted.bam)
        mapped=$(samtools view -c -F 4 results/alignment/${sample}.sorted.bam)
        unique=$(samtools view -c -q 1 results/alignment/${sample}.sorted.bam)
        rate=$(echo "scale=2; $mapped * 100 / $total" | bc -l)
        echo -e "$sample\t原始\t$total\t$mapped\t${rate}%\t$unique"
    fi
    
    # 过滤后结果
    if [ -f "results/alignment/${sample}.filtered.bam" ]; then
        total=$(samtools view -c results/alignment/${sample}.filtered.bam)
        mapped=$(samtools view -c -F 4 results/alignment/${sample}.filtered.bam)
        unique=$(samtools view -c -q 1 results/alignment/${sample}.filtered.bam)
        rate=$(echo "scale=2; $mapped * 100 / $total" | bc -l)
        echo -e "$sample\t过滤后\t$total\t$mapped\t${rate}%\t$unique"
    fi
done
EOF

chmod +x scripts/alignment_stats.sh
./scripts/alignment_stats.sh

echo "数据预处理完成！"
```

---

## 第二部分：MACS2峰检测详细实践

### 2.1 MACS2基础峰检测

#### 2.1.1 narrow峰检测（转录因子）

**实验目标：** 学会使用MACS2检测sharp peaks，理解参数设置

```bash
echo "=== MACS2 narrow峰检测 ==="

# 1. 基本峰检测
echo "执行narrow峰检测..."
macs2 callpeak \
    -t results/alignment/treatment.filtered.bam \
    -c results/alignment/control.filtered.bam \
    -f BAM \
    -g 1000 \
    -n treatment_vs_control \
    -q 0.05 \
    --outdir results/peaks/ \
    2> results/peaks/macs2_narrow.log

# 2. 检查输出文件
echo -e "\n=== MACS2输出文件 ==="
ls -la results/peaks/treatment_vs_control*

# 参数解释
cat << 'EOF'

=== MACS2参数详解 ===
-t: Treatment文件（ChIP样本）
-c: Control文件（Input样本）
-f: 输入文件格式
-g: 有效基因组大小
-n: 输出文件前缀
-q: q值阈值（FDR校正后的p值）
--outdir: 输出目录
EOF

echo "narrow峰检测完成！"
```

#### 2.1.2 broad峰检测（组蛋白修饰）

```bash
echo "=== MACS2 broad峰检测 ==="

# 1. broad峰检测
echo "执行broad峰检测..."
macs2 callpeak \
    -t results/alignment/treatment.filtered.bam \
    -c results/alignment/control.filtered.bam \
    -f BAM \
    -g 1000 \
    -n treatment_broad \
    --broad \
    --broad-cutoff 0.1 \
    -q 0.05 \
    --outdir results/peaks/ \
    2> results/peaks/macs2_broad.log

echo "broad峰检测完成！"
```

### 2.2 峰检测结果分析

#### 2.2.1 峰文件格式解读

```bash
# 创建峰文件分析脚本
cat > scripts/analyze_peaks.py << 'EOF'
#!/usr/bin/env python3
"""
MACS2峰检测结果分析脚本
解析和统计峰检测结果
"""

import pandas as pd
import numpy as np

def analyze_narrow_peaks(peak_file):
    """分析narrow峰文件"""
    print(f"=== Narrow峰分析 ===")
    
    try:
        # 读取narrowPeak文件
        columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
                  'signalValue', 'pValue', 'qValue', 'peak']
        
        df = pd.read_csv(peak_file, sep='\t', header=None, names=columns)
        
        print(f"检测到峰数量: {len(df)}")
        
        # 峰长度分析
        df['length'] = df['end'] - df['start']
        print(f"峰长度统计:")
        print(f"  平均长度: {df['length'].mean():.1f} bp")
        print(f"  中位数长度: {df['length'].median():.1f} bp")
        print(f"  最短峰: {df['length'].min()} bp")
        print(f"  最长峰: {df['length'].max()} bp")
        
        # 信号强度分析
        print(f"\n信号强度统计:")
        print(f"  平均信号值: {df['signalValue'].mean():.2f}")
        print(f"  最高信号值: {df['signalValue'].max():.2f}")
        
        # 显著性分析
        significant_peaks = len(df[df['qValue'] > 2])  # -log10(0.01) ≈ 2
        print(f"\n显著性统计:")
        print(f"  高显著性峰 (q<0.01): {significant_peaks}")
        print(f"  高显著性比例: {significant_peaks/len(df)*100:.1f}%")
        
        # 显示前5个最显著的峰
        print(f"\n最显著的峰 (前5个):")
        top_peaks = df.nlargest(5, 'qValue')[['chr', 'start', 'end', 'signalValue', 'qValue']]
        print(top_peaks.to_string(index=False))
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 文件 {peak_file} 不存在")
        return None
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return None

def analyze_broad_peaks(peak_file):
    """分析broad峰文件"""
    print(f"\n=== Broad峰分析 ===")
    
    try:
        # broadPeak文件格式
        columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
                  'signalValue', 'pValue', 'qValue']
        
        df = pd.read_csv(peak_file, sep='\t', header=None, names=columns)
        
        print(f"检测到broad峰数量: {len(df)}")
        
        # 峰长度分析
        df['length'] = df['end'] - df['start']
        print(f"Broad峰长度统计:")
        print(f"  平均长度: {df['length'].mean():.1f} bp")
        print(f"  中位数长度: {df['length'].median():.1f} bp")
        print(f"  最短峰: {df['length'].min()} bp")
        print(f"  最长峰: {df['length'].max()} bp")
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 文件 {peak_file} 不存在")
        return None
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return None

def compare_peak_types(narrow_df, broad_df):
    """比较narrow和broad峰的特征"""
    if narrow_df is None or broad_df is None:
        print("无法进行比较：缺少数据")
        return
    
    print(f"\n=== Narrow vs Broad峰比较 ===")
    print(f"Narrow峰数量: {len(narrow_df)}")
    print(f"Broad峰数量: {len(broad_df)}")
    print(f"Narrow峰平均长度: {narrow_df['length'].mean():.1f} bp")
    print(f"Broad峰平均长度: {broad_df['length'].mean():.1f} bp")
    
    # 长度分布比较
    print(f"\n长度分布比较:")
    print(f"Narrow峰 <500bp: {len(narrow_df[narrow_df['length'] < 500])}")
    print(f"Narrow峰 >1000bp: {len(narrow_df[narrow_df['length'] > 1000])}")
    print(f"Broad峰 <500bp: {len(broad_df[broad_df['length'] < 500])}")
    print(f"Broad峰 >1000bp: {len(broad_df[broad_df['length'] > 1000])}")

if __name__ == "__main__":
    # 分析narrow峰
    narrow_df = analyze_narrow_peaks("results/peaks/treatment_vs_control_peaks.narrowPeak")
    
    # 分析broad峰  
    broad_df = analyze_broad_peaks("results/peaks/treatment_broad_peaks.broadPeak")
    
    # 比较分析
    compare_peak_types(narrow_df, broad_df)
    
    print(f"\n=== 峰检测质量评估建议 ===")
    print("1. 峰数量: 转录因子通常1000-10000个峰，组蛋白修饰可能更多")
    print("2. 峰长度: 转录因子峰通常<500bp，组蛋白修饰峰可能>1000bp")
    print("3. 信号强度: 高质量峰应有较高的信号值和显著性")
    print("4. 与已知位点比较: 验证峰是否在预期的基因组区域")
EOF

python3 scripts/analyze_peaks.py
```
