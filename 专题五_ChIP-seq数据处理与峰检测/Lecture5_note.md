# 专题五：表观基因组测序数据分析 - 理论课

## 表观基因组学基础知识

### 1. 表观基因组学概述
你是否曾经想过，为什么我们体内的每个细胞都含有相同的DNA，却能形成如此多样的细胞类型和功能？这个谜题的答案就隐藏在表观基因组学中。

表观基因组学是研究基因组范围内表观遗传修饰的科学。它探讨不依赖于DNA序列改变的基因表达调控机制，包括DNA甲基化、组蛋白修饰、染色质结构变化等。这些表观遗传修饰在细胞分化、发育和疾病过程中起着关键作用。表观基因组学的研究有助于我们理解基因组如何在不同细胞类型和条件下被动态调控，以及环境因素如何影响基因表达。通过研究表观基因组学，我们可以揭示生命的奥秘，并为疾病诊断和治疗开辟新的途径。

![表观遗传修饰类型](images/epigenetic_modifications.svg)
*(Figure: Overview of major epigenetic modification types including DNA methylation and various histone modifications.)*

*   **表观遗传学定义与核心特征**
    *   **表观遗传学（Epigenetics）**：研究在不改变DNA核苷酸序列（基因型）的前提下，通过某些机制引起基因表达水平（表型）发生可遗传变化的学科。这些变化是细胞响应环境信号、进行分化和维持身份的关键。
    *   **核心特征**：
        *   **可遗传性 (Heritability)**：表观遗传标记（Epigenetic marks）在细胞分裂过程中可以传递给子代细胞（有丝分裂遗传），有时甚至可以跨代传递（减数分裂遗传）。
        *   **可逆性 (Reversibility)**：与DNA序列的突变不同，表观遗传修饰通常是动态且可逆的，受到特定酶系统（"writers", "erasers"）的调控。
        *   **环境响应性 (Environmental Sensitivity)**：表观遗传状态可以受到发育信号、环境因素（营养、压力、毒素等）的影响而发生改变，是连接环境与基因表达的桥梁。
        *   **细胞特异性 (Cell-type Specificity)**：不同细胞类型具有独特的表观基因组谱，这对于维持细胞身份和功能至关重要。

*   **表观基因组学研究内容 (Scope of Epigenomics)**
    *   **DNA甲基化 (DNA Methylation)**：最经典和研究最广泛的表观修饰。主要发生在哺乳动物基因组的CpG二核苷酸上，胞嘧啶（C）被添加甲基基团（5mC）。其模式与基因表达调控密切相关。主要研究技术：**WGBS (Whole-Genome Bisulfite Sequencing), RRBS (Reduced Representation Bisulfite Sequencing), MeDIP-seq, 甲基化芯片**。
    *   **组蛋白修饰 (Histone Modifications)**：组蛋白（特别是核心组蛋白H2A, H2B, H3, H4的N端尾巴）可以被多种化学基团修饰，如乙酰化、甲基化、磷酸化、泛素化等。这些修饰影响染色质结构和DNA的可及性，形成复杂的“组蛋白密码”（histone code）。主要研究技术：**ChIP-seq (Chromatin Immunoprecipitation sequencing)**。
    *   **染色质可及性 (Chromatin Accessibility)**：指基因组DNA缠绕在组蛋白上形成染色质后的开放程度。开放区域（通常是核小体稀疏区域）允许转录因子等调控蛋白结合，通常与活跃的基因调控元件相关。主要研究技术：**ATAC-seq (Assay for Transposase-Accessible Chromatin using sequencing), DNase-seq (DNase I hypersensitive sites sequencing), MNase-seq (Micrococcal Nuclease sequencing)**。
    *   **非编码RNA调控 (Non-coding RNA Regulation)**：长非编码RNA (lncRNA)、微小RNA (miRNA)等可以通过多种机制（如引导表观修饰酶、影响染色质结构、调控转录后过程）参与表观遗传调控。主要研究技术：**RNA-seq (关注非编码RNA部分), CLIP-seq (Crosslinking and immunoprecipitation sequencing)**。
    *   **三维基因组结构 (3D Genome Architecture)**：染色质在细胞核内的空间组织，如拓扑关联结构域（TADs）和染色质环（loops），影响着增强子-启动子的远程互作，也属于广义的表观调控范畴。主要研究技术：**Hi-C, ChIA-PET**。
    *   **核小体定位与组蛋白变体 (Nucleosome Positioning and Histone Variants)**：核小体在基因组上的精确位置及其组成的组蛋白变体（如H3.3, H2A.Z）影响DNA可及性和调控蛋白的结合。主要研究技术：**MNase-seq, ChIP-seq (针对组蛋白变体)**。

*   **表观修饰的可逆性与动态性 (Reversibility and Dynamics)**
    *   **可逆性机制**：
        *   **写入器 (Writers)**：添加修饰的酶，如DNA甲基转移酶（DNMTs）、组蛋白乙酰转移酶（HATs）、组蛋白甲基转移酶（HMTs）。
        *   **擦除器 (Erasers)**：去除修饰的酶，如TET酶家族（催化5mC氧化去甲基化）、组蛋白去乙酰化酶（HDACs）、组蛋白去甲基化酶（KDMs）。
        *   **阅读器 (Readers)**：识别特定修饰并介导下游效应的蛋白质，如含有特定结构域（Bromo-, Chromo-, PHD-finger等）的蛋白。
        ![表观修饰的可逆性](images/epigenetic_reversibility.svg)
    *   **动态性体现**：
        *   **发育过程**：受精卵到成体的发育伴随着剧烈的表观基因组重编程（Epigenetic reprogramming）。细胞分化过程中，特定基因的表观状态会发生精确改变以建立和维持细胞谱系。
        *   **环境响应**：营养状况、物理化学刺激、生物胁迫等均可诱导表观修饰发生适应性变化。
        *   **疾病状态**：癌症等多种疾病中常观察到表观遗传模式的异常改变。
        *   **药物干预**：表观遗传药物（Epi-drugs），如DNMT抑制剂（Azacitidine, Decitabine）和HDAC抑制剂（Vorinostat/SAHA, Romidepsin），已被用于临床治疗（主要是癌症）。

*   **表观基因组学在生物学研究中的意义 (Significance in Biological Research)**
    *   **深化基因表达调控理解**：揭示了超越DNA序列本身的调控层级，解释了细胞类型多样性、环境适应性等复杂生命现象。阐明了X染色体失活、基因组印记、等位基因特异性表达等经典遗传现象的分子机制。
    *   **解析发育与分化机制**：表观遗传调控是细胞命运决定和维持的关键。研究干细胞多能性、谱系分化、组织器官发生的表观动态变化，有助于理解发育生物学的基本规律。
    *   **揭示疾病发生发展机理**：表观遗传失调（Epigenetic dysregulation）是许多复杂疾病（如癌症、神经退行性疾病、代谢性疾病、免疫性疾病）的重要驱动因素或标志物。研究疾病特异的表观遗传改变有助于理解发病机制。
    *   **推动新型诊断与治疗策略**：
        *   **生物标志物 (Biomarkers)**：特定的表观遗传标记（如特定基因的甲基化状态）可用于疾病的早期诊断、预后判断和疗效预测。
        *   **治疗靶点 (Therapeutic Targets)**：针对表观遗传调控酶（writers, erasers）的药物开发是当前的热点领域。
        *   **表观遗传编辑 (Epigenome Editing)**：利用CRISPR等基因编辑技术改造特定位点的表观修饰状态，为疾病治疗提供了新的潜在途径。

### 2. 主要表观遗传修饰类型详解
![表观修饰类型](images/epigenetic_modifications_types.svg)
*   **DNA甲基化 (DNA Methylation)**
    *   **CpG甲基化 (CpG methylation)**：在哺乳动物中，DNA甲基化主要发生在CpG二核苷酸的胞嘧啶（C）的5号碳位上，形成5-甲基胞嘧啶（5mC）。CpG位点在基因组中分布不均，常富集在CpG岛（CpG islands, CGIs）中。
    *   **非CpG甲基化 (Non-CpG methylation)**：在特定细胞类型（如神经元、胚胎干细胞）和物种中，CHG和CHH（H代表A, T或C）位点的甲基化也存在，具有独特的调控功能。
    *   **5mC及其氧化衍生物 (5mC and its derivatives)**：
        *   **5mC (5-methylcytosine)**：经典的甲基化形式，通常与基因沉默相关，特别是在启动子区域。
        *   **5hmC (5-hydroxymethylcytosine)**：由TET酶氧化5mC产生，不仅是去甲基化的中间体，本身也可能作为一种独立的表观遗传标记，具有调控功能，常富集在基因体和增强子区域。
        *   **5fC (5-formylcytosine) & 5caC (5-carboxylcytosine)**：由TET酶进一步氧化5hmC产生，是主动去甲基化途径的关键中间体，含量较低。

*   **组蛋白修饰 (Histone Modifications)**
    *   **乙酰化 (Acetylation, e.g., H3K27ac, H3K9ac)**：通常发生在组蛋白赖氨酸（K）残基上，中和了赖氨酸的正电荷，使染色质结构变得松散，有利于转录。H3K27ac和H3K9ac是活跃启动子和增强子的标志。
    *   **甲基化 (Methylation, e.g., H3K4me3, H3K27me3, H3K9me3)**：发生在赖氨酸（K）或精氨酸（R）残基上，可以有单甲基化（me1）、二甲基化（me2）或三甲基化（me3）。效果取决于位点和甲基化程度：
        *   **H3K4me3**：通常富集在活跃基因的启动子区域，与转录起始相关。
        *   **H3K27me3**：由Polycomb抑制复合物（PRC2）催化，是基因沉默的标志，特别是在发育调控基因中。
        *   **H3K9me3**：主要与异染色质形成和基因长期沉默相关。
        *   **H3K4me1**：常标记增强子区域（与H3K27ac共同标记活跃增强子）。
        *   **H3K36me3**：主要分布在活跃转录基因的基因体区域，与转录延伸和RNA剪接有关。
    *   **其他修饰 (Other Modifications)**：磷酸化（Phosphorylation）、泛素化（Ubiquitination）、SUMO化（SUMOylation）等，这些修饰同样参与调控染色质结构和功能，且常常与其他修饰发生串扰（crosstalk）。

*   **染色质可及性 (Chromatin Accessibility)**
    *   反映了DNA序列对调控因子（如转录因子）和转录机器的可接触程度。
    *   开放染色质区域通常对应于活跃的调控元件，如启动子、增强子、绝缘子。
    *   染色质可及性受到多种因素调控：
        *   **染色质重塑复合物 (Chromatin Remodelers)**：如SWI/SNF, ISWI, CHD, INO80家族，利用ATP水解的能量来移动、移除或改变核小体的组成。
        *   **组蛋白修饰**：如乙酰化通常促进开放，而某些甲基化（如H3K9me3）促进致密化。
        *   **先锋转录因子 (Pioneer Transcription Factors)**：能够结合到致密染色质中的特定位点，并启动染色质开放过程。
    *   **检测技术**：ATAC-seq, DNase-seq, FAIRE-seq (Formaldehyde-Assisted Isolation of Regulatory Elements)。

*   **非编码RNA调控 (Non-coding RNA Regulation)**
    *   多种非编码RNA参与表观调控：
        *   **miRNA (microRNA)**：主要在转录后水平调控基因表达，但也可影响表观修饰。
        *   **lncRNA (Long non-coding RNA)**：可以作为支架（scaffold）招募染色质修饰复合物到特定基因位点，或通过其他机制调控染色质结构和基因表达（如Xist介导的X染色体失活）。
        *   **circRNA (Circular RNA)**：功能仍在探索中，部分研究表明可参与表观调控。

*   **核小体定位与组蛋白变体 (Nucleosome Positioning and Histone Variants)**
    *   核小体是染色质的基本结构单元，其在基因组上的精确位置（定位）影响了DNA序列的可及性。启动子区域常存在核小体稀疏区（Nucleosome-Depleted Region, NDR）。
    *   除了经典的组蛋白，细胞还表达多种组蛋白变体（如H3.3, H2A.Z, macroH2A），它们可以替换经典组蛋白进入核小体，赋予染色质区域特殊的结构和功能特性。

### 3. 表观修饰与基因调控

*   **启动子区域表观修饰特征 (Epigenetic Features at Promoters)**
    *   **活跃启动子 (Active Promoters)**：通常具有低DNA甲基化水平（尤其是在CpG岛区域）、高组蛋白乙酰化（如H3K9ac, H3K27ac）、高H3K4me3水平以及开放的染色质结构。
    *   **抑制启动子 (Repressed Promoters)**：可能具有高DNA甲基化水平（CpG岛甲基化）、低乙酰化、高H3K27me3或H3K9me3水平以及致密的染色质结构。
    *   **“准备就绪”或“两价”启动子 (Poised/Bivalent Promoters)**：在干细胞和发育过程中常见，同时具有激活标记（H3K4me3）和抑制标记（H3K27me3），基因处于低表达状态但随时准备响应信号而被激活或进一步抑制。

*   **增强子区域表观修饰特征 (Epigenetic Features at Enhancers)**
    *   **活跃增强子 (Active Enhancers)**：通常具有开放的染色质结构、低DNA甲基化、高H3K4me1和高H3K27ac水平。
    *   **“准备就绪”增强子 (Poised Enhancers)**：通常具有H3K4me1但缺乏H3K27ac。
    *   增强子通过形成染色质环（chromatin loops）与远距离的靶基因启动子相互作用，调控基因表达。

*   **异染色质与常染色质 (Heterochromatin vs. Euchromatin)**
    *   **异染色质 (Heterochromatin)**：染色质高度压缩，DNA复制较晚，基因密度低，转录不活跃。通常富含抑制性标记，如DNA高甲基化和H3K9me3（组成型异染色质）或H3K27me3（兼性异染色质）。
    *   **常染色质 (Euchromatin)**：染色质结构相对松散，DNA复制较早，基因密度高，是转录活跃区域。通常富含激活性标记，如组蛋白乙酰化和H3K4me3。

*   **表观修饰与转录因子结合 (Epigenetic Modifications and TF Binding)**
    *   表观修饰可以直接或间接影响转录因子（TF）与DNA的结合：
        *   DNA甲基化（尤其在TF结合位点内部）可以直接阻止某些TF的结合。
        *   组蛋白修饰可以改变染色质结构，从而影响TF对结合位点的可及性。
        *   某些TF优先结合带有特定组蛋白修饰的区域，或者TF可以招募表观修饰酶来改变局部的表观状态。

*   **表观修饰与基因表达的关系 (Relationship between Epigenetics and Gene Expression)**
    *   总体而言，存在相关性模式（如启动子低甲基化/高乙酰化/高H3K4me3 -> 高表达；启动子高甲基化/高H3K27me3 -> 低表达），但这种关系是复杂的、上下文依赖的，并且涉及多种修饰的组合效应。表观修饰、染色质结构、TF结合和基因表达构成了一个相互作用的调控网络。

### 4. 表观基因组学研究技术概述

*   **基于芯片的技术 (Array-based Technologies, e.g., ChIP-chip, MeDIP-chip, Methylation Arrays)**
    *   **原理**：将通过特定方法富集（如ChIP, MeDIP）或处理（如Bisulfite conversion后）的DNA片段与预先设计好的包含大量探针的芯片进行杂交，通过检测杂交信号强度来推断特定位点或区域的表观修饰水平。
    *   **代表**：ChIP-chip, MeDIP-chip, Illumina Infinium Methylation Arrays (e.g., 450k, EPIC 850k)。
    *   **优缺点**：成本相对较低（尤其对于甲基化芯片），数据分析相对成熟；但分辨率受探针设计限制，覆盖范围不如测序广，可能存在杂交偏好性。

*   **基于测序的技术 (Sequencing-based Technologies, e.g., ChIP-seq, ATAC-seq, BS-seq, etc.)**
    *   **原理**：将通过特定方法富集或处理的DNA片段构建成测序文库，然后进行高通量测序（Next-Generation Sequencing, NGS），通过分析测序读段（reads）在基因组上的分布和特征来推断表观修饰信息。
    *   **代表**：ChIP-seq, ATAC-seq, DNase-seq, WGBS, RRBS, MeDIP-seq, Hi-C, etc.
    *   **优缺点**：提供全基因组（或目标区域）的覆盖，分辨率高（可达单碱基对DNA甲基化），动态范围宽，能够发现新的表观遗传特征；但成本相对较高，数据量大，分析流程更复杂。

*   **单细胞表观基因组学技术 (Single-cell Epigenomics Techniques)**
    *   **原理**：将上述技术应用于单个细胞，以解析细胞群体中的异质性。
    *   **代表**：scATAC-seq, scChIP-seq, scBS-seq, sci-CAR, scNMT-seq (simultaneous profiling of multiple modalities)。
    *   **挑战**：需要处理极微量的起始材料，数据稀疏性高，分析方法仍在快速发展中。

*   **技术选择考量 (Choosing the Right Technology)**
    *   **研究目标**：是研究特定蛋白结合位点（ChIP-seq），染色质开放性（ATAC-seq），DNA甲基化（BS-seq/RRBS/Array），还是三维结构（Hi-C）？
    *   **分辨率需求**：需要单碱基分辨率（WGBS）还是区域性富集信息（ChIP-seq, ATAC-seq）？
    *   **样本量与起始材料量**：单细胞技术？低起始量技术？
    *   **覆盖范围**：全基因组（WGBS, ATAC-seq）还是特定区域（RRBS, Target enrichment）？
    *   **成本预算**：芯片通常比测序便宜，WGBS通常最贵。
    *   **现有数据与整合需求**：是否需要与其他组学数据（如RNA-seq）整合分析？

**总结 (Section Summary)**

表观基因组学研究不依赖于DNA序列改变的基因表达调控机制，涉及DNA甲基化、组蛋白修饰、染色质可及性等多个层面。这些修饰具有可遗传、可逆、动态响应环境的特点，在发育、疾病和基因调控中扮演核心角色。多种高通量技术（芯片和测序）被用于绘制表观基因组图谱，各有优劣，需根据研究目标审慎选择。

## ChIP-seq实验原理与分析流程

### 1. ChIP-seq实验原理

![染色质免疫沉淀实验原理与流程](images/chipseq_workflow.svg)
*(Figure: Schematic workflow of Chromatin Immunoprecipitation followed by Sequencing (ChIP-seq), illustrating key steps from cell crosslinking to sequencing and data analysis.)*

*   **染色质免疫沉淀 (ChIP) 基本步骤**
    1.  **交联 (Crosslinking)**：通常使用甲醛（formaldehyde）处理活细胞或组织，使DNA与结合在其上的蛋白质（如组蛋白、转录因子）发生共价交联，固定它们在体内的相互作用状态。对于某些相互作用较弱或动态的蛋白，可能需要双交联（如使用DSG + 甲醛）。
    2.  **细胞裂解与染色质片段化 (Cell Lysis and Chromatin Fragmentation)**：裂解细胞核，释放染色质。然后通过物理方法（超声波破碎，Sonication）或酶解方法（如MNase酶切，主要用于研究组蛋白或核小体定位）将染色质随机打断成一定长度范围的片段（通常为200-600 bp）。
    3.  **免疫沉淀 (Immunoprecipitation, IP)**：加入针对目标蛋白（Target Protein, 如特定修饰的组蛋白或某个转录因子）的特异性抗体。抗体与染色质片段中含有目标蛋白的复合物结合。随后，加入能够结合抗体Fc片段的Protein A/G磁珠（或琼脂糖珠），将“抗体-目标蛋白-DNA”复合物沉淀下来。洗涤数次以去除**非特异性结合**的染色质片段。
    4.  **洗脱与解交联 (Elution and Reverse Crosslinking)**：从磁珠上洗脱“抗体-目标蛋白-DNA”复合物。然后通过加热或蛋白酶K处理，解除蛋白质与DNA之间的交联，并降解蛋白质。
    5.  **DNA纯化 (DNA Purification)**：纯化得到被目标蛋白结合的DNA片段。
    6.  **文库构建与测序 (Library Preparation and Sequencing)**：将纯化后的DNA片段进行末端修复、加A尾、连接测序接头（Adapters）等步骤，构建成适用于高通量测序的文库。然后进行NGS测序。同时，通常会保留一部分未经过IP的片段化染色质作为**Input对照**，进行同样的文库构建和测序。

*   **抗体选择的关键因素 (Critical Factors for Antibody Selection)**
    *   **特异性 (Specificity)**：抗体必须高度特异地识别目标蛋白（或特定修饰状态），尽量减少与其它蛋白或非目标修饰的交叉反应。需要通过Western Blot、ELISA、Peptide array或IP-Mass Spec等方法进行严格验证。
    *   **亲和力 (Affinity)**：抗体与目标蛋白的结合能力要足够强，以确保能有效富集目标复合物。
    *   **验证用于ChIP (ChIP-validated)**：最好选用经过验证可用于ChIP实验的商业抗体。供应商通常会提供验证数据。自行验证时，需要确保抗体在ChIP条件下能有效工作。
    *   **批次一致性 (Lot-to-lot consistency)**：不同批次的抗体性能可能存在差异，对于长期项目或需要比较不同实验结果时，需要注意。

*   **实验对照设计 (Experimental Controls)**
    *   **Input DNA Control**：取少量片段化后的起始染色质（未进行IP），与IP样本平行处理（解交联、DNA纯化、文库构建、测序）。Input代表了基因组各个区域的基础可及性、片段化偏好性和测序偏好性，是**标准化**IP信号和**评估背景噪音**的重要基线。
    *   **阴性对照抗体 (Negative Control Antibody, e.g., IgG control)**：使用与目标抗体同种属、同亚型的非特异性免疫球蛋白（如IgG）进行平行IP。IgG对照用于评估由抗体或磁珠引起的非特异性结合水平。对于某些目标（如高丰度蛋白），IgG对照可能不是最佳选择，有时会使用敲除/敲低目标蛋白的细胞作为对照，或使用针对无关蛋白的抗体。
    *   **阳性对照位点 (Positive Control Loci, Optional)**：如果已知目标蛋白会结合某些特定基因组区域，可以通过qPCR检测这些区域在IP样本中的富集情况，作为实验成功的初步判断。

*   **技术变体 (Technical Variations)**
    *   **Native ChIP (N-ChIP)**：不使用交联剂，适用于研究组蛋白等与DNA结合紧密的蛋白。通常使用MNase酶切片段化。
    *   **ChIP-exo**：在ChIP后使用λ外切酶消化DNA片段，能够更精确地定位蛋白结合的边界，分辨率更高（接近单碱基）。
    *   **ChIP-nexus**：结合了ChIP-exo的酶切和特定的文库构建策略，进一步提高分辨率和信噪比。
    *   **CUT&RUN (Cleavage Under Targets and Release Using Nuclease)** 和 **CUT&Tag (Cleavage Under Targets and Tagmentation)**：是较新的替代技术，使用抗体引导的酶（MNase或Tn5转座酶）在原位切割或标记目标蛋白附近的DNA，具有低背景、低细胞起始量、高效率等优点。

### 2. ChIP-seq实验设计考虑因素

*   **生物学重复设计 (Biological Replicates)**：**至关重要**。用于评估实验结果的生物学变异和可靠性。建议至少设置**2-3个**独立的生物学重复（即从不同的细胞培养批次或不同的个体样本开始）。结果的一致性是结论有效性的基础。
*   **测序深度要求 (Sequencing Depth)**：取决于目标蛋白的性质（丰度、结合模式）和基因组大小。对于点状结合的转录因子，通常需要**20-50 million reads** (有效比对上的读段) per replicate。对于广泛分布的组蛋白修饰（如H3K27me3），可能需要更高的深度（**50-100 million reads** or more）。Input对照的深度通常建议与IP样本相当或稍低。
*   **片段大小选择 (Fragment Size Selection)**：片段化后的DNA大小分布影响峰（peak）的分辨率。理想的片段大小范围通常在**100-500 bp**之间。文库构建过程中通常会有片段大小选择步骤（如磁珠分选或凝胶回收）。过长或过短的片段都可能影响结果。
*   **抗体特异性验证 (Antibody Specificity Validation)**：如前所述，这是实验成败的关键。必须在使用前通过可靠方法（如WB）确认抗体能识别正确大小的目标蛋白，且最好有ChIP级别的验证数据。
*   **常见技术挑战与解决方案 (Common Challenges and Solutions)**
    *   **低信噪比 (Low Signal-to-Noise Ratio)**：原因可能是抗体质量差、IP条件不佳、洗涤不充分、细胞量不足等。解决方案：优化抗体浓度、孵育时间、洗涤条件；增加细胞起始量；使用更高质量的抗体。
    *   **PCR扩增偏好性 (PCR Amplification Bias)**：文库构建中的PCR步骤可能导致某些片段被过度扩增。解决方案：优化PCR循环数；使用PCR-free文库构建方法（如果起始DNA量足够）；在数据分析中去除PCR duplicates。
    *   **批次效应 (Batch Effects)**：不同时间、不同人员操作或使用不同批次试剂可能引入系统性差异。解决方案：合理设计实验批次，尽量将需要比较的样本放在同一批次处理；在数据分析中使用批次校正方法。
    *   **片段化偏好性 (Fragmentation Bias)**：超声或酶切可能对某些基因组区域（如GC含量高/低区域）有偏好。Input对照有助于校正这种偏好。

### 3. ChIP-seq数据分析流程

![染色质免疫沉淀数据分析流程](images/chipseq_analysis.svg)
*(Figure: A typical bioinformatics workflow for ChIP-seq data analysis, starting from raw sequencing reads to peak calling, annotation, and downstream analyses.)*

*   **数据质控与预处理 (Quality Control and Preprocessing)**
    *   **原始数据质控 (Raw Read Quality Check)**：使用**FastQC**等工具评估测序数据的质量，检查指标包括碱基质量分数、GC含量分布、接头序列污染、序列重复水平等。
    *   **序列修剪 (Read Trimming)**：使用**Trimmomatic**或**Cutadapt**等工具去除低质量碱基（通常从读段末端）和测序接头序列（adapter contamination）。
    *   **rRNA等污染序列过滤 (Filtering Contaminants, Optional)**：根据需要，可以比对到rRNA数据库等，过滤掉污染序列。

*   **参考基因组比对 (Genome Alignment/Mapping)**
    *   **比对工具选择 (Aligner Choice)**：常用的比对工具有**Bowtie2**, **BWA (Burrows-Wheeler Aligner)**。它们能有效地将短读段比对到参考基因组上。选择时考虑速度、内存占用和比对准确性。
    *   **比对参数设置**：允许一定的错配（mismatches）和插入缺失（indels）以适应测序错误和基因组变异。
    *   **多重比对处理 (Handling Multi-mapped Reads)**：一个读段可能比对到基因组的多个位置（常见于重复序列区域）。处理策略包括：只保留唯一比对的读段（unique mappers, 常用）；随机分配到其中一个位置；按比对质量加权分配等。选择哪种策略取决于下游分析需求。
    *   **输出格式**：比对结果通常以SAM (Sequence Alignment/Map) 或其二进制压缩格式BAM (Binary Alignment/Map) 文件形式存储。需要对BAM文件进行**排序 (Sorting)** 和 **索引 (Indexing)** (使用**samtools**)。

*   **数据过滤与质量评估 (Post-alignment Filtering and Quality Assessment)**
    *   **去除PCR重复 (Removing PCR Duplicates)**：由于PCR扩增，完全相同的DNA片段可能被测序多次，形成PCR duplicates。这些重复会人为地抬高信号，需要被识别并去除（或标记）。常用工具是**Picard MarkDuplicates**或**samtools markdup**。它们基于比对到基因组的起始和终止坐标来判断是否为duplicate。**注意**：对于天然存在的高度重叠信号（如某些高深度测序或特殊实验设计），过度去重可能丢失真实信号。
    *   **比对质量过滤 (Mapping Quality Filtering)**：可以过滤掉比对质量分数（MAPQ）过低的读段，以提高信号的可靠性。
    *   **评估ChIP效率和信噪比**：使用**deepTools**等工具计算富集分数（如SES）、绘制信号谱图（profile plots around TSS or other features）、进行主成分分析（PCA）、计算样本间相关性（correlation heatmap）等，评估实验重复性、信噪比和与预期的符合程度（如特定组蛋白修饰应在特定基因组区域富集）。常用指标有**FRiP score (Fraction of Reads in Peaks)**。

*   **峰检测 (Peak Calling)**
    *   **目标**：识别基因组上相对于背景（通常是Input对照）显著富集的区域，这些区域被认为是蛋白质结合或表观修饰存在的位点（即“峰”，peaks）。
    *   **常用工具**：
        *   **MACS2 (Model-based Analysis of ChIP-Seq 2)**：最广泛使用的工具之一，适用于检测转录因子等点状信号源（narrow peaks）和部分组蛋白修饰（可设置`--broad`模式检测broad peaks）。它使用泊松分布或负二项分布模型来评估IP信号相对于局部背景（由Input或扩展的IP读段估计）的富集显著性。
        *   **HOMER (Hypergeometric Optimization of Motif EnRichment)**：不仅进行peak calling，还整合了motif分析等功能。
        *   **SICER/EPIC2**：专门设计用于检测如H3K27me3、H3K9me3等覆盖范围较广的组蛋白修饰（broad peaks or domains）。
    *   **关键概念**：
        *   **信号富集区域识别 (Signal Enrichment Identification)**：通过滑窗或基于模型的方法扫描基因组，找到读段密度高的区域。
        *   **背景模型构建 (Background Modeling)**：利用Input对照数据或根据IP样本自身估计局部或全局的背景噪音水平。
        *   **统计显著性评估 (Statistical Significance Assessment)**：计算每个潜在peak的p-value（富集相对于背景的概率）和FDR (False Discovery Rate, 如q-value) 来控制假阳性。通常使用q-value < 0.05 或 0.01 作为阈值。
    *   **输出**：Peak calling结果通常是BED或类似格式的文件，包含每个peak的染色体位置、起始终止坐标、峰顶位置、富集倍数（fold enrichment）、p-value、q-value等信息。

*   **差异结合分析 (Differential Binding Analysis)**
    *   **目标**：比较不同实验条件或细胞类型（例如，处理组 vs. 对照组）下蛋白质结合或表观修饰水平的差异。
    *   **常用工具**：**DiffBind**, **edgeR**, **DESeq2** (这些最初为RNA-seq设计，但可应用于ChIP-seq的read counts in peaks), **csaw**。
    *   **方法**：通常先合并所有样本的peak集得到一个共识peak集（consensus peak set），然后计算每个样本在这些共识peak区域内的读段数（read counts），最后使用统计模型（通常基于负二项分布）来检验差异显著性。需要考虑生物学重复和标准化。

*   **结合位点注释与功能分析 (Peak Annotation and Functional Analysis)**
    *   **注释 (Annotation)**：将鉴定出的peaks关联到基因组特征上，如启动子（TSS附近）、基因体（内含子、外显子）、增强子、CpG岛、重复序列等。常用工具：**HOMER annotatePeaks.pl**, **ChIPseeker**, **BEDTools intersect**。
    *   **功能富集分析 (Functional Enrichment Analysis)**：对与peaks关联的基因（通常是最近的基因或通过其他证据关联的基因）进行GO (Gene Ontology) 或KEGG pathway富集分析，以推断目标蛋白可能参与的生物学过程。常用工具：**DAVID**, **Metascape**, **g:Profiler**, R/Bioconductor包（如**clusterProfiler**）。

*   **结合模式与序列分析 (Binding Pattern and Sequence Analysis)**
    *   **Motif分析 (Motif Discovery and Enrichment)**：对于转录因子的ChIP-seq数据，分析peaks中心区域富集的DNA序列模式（motifs），可以鉴定或验证TF的结合基序（binding motif）。常用工具：**MEME-ChIP**, **HOMER findMotifsGenome.pl**。
    *   **峰形状分析 (Peak Shape Analysis)**：不同类型的蛋白或修饰可能产生不同形状的峰，分析峰形可以提供额外信息。
    *   **可视化 (Visualization)**：使用基因组浏览器（如**IGV**, **UCSC Genome Browser**）查看特定区域的信号分布和peaks。使用**deepTools**等绘制信号的热图（heatmap）和平均谱图（profile plot）来展示整体结合模式。

### 4. 主要峰检测算法原理简介

*   **MACS2算法 (Model-based Analysis of ChIP-Seq 2)**
    *   **核心思想**：通过比较ChIP样本与对照样本（Input或IgG）的读段分布来识别显著富集的区域。
    *   **局部背景建模 (Local Background Modeling)**：MACS2考虑了基因组不同区域的背景噪音可能不同。它通过在目标区域周围选择不同大小的窗口（如1kb, 5kb, 10kb）来估计局部偏置（local bias），并建立动态的泊松分布模型作为局部背景。
    *   **峰位移模型 (Shifting Model)**：由于测序读段来自DNA片段的两端，MACS2会估计正链和负链读段分布中心之间的平均距离（即片段长度的一半，d），然后将所有读段向其来源片段的中心移动d/2距离，从而更精确地定位结合事件的中心。
    *   **显著性检验**：基于局部背景模型（泊松分布），计算每个候选区域的p-value。然后使用Benjamini-Hochberg方法计算FDR（q-value）来控制多重检验的假阳性率。
    *   **宽峰检测 (`--broad`模式)**：对于组蛋白修饰等宽峰，MACS2可以连接邻近的显著区域，并计算更适合宽峰的统计量。

*   **HOMER算法 (Hypergeometric Optimization of Motif EnRichment)**
    *   **特点**：不仅是peak caller，更是一个集成了多种基因组分析功能的套件。
    *   **Peak Calling原理**：也基于比较IP与Input信号的富集程度。它使用标准化的标签计数，并考虑了局部背景。其显著性评估可能基于超几何分布或泊松分布。
    *   **整合Motif分析**：HOMER的一个强项是在peak calling后直接进行motif发现和富集分析。

*   **SICER / EPIC2 (适用于宽峰, Broad Peaks)**
    *   **设计初衷**：专门用于检测像H3K27me3这样可以覆盖数kb甚至Mb范围的“宽”或“域状”组蛋白修饰。传统peak caller可能将一个宽域切成多个小峰。
    *   **SICER原理**：通过识别基因组上连续的、信号高于背景（基于Input估计）的“岛屿”（islands），并考虑了岛屿之间的“间隙”（gaps）。它使用统计模型（如泊松分布）来评估这些岛屿的显著性。
    *   **EPIC2 (原名EPIC)**：是另一个优化用于宽峰检测的工具，速度较快，内存效率较高。

*   **算法选择建议**
    *   **转录因子 (TF) 或点状信号**：**MACS2**（默认模式）是标准且常用的选择。**HOMER**也是一个不错的选择，特别是如果后续需要进行motif分析。
    *   **尖锐的组蛋白修饰 (e.g., H3K4me3, H3K27ac)**：**MACS2**（默认模式）通常效果很好。
    *   **宽泛的组蛋白修饰 (e.g., H3K27me3, H3K9me3, H3K36me3)**：**MACS2**（使用`--broad`选项），**SICER**，或**EPIC2**。选择哪一个可能取决于具体数据和偏好，有时可以尝试比较不同工具的结果。
    *   **CUT&RUN / CUT&Tag 数据**：这些技术产生的信号通常更清晰，背景更低。可以使用**MACS2**，但有时需要调整参数（如使用更小的基因组有效大小比例，关闭移位模型或调整移位大小）。也有专门为这些技术设计的工具，如**SEACR**。

### 5. ChIP-seq数据质量评估 (Quality Assessment Metrics)

*   **信噪比相关指标 (Signal-to-Noise Ratio Metrics)**
    *   **FRiP Score (Fraction of Reads in Peaks)**：落在鉴定出的peaks区域内的读段数占总比对读段数的比例。FRiP值越高，通常表示ChIP富集效率越高，信噪比越好。对TF ChIP-seq，FRiP > 1% 通常被认为是可接受的，好的实验可能达到5-20%甚至更高。对于组蛋白修饰，期望值会根据修饰的普遍性而变化。
    *   **富集倍数 (Fold Enrichment)**：Peak calling软件通常会报告每个peak相对于背景的富集倍数。检查富集倍数的分布可以了解信号强度。
    *   **可视化检查 (Visual Inspection)**：在基因组浏览器中查看信号轨迹（track），比较IP和Input样本在已知阳性位点和阴性区域的信号强度差异。

*   **片段长度分布 (Fragment Length Distribution)**：通过**Picard CollectInsertSizeMetrics**或**deepTools bamPEFragmentSize**等工具计算。对于交联ChIP-seq，片段长度通常呈单峰或多峰分布（反映单核小体、双核小体等大小），峰值大约在150-300 bp左右。异常的分布（如过短或过长）可能提示片段化或大小选择步骤存在问题。

*   **重复性评估 (Reproducibility Assessment)**
    *   **相关性分析 (Correlation Analysis)**：计算生物学重复样本之间全基因组信号（binned read counts）或peaks区域内信号的相关性（通常使用Pearson或Spearman相关系数）。重复样本间应有高相关性（如 R > 0.8-0.9）。
    *   **主成分分析 (PCA)**：观察样本在PCA图上的聚类情况，生物学重复应该聚在一起，不同处理组应能分开。
    *   **IDR (Irreproducible Discovery Rate)**：一种更严格的评估重复性的统计方法，用于确定在不同重复实验中都一致可信的peaks列表。

*   **峰数量与分布 (Peak Number and Distribution)**
    *   **峰数量**：检测到的峰的数量应该在预期范围内（取决于目标蛋白/修饰的性质和细胞类型）。数量过少可能表示实验失败，过多（且很多是低质量峰）可能表示背景噪音高或阈值设置不当。
    *   **峰的基因组分布**：检查peaks在基因组特征（启动子、基因体、增强子、基因间区等）上的分布是否符合预期（如H3K4me3应主要在启动子，H3K36me3应在基因体）。

*   **与已知结合模式比较 (Comparison with Known Binding Patterns)**
    *   如果目标蛋白或修饰已有公开数据或已知生物学功能，可以将结果与之比较。例如，绘制信号在TSS（转录起始位点）、TES（转录终止位点）或已知增强子区域的平均谱图（profile plot），看是否符合预期的模式。

*   **富集模式分析 (Enrichment Profile Analysis)**
    *   使用**deepTools**等绘制信号在特定基因组区域（如TSS、gene body、enhancers）或所有peaks周围的热图（heatmap）和平均谱图（profile plot）。这些图可以直观地展示结合模式、信号强度和峰形。

### 6. ChIP-seq结果解读与下游分析

*   **峰与基因关联 (Peak-Gene Association)**：确定每个peak可能调控的靶基因。最简单的方法是关联到最近的基因TSS。更复杂的方法考虑染色质相互作用数据（如Hi-C）或基因表达与peak信号的相关性。
*   **结合位点序列分析 (Sequence Analysis of Binding Sites)**：对于TF ChIP-seq，进行**Motif分析**以鉴定或确认TF的DNA结合基序。分析motif在peaks中的分布（如是否集中在峰顶）。
*   **识别共结合因子 (Identifying Co-binding Factors)**：比较不同TF的ChIP-seq数据，寻找共定位（co-localization）的peaks，可能提示这些TF协同作用。
*   **与基因表达数据整合 (Integration with Gene Expression Data, e.g., RNA-seq)**：
    *   分析peaks附近基因的表达水平变化，研究蛋白质结合或表观修饰与基因转录调控的关系（如，某TF结合增强子后，靶基因表达上调）。
    *   将细胞按表观特征（如某修饰水平）和基因表达聚类。
    *   构建基因调控网络模型。
*   **功能富集分析 (Functional Enrichment Analysis)**：对差异结合peaks关联的基因或所有peaks关联的基因进行GO/KEGG分析，揭示该蛋白/修饰可能参与的生物学通路或功能。
*   **比较不同条件下的表观状态 (Comparing Epigenetic Landscapes)**：通过差异结合分析和可视化，描述在不同细胞类型、发育阶段或疾病状态下，表观遗传模式如何变化。

**总结 (Section Summary)**

ChIP-seq是研究体内蛋白质-DNA相互作用（如TF结合、组蛋白修饰）的核心技术。其流程包括实验（ChIP）和数据分析两部分。成功的ChIP-seq需要精心设计实验（特别是抗体选择和对照设置）和严谨的数据分析流程（包括质控、比对、peak calling、差异分析和下游功能解读）。理解其原理、掌握分析方法、注意质量评估是准确解读ChIP-seq结果的关键。

## ATAC-seq技术原理与应用

### 1. ATAC-seq技术原理

![转座酶可及染色质测序原理](images/atacseq_principle.svg)
*(Figure: Principle of ATAC-seq. The hyperactive Tn5 transposase inserts sequencing adapters into open chromatin regions, effectively fragmenting the DNA and tagging accessible sites simultaneously.)*

*   **转座酶介导的标记技术 (Transposase-mediated Tagging)**
    *   ATAC-seq (Assay for Transposase-Accessible Chromatin using sequencing) 是一种利用**超活性Tn5转座酶 (hyperactive Tn5 transposase)** 来探测**染色质可及性 (chromatin accessibility)** 的高通量测序技术。
    *   核心原理：Tn5转座酶在体外被预先加载（pre-loaded）上测序接头（sequencing adapters），形成**转座复合物 (transposome)**。当将这些转座复合物加入到细胞核（或完整的细胞，需先进行通透处理）中时，Tn5会优先进入并“攻击”（切割并插入接头）**染色质开放区域 (open chromatin regions)** 的DNA。染色质致密区域（如紧密包裹在核小体上的DNA）则不易被Tn5接近和切割。
    *   因此，ATAC-seq通过一次酶反应，同时完成了**染色质片段化 (fragmentation)** 和 **测序接头连接 (adapter ligation)** 这两个步骤，并且这种片段化和标记是偏好发生在染色质可及区域的。

*   **Tn5转座酶作用机制 (Mechanism of Tn5 Transposase)**
    *   Tn5转座酶本身是一种细菌来源的DNA转座酶。经过改造后的超活性形式（如 Nextera Tn5）具有很高的催化效率。
    *   它以二聚体的形式结合DNA，并在结合位点进行切割，然后将自身携带的DNA片段（在这里是测序接头）插入到切割位点，这个过程称为“**tagmentation**”（fragmentation + tagging）。
    *   反应通常会在DNA两端留下9bp的间隔（gap），并在插入片段两侧产生9bp的靶位重复序列（Target Site Duplication, TSD），但这些细节在标准ATAC-seq分析中通常不直接利用。

*   **开放染色质区域优先标记 (Preferential Tagging of Open Chromatin)**
    *   基因组中并非所有DNA都具有相同的可及性。包裹在核小体上或处于高度压缩状态的DNA不易被大分子（如Tn5转座酶）接触。
    *   启动子、增强子、绝缘子等调控元件区域，为了让转录因子等蛋白能够结合，通常处于相对“开放”或“核小体稀疏”的状态。
    *   因此，Tn5转座酶能够优先进入这些开放区域进行切割和接头插入。测序后，这些区域会产生更多的测序读段（reads），形成信号峰（peaks）。

*   **与DNase-seq、MNase-seq、FAIRE-seq的比较**
    *   **DNase-seq (DNase I hypersensitive sites sequencing)**：使用DNase I 酶优先切割染色质开放区域（对DNase I超敏感位点, DHSs）。需要优化酶切浓度和时间，步骤相对繁琐。
    *   **MNase-seq (Micrococcal Nuclease sequencing)**：使用MNase酶优先切割核小体之间的连接DNA（linker DNA）。主要用于研究核小体定位，也能反映部分开放区域信息（如启动子处的NDR）。对酶切程度非常敏感。
    *   **FAIRE-seq (Formaldehyde-Assisted Isolation of Regulatory Elements)**：通过甲醛交联、超声破碎、酚氯仿抽提来富集开放染色质区域（因为蛋白质含量相对较低，不易被交联到大复合物中，倾向于留在水相）。
    *   **ATAC-seq优势**：相比之下，ATAC-seq具有**实验流程简单快速**（通常几小时内完成）、**所需细胞起始量低**（可低至几百到几万个细胞，甚至单细胞）、**信噪比较高**等显著优点，使其迅速成为研究染色质可及性的主流技术。

*   **技术优势与局限性 (Advantages and Limitations)**
    *   **优势 (Advantages)**：
        *   **快速简单 (Fast and Simple)**：实验流程大大简化，耗时短。
        *   **低起始量 (Low Input Requirement)**：适用于稀有细胞类型或临床样本。已成功应用于单细胞（scATAC-seq）。
        *   **高信噪比 (High Signal-to-Noise Ratio)**：通常能产生清晰的信号峰。
        *   **提供多重信息 (Provides Multiple Layers of Information)**：除了开放区域位置，片段长度分布还能反映核小体定位信息，峰内信号模式可用于转录因子足迹分析。
    *   **局限性 (Limitations)**：
        *   **线粒体DNA污染 (Mitochondrial DNA Contamination)**：Tn5也能进入线粒体并切割线粒体DNA（mtDNA），因为mtDNA没有组蛋白保护，是高度“可及”的。mtDNA reads可能占总reads的很大比例（有时高达20-70%），需要在分析中处理。
        *   **Tn5插入偏好性 (Tn5 Insertion Bias)**：Tn5酶本身对某些DNA序列可能存在一定的插入偏好性，尽管通常认为这种偏好性相对较弱。
        *   **细胞状态敏感性 (Sensitivity to Cell State)**：细胞的活力、细胞核的完整性等对实验结果影响较大。
        *   **数据分析需要特殊考量**：如处理线粒体污染、利用片段长度信息等。

### 2. ATAC-seq实验设计

*   **样本制备关键步骤 (Key Steps in Sample Preparation)**
    1.  **细胞获取与计数 (Cell Isolation and Counting)**：获取新鲜、高活力的细胞悬液。准确计数细胞数量是保证后续反应体系合适的关键。
    2.  **细胞核分离 (Nuclei Isolation, Optional but Recommended)**：通常推荐先分离出细胞核，可以减少线粒体污染，并使Tn5更容易接触核内染色质。需要温和的裂解条件以保持细胞核完整。
    3.  **转座反应 (Tagmentation Reaction)**：将细胞核（或通透化的全细胞）与预装载接头的Tn5转座酶在优化的缓冲液中孵育。反应时间（如30-60分钟）和温度（如37°C）需要精确控制。
    4.  **DNA纯化 (DNA Purification)**：终止转座反应，并使用试剂盒（如柱纯化或磁珠纯化）回收被标记的DNA片段。
    5.  **PCR扩增 (PCR Amplification)**：使用带有index（用于样本区分）和测序通用引物序列的PCR引物，对标记的DNA片段进行有限次数的PCR扩增，以获得足够量的测序文库。PCR循环数需要优化，避免过度扩增。
    6.  **文库纯化与质控 (Library Purification and QC)**：纯化PCR产物，去除引物二聚体等杂质。通过Qubit定量、Agilent Bioanalyzer或TapeStation检测文库浓度和片段大小分布。

*   **细胞数量要求 (Cell Number Requirement)**
    *   标准Bulk ATAC-seq通常推荐使用**50,000 - 100,000**个细胞，但根据优化方案，可以低至**500 - 5,000**个细胞。
    *   对于单细胞ATAC-seq (scATAC-seq)，则是对单个细胞进行操作。

*   **转座酶浓度优化 (Optimization of Transposase Concentration)**
    *   Tn5酶的量需要与细胞核（或细胞）的数量相匹配。过量的酶会导致染色质被过度切割，产生过多的小片段，丢失核小体定位信息，甚至切割封闭区域。酶量不足则导致标记效率低，信号弱。
    *   通常需要根据细胞类型和数量进行**滴定实验 (titration)** 来确定最佳酶浓度。

*   **测序深度考虑 (Sequencing Depth Considerations)**
    *   对于Bulk ATAC-seq，推荐获得**~50 million** 总读段数（total reads）per replicate。考虑到比对率和去除线粒体污染及PCR重复后的有效读段数（unique non-mitochondrial reads），通常目标是获得**25-50 million**有效读段。
    *   深度需求也取决于研究目标，如需要进行精细的转录因子足迹分析可能需要更高深度。

*   **质控指标 (Quality Control Metrics)**
    *   **文库片段大小分布 (Library Fragment Size Distribution)**：这是ATAC-seq最重要的QC指标之一。理想的分布应显示：
        *   一个大的峰在 < 100 bp (对应于**无核小体区域 NFR**，即开放区域被Tn5切割产生的短片段)。
        *   周期性的峰，大约在 ~200 bp (单核小体), ~400 bp (双核小体), ~600 bp (三核小体) 等位置，反映了核小体保护的DNA片段被切割的情况。这种周期性模式是实验成功的标志。
        *   如果只有小片段峰，或周期性不明显，可能表示过度片段化或细胞状态不佳。
    *   **线粒体DNA污染率 (Mitochondrial DNA Contamination Rate)**：比对后计算比对到线粒体基因组的读段比例。通常希望这个比例尽可能低（如 < 15-20%），但实际值受细胞类型和核分离效率影响很大。
    *   **TSS富集分数 (Transcription Start Site Enrichment Score)**：开放染色质通常在活跃基因的TSS区域富集。计算所有基因TSS中心区域的ATAC-seq信号相对于两侧翼区域信号的富集倍数。一个好的ATAC-seq实验通常显示明显的TSS富集（如富集分数 > 6-10）。

### 3. ATAC-seq数据分析流程

*   **数据质控与预处理 (QC and Preprocessing)**
    *   与ChIP-seq类似，使用**FastQC**进行原始数据质控，使用**Trimmomatic/Cutadapt**进行接头和低质量碱基修剪。

*   **参考基因组比对 (Genome Alignment)**
    *   使用**Bowtie2**或**BWA**将处理后的读段比对到参考基因组。ATAC-seq是**双端测序 (Paired-end sequencing)**，保留配对信息有助于后续的片段长度分析和精确的比对。
    *   同样需要处理多重比对读段，进行SAM/BAM转换、排序和索引。

*   **数据过滤与校正 (Post-alignment Filtering and Correction)**
    *   **去除PCR重复 (Remove PCR Duplicates)**：使用**Picard MarkDuplicates**或**samtools markdup**。
    *   **过滤低质量比对 (Filter Low Quality Alignments)**：去除MAPQ值低的读段。
    *   **去除线粒体读段 (Remove Mitochondrial Reads)**：将在比对步骤中比对到线粒体染色体（chrM）的读段移除。
    *   **移位校正 (Shift Correction, Optional but common)**：考虑到Tn5结合时占据一定空间并在结合位点两侧插入接头，有研究表明将正链读段向下游移动4bp，负链读段向上游移动5bp，可以更精确地反映Tn5切割事件发生的中心。

*   **开放区域检测 (Peak Calling)**
    *   **峰检测工具 (Peak Caller Choice)**：
        *   **MACS2**：虽然最初为ChIP-seq设计，但也可用于ATAC-seq peak calling。通常需要调整参数，如使用`--nomodel`（因为ATAC-seq的片段长度分布与ChIP-seq不同，MACS2的移位模型可能不适用或需要调整），可能需要指定`--shift`和`--extsize`或直接使用BAMPE模式。
        *   **Genrich**: 一个考虑了PCR重复等因素的peak caller，有时被推荐用于ATAC-seq。
        *   **HMMRATAC**: 使用隐马尔可夫模型（HMM）来识别开放区域，并能同时考虑信号强度和核小体定位模式。
    *   **参数优化**：需要根据数据特点调整p-value/q-value阈值。由于ATAC-seq信号动态范围广，有时会检测到大量peaks，需要根据信号强度或宽度进行筛选。
    *   **输出**：同样是BED等格式的peak文件。

*   **差异开放区域分析 (Differential Accessibility Analysis)**
    *   **目标**：比较不同条件下染色质可及性的差异。
    *   **常用工具**：与ChIP-seq类似，可以使用**DiffBind**, **edgeR**, **DESeq2**。它们基于在peaks区域内的读段计数进行统计检验。**csaw**是另一个基于滑窗策略的差异分析工具，不依赖于预先定义的peaks。
    *   **需要注意**：标准化方法对于ATAC-seq尤为重要，可能需要考虑文库大小、GC含量、TSS富集度等因素。

*   **染色质可及性定量 (Quantifying Chromatin Accessibility)**
    *   除了鉴定peaks，还可以对基因组进行分箱（binning）或关注特定区域（如启动子、增强子），计算这些区域内的标准化信号强度（如TPM, Reads Per Kilobase per Million mapped reads - RPKM/FPKM类似概念），作为该区域染色质可及性的量度。

*   **下游分析 (Downstream Analyses)**
    *   **Peak注释与功能富集**：同ChIP-seq。
    *   **Motif富集分析**：在ATAC-seq peaks中寻找富集的TF motifs，预测哪些TF可能在这些开放区域结合并发挥作用。
    *   **转录因子足迹分析 (Transcription Factor Footprinting)**：见下一节。
    *   **整合分析**：与RNA-seq, ChIP-seq, Hi-C等数据整合，构建更全面的基因调控模型。
    *   **可视化**：使用IGV等工具查看信号，使用deepTools绘制热图和谱图。

### 4. ATAC-seq数据特征与质量评估 (Data Characteristics and QA)

*   **核小体定位信号 (Nucleosome Positioning Signal)**
    *   如前所述，**插入片段大小分布 (Insert size distribution)** 的周期性是关键特征。使用**ATACseqQC**包或**deepTools**可以绘制此分布图。清晰的NFR峰（<100bp）和随后的单、双、三核小体峰（~200bp, ~400bp, ~600bp）表明高质量的染色质和成功的实验。

*   **转录起始位点(TSS)富集 (Transcription Start Site Enrichment)**
    *   活跃基因的TSS区域通常是高度开放的。计算TSS中心 +/- 几kb范围内的ATAC-seq信号平均谱图，应显示在TSS中心有明显的信号富集尖峰。TSS富集分数（如**deepTools plotEnrichment**或**ATACseqQC**计算）是衡量数据质量的重要指标。

*   **插入片段大小周期性 (Periodicity in Fragment Sizes)**：即上述核小体定位信号。

*   **与已知开放区域比较 (Comparison with Known Accessible Regions)**
    *   可以将检测到的ATAC-seq peaks与已知的DNase I超敏位点（DHSs）或其他细胞类型中的ATAC-seq数据（如来自ENCODE项目）进行比较，看重叠度是否符合预期。

*   **其他QC指标**：
    *   **比对率 (Alignment Rate)**：总读段中成功比对到参考基因组的比例。
    *   **有效读段比例 (Fraction of Useful Reads)**：去除PCR重复、线粒体读段、低质量比对后剩余的读段比例。
    *   **重复性**：生物学重复间的相关性（如使用**deepTools plotCorrelation/plotPCA**）。

### 5. ATAC-seq结果解读与应用

*   **开放染色质区域注释 (Annotation of Accessible Regions)**：将ATAC-seq peaks注释到基因组元件（启动子、增强子、基因体、基因间区等），了解哪些类型的调控区域是开放的。
*   **转录因子足迹分析 (Transcription Factor Footprinting)**
    *   **原理**：当一个转录因子（TF）紧密结合在DNA上时，它会保护其结合位点（TF binding site, TFBS）免受Tn5酶的切割。因此，在TF结合位点的精确位置，ATAC-seq信号会局部下降，形成一个“足迹”（footprint），而在足迹两侧则因为染色质更开放而有较高的切割频率。
    *   **分析**：需要高分辨率、高深度的ATAC-seq数据。通过专门的算法（如**HINT-ATAC**, **TOBIAS**, **CENTIPEDE**）结合已知的TF结合基序（motifs），可以识别基因组上的TF足迹，从而推断TF的实际结合活性（而不仅仅是位点的可及性）。
*   **染色质状态预测 (Chromatin State Prediction)**：结合ATAC-seq数据（可及性）与组蛋白修饰ChIP-seq数据，可以使用计算模型（如**ChromHMM**, **Segway**）将基因组划分为不同的染色质状态（如活跃启动子、强增强子、转录延伸、异染色质等），提供更全面的表观基因组图景。
*   **与表观修饰数据整合 (Integration with Epigenetic Modification Data)**：比较ATAC-seq信号与特定组蛋白修饰（如H3K27ac, H3K4me1, H3K4me3）的分布，可以更精确地定义活跃的启动子和增强子。
*   **调控元件识别与功能研究 (Regulatory Element Identification and Functional Studies)**：
    *   ATAC-seq是发现潜在的顺式调控元件（cis-regulatory elements）的有力工具。
    *   结合差异可及性分析和基因表达数据，可以推断这些元件在不同条件下的活性变化及其对基因表达的影响。
    *   识别出的潜在调控元件可以通过后续的功能实验（如报告基因实验、CRISPR编辑）进行验证。

**总结 (Section Summary)**

ATAC-seq是一种强大、高效的技术，用于绘制全基因组范围内的染色质可及性图谱。其原理基于Tn5转座酶优先标记开放染色质区域。该技术以实验简单、样本量需求低而著称，并能提供关于核小体定位和TF足迹的额外信息。数据分析需要关注特有的QC指标（如片段大小分布、TSS富集）和分析流程（如处理线粒体DNA、足迹分析）。ATAC-seq已成为表观基因组学研究中不可或缺的工具，尤其在鉴定调控元件和整合多组学数据方面发挥重要作用。

## 全基因组甲基化测序原理 (WGBS/BS-seq)

### 1. DNA甲基化基础

![DNA甲基化示意图](images/dna_methylation.svg)
*(Figure: Illustration of DNA methylation, primarily showing the addition of a methyl group to cytosine at CpG dinucleotides, and mentioning other contexts like CHG and CHH.)*

*   **甲基化位点类型 (Contexts of DNA Methylation)**
    *   **CpG**：在哺乳动物中，DNA甲基化最主要发生在胞嘧啶（Cytosine, C）后面紧跟着鸟嘌呤（Guanine, G）的二核苷酸序列上，即CpG dinucleotide。甲基基团加在胞嘧啶的第5位碳原子上，形成5-甲基胞嘧啶（5mC）。CpG位点通常是对称的（即两条链相应位置都有CpG），因此甲基化状态通常在DNA复制后可以被维持酶（如DNMT1）遗传给子链。
    *   **CHG 和 CHH (非CpG甲基化, non-CpG methylation)**：其中H代表A, T或C。非CpG甲基化在植物中非常普遍，在哺乳动物的某些特定细胞类型（如胚胎干细胞、神经元）和发育阶段也存在，且具有重要的调控功能。非CpG甲基化通常不对称，其维持机制与CpG甲基化不同。

*   **CpG岛与CpG岛岸/架 (CpG Islands, Shores, and Shelves)**
    *   **CpG岛 (CpG Islands, CGIs)**：基因组中一些长度在几百到几千bp的区域，其CpG二核苷酸的实际观测频率远高于基因组平均期望频率，并且GC含量较高。CGIs经常位于基因的启动子区域（大约覆盖了60-70%的人类基因启动子）。在正常体细胞中，大多数CGIs处于**非甲基化**状态，这与基因的表达潜能相关。
    *   **CpG岛岸 (CpG Island Shores)**：指紧邻CpG岛两侧，距离CpG岛边界约2kb以内的区域。研究发现，在发育和疾病（尤其是癌症）中，CpG岛岸的甲基化水平变化往往比CpG岛内部更为动态和显著，与基因表达变化关系密切。
    *   **CpG岛架 (CpG Island Shelves)**：指位于CpG岛岸之外，距离CpG岛边界2-4kb的区域。
    *   **开阔海域 (Open Sea)**：指基因组中远离CpG岛、CpG含量稀疏的区域。这些区域的CpG位点通常处于**甲基化**状态。

*   **甲基化在不同基因组区域的分布特征 (Distribution Patterns in Genomic Regions)**
    *   **启动子区域 (Promoters)**：位于CpG岛内的启动子通常是低甲基化的，这有利于基因表达。而非CpG岛启动子的甲基化状态则比较多变。启动子区域的高甲基化通常与基因的长期沉默相关。
    *   **基因体区域 (Gene Bodies)**：基因体（特别是外显子）的CpG甲基化水平通常较高，尤其是在活跃表达的基因中。基因体甲基化的功能尚不完全清楚，可能与抑制基因内的隐蔽启动子、调控选择性剪接、维持转录延伸稳定性等有关。
    *   **增强子区域 (Enhancers)**：活跃增强子通常表现为低甲基化状态，而失活或poised状态的增强子可能甲基化水平较高。增强子甲基化动态变化与细胞类型特异性基因表达有关。
    *   **重复序列区域 (Repetitive Elements)**：如LINEs, SINEs, LTRs等转座子元件，通常是高度甲基化的。这被认为是宿主基因组的一种防御机制，以抑制这些元件的转座活性，维持基因组稳定性。

*   **甲基化与基因表达调控 (Methylation and Gene Expression Regulation)**
    *   **启动子甲基化与转录抑制 (Promoter Methylation and Transcriptional Repression)**：
        *   **直接机制**：甲基化的CpG位点可以直接阻止某些对甲基化敏感的转录因子（如Sp1, c-Myc, E2F）与其DNA结合位点的结合。
        *   **间接机制**：甲基化的DNA可以被**甲基化CpG结合蛋白（Methyl-CpG-binding domain proteins, MBDs)**（如MeCP2, MBD1-4）识别和结合。这些MBD蛋白进而可以招募**辅抑制复合物（co-repressor complexes）**，这些复合物通常包含组蛋白去乙酰化酶（HDACs）和组蛋白甲基转移酶（如催化H3K9me3的酶），导致染色质结构致密化，从而抑制基因转录。
    *   **基因体甲基化与基因表达**：关系更为复杂。在某些情况下，基因体高甲基化与稳定、活跃的转录相关，但也可能参与调控选择性剪接等过程。
    *   **增强子甲基化与调控活性**：低甲基化通常与增强子活性相关。
    *   **维持基因组稳定性**：重复序列的高甲基化有助于抑制其转座，防止基因组插入突变和结构变异。

### 2. 甲基化测序技术类型

*   **全基因组亚硫酸氢盐测序 (Whole-Genome Bisulfite Sequencing, WGBS)**
    *   **原理与工作流程 (Principle and Workflow)**：
        1.  **DNA提取与片段化 (DNA Extraction and Fragmentation)**：提取基因组DNA，并将其片段化（通常通过超声）。
        2.  **末端修复、加A尾、连接接头 (End Repair, A-tailing, Adapter Ligation)**：与标准NGS文库构建类似，但通常使用**甲基化的测序接头**（即接头序列中的胞嘧啶是预先甲基化过的），以防止接头序列在后续亚硫酸氢盐处理中被转换。
        3.  **亚硫酸氢盐转换 (Bisulfite Conversion)**：将带有接头的DNA片段用亚硫酸氢钠（Sodium Bisulfite）处理。该处理能将**未甲基化的胞嘧啶（C）**高效地脱氨基转化为**尿嘧啶（Uracil, U）**，而**5-甲基胞嘧啶（5mC）**和**5-羟甲基胞嘧啶（5hmC）**（对标准BS处理）则很大程度上不受影响，保持为C。
        4.  **PCR扩增 (PCR Amplification)**：进行PCR扩增以富集文库。在PCR过程中，尿嘧啶（U）会作为模板被读成胸腺嘧啶（T），而未改变的胞嘧啶（C）仍然读成胞嘧啶（C）。
        5.  **测序 (Sequencing)**：对PCR产物进行高通量测序（通常是双端测序）。
        6.  **比对与甲基化状态判定 (Alignment and Methylation Calling)**：将测序读段比对到参考基因组。由于C可能转变为T，需要使用专门的比对软件（如Bismark, BSMAP）。比对后，在原始基因组的每个C位点，如果测序读段中对应位置是C，则推断该位点是甲基化的（5mC）；如果是T，则推断是未甲基化的。通过统计覆盖该位点的所有读段中C和T的数量，可以计算该位点的甲基化水平（methylation level = #C reads / (#C reads + #T reads)）。
    *   **覆盖度与分辨率 (Coverage and Resolution)**：WGBS旨在覆盖全基因组范围。理论上可以提供**单碱基分辨率 (single-base resolution)** 的甲基化信息。所需的测序深度通常较高（如30x覆盖度）以获得可靠的甲基化状态判定，尤其是对于等位基因特异性甲基化分析。
    *   **优势 (Advantages)**：提供全基因组、单碱基分辨率的甲基化图谱，是当前甲基化研究的“金标准”。可以同时检测CpG和非CpG甲基化。
    *   **局限性 (Limitations)**：**成本高昂**（需要深度测序）；亚硫酸氢盐处理会**降解DNA**，导致文库构建效率降低，需要相对较高的起始DNA量；处理过程可能引入**偏好性**；标准WGBS**不能区分5mC和5hmC**（两者在BS处理下都表现为C）。（注：有改良技术如oxBS-seq或TAB-seq可以区分5mC和5hmC）。

*   **简化代表性亚硫酸氢盐测序 (Reduced Representation Bisulfite Sequencing, RRBS)**
    *   **原理与工作流程 (Principle and Workflow)**：
        1.  **基因组DNA酶切 (Genomic DNA Digestion)**：使用对CpG位点敏感的限制性内切酶（通常是**MspI**）消化基因组DNA。MspI识别CCGG序列并切割，但不论内部的C是否甲基化都会切割（是HpaII的同裂酶，但HpaII对内部C的甲基化敏感）。
        2.  **片段大小选择 (Fragment Size Selection)**：通过凝胶电泳或磁珠筛选特定大小范围的DNA片段（如40-220 bp）。由于MspI切割位点富含CpG，这个步骤能够**富集基因组中CpG含量较高的区域**（如CpG岛、启动子）。
        3.  **文库构建与BS转换 (Library Prep and Bisulfite Conversion)**：对筛选出的片段进行末端修复、加A尾、连接甲基化接头，然后进行亚硫酸氢盐转换和PCR扩增。
        4.  **测序与分析 (Sequencing and Analysis)**：流程与WGBS类似，但测序读段只代表基因组中被MspI切割且大小合适的片段区域。
    *   **与WGBS的比较 (Comparison with WGBS)**：RRBS只测序基因组的一小部分（约1-10%，取决于片段选择），因此**成本显著降低**，所需测序深度也较低。它主要覆盖CpG岛、启动子等富含CpG的区域，对于研究这些关键调控区域的甲基化非常有效。但它**无法提供真正的全基因组覆盖**，会丢失大量CpG稀疏区域（如基因间区、部分增强子）的甲基化信息。
    *   **适用场景 (Suitable Applications)**：适用于大规模样本的研究（如流行病学研究、临床队列）、预算有限的情况、或研究重点在于CpG岛和启动子区域甲基化的项目。

*   **靶向甲基化测序 (Targeted Bisulfite Sequencing)**
    *   **原理**：结合亚硫酸氢盐处理与靶向捕获技术（如液相杂交捕获或多重PCR扩增）。先对基因组DNA进行BS转换，然后使用针对特定感兴趣区域（如候选基因启动子、特定增强子集、癌症相关基因区域等）设计的探针或引物，来富集这些区域的DNA片段，再进行测序。
    *   **优势**：能够以非常高的深度（>100x甚至1000x）对特定区域进行甲基化分析，成本远低于WGBS，且高于RRBS。非常适合对已知重要区域进行精细甲基化分析或验证。
    *   **局限性**：只能分析预先选择的靶区域。

*   **甲基化芯片技术 (Methylation Array Technology)**
    *   **代表**：Illumina Infinium HumanMethylationEPIC BeadChip（约850,000个探针，覆盖CpG岛、启动子、增强子、基因体等多种区域）是目前最常用的平台。
    *   **原理**：基因组DNA经过亚硫酸氢盐处理后，与芯片上的探针进行杂交。芯片上有针对特定CpG位点的两种探针设计：一种对应甲基化状态（转换后仍为C），另一种对应非甲基化状态（转换后变为T）。通过比较这两种探针的荧光信号强度比值（β值），来定量该CpG位点的甲基化水平（β值范围0到1，近似于甲基化百分比）。
    *   **优势**：**成本相对最低**（尤其是对于大样本量），标准化程度高，数据分析流程成熟，通量高。
    *   **局限性**：**只能检测芯片上设计的有限位点**（~85万个，远少于基因组中所有CpG位点），分辨率受探针设计限制，无法检测非CpG甲基化，可能存在探针结合效率差异和交叉杂交问题。

*   **甲基化免疫沉淀测序 (Methylated DNA Immunoprecipitation Sequencing, MeDIP-seq)**
    *   **原理**：使用特异性识别5mC的抗体，对片段化后的基因组DNA进行免疫沉淀，富集含有甲基化胞嘧啶的DNA片段。然后对富集到的DNA进行测序。
    *   **优势**：相对成本较低，不需要BS转换（避免了DNA降解），可以提供全基因组的甲基化富集区域信息。
    *   **局限性**：**分辨率较低**（通常是区域性富集，而不是单碱基分辨率），结果受抗体特异性和亲和力影响，富集效率可能与CpG密度相关，定量不如下一代BS-seq技术精确。

*   **技术选择总结 (Summary of Technology Choices)**
    *   **金标准/全基因组/单碱基分辨率**：**WGBS** (但最贵，数据量最大)。
    *   **成本效益/富集CpG区域/单碱基分辨率**：**RRBS** (覆盖不全)。
    *   **特定区域/高深度/单碱基分辨率**：**靶向BS-seq** (需要预先定义区域)。
    *   **大样本/低成本/固定位点/定量**：**甲基化芯片** (覆盖有限，分辨率低)。
    *   **全基因组/富集区域/无需BS转换**：**MeDIP-seq** (分辨率低，定量性差)。
    *   **区分5mC/5hmC**：需要**oxBS-seq**或**TAB-seq** (WGBS的变种) 或特定的酶学/化学方法。

### 3. 亚硫酸氢盐转换原理 (Principle of Bisulfite Conversion)

*   **化学反应机制 (Chemical Reaction Mechanism)**
    *   亚硫酸氢钠（Sodium Bisulfite, NaHSO3）在水溶液中与胞嘧啶（C）反应。首先，亚硫酸氢根离子加成到C的C5-C6双键上。然后，C4位的氨基发生水解，被羰基取代，形成尿嘧啶亚硫酸盐中间体。最后，在碱性条件下，亚硫酸根离去，C5-C6双键恢复，最终产物是尿嘧啶（U）。
    *   对于5-甲基胞嘧啶（5mC），其C5位被甲基占据，这**显著减慢了**第一步亚硫酸氢根的加成反应速率。在标准的反应条件下，5mC基本上不发生转换，仍然保持为C。
    *   对于5-羟甲基胞嘧啶（5hmC），在标准BS条件下，其转换行为比较复杂，部分可能被转化，但通常认为它也相对抗拒转化，表现得更像5mC。（这也是标准WGBS不能区分5mC和5hmC的原因）。

*   **非甲基化胞嘧啶 vs 甲基化胞嘧啶 (Unmethylated vs. Methylated Cytosine)**
    *   **结果**：经过BS处理和后续PCR扩增，原基因组DNA中的 **未甲基化C** 会在测序读段中显示为 **T**，而 **甲基化C (5mC)** 会显示为 **C**。

*   **转换效率评估 (Assessment of Conversion Efficiency)**
    *   **重要性**：亚硫酸氢盐转换的效率（即未甲基化C被成功转化为U/T的比例）对结果的准确性至关重要。如果转换不完全（如效率只有99%），那么会有1%的未甲基化C被错误地计为甲基化C，这对于低甲基化区域的分析会产生显著影响。
    *   **评估方法**：
        *   **加入外源对照 (Spike-in Controls)**：在样本DNA中加入少量已知序列且完全未甲基化的DNA（如Lambda噬菌体DNA或特定设计的质粒）。测序后，计算这些对照DNA序列中所有C位点被读成T的比例，即为转换效率。理想的转换效率应 > 99%。
        *   **利用内源非CpG位点 (Using Endogenous Non-CpG sites)**：在某些生物体或细胞类型中（如哺乳动物体细胞），非CpG位点的甲基化水平通常极低（接近于0）。可以计算CHG和CHH位点的平均“甲基化”水平（即C/(C+T)的比率），这个值应该非常接近于0，其偏差可以反映转换的不完全程度。
        *   **利用叶绿体/线粒体DNA (Using Chloroplast/Mitochondrial DNA)**：在植物（叶绿体）或动物（线粒体）样本中，这些细胞器DNA通常被认为是没有甲基化的，可以用来评估转换效率。

*   **技术偏好性与校正 (Potential Biases and Correction)**
    *   **DNA降解 (DNA Degradation)**：BS处理条件苛刻（低pH、高温），会导致DNA链断裂和降解，特别是对于长片段DNA。这可能导致文库复杂度下降，需要较高的起始DNA量。
    *   **GC含量偏好 (GC Content Bias)**：PCR扩增步骤可能对GC含量不同的片段有不同的扩增效率。
    *   **转换偏好 (Conversion Bias)**：某些序列上下文可能影响转换效率。
    *   **校正**：在数据分析中，可以通过加入对照、标准化、以及使用考虑这些偏好的统计模型来尽量减轻这些影响。

### 4. 甲基化测序数据分析流程

*   **数据质控特殊考虑 (Specific QC Considerations)**
    *   **原始数据质控 (Raw Read QC)**：同其他NGS数据，使用FastQC检查碱基质量、接头污染等。
    *   **转换效率评估 (Bisulfite Conversion Rate Estimation)**：如上所述，必须评估转换效率。通常在比对后进行计算。如果效率低于99%，需要谨慎解读结果。
    *   **序列复杂度降低问题 (Reduced Sequence Complexity)**：由于基因组中大量的C可能转变为T，BS处理后的DNA序列复杂度会降低（尤其是AT含量增加），这可能影响比对的唯一性和准确性。

*   **参考基因组比对挑战 (Alignment Challenges)**
    *   **非对称比对 (Asymmetric Alignment)**：由于只有C可能变成T，而T不会变成C，这种转换是不对称的。标准比对软件无法直接处理。
    *   **三字母比对策略 (Three-Letter Alignment Strategy)**：为了解决这个问题，BS-seq比对软件通常采用“三字母”比对策略。它们会：
        1.  将测序读段中的所有C都临时替换成T。
        2.  将参考基因组也进行两次“in silico”转换：一次是将所有C转为T（代表原始DNA的正链经过BS转换），另一次是将所有G转为A（代表原始DNA的负链经过BS转换后，其反向互补序列）。
        3.  将处理过的读段（C->T转换后）分别比对到这两个“三字母”参考基因组上。
        4.  根据最佳比对结果，推断出读段的原始来源链，并恢复原始的C/T信息。
    *   **专用比对工具 (Dedicated Aligners)**：必须使用专门为BS-seq数据设计的比对软件。常用工具包括：
        *   **Bismark**：目前最流行的工具之一，内部使用Bowtie/Bowtie2或HISAT2作为比对引擎。
        *   **BSMAP**：另一个广泛使用的BS-seq比对工具。
        *   **BWA-meth**: 基于BWA-mem的快速BS-seq比对器。
        *   **Last**: 也能用于BS-seq比对。
    *   **比对后处理**：比对得到的BAM文件需要进行排序、索引，并且通常需要去除PCR duplicates（使用专门考虑BS转换的工具，如Bismark的`deduplicate_bismark`）。

*   **甲基化水平计算 (Methylation Level Calculation)**
    *   **提取甲基化信息 (Extracting Methylation Calls)**：使用比对软件配套的工具（如Bismark的`bismark_methylation_extractor`）从比对结果（BAM文件）中提取每个胞嘧啶位点（CpG, CHG, CHH context）的甲基化状态信息。这通常会生成一个文本文件，列出每个C位点的坐标、上下文、甲基化读段数（C reads）和非甲基化读段数（T reads）。
    *   **计算甲基化水平 (Calculating Methylation Level)**：对于基因组中的每个胞嘧啶位点，其甲基化水平（或称为甲基化比例、甲基化分数）计算为：
        `Methylation Level = Number of C reads / (Number of C reads + Number of T reads)`
        这个值介于0（完全未甲基化）和1（完全甲基化）之间。通常需要设置一个最小覆盖度阈值（如5x或10x），只有达到这个阈值的位点才被认为甲基化水平是可靠的。

*   **差异甲基化区域 (Differentially Methylated Regions, DMRs) 检测**
    *   **目标**：识别在不同样本组（如疾病 vs. 对照，不同发育阶段）之间甲基化水平存在统计学显著差异的基因组区域。DMRs通常比单个差异甲基化位点（Differentially Methylated Positions, DMPs）更具生物学意义，因为区域性的甲基化改变更可能影响基因调控。
    *   **方法学挑战**：
        *   甲基化水平是比例数据（0-1之间），不是计数数据。
        *   覆盖度在不同位点和样本间可能差异很大。
        *   邻近的CpG位点甲基化状态通常是相关的（空间相关性）。
        *   需要处理生物学重复和变异。
    *   **常用方法/工具**：
        *   **基于平滑或区域的方法**：先对甲基化水平进行平滑处理或将基因组划分为区域（如tiling windows），然后对区域内的甲基化水平进行比较。如 **BSmooth**, **DSS (Dispersion Shrinkage for Sequencing data)**, **metilene**。
        *   **基于回归模型的方法**：使用Beta二项分布回归等模型来拟合甲基化水平，并检验组间差异。如 **DSS**, **RADmeth**, **methylKit**。
        *   **非参数方法**：如Wilcoxon秩和检验等，但可能需要对数据进行转换或分组。
    *   **考虑空间相关性**：好的DMR检测方法应该考虑到邻近CpG位点甲基化状态的相关性，而不是将每个位点视为独立的。
    *   **输出**：DMRs列表，包含区域坐标、平均甲基化差异、统计显著性（p-value, q-value）等信息。

*   **甲基化模式分析 (Methylation Pattern Analysis)**
    *   **全基因组甲基化谱 (Genome-wide Methylation Profile)**：计算全基因组或特定染色体的平均甲基化水平。绘制基因组范围的甲基化景观图。
    *   **基因组元件甲基化 (Methylation across Genomic Features)**：计算启动子、CpG岛、基因体、增强子、重复序列等不同基因组元件的平均甲基化水平，并进行比较。绘制这些元件周围的甲基化谱图（metagene plot）。
    *   **甲基化变异性分析 (Methylation Variability Analysis)**：研究样本间或细胞类型间甲基化水平的变异程度，识别高变甲基化区域（VM Rs）。
    *   **等位基因特异性甲基化 (Allele-Specific Methylation, ASM)**：结合SNP信息，分析同一个体中来自父本和母本的两条染色体在同一位点的甲基化状态是否存在差异。这与基因组印记等现象相关。

### 5. 甲基化数据解读与应用

*   **全基因组甲基化模式分析 (Genome-wide Pattern Analysis)**：了解样本的整体甲基化状态。例如，癌症中常观察到全基因组低甲基化（主要发生在基因间区和重复序列）和CpG岛高甲基化（发生在特定基因启动子）。
*   **基因区域甲基化特征 (Gene Region Methylation Features)**：
    *   启动子CpG岛的高甲基化通常与基因沉默相关。
    *   基因体甲基化模式可能与基因表达水平、选择性剪接有关。
    *   增强子区域的动态甲基化变化可能驱动细胞特异性基因表达。
*   **差异甲基化与疾病/生物学过程关联 (Linking DMRs to Disease/Biology)**：
    *   将检测到的DMRs注释到基因，并进行功能富集分析，推断差异甲基化可能影响的生物学通路。
    *   比较疾病样本与正常样本的DMRs，寻找潜在的疾病生物标志物或致病机制。例如，在癌症研究中寻找抑癌基因启动子的高甲基化或癌基因启动子的低甲基化。
    *   研究发育过程中DMRs的变化，理解细胞分化和谱系决定的表观遗传调控。
*   **与基因表达数据整合 (Integration with Gene Expression Data)**：
    *   分析DMRs附近基因的表达变化，验证甲基化改变是否导致了预期的转录调控效应（如启动子高甲基化 -> 表达下调）。
    *   构建整合模型，预测基因表达如何受到DNA甲基化和其他表观遗传标记的共同调控。
*   **表观遗传时钟/甲基化年龄预测 (Epigenetic Clock / Methylation Age Prediction)**：
    *   基于特定CpG位点的甲基化水平建立的数学模型，可以相当准确地预测个体的生物学年龄（甲基化年龄）。
    *   甲基化年龄与实际年龄的偏差（年龄加速或减速）被发现与多种健康状况和疾病风险相关。
*   **临床应用潜力 (Potential Clinical Applications)**：
    *   **生物标志物**：用于癌症等疾病的早期诊断（如检测血液中的ctDNA甲基化）、预后判断、治疗反应预测。
    *   **药物靶点**：针对DNA甲基转移酶（DNMTs）的抑制剂已被用于治疗某些血液肿瘤。

**总结 (Section Summary)**

DNA甲基化是关键的表观遗传修饰，在基因调控和基因组稳定性中发挥重要作用。亚硫酸氢盐测序（特别是WGBS和RRBS）是研究DNA甲基化的主要技术，能够提供单碱基分辨率的甲基化信息。其核心原理是BS转换能区分甲基化与未甲基化胞嘧啶。数据分析流程涉及专门的比对软件、甲基化水平计算、差异甲基化区域（DMR）检测以及丰富的下游分析。理解不同技术（WGBS, RRBS, 芯片等）的优缺点、掌握分析方法、并能将甲基化数据与生物学背景结合解读，对于深入理解表观遗传调控机制及其在健康与疾病中的作用至关重要。

## 表观修饰与基因表达调控关系

### 1. 表观修饰组合模式 (Combinatorial Patterns of Epigenetic Marks)

![表观修饰与基因表达调控](images/epigenetic_regulation.svg)
*(Figure: A conceptual diagram showing how combinations of different epigenetic marks (DNA methylation, various histone modifications) and chromatin accessibility contribute to defining different chromatin states associated with varying levels of gene expression, such as active promoter, poised enhancer, repressed region, etc.)*

*   **染色质状态定义 (Defining Chromatin States)**
    *   基因组并非只有“开”和“关”两种状态，而是存在多种功能不同的染色质状态（Chromatin States）。这些状态由**多种表观遗传标记的特定组合**来定义。
    *   不同的染色质状态与特定的基因组元件（如启动子、增强子、绝缘子、异染色质）和不同的转录活性水平相关联。
    *   计算工具（如**ChromHMM**, **Segway**）利用多种表观基因组数据（通常是多种组蛋白修饰的ChIP-seq数据，有时也包括DNA甲基化和染色质可及性数据）作为输入，通过机器学习算法（如隐马尔可夫模型 HMM 或动态贝叶斯网络 DBN）在全基因组范围内无监督地学习和注释出不同的染色质状态。

*   **组蛋白修饰组合模式 (“Histone Code” Hypothesis Revisited)**
    *   “组蛋白密码”假说提出，组蛋白上不同修饰的特定组合可以被“阅读器”蛋白识别，并导致特定的下游功能输出（如转录激活、抑制、DNA修复等）。
    *   **常见组合模式示例**：
        *   **活跃启动子 (Active Promoter)**：通常同时富集 **H3K4me3** 和 **H3K27ac** (或 H3K9ac)，且DNA甲基化水平低，染色质开放。
        *   **活跃增强子 (Active Enhancer)**：通常同时富集 **H3K4me1** 和 **H3K27ac**，染色质开放。
        *   **准备就绪/两价启动子 (Poised/Bivalent Promoter)**：同时具有激活标记 **H3K4me3** 和抑制标记 **H3K27me3**。常见于干细胞中调控发育的关键基因，这些基因表达水平低但准备好响应信号而被激活或稳定抑制。
        *   **异染色质/抑制区域 (Heterochromatin/Repressed Region)**：富集 **H3K9me3** (组成型) 或 **H3K27me3** (兼性)，通常伴随高DNA甲基化（尤其在H3K9me3区域）和致密的染色质结构。
        *   **转录延伸区域 (Transcribed Region)**：活跃转录基因的基因体通常富集 **H3K36me3**。
    *   这些组合不是绝对的，存在细胞类型和基因特异性。

*   **表观修饰与染色质开放状态的关系 (Interplay with Chromatin Accessibility)**
    *   染色质可及性（如ATAC-seq信号）是表观遗传调控的直接体现，反映了DNA对调控因子是否“可见”。
    *   激活性组蛋白修饰（如乙酰化, H3K4me3）通常与开放染色质相关。
    *   抑制性标记（如H3K27me3, H3K9me3）和DNA高甲基化通常与封闭染色质相关。
    *   染色质重塑因子、组蛋白修饰酶和DNA甲基化机器之间存在复杂的相互作用，共同决定染色质的开放状态。

*   **表观修饰与DNA甲基化的相互作用 (Crosstalk between Histone Modifications and DNA Methylation)**
    *   两者之间存在密切的双向调控关系：
        *   **组蛋白修饰指导DNA甲基化**：例如，H3K9me3可以被DNMT3（de novo甲基转移酶）识别，并引导在相应区域建立DNA甲基化。相反，某些激活性标记（如H3K4me3）可能抑制DNMT的活性，保护CpG岛不被甲基化。
        *   **DNA甲基化影响组蛋白修饰**：例如，MBD蛋白结合到甲基化的DNA上后，可以招募HDACs和HMTs（如催化H3K9me3的酶），从而建立抑制性的组蛋白修饰模式。
        *   **维持机制**：在DNA复制过程中，维持性DNA甲基转移酶（DNMT1）和可能存在的组蛋白修饰遗传机制协同作用，确保表观遗传状态的稳定传递。

### 2. 表观修饰与基因表达的关联分析 (Correlating Epigenetic Marks with Gene Expression)

*   **启动子区域表观修饰与表达 (Promoter Epigenetics and Expression)**
    *   **普遍规律**：启动子区域的 **低DNA甲基化**、**高H3K4me3**、**高H3K27ac/H3K9ac** 水平通常与 **高基因表达水平** 呈**正相关**。启动子区的**高DNA甲基化**、**高H3K27me3/H3K9me3** 则通常与 **基因沉默** 或 **低表达** 呈**负相关**。
    *   **分析方法**：计算基因启动子区域（如TSS上下游1-2kb）的平均表观修饰信号强度（或甲基化水平），并将其与对应基因的表达水平（如来自RNA-seq的TPM/FPKM值）进行相关性分析（如Spearman或Pearson相关）。可以绘制散点图或对基因按表达水平分组后比较其启动子表观特征。

*   **基因体区域表观修饰与表达 (Gene Body Epigenetics and Expression)**
    *   **H3K36me3**：通常在活跃转录基因的基因体区域富集，其水平与基因**表达水平呈正相关**，并被认为与转录延伸、RNA加工和抑制基因内隐蔽转录起始有关。
    *   **DNA甲基化 (Gene Body Methylation)**：关系更为复杂。在很多情况下，中等到高水平的基因体甲基化也与活跃表达相关，但其确切功能和机制仍在研究中。

*   **增强子区域表观修饰与表达 (Enhancer Epigenetics and Expression)**
    *   增强子通过与启动子形成染色质环来远程调控基因表达。
    *   **活跃增强子**（标记为 H3K4me1 + H3K27ac，且开放）附近的基因表达水平通常较高。
    *   **分析方法**：首先需要预测增强子-基因的连接关系（如基于距离、eQTL数据、Hi-C数据等）。然后分析增强子区域的表观修饰水平（特别是H3K27ac）与预测的靶基因表达水平之间的相关性。

*   **表观修饰与转录因子结合的协同作用 (Synergy between Epigenetics and TF Binding)**
    *   基因的最终表达水平是**TF结合**和**表观遗传状态**共同作用的结果。
    *   表观遗传状态（如染色质可及性、特定组蛋白修饰）决定了TF**能否结合**到其潜在的结合位点。
    *   TF结合后，可能进一步**招募**表观修饰酶，**改变**局部的表观遗传状态，从而稳定或增强其调控效果。
    *   整合分析TF ChIP-seq数据、表观基因组数据和基因表达数据，可以更全面地理解基因调控网络。

### 3. 多组学数据整合分析方法 (Integrative Multi-Omics Analysis Approaches)

*   **相关性分析 (Correlation Analysis)**：
    *   计算不同表观遗传标记之间、表观标记与基因表达之间、表观标记与TF结合之间的相关性。
    *   可以识别协同变化的标记或区域。

*   **聚类分析 (Clustering Analysis)**：
    *   **基于基因的聚类**：根据基因在不同样本中的表观遗传特征（如启动子甲基化、H3K4me3水平）和表达水平，将基因进行聚类，可以发现具有相似调控模式的基因群组。
    *   **基于样本的聚类**：根据样本的整体表观基因组谱或表达谱进行聚类，可以识别不同的细胞状态或疾病亚型。
    *   常用算法：层次聚类（Hierarchical Clustering）、K-means聚类、非负矩阵分解（NMF）等。

*   **降维与可视化 (Dimensionality Reduction and Visualization)**：
    *   使用PCA（主成分分析）、t-SNE（t-distributed Stochastic Neighbor Embedding）、UMAP（Uniform Manifold Approximation and Projection）等方法对高维的多组学数据进行降维，并在二维或三维空间中可视化样本或基因的分布，有助于识别模式和关系。

*   **网络分析 (Network Analysis)**：
    *   构建基因调控网络（Gene Regulatory Networks, GRNs），其中节点代表基因、TF或表观遗传因子，边代表它们之间的调控关系（如TF结合、表观修饰影响表达）。
    *   可以基于相关性、因果推断（如使用贝叶斯网络）或其他先验知识来构建网络。
    *   分析网络的拓扑结构（如hub节点、模块）可以揭示关键调控因子和通路。

*   **机器学习与预测模型 (Machine Learning and Predictive Modeling)**：
    *   **预测基因表达**：使用表观基因组特征（如启动子/增强子的组蛋白修饰、DNA甲基化、染色质可及性）作为输入特征，训练机器学习模型（如线性回归、随机森林、梯度提升树、深度神经网络）来预测基因的表达水平。模型的准确性可以反映表观特征对表达的预测能力。
    *   **预测调控元件功能**：基于序列特征和表观基因组特征预测某个区域是否为活跃的增强子或启动子。
    *   **预测疾病状态或亚型**：利用多组学特征训练分类器来区分不同疾病状态或亚型。

*   **染色质状态模型 (Chromatin State Modeling)**：
    *   如前述的ChromHMM、Segway等，整合多种ChIP-seq数据来注释全基因组的染色质状态，提供了一个简化的、功能性的基因组图谱。可以分析不同条件下染色质状态的动态变化及其与基因表达的关系。

### 4. 表观基因组与疾病研究 (Epigenomics in Disease Research)

*   **癌症表观基因组特征 (Cancer Epigenome Hallmarks)**
    *   **全基因组低甲基化 (Global Hypomethylation)**：主要发生在基因间区和重复序列，可能导致基因组不稳定和癌基因激活。
    *   **局部高甲基化 (Focal Hypermethylation)**：经常发生在抑癌基因启动子区域的CpG岛，导致这些基因沉默。
    *   **组蛋白修饰异常 (Aberrant Histone Modifications)**：组蛋白修饰酶（HATs, HDACs, HMTs, KDMs）的突变或表达异常，导致全基因组或特定位点组蛋白修饰模式改变，影响基因表达程序。
    *   **染色质重塑因子突变 (Mutations in Chromatin Remodelers)**：如SWI/SNF复合体成员的失活突变在多种癌症中常见。
    *   这些表观遗传改变是癌症发生发展的重要驱动力，也是潜在的诊断标志物和治疗靶点。

*   **发育疾病与表观修饰 (Developmental Disorders and Epigenetics)**
    *   许多遗传性发育综合征是由编码表观遗传调控因子（如DNMT3A, MeCP2, KMT2D等）的基因发生突变引起的（这类疾病被称为“染色质病” or "Chromatinopathies"）。
    *   基因组印记（Genomic imprinting）的异常（由于印记控制区ICR的甲基化状态改变）可导致Prader-Willi综合征、Angelman综合征等印记相关疾病。

*   **环境因素对表观基因组的影响 (Environmental Impact on the Epigenome)**
    *   **营养**：如叶酸、维生素B12等是甲基化代谢途径的关键辅因子，其摄入水平可能影响DNA甲基化模式。母亲孕期营养状况可能通过表观遗传机制影响后代的长期健康。
    *   **毒物暴露**：如空气污染物、重金属、内分泌干扰物等被发现可以诱导表观遗传改变。
    *   **生活方式**：如吸烟、饮酒、压力、运动等也可能影响表观基因组。
    *   **研究意义**：表观遗传学为理解环境因素如何影响疾病易感性提供了分子机制层面的解释（“环境表观基因组学”）。

*   **表观治疗靶点识别 (Identification of Epigenetic Therapeutic Targets)**
    *   **靶向表观遗传酶**：如前述的DNMT抑制剂（地西他滨等）和HDAC抑制剂（伏立诺他等）已在临床使用。针对其他表观修饰酶（如EZH2抑制剂、LSD1抑制剂、BET溴结构域抑制剂等）的药物也在开发和临床试验中。
    *   **利用表观遗传标志物**：用于指导治疗选择（如MGMT启动子甲基化状态预测胶质瘤对替莫唑胺的反应）。
    *   **表观遗传编辑**：未来可能利用CRISPR-dCas9融合表观修饰酶，对特定基因位点的表观状态进行精准编辑，作为一种新的治疗策略。

### 5. 表观基因组学前沿研究方向 (Frontiers in Epigenomics Research)

*   **单细胞表观基因组学 (Single-Cell Epigenomics)**：
    *   以前所未有的分辨率解析复杂组织或发育过程中细胞间的表观遗传异质性。
    *   开发同时检测单细胞内多种表观遗传模式（如scNMT-seq测甲基化、可及性、转录组）或结合其他组学（如空间信息）的技术。
    *   发展更强大的计算方法来处理单细胞表观数据的稀疏性和高维度。

*   **时空表观基因组学 (Spatio-temporal Epigenomics)**：
    *   结合空间转录组学/表观基因组学技术，在组织的原位研究表观遗传模式的空间分布及其动态变化。
    *   研究表观遗传在时间维度（如发育、衰老、对刺激的响应）上的动态调控机制（“第四维表观基因组学”）。

*   **表观编辑技术 (Epigenome Editing)**：
    *   利用CRISPR-Cas9系统（通常是催化失活的dCas9）融合不同的表观遗传“写入器”或“擦除器”结构域，实现对基因组特定位点表观修饰（如DNA甲基化、组蛋白修饰）的精确添加或移除。
    *   作为研究表观修饰因果关系（而不仅是相关性）的强大工具，并具有潜在的治疗应用前景。

*   **计算表观基因组学 (Computational Epigenomics)**：
    *   开发更先进的算法来处理和整合日益增长的多维度、多组学表观数据。
    *   利用人工智能（AI）和机器学习（ML）来识别复杂的表观遗传模式、预测基因调控关系、构建更精准的疾病诊断和预后模型。
    *   模拟染色质动态和表观遗传调控过程。

*   **非编码RNA与表观遗传的相互作用 (ncRNA-Epigenetics Crosstalk)**：
    *   深入研究lncRNA等非编码RNA如何精确地引导和调控表观遗传修饰复合物，以及它们在三维基因组结构组织中的作用。

*   **跨代（或代际）表观遗传 (Transgenerational Epigenetic Inheritance)**：
    *   研究环境暴露或亲代经历是否能通过表观遗传机制影响子代甚至孙代的表型，及其在人类中的证据和机制（这是一个复杂且有争议的领域）。

**总结 (Overall Summary)**

表观遗传修饰并非孤立存在，而是以复杂的组合模式协同作用，定义了不同的染色质状态，进而精细调控基因表达。整合分析多种表观基因组数据（ChIP-seq, ATAC-seq, BS-seq等）与基因表达数据（RNA-seq）是理解这一调控网络的关键。表观遗传失调在多种疾病（尤其是癌症和发育性疾病）中扮演重要角色，并受到环境因素的深刻影响。表观基因组学不仅深化了我们对生命过程的理解，也为疾病诊断和治疗开辟了新途径。单细胞、时空、表观编辑和计算方法的进步正推动该领域进入一个更加精准和动态的新时代。