<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGS高通量测序技术课程平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 英雄区域 */
        .hero {
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .course-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .course-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #ffd700;
        }

        .stat-card h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 课程网格 */
        .courses-section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .course-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .course-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .course-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .course-meta h3 {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .course-meta .course-code {
            color: #666;
            font-size: 0.9rem;
        }

        .course-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .course-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #888;
        }

        .course-buttons {
            display: flex;
            gap: 10px;
            width: 100%;
        }

        .course-button {
            display: inline-block;
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            border: none;
            cursor: pointer;
            flex: 1;
            font-size: 0.9rem;
        }

        .course-button.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .course-button.primary:hover {
            background: linear-gradient(135deg, #5a6de8, #6a4495);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .course-button.secondary {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .course-button.secondary:hover {
            background: linear-gradient(135deg, #218838, #1ea080);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        /* 页脚 */
        .footer {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 4rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            margin-bottom: 1rem;
            color: #667eea;
        }

        .footer-section a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #667eea;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .courses-grid {
                grid-template-columns: 1fr;
            }

            .course-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        /* DNA动画效果 */
        .dna-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .dna-helix {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, transparent, #667eea, transparent);
            animation: rotate 20s linear infinite;
        }

        .dna-helix:nth-child(1) { left: 10%; animation-delay: 0s; }
        .dna-helix:nth-child(2) { left: 30%; animation-delay: -5s; }
        .dna-helix:nth-child(3) { left: 50%; animation-delay: -10s; }
        .dna-helix:nth-child(4) { left: 70%; animation-delay: -15s; }
        .dna-helix:nth-child(5) { left: 90%; animation-delay: -20s; }

        @keyframes rotate {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }

        /* 进度指示器 */
        .progress-indicator {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 0.5rem;
            margin-top: 1rem;
        }

        .progress-bar {
            height: 8px;
            background: linear-gradient(90deg, #ffd700, #ff6b6b);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- DNA动画背景 -->
    <div class="dna-animation">
        <div class="dna-helix"></div>
        <div class="dna-helix"></div>
        <div class="dna-helix"></div>
        <div class="dna-helix"></div>
        <div class="dna-helix"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <i class="fas fa-dna"></i>
                    NGS课程平台
                </div>
                <ul class="nav-links">
                    <li><a href="#home"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="#courses"><i class="fas fa-book"></i> 课程</a></li>
                    <li><a href="about.html"><i class="fas fa-info-circle"></i> 关于</a></li>
                    <li><a href="syllabus.html"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                    <li><a href="resources.html"><i class="fas fa-download"></i> 资源</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero" id="home">
        <div class="container">
            <h1><i class="fas fa-microscope"></i> NGS高通量测序技术课程</h1>
            <p>掌握现代生物信息学的核心技术，开启基因组数据分析之旅</p>
            
            <div class="course-info">
                <h3><i class="fas fa-university"></i> 课程信息</h3>
                <p><strong>课程编码：</strong>S0904C213 | <strong>总学时：</strong>32学时 | <strong>授课对象：</strong>硕士研究生</p>
                <p><strong>教学形式：</strong>理论课(16学时) + 实践操作课(16学时)</p>
                
                <div class="course-stats">
                    <div class="stat-card">
                        <i class="fas fa-flask"></i>
                        <h3>8个专题</h3>
                        <p>完整的NGS技术体系</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-laptop-code"></i>
                        <h3>16小时实践</h3>
                        <p>真实数据分析操作</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-seedling"></i>
                        <h3>植物保护应用</h3>
                        <p>前沿应用技术</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-certificate"></i>
                        <h3>项目导向</h3>
                        <p>实战技能培养</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 课程列表 -->
    <section class="courses-section" id="courses">
        <div class="container">
            <h2 class="section-title"><i class="fas fa-books"></i> 课程专题</h2>
            
            <div class="courses-grid">
                <!-- 专题一 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="course-meta">
                            <h3>高通量测序技术概论</h3>
                            <div class="course-code">Lecture 1 | 基础入门</div>
                        </div>
                    </div>
                    <div class="course-description">
                        深入了解NGS技术的发展历程，掌握三代测序技术原理，熟悉主流测序平台特点，学习测序实验设计要点。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 基础课程</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture1_NGS_Technology_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture1_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题二 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="course-meta">
                            <h3>数据质量控制与预处理</h3>
                            <div class="course-code">Lecture 2 | 数据处理</div>
                        </div>
                    </div>
                    <div class="course-description">
                        学习测序数据质量评估方法，掌握数据过滤与清洗策略，熟练使用FastQC、Trimmomatic等工具进行数据预处理。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 核心技能</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture2_QC_Preprocessing_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture2_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题三 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="course-meta">
                            <h3>基因组测序数据分析</h3>
                            <div class="course-code">Lecture 3 | DNA-seq</div>
                        </div>
                    </div>
                    <div class="course-description">
                        掌握基因组比对、变异检测与注释流程，学习使用BWA、GATK、ANNOVAR等工具，理解SNP/Indel检测原理。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 专业技术</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture3_DNA_seq_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture3_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题四 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="course-meta">
                            <h3>转录组测序数据分析</h3>
                            <div class="course-code">Lecture 4 | RNA-seq</div>
                        </div>
                    </div>
                    <div class="course-description">
                        学习RNA-seq分析完整流程，掌握差异表达分析、功能富集分析方法，熟练使用DESeq2、ClusterProfiler等工具。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 热门应用</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture4_RNA_seq_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture4_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题五 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="course-meta">
                            <h3>表观基因组测序分析</h3>
                            <div class="course-code">Lecture 5 | Epigenomics</div>
                        </div>
                    </div>
                    <div class="course-description">
                        深入了解表观基因组学，学习ChIP-seq、ATAC-seq、甲基化测序数据分析，掌握MACS2、Bismark等工具使用。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 前沿技术</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture5_Epigenomics_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture5_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题六 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="course-meta">
                            <h3>单细胞测序技术分析</h3>
                            <div class="course-code">Lecture 6 | Single Cell</div>
                        </div>
                    </div>
                    <div class="course-description">
                        探索单细胞测序前沿技术，学习细胞聚类、轨迹分析方法，熟练使用Seurat、Scanpy进行单细胞RNA-seq分析。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 前沿技术</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture6_SingleCell_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture6_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题七 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-bacteria"></i>
                        </div>
                        <div class="course-meta">
                            <h3>宏基因组测序分析</h3>
                            <div class="course-code">Lecture 7 | Metagenomics</div>
                        </div>
                    </div>
                    <div class="course-description">
                        掌握宏基因组学分析方法，学习微生物群落结构分析、功能预测，熟练使用MetaPhlAn、HUMAnN等专业工具。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 生态应用</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture7_Metagenomics_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture7_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>

                <!-- 专题八 -->
                <div class="course-card">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="course-meta">
                            <h3>植物保护中的应用</h3>
                            <div class="course-code">Lecture 8 | Plant Protection</div>
                        </div>
                    </div>
                    <div class="course-description">
                        探索NGS在植物保护中的创新应用，学习病原体检测、抗病基因挖掘、环境DNA监测等前沿技术应用。
                    </div>
                    <div class="course-details">
                        <span><i class="fas fa-clock"></i> 理论2h + 实践2h</span>
                        <span><i class="fas fa-layer-group"></i> 应用专题</span>
                    </div>
                    <div class="course-buttons">
                        <a href="Lecture8_Plant_Protection_Interactive_Slides.html" class="course-button primary">
                            <i class="fas fa-play"></i> 开始学习
                        </a>
                        <a href="lab_html/Lecture8_lab.html" class="course-button secondary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <script>
        // 简单的页面交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 课程卡片悬停效果
            const courseCards = document.querySelectorAll('.course-card');
            courseCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 模拟进度加载动画
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const randomProgress = Math.floor(Math.random() * 100);
                setTimeout(() => {
                    bar.style.width = randomProgress + '%';
                }, Math.random() * 1000);
            });

            // 页面加载动画
            const cards = document.querySelectorAll('.course-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 统计访问次数（模拟）
        let visitCount = localStorage.getItem('visitCount') || 0;
        visitCount++;
        localStorage.setItem('visitCount', visitCount);
        console.log(`欢迎第 ${visitCount} 次访问NGS课程平台！`);
    </script>
</body>
</html> 