<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题四：转录组测序(RNA-seq)数据分析 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #3498db;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #3498db;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #e74c3c;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #e74c3c;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #3498db;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #e74c3c;
        }

        .highlight-box {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .code-comment {
            color: #95a5a6;
            font-style: italic;
        }

        .code-keyword {
            color: #3498db;
            font-weight: bold;
        }

        .code-string {
            color: #e74c3c;
        }

        .code-function {
            color: #f39c12;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(231, 76, 60, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(231, 76, 60, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(231, 76, 60, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(231, 76, 60, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 350px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #e74c3c;
        }

        .menu-item.current {
            background: #fdf2f2;
            border-left-color: #e74c3c;
            font-weight: bold;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #e74c3c;
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .flowchart {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .flowchart-step {
            background: #e74c3c;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            position: relative;
        }

        .flowchart-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #e74c3c;
        }

        .flowchart-step:last-child::after {
            display: none;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tool-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tool-card:hover {
            border-color: #e74c3c;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .tool-card h4 {
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .key-points {
            background: #fff9c4;
            border: 2px solid #fbc02d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .key-points h4 {
            color: #e65100 !important;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .key-points ul li {
            color: #333 !important;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .algorithm-box h3 {
            color: #2E7D32 !important;
        }

        .comparison-table th {
            color: #FFFFFF !important;
        }

        .comparison-table td {
            color: #333 !important;
        }

        .flowchart-step {
            color: #FFFFFF !important;
            font-weight: bold;
        }

        .tool-card h4 {
            color: #C62828 !important;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .tool-card p {
            color: #333 !important;
            line-height: 1.6;
        }

        .fullscreen-btn {
            position: fixed;
            top: 30px;
            right: 100px;
            background: rgba(231, 76, 60, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fullscreen-btn:hover {
            background: rgba(231, 76, 60, 1);
            transform: scale(1.1);
        }

        .rna-highlight {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .statistics-box {
            background: #e8f4fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .statistics-box h4 {
            color: #1976d2 !important;
            margin-bottom: 15px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture4_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">14</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题四：转录组测序(RNA-seq)数据分析",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🧬 转录组测序数据分析流程图形摘要</h3>
                        <svg width="100%" height="520" viewBox="0 0 1000 520" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient4" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="rnaGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="33%" style="stop-color:#f39c12" />
                                    <stop offset="66%" style="stop-color:#27ae60" />
                                    <stop offset="100%" style="stop-color:#3498db" />
                                </linearGradient>
                                <linearGradient id="expressionGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="50%" style="stop-color:#f39c12" />
                                    <stop offset="100%" style="stop-color:#27ae60" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="520" fill="url(#bgGradient4)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">转录组测序数据分析工作流</text>
                            
                            <!-- RNA类型与技术选择 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">RNA-seq技术类型选择</text>
                                
                                <!-- mRNA-seq -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="100" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="50" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">mRNA-seq</text>
                                    <text x="50" y="28" text-anchor="middle" fill="white" font-size="9">编码基因</text>
                                </g>
                                
                                <!-- Total RNA-seq -->
                                <g transform="translate(170, 35)">
                                    <rect x="0" y="0" width="100" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="50" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Total RNA</text>
                                    <text x="50" y="28" text-anchor="middle" fill="white" font-size="9">全转录组</text>
                                </g>
                                
                                <!-- small RNA-seq -->
                                <g transform="translate(290, 35)">
                                    <rect x="0" y="0" width="100" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="50" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">small RNA</text>
                                    <text x="50" y="28" text-anchor="middle" fill="white" font-size="9">调控RNA</text>
                                </g>
                                
                                <!-- 单细胞RNA-seq -->
                                <g transform="translate(410, 35)">
                                    <rect x="0" y="0" width="100" height="35" fill="#3498db" rx="5" opacity="0.9"/>
                                    <text x="50" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">scRNA-seq</text>
                                    <text x="50" y="28" text-anchor="middle" fill="white" font-size="9">单细胞</text>
                                </g>
                                
                                <!-- 空间转录组 -->
                                <g transform="translate(530, 35)">
                                    <rect x="0" y="0" width="100" height="35" fill="#9b59b6" rx="5" opacity="0.9"/>
                                    <text x="50" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">空间转录组</text>
                                    <text x="50" y="28" text-anchor="middle" fill="white" font-size="9">位置信息</text>
                                </g>
                                
                                <!-- 实验设计要点 -->
                                <g transform="translate(650, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">实验设计要点:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">生物学重复 ≥3 | 技术重复可选 | 批次效应控制</text>
                                </g>
                            </g>
                            
                            <!-- 主要分析流程 - 中部 -->
                            <g transform="translate(50, 160)">
                                <!-- 原始数据 -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="55" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">原始数据</text>
                                    <text x="55" y="35" text-anchor="middle" fill="white" font-size="10">FASTQ</text>
                                    <text x="55" y="47" text-anchor="middle" fill="white" font-size="9">质量控制</text>
                                    <text x="55" y="57" text-anchor="middle" fill="white" font-size="9">接头去除</text>
                                </g>
                                
                                <!-- 转录本比对 -->
                                <g transform="translate(150, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">转录本比对</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">HISAT2</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">STAR</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">剪接识别</text>
                                </g>
                                
                                <!-- 转录本组装 -->
                                <g transform="translate(300, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">转录本组装</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">StringTie</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">Cufflinks</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">异构体识别</text>
                                </g>
                                
                                <!-- 表达定量 -->
                                <g transform="translate(450, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">表达定量</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">featureCounts</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">Salmon</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">TPM/FPKM</text>
                                </g>
                                
                                <!-- 差异分析 -->
                                <g transform="translate(600, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">差异分析</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">DESeq2</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">edgeR</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">统计检验</text>
                                </g>
                                
                                <!-- 功能分析 -->
                                <g transform="translate(750, 0)">
                                    <rect x="0" y="0" width="110" height="60" fill="#34495e" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">功能分析</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">GO富集</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">KEGG通路</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">网络分析</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M110,30 L140,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead4)"/>
                                <path d="M260,30 L290,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead4)"/>
                                <path d="M410,30 L440,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead4)"/>
                                <path d="M560,30 L590,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead4)"/>
                                <path d="M710,30 L740,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead4)"/>
                            </g>
                            
                            <!-- 表达定量方法对比 - 中下部 -->
                            <g transform="translate(50, 260)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">表达定量方法对比</text>
                                
                                <!-- Raw Counts -->
                                <g transform="translate(50, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">Raw Counts</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">原始读段计数</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">差异分析输入</text>
                                </g>
                                
                                <!-- RPKM/FPKM -->
                                <g transform="translate(200, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">RPKM/FPKM</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">长度+深度标准化</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">样本内比较</text>
                                </g>
                                
                                <!-- TPM -->
                                <g transform="translate(380, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">TPM</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">转录本丰度比例</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">样本间比较</text>
                                </g>
                                
                                <!-- 表达热图示意 -->
                                <g transform="translate(550, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">表达模式</text>
                                    <rect x="0" y="20" width="8" height="8" fill="#e74c3c"/>
                                    <rect x="10" y="20" width="8" height="8" fill="#f39c12"/>
                                    <rect x="20" y="20" width="8" height="8" fill="#27ae60"/>
                                    <rect x="30" y="20" width="8" height="8" fill="#3498db"/>
                                    <text x="20" y="42" text-anchor="middle" fill="#2c3e50" font-size="9">高→低表达</text>
                                </g>
                                
                                <!-- 统计方法 -->
                                <g transform="translate(700, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">统计模型</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">负二项分布</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">FDR校正</text>
                                </g>
                            </g>
                            
                            <!-- 分析应用场景 - 底部 -->
                            <g transform="translate(50, 380)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">转录组分析应用场景</text>
                                
                                <!-- 差异表达 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">差异表达</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">分析</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 疾病vs正常</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 处理vs对照</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 时间序列</text>
                                </g>
                                
                                <!-- 功能富集 -->
                                <g transform="translate(200, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">功能富集</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">分析</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• GO分析</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• KEGG通路</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 基因集富集</text>
                                </g>
                                
                                <!-- 共表达网络 -->
                                <g transform="translate(350, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">共表达</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">网络</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 模块识别</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 关键基因</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 调控网络</text>
                                </g>
                                
                                <!-- 可变剪接 -->
                                <g transform="translate(500, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">可变剪接</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">分析</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 异构体发现</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 剪接事件</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 功能预测</text>
                                </g>
                                
                                <!-- 单细胞分析 -->
                                <g transform="translate(650, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">单细胞</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">分析</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 细胞分型</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 发育轨迹</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 细胞通讯</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M120,420 Q150,400 180,420" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M270,420 Q300,400 330,420" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M420,420 Q450,400 480,420" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M570,420 Q600,400 630,420" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解转录组测序的基本原理、实验设计考量、主要技术类型、标准分析流程及其核心步骤的生物信息学方法。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>转录组测序实验设计与应用场景</li>
                                <li>RNA-seq数据分析流程概述</li>
                                <li>转录本比对和拼接技术</li>
                                <li>基因表达定量方法</li>
                                <li>差异表达分析统计原理</li>
                                <li>功能富集分析方法</li>
                                <li>单细胞RNA-seq前沿技术</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>掌握转录组测序的核心概念</li>
                                <li>理解RNA-seq分析的统计原理</li>
                                <li>学会选择合适的分析工具</li>
                                <li>具备解读分析结果的能力</li>
                                <li>了解前沿技术发展趋势</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>转录组是基因组功能的动态体现，代表了在特定条件下哪些基因被激活以及它们的活跃程度。
                    </div>
                `
            },
            {
                title: "转录组基本概念",
                subtitle: "第一部分：转录组测序实验设计与应用场景",
                content: `
                    <h2>转录组 vs 基因组</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>特征</th>
                                <th>基因组 (Genome)</th>
                                <th>转录组 (Transcriptome)</th>
                            </tr>
                            <tr>
                                <td><strong>定义</strong></td>
                                <td>完整的遗传物质(DNA)</td>
                                <td>特定条件下所有RNA分子的总和</td>
                            </tr>
                            <tr>
                                <td><strong>稳定性</strong></td>
                                <td>相对稳定，个体间基本相同</td>
                                <td>高度动态，随时间、空间、环境变化</td>
                            </tr>
                            <tr>
                                <td><strong>功能角色</strong></td>
                                <td>"蓝图" - 遗传信息载体</td>
                                <td>"执行指令" - 基因表达的产物</td>
                            </tr>
                            <tr>
                                <td><strong>复杂性</strong></td>
                                <td>序列复杂性高，但结构相对固定</td>
                                <td>表达丰度差异巨大，可变剪接增加多样性</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🧬 转录组的动态性特征</h4>
                        <ul>
                            <li><strong>发育调控：</strong>胚胎发育不同阶段的基因表达模式</li>
                            <li><strong>细胞特异性：</strong>神经元vs肝细胞的转录组差异</li>
                            <li><strong>应激响应：</strong>热休克、药物处理的快速转录反应</li>
                            <li><strong>疾病状态：</strong>健康vs疾病的转录组变化</li>
                        </ul>
                    </div>

                    <div class="highlight-box">
                        <h3>转录组研究的意义</h3>
                        <p><strong>连接基因型与表型：</strong>揭示基因功能与调控网络 → <strong>发现新靶点：</strong>识别疾病相关基因和通路 → <strong>精准医疗：</strong>个体化治疗策略</p>
                    </div>
                `
            },
            {
                title: "RNA-seq技术类型",
                subtitle: "不同RNA-seq技术的特点与应用",
                content: `
                    <h2>主要RNA-seq技术分类</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>mRNA-seq</h4>
                            <p><strong>原理：</strong>Poly(A)尾富集mRNA<br>
                            <strong>优势：</strong>成本效益高，聚焦编码基因<br>
                            <strong>应用：</strong>差异表达分析的首选</p>
                        </div>
                        <div class="tool-card">
                            <h4>Total RNA-seq</h4>
                            <p><strong>原理：</strong>去除rRNA后测序所有RNA<br>
                            <strong>优势：</strong>捕获非编码RNA信息<br>
                            <strong>应用：</strong>全面转录景观研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>small RNA-seq</h4>
                            <p><strong>原理：</strong>富集小分子RNA(<200nt)<br>
                            <strong>优势：</strong>专门研究miRNA等调控RNA<br>
                            <strong>应用：</strong>转录后调控机制研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>靶向RNA-seq</h4>
                            <p><strong>原理：</strong>特异性探针捕获目标基因<br>
                            <strong>优势：</strong>高深度、低成本、高灵敏度<br>
                            <strong>应用：</strong>临床检测、验证研究</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>前沿技术</h3>
                            <ul>
                                <li><span class="rna-highlight">单细胞RNA-seq</span>：解析细胞异质性</li>
                                <li><span class="rna-highlight">空间转录组</span>：保留空间位置信息</li>
                                <li><span class="rna-highlight">长读长RNA-seq</span>：全长转录本测序</li>
                                <li><span class="rna-highlight">实时RNA-seq</span>：动态监测表达变化</li>
                            </ul>
                        </div>
                        <div>
                            <h3>技术选择考虑因素</h3>
                            <ul>
                                <li><strong>研究目标：</strong>编码基因 vs 非编码RNA</li>
                                <li><strong>样本类型：</strong>组织 vs 单细胞</li>
                                <li><strong>预算限制：</strong>成本与信息量平衡</li>
                                <li><strong>分辨率需求：</strong>群体 vs 单细胞水平</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 技术选择原则：</strong>根据具体研究问题选择最适合的技术，避免过度测序造成资源浪费。
                    </div>
                `
            },
            {
                title: "实验设计关键因素",
                subtitle: "RNA-seq实验设计的核心考量",
                content: `
                    <h2>实验设计的关键要素</h2>

                    <div class="algorithm-box">
                        <h3>样本量与统计功效</h3>
                        <div class="code-block">
# 推荐样本量指导原则
基础研究：每组 ≥ 3个生物学重复
临床研究：每组 ≥ 5个生物学重复
复杂系统：每组 ≥ 8个生物学重复

# 功效分析考虑因素
- 预期效应大小 (Effect Size)
- 样本内变异程度
- 显著性水平 (α = 0.05)
- 统计功效 (Power ≥ 0.8)
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>生物学重复 vs 技术重复</h3>
                            <ul>
                                <li><strong>生物学重复：</strong>不同个体/培养体系</li>
                                <li><strong>必要性：</strong>评估生物学变异</li>
                                <li><strong>统计基础：</strong>差异分析的前提</li>
                                <li><strong>优先级：</strong>远高于技术重复</li>
                            </ul>

                            <h3>批次效应控制</h3>
                            <ul>
                                <li><strong>随机化设计：</strong>样本随机分配</li>
                                <li><strong>平衡设计：</strong>各组样本均匀分布</li>
                                <li><strong>统计校正：</strong>模型中包含批次因子</li>
                            </ul>
                        </div>
                        <div>
                            <h3>RNA质量控制</h3>
                            <ul>
                                <li><strong>RIN值：</strong>≥7 (推荐≥8)</li>
                                <li><strong>纯度评估：</strong>A260/A280 ≈ 2.0</li>
                                <li><strong>污染控制：</strong>无RNase、无gDNA</li>
                                <li><strong>完整性：</strong>避免降解影响</li>
                            </ul>

                            <h3>文库构建策略</h3>
                            <ul>
                                <li><strong>链特异性：</strong>保留链信息(推荐)</li>
                                <li><strong>起始材料：</strong>Poly(A) vs rRNA去除</li>
                                <li><strong>片段化：</strong>适合测序平台</li>
                                <li><strong>PCR循环：</strong>最小化扩增偏差</li>
                            </ul>
                        </div>
                    </div>

                    <div class="statistics-box">
                        <h4>📊 测序深度推荐</h4>
                        <ul>
                            <li><strong>基因表达谱分析：</strong>20-30M reads/sample</li>
                            <li><strong>可变剪接分析：</strong>50-100M reads/sample</li>
                            <li><strong>新转录本发现：</strong>100M+ reads/sample</li>
                            <li><strong>单细胞RNA-seq：</strong>50K-100K reads/cell</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "RNA-seq分析流程概述",
                subtitle: "第二部分：标准分析流程与质量控制",
                content: `
                    <h2>RNA-seq数据分析标准流程</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">原始数据<br>FASTQ</div>
                        <div class="flowchart-step">质量控制<br>FastQC</div>
                        <div class="flowchart-step">序列比对<br>HISAT2/STAR</div>
                        <div class="flowchart-step">表达定量<br>featureCounts</div>
                        <div class="flowchart-step">差异分析<br>DESeq2</div>
                        <div class="flowchart-step">功能注释<br>GO/KEGG</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>基于比对的分析路径</h3>
                            <ul>
                                <li><strong>序列比对：</strong>HISAT2, STAR, GSNAP</li>
                                <li><strong>转录本组装：</strong>StringTie, Cufflinks</li>
                                <li><strong>表达定量：</strong>featureCounts, HTSeq</li>
                                <li><strong>优势：</strong>成熟稳定，结果可靠</li>
                                <li><strong>适用：</strong>有参考基因组的物种</li>
                            </ul>
                        </div>
                        <div>
                            <h3>无比对的分析路径</h3>
                            <ul>
                                <li><strong>伪比对定量：</strong>Salmon, Kallisto</li>
                                <li><strong>从头组装：</strong>Trinity, SOAPdenovo</li>
                                <li><strong>优势：</strong>速度快，资源需求低</li>
                                <li><strong>适用：</strong>快速分析，非模式生物</li>
                                <li><strong>局限：</strong>缺少碱基级比对信息</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🔍 质量控制检查点</h4>
                        <ul>
                            <li><strong>原始数据QC：</strong>碱基质量、GC含量、接头污染</li>
                            <li><strong>比对质量QC：</strong>比对率、唯一比对率、片段长度</li>
                            <li><strong>表达量QC：</strong>样本相关性、PCA分析、批次效应</li>
                            <li><strong>差异分析QC：</strong>MA图、火山图、P值分布</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong>💡 分析策略选择：</strong>参考基因组依赖方法是首选，当研究非模式生物或希望发现新转录本时考虑从头组装。
                    </div>
                `
            },
            {
                title: "RNA-seq比对的特殊挑战",
                subtitle: "第三部分：转录本比对和拼接技术",
                content: `
                    <h2>RNA-seq比对 vs DNA比对</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>挑战</th>
                                <th>DNA比对</th>
                                <th>RNA比对</th>
                            </tr>
                            <tr>
                                <td><strong>序列连续性</strong></td>
                                <td>连续比对到基因组</td>
                                <td>跨越内含子的剪接比对</td>
                            </tr>
                            <tr>
                                <td><strong>可变剪接</strong></td>
                                <td>无此问题</td>
                                <td>同一基因多种剪接异构体</td>
                            </tr>
                            <tr>
                                <td><strong>基因融合</strong></td>
                                <td>结构变异检测</td>
                                <td>嵌合转录本检测</td>
                            </tr>
                            <tr>
                                <td><strong>重复序列</strong></td>
                                <td>多重比对处理</td>
                                <td>同源基因家族区分</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>剪接感知比对原理</h3>
                        <div class="code-block">
# 剪接比对步骤
1. 种子查找：在read中寻找能精确匹配的片段
2. 剪接位点识别：检测GT-AG等经典剪接信号
3. 跨内含子比对：将read分段比对到不同外显子
4. 评分与选择：选择最佳的剪接比对方案

# 剪接位点信号
经典型：GT...AG (>99%)
非经典：GC...AG, AT...AC
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🧬 RNA-seq比对的核心挑战</h4>
                        <ul>
                            <li><strong>跨越内含子：</strong>reads来源于成熟mRNA，需要跨越内含子比对</li>
                            <li><strong>可变剪接：</strong>同一基因的多种剪接方式增加复杂性</li>
                            <li><strong>新剪接位点：</strong>发现未注释的剪接事件</li>
                            <li><strong>基因融合：</strong>检测染色体重排产生的嵌合转录本</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 重要提醒：</strong>RNA-seq比对工具必须具备"剪接感知"能力，不能使用普通的DNA比对工具。
                    </div>
                `
            },
            {
                title: "HISAT2算法原理",
                subtitle: "分层图FM索引技术",
                content: `
                    <h2>HISAT2核心技术架构</h2>

                    <div class="algorithm-box">
                        <h3>分层图FM索引 (HGFM)</h3>
                        <ol>
                            <li><strong>全局索引：</strong>覆盖整个基因组的FM索引</li>
                            <li><strong>局部索引：</strong>数万个小的区域索引(~64kbp)</li>
                            <li><strong>分层策略：</strong>先全局比对，失败后局部精细比对</li>
                            <li><strong>剪接位点数据库：</strong>内置已知剪接位点信息</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>HISAT2</h4>
                            <p><strong>特点：</strong>内存效率高，速度快<br>
                            <strong>索引：</strong>分层图FM索引<br>
                            <strong>适用：</strong>内存受限环境</p>
                        </div>
                        <div class="tool-card">
                            <h4>STAR</h4>
                            <p><strong>特点：</strong>速度极快，功能强大<br>
                            <strong>索引：</strong>后缀数组索引<br>
                            <strong>适用：</strong>大内存服务器</p>
                        </div>
                        <div class="tool-card">
                            <h4>Salmon/Kallisto</h4>
                            <p><strong>特点：</strong>伪比对，极快速度<br>
                            <strong>索引：</strong>k-mer哈希表<br>
                            <strong>适用：</strong>快速定量分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>GSNAP</h4>
                            <p><strong>特点：</strong>处理复杂变异<br>
                            <strong>索引：</strong>多种索引策略<br>
                            <strong>适用：</strong>长读长数据</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>HISAT2优势</h3>
                            <ul>
                                <li>内存需求相对较低</li>
                                <li>比对速度快</li>
                                <li>准确性高</li>
                                <li>支持链特异性</li>
                                <li>可检测新剪接位点</li>
                            </ul>
                        </div>
                        <div>
                            <h3>关键参数</h3>
                            <ul>
                                <li><strong>--known-splicesite-infile：</strong>已知剪接位点</li>
                                <li><strong>--rna-strandness：</strong>链特异性类型</li>
                                <li><strong>--dta：</strong>下游组装优化</li>
                                <li><strong>--threads：</strong>并行线程数</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 工具选择建议：</strong>STAR适合大内存环境，HISAT2适合内存受限情况，Salmon/Kallisto适合快速定量。
                    </div>
                `
            },
            {
                title: "STAR算法原理",
                subtitle: "最高可比对种子搜索技术",
                content: `
                    <h2>STAR核心算法机制</h2>

                    <div class="algorithm-box">
                        <h3>最高可比对种子搜索 (MMS)</h3>
                        <div class="code-block">
# STAR比对流程
1. 种子搜索：寻找read中最长的精确匹配片段
2. 种子扩展：尝试扩展种子覆盖整个read
3. 剪接检测：当种子无法覆盖时，寻找剪接位点
4. 多重比对：处理重复区域的多重比对
5. 质量评分：计算比对质量分数

# 两阶段模式 (推荐)
第一阶段：发现新的剪接位点
第二阶段：利用所有样本信息重新比对
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>STAR技术特点</h3>
                            <ul>
                                <li><strong>后缀数组索引：</strong>快速种子查找</li>
                                <li><strong>两阶段模式：</strong>提高剪接检测</li>
                                <li><strong>嵌合体检测：</strong>内置融合基因检测</li>
                                <li><strong>非规范剪接：</strong>检测GC-AG等</li>
                                <li><strong>并行计算：</strong>多线程优化</li>
                            </ul>
                        </div>
                        <div>
                            <h3>资源需求</h3>
                            <ul>
                                <li><strong>内存需求：</strong>人类基因组~30GB</li>
                                <li><strong>运行内存：</strong>与索引大小相当</li>
                                <li><strong>速度优势：</strong>多线程下极快</li>
                                <li><strong>存储需求：</strong>索引文件较大</li>
                            </ul>
                        </div>
                    </div>

                    <div class="statistics-box">
                        <h4>📊 STAR关键参数</h4>
                        <ul>
                            <li><strong>--runMode genomeGenerate：</strong>构建基因组索引</li>
                            <li><strong>--outSAMtype BAM SortedByCoordinate：</strong>输出排序BAM</li>
                            <li><strong>--twopassMode Basic：</strong>启用两阶段模式</li>
                            <li><strong>--chimSegmentMin：</strong>嵌合体检测阈值</li>
                        </ul>
                    </div>

                    <div class="highlight-box">
                        <h3>STAR vs HISAT2选择指南</h3>
                        <p><strong>选择STAR：</strong>大内存服务器，需要最快速度和最强功能 → <strong>选择HISAT2：</strong>内存受限，需要平衡性能和资源</p>
                    </div>
                `
            },
            {
                title: "转录本组装策略",
                subtitle: "StringTie与Trinity算法",
                content: `
                    <h2>转录本组装的两大策略</h2>

                    <div class="two-column">
                        <div>
                            <h3>参考指导组装</h3>
                            <ul>
                                <li><strong>输入：</strong>BAM比对文件</li>
                                <li><strong>原理：</strong>构建剪接图，寻找路径</li>
                                <li><strong>代表工具：</strong>StringTie, Cufflinks</li>
                                <li><strong>优势：</strong>准确性高，计算效率好</li>
                                <li><strong>适用：</strong>有参考基因组的物种</li>
                            </ul>
                        </div>
                        <div>
                            <h3>从头组装</h3>
                            <ul>
                                <li><strong>输入：</strong>原始FASTQ文件</li>
                                <li><strong>原理：</strong>De Bruijn图组装</li>
                                <li><strong>代表工具：</strong>Trinity, SOAPdenovo</li>
                                <li><strong>优势：</strong>发现新转录本</li>
                                <li><strong>适用：</strong>非模式生物</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>StringTie流网络模型</h3>
                        <ol>
                            <li><strong>构建剪接图：</strong>基于reads比对信息</li>
                            <li><strong>转化流网络：</strong>边容量对应reads覆盖度</li>
                            <li><strong>最大流算法：</strong>寻找最可能的转录本路径</li>
                            <li><strong>迭代组装：</strong>重复寻找路径直到无足够流量</li>
                            <li><strong>表达量估计：</strong>基于流量计算FPKM/TPM</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>StringTie</h4>
                            <p><strong>算法：</strong>流网络模型<br>
                            <strong>功能：</strong>组装+定量<br>
                            <strong>特点：</strong>速度快，准确性高</p>
                        </div>
                        <div class="tool-card">
                            <h4>Trinity</h4>
                            <p><strong>算法：</strong>De Bruijn图<br>
                            <strong>功能：</strong>从头组装<br>
                            <strong>特点：</strong>处理可变剪接强</p>
                        </div>
                        <div class="tool-card">
                            <h4>Cufflinks</h4>
                            <p><strong>算法：</strong>重叠图<br>
                            <strong>功能：</strong>组装+定量<br>
                            <strong>特点：</strong>经典工具，功能全面</p>
                        </div>
                        <div class="tool-card">
                            <h4>SPAdes</h4>
                            <p><strong>算法：</strong>多k-mer策略<br>
                            <strong>功能：</strong>RNA模式组装<br>
                            <strong>特点：</strong>错误校正能力强</p>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🔧 组装质量评估</h4>
                        <ul>
                            <li><strong>N50/Nx：</strong>衡量组装连续性</li>
                            <li><strong>BUSCO：</strong>评估基因完整性</li>
                            <li><strong>比对回参考：</strong>验证组装准确性</li>
                            <li><strong>表达量分布：</strong>检查定量合理性</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "基因表达定量概述",
                subtitle: "第四部分：基因表达定量方法",
                content: `
                    <h2>表达定量的核心挑战</h2>

                    <div class="key-points">
                        <h4>🎯 定量分析的主要挑战</h4>
                        <ul>
                            <li><strong>多重比对：</strong>reads比对到多个基因或转录本</li>
                            <li><strong>可变剪接：</strong>同一基因的不同异构体</li>
                            <li><strong>基因重叠：</strong>重叠基因的reads归属问题</li>
                            <li><strong>技术偏差：</strong>GC含量、长度、位置偏好性</li>
                            <li><strong>RNA组成效应：</strong>高表达基因对其他基因的影响</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>基于比对的计数方法</h3>
                            <ul>
                                <li><strong>HTSeq-count：</strong>经典Python工具</li>
                                <li><strong>featureCounts：</strong>快速C语言实现</li>
                                <li><strong>STAR：</strong>比对时直接计数</li>
                                <li><strong>优势：</strong>精确，可控制参数</li>
                                <li><strong>缺点：</strong>需要比对步骤，速度较慢</li>
                            </ul>
                        </div>
                        <div>
                            <h3>基于k-mer的伪比对</h3>
                            <ul>
                                <li><strong>Salmon：</strong>准确性高，功能丰富</li>
                                <li><strong>Kallisto：</strong>速度极快，内存低</li>
                                <li><strong>Sailfish：</strong>早期代表工具</li>
                                <li><strong>优势：</strong>极快速度，无需比对</li>
                                <li><strong>缺点：</strong>缺少碱基级信息</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>featureCounts处理策略</h3>
                        <div class="code-block">
# 重叠处理模式
-M: 允许多重比对reads
-O: 允许重叠特征计数
--fraction: 分数分配多重比对reads

# 链特异性
-s 0: 非链特异性
-s 1: 正向链特异性
-s 2: 反向链特异性

# 特征类型
-t exon: 计数外显子
-g gene_id: 按基因ID分组
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 方法选择建议：</strong>featureCounts适合基因水平分析，Salmon/Kallisto适合转录本水平快速定量。
                    </div>
                `
            },
            {
                title: "表达量标准化方法",
                subtitle: "从原始计数到可比较的表达量",
                content: `
                    <h2>标准化方法比较</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>方法</th>
                                <th>公式</th>
                                <th>校正因子</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>RPM/CPM</strong></td>
                                <td>Count / Total × 10⁶</td>
                                <td>测序深度</td>
                                <td>样本间比较(有限)</td>
                            </tr>
                            <tr>
                                <td><strong>RPKM/FPKM</strong></td>
                                <td>Count / (Length × Total)</td>
                                <td>长度 + 深度</td>
                                <td>样本内基因比较</td>
                            </tr>
                            <tr>
                                <td><strong>TPM</strong></td>
                                <td>RPK / Total_RPK × 10⁶</td>
                                <td>长度 + 深度(改进)</td>
                                <td>样本间比较(推荐)</td>
                            </tr>
                            <tr>
                                <td><strong>TMM/RLE</strong></td>
                                <td>基于参考基因</td>
                                <td>RNA组成偏差</td>
                                <td>差异分析专用</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>TPM计算步骤</h3>
                        <div class="code-block">
# TPM (Transcripts Per Million) 计算
1. 长度标准化: RPK = Count / (Length_kb)
2. 计算总RPK: Total_RPK = Σ(RPK_all_genes)
3. 深度标准化: TPM = RPK / Total_RPK × 1,000,000

# TPM的优势
- 每个样本TPM总和 = 1,000,000
- 更适合样本间比较
- 反映基因在总mRNA中的比例
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>相对表达量 vs 绝对表达量</h3>
                            <ul>
                                <li><strong>相对表达量：</strong>RNA-seq的标准输出</li>
                                <li><strong>优势：</strong>技术成熟，成本低</li>
                                <li><strong>局限：</strong>无法获得绝对分子数</li>
                                <li><strong>应用：</strong>差异分析，模式识别</li>
                            </ul>
                        </div>
                        <div>
                            <h3>绝对表达量测量</h3>
                            <ul>
                                <li><strong>Spike-in标准：</strong>已知浓度的外源RNA</li>
                                <li><strong>UMI技术：</strong>分子标签去重</li>
                                <li><strong>优势：</strong>真实分子数定量</li>
                                <li><strong>应用：</strong>单细胞，定量生物学</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 重要提醒：</strong>差异表达分析应使用原始计数，不要使用标准化后的值作为输入。标准化主要用于可视化和比较。
                    </div>
                `
            },
            {
                title: "Salmon伪比对算法",
                subtitle: "k-mer索引与EM算法",
                content: `
                    <h2>Salmon核心技术架构</h2>

                    <div class="algorithm-box">
                        <h3>Salmon工作流程</h3>
                        <ol>
                            <li><strong>索引构建：</strong>对转录本序列构建k-mer索引</li>
                            <li><strong>伪比对：</strong>确定reads与转录本的兼容性</li>
                            <li><strong>EM算法：</strong>迭代估计转录本丰度</li>
                            <li><strong>偏差校正：</strong>校正GC、长度、位置偏好</li>
                            <li><strong>输出定量：</strong>生成TPM和估计计数</li>
                        </ol>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>伪比对原理</h3>
                            <ul>
                                <li><strong>k-mer分解：</strong>将reads分解为k-mers</li>
                                <li><strong>快速查找：</strong>哈希表查找兼容转录本</li>
                                <li><strong>兼容性判断：</strong>k-mer覆盖度阈值</li>
                                <li><strong>速度优势：</strong>跳过精确比对步骤</li>
                            </ul>
                        </div>
                        <div>
                            <h3>EM算法估计</h3>
                            <ul>
                                <li><strong>E步：</strong>计算reads来源概率</li>
                                <li><strong>M步：</strong>更新转录本丰度估计</li>
                                <li><strong>迭代收敛：</strong>直到参数稳定</li>
                                <li><strong>处理歧义：</strong>多重比对reads分配</li>
                            </ul>
                        </div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>Salmon</h4>
                            <p><strong>特点：</strong>准确性高，偏差校正强<br>
                            <strong>功能：</strong>转录本和基因定量<br>
                            <strong>输出：</strong>TPM, 计数, 有效长度</p>
                        </div>
                        <div class="tool-card">
                            <h4>Kallisto</h4>
                            <p><strong>特点：</strong>速度极快，内存低<br>
                            <strong>功能：</strong>转录本定量<br>
                            <strong>输出：</strong>TPM, 估计计数</p>
                        </div>
                        <div class="tool-card">
                            <h4>RSEM</h4>
                            <p><strong>特点：</strong>基于比对，功能全面<br>
                            <strong>功能：</strong>基因和转录本定量<br>
                            <strong>输出：</strong>FPKM, TPM, 计数</p>
                        </div>
                        <div class="tool-card">
                            <h4>eXpress</h4>
                            <p><strong>特点：</strong>在线EM算法<br>
                            <strong>功能：</strong>实时定量更新<br>
                            <strong>输出：</strong>FPKM, 有效计数</p>
                        </div>
                    </div>

                    <div class="statistics-box">
                        <h4>📊 Salmon关键参数</h4>
                        <ul>
                            <li><strong>--libType：</strong>文库类型(A=自动检测)</li>
                            <li><strong>--gcBias：</strong>GC偏好校正</li>
                            <li><strong>--seqBias：</strong>序列偏好校正</li>
                            <li><strong>--posBias：</strong>位置偏好校正</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "差异表达分析概述",
                subtitle: "第五部分：差异表达分析统计原理",
                content: `
                    <h2>RNA-seq数据的统计特性</h2>

                    <div class="key-points">
                        <h4>🎯 RNA-seq计数数据特点</h4>
                        <ul>
                            <li><strong>离散性：</strong>计数数据，非连续分布</li>
                            <li><strong>过度离散：</strong>方差大于均值</li>
                            <li><strong>零膨胀：</strong>大量基因表达量为0</li>
                            <li><strong>异方差性：</strong>方差随均值变化</li>
                            <li><strong>组成效应：</strong>总reads数固定的约束</li>
                        </ul>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>分布模型</th>
                                <th>假设</th>
                                <th>适用性</th>
                                <th>代表工具</th>
                            </tr>
                            <tr>
                                <td><strong>泊松分布</strong></td>
                                <td>均值 = 方差</td>
                                <td>技术重复</td>
                                <td>早期方法</td>
                            </tr>
                            <tr>
                                <td><strong>负二项分布</strong></td>
                                <td>方差 > 均值</td>
                                <td>生物学重复</td>
                                <td>DESeq2, edgeR</td>
                            </tr>
                            <tr>
                                <td><strong>零膨胀模型</strong></td>
                                <td>额外的零值</td>
                                <td>单细胞数据</td>
                                <td>ZINB-WaVE</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>负二项分布模型</h3>
                        <div class="code-block">
# 负二项分布参数
μ: 均值 (期望表达量)
φ: 离散参数 (生物学变异)

# 方差-均值关系
Var = μ + φ × μ²

# 当φ→0时，退化为泊松分布
# φ越大，过度离散越严重
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>统计检验策略</h3>
                            <ul>
                                <li><strong>精确检验：</strong>Fisher精确检验</li>
                                <li><strong>似然比检验：</strong>广义线性模型</li>
                                <li><strong>Wald检验：</strong>参数显著性检验</li>
                                <li><strong>经验贝叶斯：</strong>信息借用策略</li>
                            </ul>
                        </div>
                        <div>
                            <h3>多重检验校正</h3>
                            <ul>
                                <li><strong>Bonferroni：</strong>保守校正</li>
                                <li><strong>FDR (BH)：</strong>控制假发现率</li>
                                <li><strong>q-value：</strong>局部FDR估计</li>
                                <li><strong>IHW：</strong>独立假设加权</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心理念：</strong>RNA-seq差异分析的核心是建立合适的统计模型来描述计数数据的分布特性，并进行假设检验。
                    </div>
                `
            },
            {
                title: "DESeq2算法原理",
                subtitle: "经典差异表达分析工具",
                content: `
                    <h2>DESeq2核心算法流程</h2>

                    <div class="algorithm-box">
                        <h3>DESeq2分析步骤</h3>
                        <ol>
                            <li><strong>标准化：</strong>中位数比值法(Median of Ratios)</li>
                            <li><strong>离散度估计：</strong>基因特异性+共同离散度</li>
                            <li><strong>拟合GLM：</strong>负二项广义线性模型</li>
                            <li><strong>假设检验：</strong>Wald检验或似然比检验</li>
                            <li><strong>多重校正：</strong>Benjamini-Hochberg方法</li>
                        </ol>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>标准化策略</h3>
                            <ul>
                                <li><strong>中位数比值法：</strong>对每个基因计算几何平均</li>
                                <li><strong>假设：</strong>大多数基因不差异表达</li>
                                <li><strong>优势：</strong>对极端值不敏感</li>
                                <li><strong>输出：</strong>样本特异性标准化因子</li>
                            </ul>
                        </div>
                        <div>
                            <h3>离散度估计</h3>
                            <ul>
                                <li><strong>基因特异性：</strong>每个基因独立估计</li>
                                <li><strong>信息借用：</strong>向共同离散度收缩</li>
                                <li><strong>经验贝叶斯：</strong>提高小样本稳定性</li>
                                <li><strong>拟合曲线：</strong>均值-离散度关系</li>
                            </ul>
                        </div>
                    </div>

                    <div class="statistics-box">
                        <h4>📊 DESeq2关键概念</h4>
                        <ul>
                            <li><strong>Log2FoldChange：</strong>倍数变化的对数值</li>
                            <li><strong>baseMean：</strong>所有样本的标准化均值</li>
                            <li><strong>lfcSE：</strong>log2FC的标准误</li>
                            <li><strong>padj：</strong>多重检验校正后的p值</li>
                        </ul>
                    </div>

                    <div class="code-block">
# DESeq2基本用法
library(DESeq2)
dds <- DESeqDataSetFromMatrix(countData = counts,
                              colData = metadata,
                              design = ~ condition)
dds <- DESeq(dds)
results <- results(dds, contrast = c("condition", "treated", "control"))
                    </div>

                    <div class="highlight-box">
                        <h3>DESeq2优势</h3>
                        <p><strong>稳健性强：</strong>适合小样本分析 → <strong>统计严谨：</strong>经过充分验证 → <strong>功能丰富：</strong>支持复杂实验设计</p>
                    </div>
                `
            },
            {
                title: "edgeR算法原理",
                subtitle: "经验贝叶斯方法",
                content: `
                    <h2>edgeR核心技术特点</h2>

                    <div class="algorithm-box">
                        <h3>edgeR分析流程</h3>
                        <ol>
                            <li><strong>标准化：</strong>TMM (Trimmed Mean of M-values)</li>
                            <li><strong>离散度估计：</strong>共同+标签特异性+基因特异性</li>
                            <li><strong>精确检验：</strong>Fisher精确检验的推广</li>
                            <li><strong>似然比检验：</strong>广义线性模型框架</li>
                            <li><strong>准似然：</strong>处理过度离散的替代方法</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>DESeq2</h4>
                            <p><strong>标准化：</strong>中位数比值法<br>
                            <strong>检验：</strong>Wald检验<br>
                            <strong>特点：</strong>保守，适合小样本</p>
                        </div>
                        <div class="tool-card">
                            <h4>edgeR</h4>
                            <p><strong>标准化：</strong>TMM方法<br>
                            <strong>检验：</strong>精确检验<br>
                            <strong>特点：</strong>灵活，多种方法</p>
                        </div>
                        <div class="tool-card">
                            <h4>limma-voom</h4>
                            <p><strong>标准化：</strong>voom变换<br>
                            <strong>检验：</strong>线性模型<br>
                            <strong>特点：</strong>速度快，复杂设计</p>
                        </div>
                        <div class="tool-card">
                            <h4>NOISeq</h4>
                            <p><strong>标准化：</strong>多种选择<br>
                            <strong>检验：</strong>非参数方法<br>
                            <strong>特点：</strong>无分布假设</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>TMM标准化</h3>
                            <ul>
                                <li><strong>M值：</strong>log2(基因比值)</li>
                                <li><strong>A值：</strong>log2(平均表达量)</li>
                                <li><strong>修剪：</strong>去除极端M值和A值</li>
                                <li><strong>加权平均：</strong>计算标准化因子</li>
                            </ul>
                        </div>
                        <div>
                            <h3>精确检验</h3>
                            <ul>
                                <li><strong>条件分布：</strong>给定总计数的条件下</li>
                                <li><strong>精确p值：</strong>不依赖渐近理论</li>
                                <li><strong>适用性：</strong>小样本情况</li>
                                <li><strong>计算复杂：</strong>大样本时较慢</li>
                            </ul>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>特征</th>
                                <th>DESeq2</th>
                                <th>edgeR</th>
                                <th>limma-voom</th>
                            </tr>
                            <tr>
                                <td><strong>标准化</strong></td>
                                <td>中位数比值</td>
                                <td>TMM</td>
                                <td>TMM + voom</td>
                            </tr>
                            <tr>
                                <td><strong>离散度</strong></td>
                                <td>经验贝叶斯收缩</td>
                                <td>多层次估计</td>
                                <td>均值-方差建模</td>
                            </tr>
                            <tr>
                                <td><strong>检验方法</strong></td>
                                <td>Wald检验</td>
                                <td>精确/似然比</td>
                                <td>线性模型t检验</td>
                            </tr>
                            <tr>
                                <td><strong>适用场景</strong></td>
                                <td>标准分析</td>
                                <td>灵活分析</td>
                                <td>复杂设计</td>
                            </tr>
                        </table>
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成幻灯片HTML
            const container = document.querySelector('.container');
            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <div class="slide-header">
                        <h1 class="slide-title">${slide.title}</h1>
                        <p class="slide-subtitle">${slide.subtitle}</p>
                    </div>
                    <div class="slide-content">
                        ${slide.content}
                    </div>
                `;
                container.appendChild(slideElement);
            });

            // 生成菜单
            generateMenu();
            updateNavigation();
            updateProgress();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = slides.map((slide, index) => `
                <div class="menu-item ${index === 0 ? 'current' : ''}" onclick="goToSlide(${index})">
                    ${index + 1}. ${slide.title}
                </div>
            `).join('');
        }

        // 切换菜单显示
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                // 隐藏当前幻灯片
                document.querySelectorAll('.slide')[currentSlideIndex].classList.remove('active');

                // 显示目标幻灯片
                currentSlideIndex = index;
                document.querySelectorAll('.slide')[currentSlideIndex].classList.add('active');

                // 更新界面
                updateNavigation();
                updateProgress();
                updateMenu();

                // 隐藏菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        // 更新导航按钮状态
        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            
            // 更新主题导航栏的active状态
            document.querySelectorAll('.topic-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据当前幻灯片索引设置active状态
            if (currentSlideIndex === 0) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(0)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 1 && currentSlideIndex <= 3) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(1)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 4 && currentSlideIndex <= 7) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(4)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 8 && currentSlideIndex <= 10) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(8)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 11 && currentSlideIndex <= 12) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(11)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 13) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(13)"]')?.classList.add('active');
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新菜单当前项
        function updateMenu() {
            document.querySelectorAll('.menu-item').forEach((item, index) => {
                item.classList.toggle('current', index === currentSlideIndex);
            });
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay(); // 到达最后一页时停止自动播放
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        // 重新开始演示
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    goToSlide(0);
                    break;
                case 'End':
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    document.getElementById('menuDropdown').classList.remove('active');
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.slide-menu')) {
                document.getElementById('menuDropdown').classList.remove('active');
            }
        });

        // 初始化
        initPresentation();
    </script>
</body>
</html>
