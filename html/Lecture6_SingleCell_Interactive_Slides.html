<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题六：单细胞测序技术与数据分析 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #3498db;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #3498db;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #e67e22, #d35400);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #27ae60;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #27ae60;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #16a085;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #27ae60;
        }

        .highlight-box {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .code-comment {
            color: #95a5a6;
            font-style: italic;
        }

        .code-keyword {
            color: #3498db;
            font-weight: bold;
        }

        .code-string {
            color: #e74c3c;
        }

        .code-function {
            color: #f39c12;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(39, 174, 96, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(39, 174, 96, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(39, 174, 96, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(39, 174, 96, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 350px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #27ae60;
        }

        .menu-item.current {
            background: #e8f5e8;
            border-left-color: #27ae60;
            font-weight: bold;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #27ae60;
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .flowchart {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .flowchart-step {
            background: #27ae60;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            position: relative;
        }

        .flowchart-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #27ae60;
        }

        .flowchart-step:last-child::after {
            display: none;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tool-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tool-card:hover {
            border-color: #27ae60;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .tool-card h4 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .key-points {
            background: #fff9c4;
            border: 2px solid #fbc02d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .key-points h4 {
            color: #e65100 !important;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .key-points ul li {
            color: #333 !important;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .algorithm-box h3 {
            color: #2E7D32 !important;
        }

        .comparison-table th {
            color: #FFFFFF !important;
        }

        .comparison-table td {
            color: #333 !important;
        }

        .flowchart-step {
            color: #FFFFFF !important;
            font-weight: bold;
        }

        .tool-card h4 {
            color: #2E7D32 !important;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .tool-card p {
            color: #333 !important;
            line-height: 1.6;
        }

        .fullscreen-btn {
            position: fixed;
            top: 30px;
            right: 100px;
            background: rgba(39, 174, 96, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fullscreen-btn:hover {
            background: rgba(39, 174, 96, 1);
            transform: scale(1.1);
        }

        .single-cell-highlight {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .platform-box {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .platform-box h4 {
            color: #1e8449 !important;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .cell-type {
            background: #f0f8ff;
            border-left: 5px solid #4169e1;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture6_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">15</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题六：单细胞测序技术与数据分析",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🔬 单细胞测序技术与数据分析图形摘要</h3>
                        <svg width="100%" height="500" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient6" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="cellGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="25%" style="stop-color:#f39c12" />
                                    <stop offset="50%" style="stop-color:#27ae60" />
                                    <stop offset="75%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#9b59b6" />
                                </linearGradient>
                                <linearGradient id="singleCellGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead6" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="500" fill="url(#bgGradient6)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">单细胞测序技术与数据分析工作流</text>
                            
                            <!-- 技术平台对比 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">单细胞测序技术平台</text>
                                
                                <!-- 10X Genomics -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">10X Genomics</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">液滴微流控</text>
                                </g>
                                
                                <!-- Smart-seq -->
                                <g transform="translate(190, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Smart-seq</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">全长转录本</text>
                                </g>
                                
                                <!-- Drop-seq -->
                                <g transform="translate(330, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Drop-seq</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">开源平台</text>
                                </g>
                                
                                <!-- 空间转录组 -->
                                <g transform="translate(470, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#3498db" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">空间转录组</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">位置保留</text>
                                </g>
                                
                                <!-- 技术特点 -->
                                <g transform="translate(610, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">技术特点:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">高通量 | 低成本 | 细胞异质性解析</text>
                                </g>
                            </g>
                            
                            <!-- 主要分析流程 - 中部 -->
                            <g transform="translate(50, 160)">
                                <!-- 细胞分离 -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">细胞分离</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">单细胞捕获</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">条形码标记</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">cDNA合成</text>
                                </g>
                                
                                <!-- 质量控制 -->
                                <g transform="translate(130, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">质量控制</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">细胞过滤</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">基因过滤</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">批次校正</text>
                                </g>
                                
                                <!-- 标准化 -->
                                <g transform="translate(260, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">标准化</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">表达标准化</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">特征选择</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">降维分析</text>
                                </g>
                                
                                <!-- 细胞聚类 -->
                                <g transform="translate(390, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">细胞聚类</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">PCA/UMAP</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">Leiden算法</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">细胞类型</text>
                                </g>
                                
                                <!-- 差异分析 -->
                                <g transform="translate(520, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">差异分析</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">标记基因</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">细胞注释</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">功能富集</text>
                                </g>
                                
                                <!-- 轨迹分析 -->
                                <g transform="translate(650, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#34495e" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">轨迹分析</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">发育轨迹</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">伪时间</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">分化路径</text>
                                </g>
                                
                                <!-- 细胞通讯 -->
                                <g transform="translate(780, 0)">
                                    <rect x="0" y="0" width="100" height="60" fill="#e67e22" rx="8" opacity="0.9"/>
                                    <text x="50" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">细胞通讯</text>
                                    <text x="50" y="32" text-anchor="middle" fill="white" font-size="10">配体受体</text>
                                    <text x="50" y="44" text-anchor="middle" fill="white" font-size="9">信号通路</text>
                                    <text x="50" y="56" text-anchor="middle" fill="white" font-size="9">网络分析</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M100,30 L120,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                                <path d="M230,30 L250,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                                <path d="M360,30 L380,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                                <path d="M490,30 L510,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                                <path d="M620,30 L640,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                                <path d="M750,30 L770,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead6)"/>
                            </g>
                            
                            <!-- 细胞异质性展示 - 下部 -->
                            <g transform="translate(50, 280)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">单细胞分析核心概念</text>
                                
                                <!-- 细胞异质性 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">细胞</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">异质性</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 表达差异</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 状态多样</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 功能特化</text>
                                </g>
                                
                                <!-- 细胞类型识别 -->
                                <g transform="translate(200, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">类型</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">识别</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 标记基因</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 聚类分析</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 参考注释</text>
                                </g>
                                
                                <!-- 发育轨迹 -->
                                <g transform="translate(350, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">发育</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">轨迹</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 伪时间</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 分化路径</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 状态转换</text>
                                </g>
                                
                                <!-- 空间信息 -->
                                <g transform="translate(500, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">空间</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">信息</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 位置保留</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 邻域分析</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 组织结构</text>
                                </g>
                                
                                <!-- 细胞通讯 -->
                                <g transform="translate(650, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">细胞</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">通讯</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 配体受体</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 信号传导</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 微环境</text>
                                </g>
                            </g>
                            
                            <!-- 技术挑战与解决方案 - 底部 -->
                            <g transform="translate(50, 420)">
                                <rect x="0" y="0" width="800" height="60" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">技术挑战与解决方案</text>
                                
                                <text x="50" y="40" fill="#2c3e50" font-size="11" font-weight="bold">技术挑战:</text>
                                <text x="50" y="52" fill="#2c3e50" font-size="10">低捕获效率 • 扩增偏差 • 批次效应 • 计算复杂度</text>
                                
                                <text x="450" y="40" fill="#2c3e50" font-size="11" font-weight="bold">解决方案:</text>
                                <text x="450" y="52" fill="#2c3e50" font-size="10">UMI标记 • 标准化算法 • 整合分析 • 高性能计算</text>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M120,320 Q150,300 180,320" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M270,320 Q300,300 330,320" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M420,320 Q450,300 480,320" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M570,320 Q600,300 630,320" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解单细胞测序技术的基本原理、主要技术平台、实验设计考量及其数据分析方法，掌握单细胞RNA-seq数据处理的完整流程和细胞异质性分析策略。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>单细胞测序技术平台与原理</li>
                                <li>单细胞分离与捕获技术</li>
                                <li>单细胞RNA-seq数据分析流程</li>
                                <li>质量控制与预处理方法</li>
                                <li>细胞聚类与类型鉴定</li>
                                <li>轨迹分析与细胞通讯</li>
                                <li>多组学单细胞技术</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>掌握单细胞测序的核心概念</li>
                                <li>理解主流单细胞技术平台原理</li>
                                <li>学会设计单细胞测序实验</li>
                                <li>具备单细胞数据分析能力</li>
                                <li>了解单细胞技术的应用前景</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>单细胞测序技术能够在单个细胞水平解析基因表达、表观遗传等分子特征，揭示细胞异质性和发育轨迹。
                    </div>
                `
            },
            {
                title: "单细胞测序技术概述",
                subtitle: "第一部分：单细胞测序技术平台与原理",
                content: `
                    <h2>单细胞测序 vs 批量测序</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>特征</th>
                                <th>批量测序 (Bulk Sequencing)</th>
                                <th>单细胞测序 (Single-cell Sequencing)</th>
                            </tr>
                            <tr>
                                <td><strong>样本类型</strong></td>
                                <td>大量细胞混合物</td>
                                <td>单个细胞</td>
                            </tr>
                            <tr>
                                <td><strong>信息类型</strong></td>
                                <td>平均水平信息</td>
                                <td>细胞特异性信息</td>
                            </tr>
                            <tr>
                                <td><strong>异质性检测</strong></td>
                                <td>无法检测</td>
                                <td>可以检测细胞间差异</td>
                            </tr>
                            <tr>
                                <td><strong>稀有细胞</strong></td>
                                <td>信号被稀释</td>
                                <td>可以识别稀有细胞类型</td>
                            </tr>
                            <tr>
                                <td><strong>成本</strong></td>
                                <td>相对较低</td>
                                <td>相对较高</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🔬 单细胞异质性研究的意义</h4>
                        <ul>
                            <li><strong>揭示细胞间差异：</strong>发现同一组织中不同细胞的功能状态</li>
                            <li><strong>发现新细胞类型：</strong>识别传统方法无法检测的稀有细胞群</li>
                            <li><strong>研究细胞分化：</strong>追踪细胞发育和分化轨迹</li>
                            <li><strong>疾病机制研究：</strong>理解疾病发生发展的细胞基础</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术发展历程</h3>
                            <ul>
                                <li><strong>2009年：</strong>首个单细胞RNA-seq技术</li>
                                <li><strong>2013年：</strong>Drop-seq和inDrop技术</li>
                                <li><strong>2015年：</strong>10x Genomics商业化</li>
                                <li><strong>2018年：</strong>空间转录组技术</li>
                                <li><strong>2020年：</strong>多组学单细胞技术</li>
                                <li><strong>2023年：</strong>高分辨率空间技术</li>
                            </ul>
                        </div>
                        <div>
                            <h3>应用领域</h3>
                            <ul>
                                <li><strong>发育生物学：</strong>胚胎发育轨迹</li>
                                <li><strong>免疫学：</strong>免疫细胞功能</li>
                                <li><strong>神经生物学：</strong>神经元多样性</li>
                                <li><strong>肿瘤生物学：</strong>肿瘤异质性</li>
                                <li><strong>药物研发：</strong>药物作用机制</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 技术革命：</strong>单细胞测序技术正在改变我们对生物学的理解，从群体平均转向个体细胞精度。
                    </div>
                `
            },
            {
                title: "单细胞分离与捕获技术",
                subtitle: "细胞分离的技术策略与平台比较",
                content: `
                    <h2>主要单细胞分离技术</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>流式细胞分选(FACS)</h4>
                            <p><strong>原理：</strong>荧光标记抗体识别<br>
                            <strong>优势：</strong>细胞选择性高<br>
                            <strong>劣势：</strong>通量低，需要标记</p>
                        </div>
                        <div class="tool-card">
                            <h4>显微操作</h4>
                            <p><strong>原理：</strong>显微镜下手动挑选<br>
                            <strong>优势：</strong>精确度高，适合稀有细胞<br>
                            <strong>劣势：</strong>通量极低，操作复杂</p>
                        </div>
                        <div class="tool-card">
                            <h4>微流控技术</h4>
                            <p><strong>原理：</strong>微通道控制细胞流动<br>
                            <strong>优势：</strong>高通量，自动化<br>
                            <strong>劣势：</strong>细胞选择性较低</p>
                        </div>
                        <div class="tool-card">
                            <h4>液滴法</h4>
                            <p><strong>原理：</strong>细胞包裹在微液滴中<br>
                            <strong>优势：</strong>高通量，成本低<br>
                            <strong>劣势：</strong>细胞选择性有限</p>
                        </div>
                    </div>

                    <div class="platform-box">
                        <h4>技术选择考虑因素</h4>
                        <div class="two-column">
                            <div>
                                <h3>实验需求</h3>
                                <ul>
                                    <li><strong>细胞数量：</strong>几十个 vs 数万个</li>
                                    <li><strong>细胞类型：</strong>特定类型 vs 全部细胞</li>
                                    <li><strong>分析深度：</strong>全长转录本 vs 3'端</li>
                                    <li><strong>时间要求：</strong>快速筛选 vs 精细分析</li>
                                </ul>
                            </div>
                            <div>
                                <h3>技术限制</h3>
                                <ul>
                                    <li><strong>成本预算：</strong>设备和试剂成本</li>
                                    <li><strong>技术难度：</strong>操作复杂程度</li>
                                    <li><strong>设备可用性：</strong>仪器设备要求</li>
                                    <li><strong>样本特性：</strong>细胞大小和脆性</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>液滴法技术原理</h3>
                        <ol>
                            <li><strong>液滴形成：</strong>微流控芯片产生单分散液滴</li>
                            <li><strong>细胞包封：</strong>单个细胞被包裹在液滴中</li>
                            <li><strong>条形码标记：</strong>每个液滴含有唯一条形码</li>
                            <li><strong>反应进行：</strong>细胞裂解和RNA捕获</li>
                            <li><strong>液滴破碎：</strong>收集产物进行后续处理</li>
                        </ol>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 技术挑战：</strong>细胞活力保持、双细胞污染、捕获效率和技术偏好性是所有单细胞分离技术面临的共同挑战。
                    </div>
                `
            },
            {
                title: "主要单细胞RNA-seq技术平台",
                subtitle: "商业化平台的技术特点与应用",
                content: `
                    <h2>10x Genomics Chromium系统</h2>

                    <div class="algorithm-box">
                        <h3>GEM液滴形成原理</h3>
                        <ol>
                            <li><strong>微流控芯片：</strong>精确控制液滴大小和细胞分布</li>
                            <li><strong>条形码珠：</strong>每个珠子含有~750,000个寡核苷酸</li>
                            <li><strong>细胞条形码：</strong>16bp序列区分不同细胞</li>
                            <li><strong>UMI标记：</strong>12bp序列区分不同RNA分子</li>
                            <li><strong>反转录：</strong>在液滴内完成cDNA合成</li>
                        </ol>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>平台</th>
                                <th>技术原理</th>
                                <th>通量</th>
                                <th>优势</th>
                                <th>局限性</th>
                            </tr>
                            <tr>
                                <td><strong>10x Genomics</strong></td>
                                <td>液滴微流控</td>
                                <td>500-10,000细胞</td>
                                <td>标准化，易用</td>
                                <td>成本高，3'端测序</td>
                            </tr>
                            <tr>
                                <td><strong>Drop-seq</strong></td>
                                <td>液滴微流控</td>
                                <td>数千细胞</td>
                                <td>开源，成本低</td>
                                <td>操作复杂</td>
                            </tr>
                            <tr>
                                <td><strong>Smart-seq2</strong></td>
                                <td>微孔板法</td>
                                <td>96-384细胞</td>
                                <td>全长转录本</td>
                                <td>通量低，成本高</td>
                            </tr>
                            <tr>
                                <td><strong>BD Rhapsody</strong></td>
                                <td>微孔板法</td>
                                <td>数百细胞</td>
                                <td>多重PCR</td>
                                <td>通量有限</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>10x Genomics技术参数</h3>
                            <ul>
                                <li><strong>细胞捕获率：</strong>~65%</li>
                                <li><strong>双细胞率：</strong><1%</li>
                                <li><strong>基因检测数：</strong>~2,000-8,000/细胞</li>
                                <li><strong>UMI数量：</strong>~1,000-10,000/细胞</li>
                                <li><strong>测序深度：</strong>~50,000 reads/细胞</li>
                            </ul>
                        </div>
                        <div>
                            <h3>Smart-seq2技术特点</h3>
                            <ul>
                                <li><strong>全长覆盖：</strong>5'到3'端完整序列</li>
                                <li><strong>高灵敏度：</strong>检测更多基因</li>
                                <li><strong>剪接分析：</strong>可分析可变剪接</li>
                                <li><strong>SNP检测：</strong>单核苷酸变异分析</li>
                                <li><strong>等位基因：</strong>等位基因特异性表达</li>
                            </ul>
                        </div>
                    </div>

                    <div class="platform-box">
                        <h4>新兴技术平台</h4>
                        <ul>
                            <li><strong>Parse Biosciences (SPLiT-seq)：</strong>组合索引技术，超高通量</li>
                            <li><strong>Microwell-seq：</strong>微孔阵列技术</li>
                            <li><strong>CEL-seq2：</strong>线性扩增技术</li>
                            <li><strong>MARS-seq：</strong>大规模并行测序</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "单细胞RNA-seq数据分析流程",
                subtitle: "第二部分：单细胞RNA-seq数据分析流程",
                content: `
                    <h2>标准分析流程框架</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">原始数据<br>处理</div>
                        <div class="flowchart-step">质量控制<br>预处理</div>
                        <div class="flowchart-step">特征选择<br>降维</div>
                        <div class="flowchart-step">细胞聚类<br>鉴定</div>
                        <div class="flowchart-step">下游分析<br>功能</div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 计算挑战与解决策略</h4>
                        <ul>
                            <li><strong>高维稀疏矩阵：</strong>使用降维方法(PCA、t-SNE、UMAP)</li>
                            <li><strong>技术噪声：</strong>标准化方法(LogNormalization、SCTransform)</li>
                            <li><strong>批次效应：</strong>批次校正(Harmony、Seurat整合)</li>
                            <li><strong>计算资源：</strong>并行计算、云计算资源利用</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要分析工具</h3>
                            <ul>
                                <li><strong>Seurat (R)：</strong>最流行的R包</li>
                                <li><strong>Scanpy (Python)：</strong>Python生态系统</li>
                                <li><strong>Monocle (R)：</strong>轨迹分析专用</li>
                                <li><strong>CellChat (R)：</strong>细胞通讯分析</li>
                                <li><strong>SingleCellExperiment：</strong>Bioconductor框架</li>
                            </ul>
                        </div>
                        <div>
                            <h3>分析平台选择</h3>
                            <ul>
                                <li><strong>R语言：</strong>统计分析强，可视化丰富</li>
                                <li><strong>Python：</strong>机器学习强，深度学习支持</li>
                                <li><strong>云平台：</strong>Terra、Galaxy、Seurat在线</li>
                                <li><strong>商业软件：</strong>Loupe Browser、Partek</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>原始数据处理步骤</h3>
                        <ol>
                            <li><strong>测序数据质控：</strong>FastQC评估数据质量</li>
                            <li><strong>条形码识别：</strong>细胞条形码和UMI提取</li>
                            <li><strong>序列比对：</strong>HISAT2/STAR或伪比对Alevin/Kallisto</li>
                            <li><strong>UMI去重：</strong>PCR重复序列去除</li>
                            <li><strong>表达矩阵：</strong>基因×细胞计数矩阵生成</li>
                        </ol>
                    </div>

                    <div class="info-box">
                        <strong>💡 分析策略：</strong>单细胞数据分析需要平衡技术噪声去除和生物学信号保留，选择合适的方法和参数至关重要。
                    </div>
                `
            },
            {
                title: "Seurat分析框架",
                subtitle: "R语言单细胞分析的标准流程",
                content: `
                    <h2>Seurat对象数据结构</h2>

                    <div class="algorithm-box">
                        <h3>Seurat对象组成</h3>
                        <ul>
                            <li><strong>counts：</strong>原始计数矩阵</li>
                            <li><strong>data：</strong>标准化后的表达矩阵</li>
                            <li><strong>scale.data：</strong>缩放后的数据用于降维</li>
                            <li><strong>meta.data：</strong>细胞元数据信息</li>
                            <li><strong>reductions：</strong>降维结果(PCA、UMAP等)</li>
                        </ul>
                    </div>

                    <div class="code-block"><span class="code-comment"># Seurat标准分析流程</span>
<span class="code-keyword">library</span>(<span class="code-string">Seurat</span>)

<span class="code-comment"># 1. 创建Seurat对象</span>
seurat_obj <- <span class="code-function">CreateSeuratObject</span>(counts = count_matrix,
                                project = <span class="code-string">"scRNA_analysis"</span>)

<span class="code-comment"># 2. 质量控制指标计算</span>
seurat_obj[[<span class="code-string">"percent.mt"</span>]] <- <span class="code-function">PercentageFeatureSet</span>(seurat_obj,
                                                   pattern = <span class="code-string">"^MT-"</span>)

<span class="code-comment"># 3. 细胞过滤</span>
seurat_obj <- <span class="code-function">subset</span>(seurat_obj,
                    subset = nFeature_RNA > 200 &
                            nFeature_RNA < 5000 &
                            percent.mt < 20)

<span class="code-comment"># 4. 数据标准化和特征选择</span>
seurat_obj <- <span class="code-function">NormalizeData</span>(seurat_obj)
seurat_obj <- <span class="code-function">FindVariableFeatures</span>(seurat_obj)
seurat_obj <- <span class="code-function">ScaleData</span>(seurat_obj)

<span class="code-comment"># 5. 降维分析</span>
seurat_obj <- <span class="code-function">RunPCA</span>(seurat_obj)
seurat_obj <- <span class="code-function">RunUMAP</span>(seurat_obj, dims = 1:20)

<span class="code-comment"># 6. 细胞聚类</span>
seurat_obj <- <span class="code-function">FindNeighbors</span>(seurat_obj, dims = 1:20)
seurat_obj <- <span class="code-function">FindClusters</span>(seurat_obj, resolution = 0.5)</div>

                    <div class="two-column">
                        <div>
                            <h3>主要功能模块</h3>
                            <ul>
                                <li><strong>数据质控：</strong>QC指标计算和过滤</li>
                                <li><strong>标准化：</strong>LogNormalization、SCTransform</li>
                                <li><strong>特征选择：</strong>高变异基因识别</li>
                                <li><strong>降维：</strong>PCA、t-SNE、UMAP</li>
                                <li><strong>聚类：</strong>图论聚类算法</li>
                                <li><strong>差异分析：</strong>多种统计检验方法</li>
                            </ul>
                        </div>
                        <div>
                            <h3>可视化功能</h3>
                            <ul>
                                <li><strong>DimPlot：</strong>降维结果可视化</li>
                                <li><strong>FeaturePlot：</strong>基因表达可视化</li>
                                <li><strong>VlnPlot：</strong>小提琴图展示分布</li>
                                <li><strong>DoHeatmap：</strong>热图展示表达模式</li>
                                <li><strong>DotPlot：</strong>点图展示标记基因</li>
                            </ul>
                        </div>
                    </div>

                    <div class="platform-box">
                        <h4>Seurat vs Scanpy比较</h4>
                        <div class="comparison-table">
                            <table>
                                <tr>
                                    <th>特征</th>
                                    <th>Seurat (R)</th>
                                    <th>Scanpy (Python)</th>
                                </tr>
                                <tr>
                                    <td><strong>数据结构</strong></td>
                                    <td>Seurat对象</td>
                                    <td>AnnData对象</td>
                                </tr>
                                <tr>
                                    <td><strong>生态系统</strong></td>
                                    <td>R/Bioconductor</td>
                                    <td>Python/scikit-learn</td>
                                </tr>
                                <tr>
                                    <td><strong>可视化</strong></td>
                                    <td>ggplot2基础</td>
                                    <td>matplotlib基础</td>
                                </tr>
                                <tr>
                                    <td><strong>机器学习</strong></td>
                                    <td>有限支持</td>
                                    <td>丰富的ML库</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                `
            },
            {
                title: "单细胞数据质量控制",
                subtitle: "第三部分：单细胞数据特有的质控与预处理方法",
                content: `
                    <h2>单细胞数据特点与挑战</h2>

                    <div class="key-points">
                        <h4>🎯 数据特征与挑战</h4>
                        <ul>
                            <li><strong>高维稀疏矩阵：</strong>大部分基因在大部分细胞中不表达</li>
                            <li><strong>技术噪声vs生物变异：</strong>区分真实生物学差异和技术误差</li>
                            <li><strong>零膨胀现象：</strong>大量零值可能来自技术限制</li>
                            <li><strong>Dropout问题：</strong>低表达基因检测不到</li>
                            <li><strong>批次效应：</strong>实验批次间的系统性差异</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>细胞水平质控指标</h3>
                            <ul>
                                <li><strong>检测基因数：</strong>nFeature_RNA</li>
                                <li><strong>UMI总数：</strong>nCount_RNA</li>
                                <li><strong>线粒体基因比例：</strong>percent.mt</li>
                                <li><strong>核糖体基因比例：</strong>percent.ribo</li>
                                <li><strong>细胞周期评分：</strong>S/G2M期标记</li>
                            </ul>
                        </div>
                        <div>
                            <h3>基因水平质控指标</h3>
                            <ul>
                                <li><strong>表达细胞数：</strong>基因在多少细胞中表达</li>
                                <li><strong>平均表达量：</strong>跨细胞平均表达水平</li>
                                <li><strong>变异系数：</strong>表达变异程度</li>
                                <li><strong>检测频率：</strong>非零表达的细胞比例</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>细胞过滤策略</h3>
                        <div class="code-block"><span class="code-comment"># 单细胞数据质量控制</span>

<span class="code-comment"># 1. 计算质控指标</span>
seurat_obj[[<span class="code-string">"percent.mt"</span>]] <- <span class="code-function">PercentageFeatureSet</span>(seurat_obj,
                                                   pattern = <span class="code-string">"^MT-"</span>)
seurat_obj[[<span class="code-string">"percent.ribo"</span>]] <- <span class="code-function">PercentageFeatureSet</span>(seurat_obj,
                                                     pattern = <span class="code-string">"^RP[SL]"</span>)

<span class="code-comment"># 2. 设置过滤标准</span>
seurat_obj <- <span class="code-function">subset</span>(seurat_obj, subset =
                    nFeature_RNA > 200 &     <span class="code-comment"># 最少检测基因数</span>
                    nFeature_RNA < 5000 &    <span class="code-comment"># 最多检测基因数</span>
                    nCount_RNA > 500 &       <span class="code-comment"># 最少UMI数</span>
                    percent.mt < 20)         <span class="code-comment"># 线粒体基因比例</span></div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 质控参数优化：</strong>过滤阈值需要根据具体数据特点调整，过严可能丢失重要细胞类型，过松可能保留低质量细胞。
                    </div>
                `
            },
            {
                title: "数据标准化与特征选择",
                subtitle: "单细胞数据预处理的核心步骤",
                content: `
                    <h2>标准化方法比较</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>方法</th>
                                <th>原理</th>
                                <th>优势</th>
                                <th>局限性</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>LogNormalization</strong></td>
                                <td>log(CPM/100 + 1)</td>
                                <td>简单快速</td>
                                <td>假设所有细胞总RNA相同</td>
                                <td>标准分析</td>
                            </tr>
                            <tr>
                                <td><strong>SCTransform</strong></td>
                                <td>负二项回归模型</td>
                                <td>考虑技术噪声</td>
                                <td>计算复杂</td>
                                <td>高质量数据</td>
                            </tr>
                            <tr>
                                <td><strong>scran</strong></td>
                                <td>池化标准化</td>
                                <td>稳健性好</td>
                                <td>需要预聚类</td>
                                <td>异质性高的数据</td>
                            </tr>
                            <tr>
                                <td><strong>Linnorm</strong></td>
                                <td>线性模型</td>
                                <td>保持基因间关系</td>
                                <td>参数敏感</td>
                                <td>差异表达分析</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>SCTransform算法原理</h3>
                        <ol>
                            <li><strong>负二项回归：</strong>对每个基因建立GLM模型</li>
                            <li><strong>技术噪声建模：</strong>UMI数量作为协变量</li>
                            <li><strong>残差计算：</strong>提取标准化残差</li>
                            <li><strong>方差稳定：</strong>消除均值-方差依赖关系</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># SCTransform使用示例</span>
<span class="code-keyword">library</span>(<span class="code-string">Seurat</span>)

<span class="code-comment"># 执行SCTransform标准化</span>
seurat_obj <- <span class="code-function">SCTransform</span>(seurat_obj,
                         vars.to.regress = <span class="code-string">"percent.mt"</span>,
                         verbose = <span class="code-keyword">FALSE</span>)

<span class="code-comment"># 查看高变异基因</span>
top_features <- <span class="code-function">head</span>(<span class="code-function">VariableFeatures</span>(seurat_obj), 20)
plot1 <- <span class="code-function">VariableFeaturePlot</span>(seurat_obj)</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>高变异基因选择策略</h3>
                            <ul>
                                <li><strong>方差-均值关系：</strong>识别高于期望变异的基因</li>
                                <li><strong>标准化方差：</strong>VST (Variance Stabilizing Transformation)</li>
                                <li><strong>基因数量：</strong>通常选择2000-3000个基因</li>
                                <li><strong>生物学意义：</strong>排除细胞周期、应激反应基因</li>
                            </ul>
                        </div>
                        <div>
                            <h3>批次效应处理</h3>
                            <ul>
                                <li><strong>Harmony：</strong>迭代聚类和校正</li>
                                <li><strong>Seurat Integration：</strong>锚点识别和整合</li>
                                <li><strong>Combat：</strong>线性模型批次校正</li>
                                <li><strong>scVI：</strong>变分自编码器方法</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 方法选择：</strong>SCTransform适合高质量数据，LogNormalization适合快速分析，批次效应严重时需要专门的整合方法。
                    </div>
                `
            },
            {
                title: "降维算法详解",
                subtitle: "PCA、t-SNE、UMAP算法原理与应用",
                content: `
                    <h2>主成分分析(PCA)原理</h2>

                    <div class="algorithm-box">
                        <h3>PCA数学原理</h3>
                        <ol>
                            <li><strong>数据中心化：</strong>X_centered = X - mean(X)</li>
                            <li><strong>协方差矩阵：</strong>C = X_centered^T × X_centered</li>
                            <li><strong>特征值分解：</strong>C = V × Λ × V^T</li>
                            <li><strong>主成分选择：</strong>选择前k个最大特征值对应的特征向量</li>
                            <li><strong>数据投影：</strong>Y = X_centered × V_k</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># PCA分析示例</span>
seurat_obj <- <span class="code-function">RunPCA</span>(seurat_obj,
                    features = <span class="code-function">VariableFeatures</span>(object = seurat_obj))

<span class="code-comment"># 查看主成分贡献</span>
<span class="code-function">print</span>(seurat_obj[[<span class="code-string">"pca"</span>]], dims = 1:5, nfeatures = 5)

<span class="code-comment"># 肘部图确定PC数量</span>
<span class="code-function">ElbowPlot</span>(seurat_obj, ndims = 50)

<span class="code-comment"># 热图可视化主成分</span>
<span class="code-function">DimHeatmap</span>(seurat_obj, dims = 1:6, cells = 500, balanced = <span class="code-keyword">TRUE</span>)</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>算法</th>
                                <th>类型</th>
                                <th>保持性质</th>
                                <th>计算复杂度</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>PCA</strong></td>
                                <td>线性降维</td>
                                <td>全局结构</td>
                                <td>O(n²)</td>
                                <td>初步降维</td>
                            </tr>
                            <tr>
                                <td><strong>t-SNE</strong></td>
                                <td>非线性降维</td>
                                <td>局部结构</td>
                                <td>O(n²)</td>
                                <td>可视化</td>
                            </tr>
                            <tr>
                                <td><strong>UMAP</strong></td>
                                <td>非线性降维</td>
                                <td>局部+全局</td>
                                <td>O(n log n)</td>
                                <td>聚类+可视化</td>
                            </tr>
                            <tr>
                                <td><strong>diffusion map</strong></td>
                                <td>非线性降维</td>
                                <td>流形结构</td>
                                <td>O(n²)</td>
                                <td>轨迹分析</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>t-SNE算法特点</h3>
                            <ul>
                                <li><strong>概率分布：</strong>高维空间用高斯分布</li>
                                <li><strong>低维映射：</strong>低维空间用t分布</li>
                                <li><strong>KL散度：</strong>最小化两个分布的差异</li>
                                <li><strong>参数调节：</strong>perplexity影响局部邻域大小</li>
                                <li><strong>随机性：</strong>每次运行结果略有不同</li>
                            </ul>
                        </div>
                        <div>
                            <h3>UMAP算法优势</h3>
                            <ul>
                                <li><strong>拓扑保持：</strong>基于拓扑数据分析</li>
                                <li><strong>速度快：</strong>近似最近邻算法</li>
                                <li><strong>参数稳定：</strong>默认参数通常效果好</li>
                                <li><strong>全局结构：</strong>更好保持全局关系</li>
                                <li><strong>确定性：</strong>固定随机种子结果一致</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 降维注意事项：</strong>t-SNE和UMAP主要用于可视化，不应直接用于下游分析。聚类应基于PCA空间进行。
                    </div>
                `
            },
            {
                title: "细胞聚类算法",
                subtitle: "第四部分：细胞聚类与类型鉴定",
                content: `
                    <h2>图论聚类算法原理</h2>

                    <div class="algorithm-box">
                        <h3>Louvain聚类算法步骤</h3>
                        <ol>
                            <li><strong>构建KNN图：</strong>基于PCA空间计算细胞间距离</li>
                            <li><strong>边权重计算：</strong>使用Jaccard相似性或SNN</li>
                            <li><strong>模块度优化：</strong>迭代优化社区划分</li>
                            <li><strong>层次聚类：</strong>构建聚类层次结构</li>
                            <li><strong>分辨率调节：</strong>控制聚类粒度</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># Seurat聚类流程</span>
<span class="code-comment"># 1. 构建邻接图</span>
seurat_obj <- <span class="code-function">FindNeighbors</span>(seurat_obj, dims = 1:20)

<span class="code-comment"># 2. 多分辨率聚类</span>
seurat_obj <- <span class="code-function">FindClusters</span>(seurat_obj,
                          resolution = <span class="code-function">c</span>(0.1, 0.3, 0.5, 0.8, 1.0))

<span class="code-comment"># 3. 查看不同分辨率结果</span>
<span class="code-keyword">library</span>(<span class="code-string">clustree</span>)
<span class="code-function">clustree</span>(seurat_obj, prefix = <span class="code-string">"RNA_snn_res."</span>)

<span class="code-comment"># 4. 选择最优分辨率</span>
<span class="code-function">Idents</span>(seurat_obj) <- <span class="code-string">"RNA_snn_res.0.5"</span></div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>Louvain算法</h4>
                            <p><strong>原理：</strong>模块度优化<br>
                            <strong>优势：</strong>快速，适合大数据<br>
                            <strong>参数：</strong>分辨率控制聚类数</p>
                        </div>
                        <div class="tool-card">
                            <h4>Leiden算法</h4>
                            <p><strong>原理：</strong>改进的Louvain<br>
                            <strong>优势：</strong>更好的聚类质量<br>
                            <strong>特点：</strong>保证连通的社区</p>
                        </div>
                        <div class="tool-card">
                            <h4>K-means</h4>
                            <p><strong>原理：</strong>距离最小化<br>
                            <strong>优势：</strong>简单直观<br>
                            <strong>局限：</strong>需要预设聚类数</p>
                        </div>
                        <div class="tool-card">
                            <h4>层次聚类</h4>
                            <p><strong>原理：</strong>距离矩阵聚合<br>
                            <strong>优势：</strong>层次结构清晰<br>
                            <strong>局限：</strong>计算复杂度高</p>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 聚类质量评估</h4>
                        <ul>
                            <li><strong>轮廓系数：</strong>衡量聚类内聚性和分离性</li>
                            <li><strong>模块度：</strong>图论聚类质量指标</li>
                            <li><strong>稳定性：</strong>重采样聚类一致性</li>
                            <li><strong>生物学意义：</strong>标记基因表达模式</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>分辨率参数选择</h3>
                            <ul>
                                <li><strong>低分辨率(0.1-0.3)：</strong>粗粒度聚类</li>
                                <li><strong>中分辨率(0.4-0.8)：</strong>标准细胞类型</li>
                                <li><strong>高分辨率(0.9-2.0)：</strong>细分亚群</li>
                                <li><strong>自适应选择：</strong>基于数据特点调整</li>
                            </ul>
                        </div>
                        <div>
                            <h3>聚类结果验证</h3>
                            <ul>
                                <li><strong>标记基因：</strong>已知细胞类型标记</li>
                                <li><strong>功能富集：</strong>GO/KEGG通路分析</li>
                                <li><strong>文献验证：</strong>与已发表结果比较</li>
                                <li><strong>实验验证：</strong>流式细胞术等</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "细胞类型注释方法",
                subtitle: "自动化细胞类型识别策略",
                content: `
                    <h2>细胞类型注释策略</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>方法类型</th>
                                <th>代表工具</th>
                                <th>原理</th>
                                <th>优势</th>
                                <th>局限性</th>
                            </tr>
                            <tr>
                                <td><strong>标记基因法</strong></td>
                                <td>手动注释</td>
                                <td>已知标记基因表达</td>
                                <td>准确性高</td>
                                <td>依赖先验知识</td>
                            </tr>
                            <tr>
                                <td><strong>参考数据集</strong></td>
                                <td>SingleR, Azimuth</td>
                                <td>与参考数据比较</td>
                                <td>自动化程度高</td>
                                <td>受参考数据限制</td>
                            </tr>
                            <tr>
                                <td><strong>机器学习</strong></td>
                                <td>scPred, CellTypist</td>
                                <td>监督学习分类</td>
                                <td>泛化能力强</td>
                                <td>需要训练数据</td>
                            </tr>
                            <tr>
                                <td><strong>无监督方法</strong></td>
                                <td>scCATCH, CellMarker</td>
                                <td>基于表达模式</td>
                                <td>发现新类型</td>
                                <td>准确性有限</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>SingleR算法原理</h3>
                        <ol>
                            <li><strong>参考数据准备：</strong>已注释的单细胞或批量数据</li>
                            <li><strong>特征基因选择：</strong>每个细胞类型的标记基因</li>
                            <li><strong>相关性计算：</strong>查询细胞与参考细胞类型的相关性</li>
                            <li><strong>精细调节：</strong>使用可变基因进行精确分类</li>
                            <li><strong>置信度评估：</strong>计算分类置信度分数</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># SingleR细胞类型注释</span>
<span class="code-keyword">library</span>(<span class="code-string">SingleR</span>)
<span class="code-keyword">library</span>(<span class="code-string">celldex</span>)

<span class="code-comment"># 1. 获取参考数据集</span>
ref <- <span class="code-function">HumanPrimaryCellAtlasData</span>()

<span class="code-comment"># 2. 执行细胞类型注释</span>
predictions <- <span class="code-function">SingleR</span>(test = seurat_obj@assays$RNA@data,
                      ref = ref,
                      labels = ref$label.main)

<span class="code-comment"># 3. 添加注释结果到Seurat对象</span>
seurat_obj$SingleR_labels <- predictions$labels

<span class="code-comment"># 4. 可视化注释结果</span>
<span class="code-function">DimPlot</span>(seurat_obj, group.by = <span class="code-string">"SingleR_labels"</span>, label = <span class="code-keyword">TRUE</span>)</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>标记基因数据库</h3>
                            <ul>
                                <li><strong>CellMarker：</strong>人工整理的标记基因</li>
                                <li><strong>PanglaoDB：</strong>单细胞标记基因数据库</li>
                                <li><strong>CellTypist：</strong>机器学习训练的模型</li>
                                <li><strong>Azimuth：</strong>Seurat官方参考数据</li>
                            </ul>
                        </div>
                        <div>
                            <h3>注释质量控制</h3>
                            <ul>
                                <li><strong>置信度分数：</strong>分类确定性评估</li>
                                <li><strong>标记基因验证：</strong>检查关键标记基因</li>
                                <li><strong>聚类一致性：</strong>同一聚类的注释一致性</li>
                                <li><strong>生物学合理性：</strong>组织特异性验证</li>
                            </ul>
                        </div>
                    </div>

                    <div class="cell-type">
                        <h4>常见细胞类型标记基因示例</h4>
                        <ul>
                            <li><strong>T细胞：</strong>CD3D, CD3E, CD3G</li>
                            <li><strong>B细胞：</strong>CD19, CD20, MS4A1</li>
                            <li><strong>NK细胞：</strong>GNLY, NKG7, KLRD1</li>
                            <li><strong>单核细胞：</strong>CD14, FCGR3A, LYZ</li>
                            <li><strong>树突状细胞：</strong>FCER1A, CST3</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "差异表达分析",
                subtitle: "单细胞差异表达的统计方法",
                content: `
                    <h2>单细胞差异表达分析挑战</h2>

                    <div class="key-points">
                        <h4>🎯 单细胞DE分析的特殊挑战</h4>
                        <ul>
                            <li><strong>零膨胀：</strong>大量基因在单细胞中不表达</li>
                            <li><strong>过度离散：</strong>方差远大于均值</li>
                            <li><strong>伪重复：</strong>同一供体的多个细胞不独立</li>
                            <li><strong>多重检验：</strong>数万基因同时检验</li>
                            <li><strong>细胞异质性：</strong>亚群内部仍有差异</li>
                        </ul>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>方法</th>
                                <th>统计模型</th>
                                <th>零膨胀处理</th>
                                <th>速度</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>Wilcoxon</strong></td>
                                <td>非参数检验</td>
                                <td>天然处理</td>
                                <td>快</td>
                                <td>快速筛选</td>
                            </tr>
                            <tr>
                                <td><strong>MAST</strong></td>
                                <td>两部分模型</td>
                                <td>专门建模</td>
                                <td>中等</td>
                                <td>精确分析</td>
                            </tr>
                            <tr>
                                <td><strong>DESeq2</strong></td>
                                <td>负二项分布</td>
                                <td>间接处理</td>
                                <td>慢</td>
                                <td>伪批量分析</td>
                            </tr>
                            <tr>
                                <td><strong>scDE</strong></td>
                                <td>混合模型</td>
                                <td>专门建模</td>
                                <td>慢</td>
                                <td>小数据集</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>MAST两部分模型</h3>
                        <ol>
                            <li><strong>离散部分：</strong>logistic回归建模基因检测概率</li>
                            <li><strong>连续部分：</strong>高斯模型建模非零表达水平</li>
                            <li><strong>协变量校正：</strong>细胞检测率、批次等</li>
                            <li><strong>似然比检验：</strong>组合两部分进行统计检验</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># Seurat差异表达分析</span>

<span class="code-comment"># 1. Wilcoxon检验 (默认方法)</span>
markers <- <span class="code-function">FindMarkers</span>(seurat_obj,
                      ident.1 = <span class="code-string">"cluster1"</span>,
                      ident.2 = <span class="code-string">"cluster2"</span>)

<span class="code-comment"># 2. MAST方法 (处理零膨胀)</span>
markers_mast <- <span class="code-function">FindMarkers</span>(seurat_obj,
                           ident.1 = <span class="code-string">"cluster1"</span>,
                           ident.2 = <span class="code-string">"cluster2"</span>,
                           test.use = <span class="code-string">"MAST"</span>)

<span class="code-comment"># 3. 所有聚类的标记基因</span>
all_markers <- <span class="code-function">FindAllMarkers</span>(seurat_obj,
                             only.pos = <span class="code-keyword">TRUE</span>,
                             min.pct = 0.25,
                             logfc.threshold = 0.25)</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>伪批量分析策略</h3>
                            <ul>
                                <li><strong>细胞聚合：</strong>按供体和细胞类型聚合</li>
                                <li><strong>批量方法：</strong>使用DESeq2、edgeR等</li>
                                <li><strong>生物学重复：</strong>考虑供体间变异</li>
                                <li><strong>统计功效：</strong>更好的假阳性控制</li>
                            </ul>
                        </div>
                        <div>
                            <h3>结果解读与验证</h3>
                            <ul>
                                <li><strong>效应大小：</strong>log2FC阈值设置</li>
                                <li><strong>表达比例：</strong>pct.1 vs pct.2</li>
                                <li><strong>统计显著性：</strong>调整后p值</li>
                                <li><strong>生物学验证：</strong>qPCR、免疫荧光</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "轨迹分析与伪时间",
                subtitle: "第五部分：轨迹分析与细胞通讯",
                content: `
                    <h2>细胞轨迹分析原理</h2>

                    <div class="algorithm-box">
                        <h3>Monocle3轨迹推断算法</h3>
                        <ol>
                            <li><strong>降维：</strong>UMAP降维到低维空间</li>
                            <li><strong>图构建：</strong>构建细胞间的k近邻图</li>
                            <li><strong>轨迹学习：</strong>使用SimplePPT算法学习主图</li>
                            <li><strong>伪时间计算：</strong>从根节点计算到各细胞的距离</li>
                            <li><strong>分支识别：</strong>检测分化分支点</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># Monocle3轨迹分析完整流程</span>
<span class="code-keyword">library</span>(<span class="code-string">monocle3</span>)

<span class="code-comment"># 1. 创建CDS对象</span>
cds <- <span class="code-function">new_cell_data_set</span>(seurat_obj@assays$RNA@counts,
                        cell_metadata = <EMAIL>,
                        gene_metadata = <span class="code-function">data.frame</span>(
                          gene_short_name = <span class="code-function">rownames</span>(seurat_obj)))

<span class="code-comment"># 2. 预处理和降维</span>
cds <- <span class="code-function">preprocess_cds</span>(cds, num_dim = 50)
cds <- <span class="code-function">reduce_dimension</span>(cds)

<span class="code-comment"># 3. 聚类和学习轨迹图</span>
cds <- <span class="code-function">cluster_cells</span>(cds)
cds <- <span class="code-function">learn_graph</span>(cds)

<span class="code-comment"># 4. 计算伪时间</span>
cds <- <span class="code-function">order_cells</span>(cds)

<span class="code-comment"># 5. 可视化轨迹和伪时间</span>
<span class="code-function">plot_cells</span>(cds, color_cells_by = <span class="code-string">"pseudotime"</span>)</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>Monocle3</h4>
                            <p><strong>方法：</strong>主图学习<br>
                            <strong>优势：</strong>处理复杂轨迹<br>
                            <strong>特点：</strong>分支检测能力强</p>
                        </div>
                        <div class="tool-card">
                            <h4>Slingshot</h4>
                            <p><strong>方法：</strong>最小生成树<br>
                            <strong>优势：</strong>简单直观<br>
                            <strong>特点：</strong>基于聚类构建轨迹</p>
                        </div>
                        <div class="tool-card">
                            <h4>PAGA</h4>
                            <p><strong>方法：</strong>分区图抽象<br>
                            <strong>优势：</strong>保持拓扑结构<br>
                            <strong>特点：</strong>适合复杂分化</p>
                        </div>
                        <div class="tool-card">
                            <h4>Velocyto</h4>
                            <p><strong>方法：</strong>RNA速度<br>
                            <strong>优势：</strong>预测分化方向<br>
                            <strong>特点：</strong>基于剪接动力学</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>轨迹分析应用</h3>
                            <ul>
                                <li><strong>发育生物学：</strong>胚胎发育轨迹</li>
                                <li><strong>细胞分化：</strong>干细胞分化过程</li>
                                <li><strong>疾病进展：</strong>肿瘤演化轨迹</li>
                                <li><strong>药物响应：</strong>治疗后细胞状态变化</li>
                            </ul>
                        </div>
                        <div>
                            <h3>结果验证策略</h3>
                            <ul>
                                <li><strong>已知标记：</strong>发育阶段特异性基因</li>
                                <li><strong>功能实验：</strong>谱系追踪实验</li>
                                <li><strong>时间序列：</strong>多时间点采样验证</li>
                                <li><strong>扰动实验：</strong>基因敲除/过表达</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 方法选择：</strong>简单线性轨迹用Slingshot，复杂分支轨迹用Monocle3，需要方向信息时考虑RNA velocity。
                    </div>
                `
            },
            {
                title: "细胞通讯分析",
                subtitle: "细胞间相互作用的计算预测",
                content: `
                    <h2>细胞通讯分析原理</h2>

                    <div class="algorithm-box">
                        <h3>CellChat算法框架</h3>
                        <ol>
                            <li><strong>配体-受体数据库：</strong>整合已知的L-R相互作用</li>
                            <li><strong>表达阈值：</strong>设定基因表达的最低阈值</li>
                            <li><strong>通讯概率：</strong>基于配体和受体表达计算</li>
                            <li><strong>信号通路：</strong>将L-R对归类到信号通路</li>
                            <li><strong>网络分析：</strong>构建细胞通讯网络</li>
                        </ol>
                        <div class="code-block"><span class="code-comment"># CellChat细胞通讯分析完整流程</span>
<span class="code-keyword">library</span>(<span class="code-string">CellChat</span>)

<span class="code-comment"># 1. 创建CellChat对象</span>
cellchat <- <span class="code-function">createCellChat</span>(object = seurat_obj@assays$RNA@data,
                          meta = <EMAIL>,
                          group.by = <span class="code-string">"cell_type"</span>)

<span class="code-comment"># 2. 设置配体-受体数据库</span>
CellChatDB <- CellChatDB.human
cellchat@DB <- CellChatDB

<span class="code-comment"># 3. 数据预处理</span>
cellchat <- <span class="code-function">subsetData</span>(cellchat)
cellchat <- <span class="code-function">identifyOverExpressedGenes</span>(cellchat)
cellchat <- <span class="code-function">identifyOverExpressedInteractions</span>(cellchat)

<span class="code-comment"># 4. 推断细胞通讯概率</span>
cellchat <- <span class="code-function">computeCommunProb</span>(cellchat)
cellchat <- <span class="code-function">filterCommunication</span>(cellchat, min.cells = 10)

<span class="code-comment"># 5. 可视化通讯网络</span>
<span class="code-function">netVisual_circle</span>(cellchat@net$count, vertex.weight = groupSize)</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>工具</th>
                                <th>数据库</th>
                                <th>计算方法</th>
                                <th>特色功能</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>CellChat</strong></td>
                                <td>CellChatDB</td>
                                <td>概率模型</td>
                                <td>信号通路分析</td>
                                <td>全面分析</td>
                            </tr>
                            <tr>
                                <td><strong>CellPhoneDB</strong></td>
                                <td>自建数据库</td>
                                <td>统计检验</td>
                                <td>复合体支持</td>
                                <td>精确预测</td>
                            </tr>
                            <tr>
                                <td><strong>NicheNet</strong></td>
                                <td>多源整合</td>
                                <td>网络传播</td>
                                <td>下游基因预测</td>
                                <td>机制研究</td>
                            </tr>
                            <tr>
                                <td><strong>iTALK</strong></td>
                                <td>文献挖掘</td>
                                <td>表达相关性</td>
                                <td>简单易用</td>
                                <td>快速筛选</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>配体-受体相互作用类型</h3>
                            <ul>
                                <li><strong>分泌信号：</strong>生长因子、细胞因子</li>
                                <li><strong>膜结合信号：</strong>细胞表面受体</li>
                                <li><strong>胞外基质：</strong>ECM-整合素相互作用</li>
                                <li><strong>细胞接触：</strong>紧密连接、胞间连接</li>
                            </ul>
                        </div>
                        <div>
                            <h3>分析结果解读</h3>
                            <ul>
                                <li><strong>通讯强度：</strong>相互作用的数量和强度</li>
                                <li><strong>信号通路：</strong>激活的信号传导通路</li>
                                <li><strong>细胞角色：</strong>发送者、接收者、调节者</li>
                                <li><strong>空间模式：</strong>邻近细胞优先通讯</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 分析局限：</strong>基于表达预测，需要实验验证；空间信息缺失；动态变化难以捕获。
                    </div>
                `
            },
            {
                title: "多组学单细胞技术",
                subtitle: "第六部分：多组学单细胞技术与前沿发展",
                content: `
                    <h2>多组学单细胞技术概览</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>CITE-seq</h4>
                            <p><strong>检测：</strong>RNA + 蛋白质<br>
                            <strong>原理：</strong>抗体-寡核苷酸偶联<br>
                            <strong>应用：</strong>免疫细胞分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>scATAC-seq</h4>
                            <p><strong>检测：</strong>染色质可及性<br>
                            <strong>原理：</strong>Tn5转座酶标记<br>
                            <strong>应用：</strong>调控元件分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>scNMT-seq</h4>
                            <p><strong>检测：</strong>甲基化+转录组<br>
                            <strong>原理：</strong>亚硫酸氢盐处理<br>
                            <strong>应用：</strong>表观调控研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>Perturb-seq</h4>
                            <p><strong>检测：</strong>扰动+转录组<br>
                            <strong>原理：</strong>CRISPR筛选<br>
                            <strong>应用：</strong>功能基因组学</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>多组学数据整合挑战</h3>
                        <ul>
                            <li><strong>数据异质性：</strong>不同组学数据的分布特征差异</li>
                            <li><strong>维度不匹配：</strong>基因数量vs蛋白质数量vs开放区域数量</li>
                            <li><strong>技术噪声：</strong>不同技术的噪声模式不同</li>
                            <li><strong>缺失数据：</strong>某些细胞某些组学数据缺失</li>
                            <li><strong>计算复杂性：</strong>高维数据的联合分析</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>整合分析方法</h3>
                            <ul>
                                <li><strong>Seurat v4：</strong>加权最近邻(WNN)</li>
                                <li><strong>MOFA+：</strong>多组学因子分析</li>
                                <li><strong>MultiVI：</strong>变分推断方法</li>
                                <li><strong>totalVI：</strong>RNA+蛋白质联合建模</li>
                                <li><strong>GLUE：</strong>图神经网络整合</li>
                            </ul>
                        </div>
                        <div>
                            <h3>应用前景</h3>
                            <ul>
                                <li><strong>细胞状态：</strong>更精确的细胞状态定义</li>
                                <li><strong>调控机制：</strong>转录调控网络重构</li>
                                <li><strong>疾病机制：</strong>多层次疾病机制解析</li>
                                <li><strong>药物开发：</strong>多靶点药物设计</li>
                            </ul>
                        </div>
                    </div>

                    <div class="platform-box">
                        <h4>空间转录组学技术</h4>
                        <ul>
                            <li><strong>10x Visium：</strong>基于芯片的空间转录组</li>
                            <li><strong>Slide-seq：</strong>高分辨率空间转录组</li>
                            <li><strong>MERFISH：</strong>单分子荧光原位杂交</li>
                            <li><strong>seqFISH+：</strong>序贯荧光原位杂交</li>
                            <li><strong>Stereo-seq：</strong>时空组学技术</li>
                        </ul>
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成幻灯片HTML
            const container = document.querySelector('.container');
            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <h1>${slide.title}</h1>
                    <div class="subtitle">${slide.subtitle}</div>
                    ${slide.content}
                `;
                container.appendChild(slideElement);
            });

            // 生成菜单
            generateMenu();
            updateNavigation();
            updateProgress();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = slides.map((slide, index) => `
                <div class="menu-item ${index === 0 ? 'current' : ''}" onclick="goToSlide(${index})">
                    ${index + 1}. ${slide.title}
                </div>
            `).join('');
        }

        // 切换菜单显示
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                // 隐藏当前幻灯片
                document.querySelectorAll('.slide')[currentSlideIndex].classList.remove('active');

                // 显示目标幻灯片
                currentSlideIndex = index;
                document.querySelectorAll('.slide')[currentSlideIndex].classList.add('active');

                // 更新界面
                updateNavigation();
                updateProgress();
                updateMenu();

                // 隐藏菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        // 更新导航按钮状态
        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            
            // 更新主题导航栏的active状态
            document.querySelectorAll('.topic-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据当前幻灯片索引设置active状态
            if (currentSlideIndex === 0) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(0)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 1 && currentSlideIndex <= 3) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(1)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 4 && currentSlideIndex <= 7) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(4)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 8 && currentSlideIndex <= 10) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(8)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 11 && currentSlideIndex <= 13) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(11)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 14) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(14)"]')?.classList.add('active');
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新菜单当前项
        function updateMenu() {
            document.querySelectorAll('.menu-item').forEach((item, index) => {
                item.classList.toggle('current', index === currentSlideIndex);
            });
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay(); // 到达最后一页时停止自动播放
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        // 重新开始演示
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    goToSlide(0);
                    break;
                case 'End':
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    document.getElementById('menuDropdown').classList.remove('active');
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.slide-menu')) {
                document.getElementById('menuDropdown').classList.remove('active');
            }
        });

        // 初始化
        initPresentation();
    </script>
</body>
</html>
