<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题二：测序数据质量控制与预处理 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px; /* 为两个导航栏留出空间 */
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #3498db;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #3498db;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        .highlight-box {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 8px 16px rgba(44, 62, 80, 0.3);
        }

        .info-box {
            background: linear-gradient(135deg, #e8f4fd, #d6eaf8);
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            color: #2c3e50;
        }

        .warning-box {
            background: linear-gradient(135deg, #fef9e7, #fcf3cf);
            border-left: 5px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            color: #2c3e50;
        }

        .error-box {
            background: linear-gradient(135deg, #fdedec, #fadbd8);
            border-left: 5px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            color: #2c3e50;
        }

        .comparison-table {
            margin: 20px 0;
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            color: #2c3e50;
            font-weight: 500;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .comparison-table tr:hover {
            background: #e8f4fd;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .platform-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .platform-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        }

        .platform-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .tech-specs {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
        }

        .tech-specs ul {
            list-style: none;
            padding: 0;
        }

        .tech-specs li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            color: #2c3e50;
            font-weight: 500;
        }

        .tech-specs li:last-child {
            border-bottom: none;
        }

        .tech-specs strong {
            color: #3498db;
            font-weight: 600;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .code-comment {
            color: #95a5a6;
            font-style: italic;
        }

        .code-keyword {
            color: #3498db;
            font-weight: bold;
        }

        .code-string {
            color: #e74c3c;
        }

        .code-function {
            color: #f39c12;
        }

        /* 导航栏样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 1rem 20px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 专题导航栏 */
        .topic-navbar {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            padding: 10px 20px;
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            z-index: 999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .topic-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            top: 70px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: 600;
            color: #2c3e50;
            z-index: 1000;
            border: 2px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .slide-menu {
            position: fixed;
            top: 70px;
            left: 20px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(255,255,255,0.95);
            border: 2px solid #3498db;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 18px;
            color: #2c3e50;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-btn:hover {
            background: #3498db;
            color: white;
        }

        .menu-dropdown {
            display: none;
            position: absolute;
            top: 50px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            min-width: 300px;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.show {
            display: block;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
            color: #2c3e50;
        }

        .menu-item:hover {
            background: #f0f0f0;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2c3e50, #3498db);
            transition: width 0.3s ease;
        }

        .controls {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            color: #2c3e50;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #3498db;
            color: white;
            transform: scale(1.1);
        }

        ul, ol {
            padding-left: 25px;
            margin: 15px 0;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
            color: #2c3e50;
        }

        strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .highlight-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .tech-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .tech-box h4 {
            color: #2E7D32 !important;
            margin-bottom: 15px;
        }

        .tech-box ul li {
            color: #333 !important;
        }

        .comparison-table {
            margin: 20px 0;
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: #FFFFFF !important;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            color: #333 !important;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .flowchart-step {
            color: #FFFFFF !important;
            font-weight: bold;
        }

        .flowchart {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .flowchart-step {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-weight: 600;
            flex: 1;
            min-width: 150px;
            position: relative;
        }

        .flowchart-step:not(:last-child)::after {
            content: "→";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .slide {
                width: 95%;
                padding: 20px;
                height: 90vh;
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.6em;
            }

            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .platform-grid {
                grid-template-columns: 1fr;
            }

            .flowchart {
                flex-direction: column;
            }

            .flowchart-step:not(:last-child)::after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="index.html" class="logo">
                <i class="fas fa-dna"></i>
                NGS课程平台
            </a>
            <ul class="nav-links">
                <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                <li><a href="index.html#courses"><i class="fas fa-book"></i> 课程</a></li>
                <li><a href="lab_html/Lecture2_lab.html" target="_blank"><i class="fas fa-flask"></i> 实践指导</a></li>
                <li><a href="about.html"><i class="fas fa-info-circle"></i> 关于</a></li>
                <li><a href="syllabus.html"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                <li><a href="resources.html"><i class="fas fa-download"></i> 资源</a></li>
            </ul>
        </div>
    </nav>


    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">15</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <div class="controls">
        <button class="control-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
    </div>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题二：测序数据质量控制与预处理",
                subtitle: "理论课程 - 测序数据质量控制与预处理技术",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">📊 测序数据质量控制与预处理流程图形摘要</h3>
                        <svg width="100%" height="480" viewBox="0 0 1000 480" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="qualityGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="50%" style="stop-color:#f39c12" />
                                    <stop offset="100%" style="stop-color:#27ae60" />
                                </linearGradient>
                                <linearGradient id="processGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="480" fill="url(#bgGradient2)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">测序数据质量控制与预处理工作流</text>
                            
                            <!-- 原始数据 - 左上 -->
                            <g transform="translate(50, 60)">
                                <rect x="0" y="0" width="150" height="80" fill="#e74c3c" rx="10" opacity="0.9"/>
                                <text x="75" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">原始测序数据</text>
                                <text x="75" y="45" text-anchor="middle" fill="white" font-size="11">FASTQ格式</text>
                                <text x="75" y="60" text-anchor="middle" fill="white" font-size="10">• 序列信息</text>
                                <text x="75" y="75" text-anchor="middle" fill="white" font-size="10">• 质量分数</text>
                            </g>
                            
                            <!-- 质量评估 - 中上 -->
                            <g transform="translate(250, 60)">
                                <rect x="0" y="0" width="150" height="80" fill="#f39c12" rx="10" opacity="0.9"/>
                                <text x="75" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">质量评估</text>
                                <text x="75" y="45" text-anchor="middle" fill="white" font-size="11">FastQC分析</text>
                                <text x="75" y="60" text-anchor="middle" fill="white" font-size="10">• Phred分数</text>
                                <text x="75" y="75" text-anchor="middle" fill="white" font-size="10">• GC含量</text>
                            </g>
                            
                            <!-- 质量过滤 - 右上 -->
                            <g transform="translate(450, 60)">
                                <rect x="0" y="0" width="150" height="80" fill="#27ae60" rx="10" opacity="0.9"/>
                                <text x="75" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">质量过滤</text>
                                <text x="75" y="45" text-anchor="middle" fill="white" font-size="11">Trimmomatic</text>
                                <text x="75" y="60" text-anchor="middle" fill="white" font-size="10">• 接头去除</text>
                                <text x="75" y="75" text-anchor="middle" fill="white" font-size="10">• 低质量剪切</text>
                            </g>
                            
                            <!-- 清洁数据 - 最右上 -->
                            <g transform="translate(650, 60)">
                                <rect x="0" y="0" width="150" height="80" fill="#3498db" rx="10" opacity="0.9"/>
                                <text x="75" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">清洁数据</text>
                                <text x="75" y="45" text-anchor="middle" fill="white" font-size="11">高质量FASTQ</text>
                                <text x="75" y="60" text-anchor="middle" fill="white" font-size="10">• Q30+ 比例高</text>
                                <text x="75" y="75" text-anchor="middle" fill="white" font-size="10">• 无接头污染</text>
                            </g>
                            
                            <!-- 流程箭头 -->
                            <path d="M200,100 L240,100" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <path d="M400,100 L440,100" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <path d="M600,100 L640,100" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            
                            <!-- 质量指标详解 - 中部 -->
                            <g transform="translate(50, 180)">
                                <rect x="0" y="0" width="700" height="120" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="350" y="25" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">关键质量指标</text>
                                
                                <!-- Phred分数 -->
                                <g transform="translate(30, 40)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">Phred质量分数</text>
                                    <rect x="0" y="20" width="120" height="8" fill="#e74c3c" rx="4"/>
                                    <rect x="40" y="20" width="80" height="8" fill="#f39c12" rx="4"/>
                                    <rect x="80" y="20" width="40" height="8" fill="#27ae60" rx="4"/>
                                    <text x="20" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">Q10</text>
                                    <text x="60" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">Q20</text>
                                    <text x="100" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">Q30+</text>
                                    <text x="60" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">质量等级</text>
                                </g>
                                
                                <!-- GC含量 -->
                                <g transform="translate(200, 40)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">GC含量分布</text>
                                    <path d="M0,25 Q30,20 60,25 Q90,30 120,25" stroke="#3498db" stroke-width="3" fill="none"/>
                                    <circle cx="60" cy="25" r="3" fill="#3498db"/>
                                    <text x="60" y="45" text-anchor="middle" fill="#2c3e50" font-size="9">正态分布</text>
                                    <text x="60" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">~41% (人类)</text>
                                </g>
                                
                                <!-- 序列长度 -->
                                <g transform="translate(370, 40)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">序列长度分布</text>
                                    <rect x="0" y="20" width="15" height="25" fill="#667eea" opacity="0.7"/>
                                    <rect x="20" y="15" width="15" height="30" fill="#667eea" opacity="0.8"/>
                                    <rect x="40" y="10" width="15" height="35" fill="#667eea" opacity="0.9"/>
                                    <rect x="60" y="15" width="15" height="30" fill="#667eea" opacity="0.8"/>
                                    <rect x="80" y="20" width="15" height="25" fill="#667eea" opacity="0.7"/>
                                    <text x="50" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">长度分布</text>
                                </g>
                                
                                <!-- 接头污染 -->
                                <g transform="translate(540, 40)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">接头污染检测</text>
                                    <rect x="0" y="20" width="80" height="6" fill="#27ae60" rx="3"/>
                                    <rect x="80" y="20" width="20" height="6" fill="#e74c3c" rx="3"/>
                                    <text x="40" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">清洁序列</text>
                                    <text x="90" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">接头</text>
                                    <text x="50" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">污染检测</text>
                                </g>
                            </g>
                            
                            <!-- 错误来源分析 - 下部 -->
                            <g transform="translate(50, 330)">
                                <text x="350" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">测序错误来源分析</text>
                                
                                <!-- 样本制备错误 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="35" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">样本制备</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">错误</text>
                                    <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">• PCR偏好性</text>
                                    <text x="0" y="75" text-anchor="middle" fill="#2c3e50" font-size="10">• 接头二聚体</text>
                                    <text x="0" y="90" text-anchor="middle" fill="#2c3e50" font-size="10">• DNA降解</text>
                                </g>
                                
                                <!-- 测序过程错误 -->
                                <g transform="translate(250, 40)">
                                    <circle cx="0" cy="0" r="35" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">测序过程</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">错误</text>
                                    <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">• 信号衰减</text>
                                    <text x="0" y="75" text-anchor="middle" fill="#2c3e50" font-size="10">• 光学串扰</text>
                                    <text x="0" y="90" text-anchor="middle" fill="#2c3e50" font-size="10">• 同聚物错误</text>
                                </g>
                                
                                <!-- 数据处理错误 -->
                                <g transform="translate(450, 40)">
                                    <circle cx="0" cy="0" r="35" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">数据处理</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">错误</text>
                                    <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">• 质量校准</text>
                                    <text x="0" y="75" text-anchor="middle" fill="#2c3e50" font-size="10">• 格式转换</text>
                                    <text x="0" y="90" text-anchor="middle" fill="#2c3e50" font-size="10">• 文件损坏</text>
                                </g>
                                
                                <!-- 质控策略 -->
                                <g transform="translate(650, 40)">
                                    <circle cx="0" cy="0" r="35" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">质控策略</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">应对</text>
                                    <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">• 多重验证</text>
                                    <text x="0" y="75" text-anchor="middle" fill="#2c3e50" font-size="10">• 阈值过滤</text>
                                    <text x="0" y="90" text-anchor="middle" fill="#2c3e50" font-size="10">• 统计校正</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M120,370 Q150,350 180,370" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M320,370 Q350,350 380,370" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M520,370 Q550,350 580,370" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解测序错误的来源与类型，掌握测序数据质量评估的关键指标，了解数据过滤与质控的策略与方法，掌握测序深度与覆盖度的计算及其生物学意义。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>测序错误来源与类型分析</li>
                                <li>测序数据质量评估指标与方法</li>
                                <li>数据过滤与质控策略</li>
                                <li>接头去除和低质量序列过滤原理</li>
                                <li>测序深度与覆盖度的计算及意义</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>理解不同测序平台的错误特征</li>
                                <li>掌握质量评估的关键指标</li>
                                <li>学会设计针对性的质控策略</li>
                                <li>了解接头去除和质量过滤算法</li>
                                <li>掌握测序深度与覆盖度的应用</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>测序数据质量控制是确保下游分析准确性的关键步骤，需要根据不同的测序平台特点和研究目标制定相应的质控策略。
                    </div>
                `
            },
            {
                title: "测序错误的主要来源",
                subtitle: "第一部分：测序错误来源与类型分析",
                content: `
                    <h2>测序错误产生的三个主要环节</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">样本制备<br>过程错误</div>
                        <div class="flowchart-step">测序过程<br>错误</div>
                        <div class="flowchart-step">数据处理<br>过程错误</div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🧪 样本制备过程错误</h4>
                            <ul>
                                <li><strong>DNA/RNA降解：</strong>核酸分子断裂或化学修饰</li>
                                <li><strong>PCR扩增偏好性：</strong>GC含量、二级结构影响</li>
                                <li><strong>接头二聚体：</strong>接头分子自身连接</li>
                                <li><strong>文库构建偏差：</strong>连接效率不均</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>影响：</strong>序列信息丢失、定量不准</li>
                                    <li><strong>预防：</strong>优化实验条件、质量控制</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔬 测序过程错误</h4>
                            <ul>
                                <li><strong>光学检测错误：</strong>信号串扰、强度异常</li>
                                <li><strong>信号衰减：</strong>化学试剂活性下降</li>
                                <li><strong>同聚物错误：</strong>连续相同碱基识别困难</li>
                                <li><strong>碱基调用算法：</strong>信号解读错误</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>特征：</strong>平台特异性错误模式</li>
                                    <li><strong>表现：</strong>质量随读长位置下降</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>💻 数据处理错误</h4>
                            <ul>
                                <li><strong>质量分数偏差：</strong>校准模型不准确</li>
                                <li><strong>格式转换错误：</strong>软件bug、操作失误</li>
                                <li><strong>文件损坏：</strong>传输、存储问题</li>
                                <li><strong>标准不一致：</strong>格式规范未遵守</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>后果：</strong>数据丢失、解析错误</li>
                                    <li><strong>检测：</strong>数据完整性验证</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 错误累积效应：</strong>各环节的错误可能累积或相互干扰，影响最终分析结果的准确性。需要在每个环节实施相应的质量控制措施。
                    </div>
                `
            },
            {
                title: "不同测序平台的错误特征",
                subtitle: "平台特异性错误模式分析",
                content: `
                    <h2>主流测序平台错误模式对比</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>测序平台</th>
                                <th>主要错误类型</th>
                                <th>错误率</th>
                                <th>错误分布特征</th>
                                <th>质量下降模式</th>
                            </tr>
                            <tr>
                                <td><strong>Illumina</strong></td>
                                <td>替换错误为主</td>
                                <td>0.1-1%</td>
                                <td>随机分布</td>
                                <td>3'端质量下降</td>
                            </tr>
                            <tr>
                                <td><strong>Ion Torrent</strong></td>
                                <td>插入/缺失错误</td>
                                <td>1-2%</td>
                                <td>同聚物区域集中</td>
                                <td>同聚物长度相关</td>
                            </tr>
                            <tr>
                                <td><strong>PacBio CLR</strong></td>
                                <td>随机Indel</td>
                                <td>10-15%</td>
                                <td>均匀分布</td>
                                <td>无明显位置偏好</td>
                            </tr>
                            <tr>
                                <td><strong>PacBio HiFi</strong></td>
                                <td>随机错误</td>
                                <td><0.1%</td>
                                <td>均匀分布</td>
                                <td>高准确性</td>
                            </tr>
                            <tr>
                                <td><strong>Oxford Nanopore</strong></td>
                                <td>混合错误</td>
                                <td>1-15%</td>
                                <td>上下文相关</td>
                                <td>持续改进中</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>Illumina平台特征</h3>
                            <div class="error-box">
                                <h4>🔍 错误模式详解</h4>
                                <ul>
                                    <li><strong>替换错误：</strong>A→C、G→T等单碱基替换</li>
                                    <li><strong>质量衰减：</strong>信号强度随循环数下降</li>
                                    <li><strong>Phasing效应：</strong>簇内分子不同步</li>
                                    <li><strong>GC偏好性：</strong>极端GC区域覆盖不均</li>
                                </ul>
                            </div>

                            <h3>Ion Torrent平台特征</h3>
                            <div class="error-box">
                                <h4>⚡ 同聚物挑战</h4>
                                <ul>
                                    <li><strong>pH检测原理：</strong>H+释放量检测</li>
                                    <li><strong>信号饱和：</strong>长同聚物信号重叠</li>
                                    <li><strong>载流子效应：</strong>相邻孔信号干扰</li>
                                    <li><strong>错误率递增：</strong>随同聚物长度增加</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>长读长平台特征</h3>
                            <div class="error-box">
                                <h4>🧬 PacBio SMRT技术</h4>
                                <ul>
                                    <li><strong>CLR模式：</strong>单次读取，高错误率</li>
                                    <li><strong>HiFi模式：</strong>多轮共识，高准确性</li>
                                    <li><strong>随机分布：</strong>无位置特异性偏好</li>
                                    <li><strong>动力学信息：</strong>可检测DNA修饰</li>
                                </ul>
                            </div>

                            <h3>Oxford Nanopore特征</h3>
                            <div class="error-box">
                                <h4>🔌 纳米孔测序</h4>
                                <ul>
                                    <li><strong>电流信号：</strong>碱基通过产生特征电流</li>
                                    <li><strong>上下文效应：</strong>相邻碱基影响信号</li>
                                    <li><strong>基线漂移：</strong>长时间测序信号变化</li>
                                    <li><strong>实时改进：</strong>算法持续优化</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 平台选择指导：</strong>了解不同平台的错误特征有助于选择合适的测序技术和制定针对性的质控策略。
                    </div>
                `
            },
            {
                title: "系统性错误与随机错误",
                subtitle: "错误类型的识别与区分方法",
                content: `
                    <h2>系统性错误 vs 随机错误</h2>

                    <div class="two-column">
                        <div>
                            <h3>系统性错误特征</h3>
                            <div class="platform-card">
                                <h4>🎯 定义与特征</h4>
                                <ul>
                                    <li><strong>可重复性：</strong>在相同条件下重复出现</li>
                                    <li><strong>序列依赖：</strong>与特定序列基序相关</li>
                                    <li><strong>位置特异：</strong>在特定基因组区域集中</li>
                                    <li><strong>批次相关：</strong>同批次样本呈现相似模式</li>
                                </ul>

                                <h4>🔍 识别方法</h4>
                                <ul>
                                    <li><strong>序列上下文分析：</strong>错误碱基周围序列模式</li>
                                    <li><strong>批次间比较：</strong>不同批次错误谱对比</li>
                                    <li><strong>对照样本分析：</strong>使用PhiX等已知序列</li>
                                    <li><strong>可视化分析：</strong>特定区域比对结果检查</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>随机错误特征</h3>
                            <div class="platform-card">
                                <h4>🎲 定义与特征</h4>
                                <ul>
                                    <li><strong>偶然性：</strong>由随机物理化学波动引起</li>
                                    <li><strong>均匀分布：</strong>在基因组上大致均匀分布</li>
                                    <li><strong>低关联性：</strong>不同读长间错误位置无关</li>
                                    <li><strong>质量相关：</strong>发生概率与质量分数对应</li>
                                </ul>

                                <h4>📊 统计特性</h4>
                                <ul>
                                    <li><strong>泊松分布：</strong>错误数量符合泊松分布</li>
                                    <li><strong>独立性：</strong>不同位置错误相互独立</li>
                                    <li><strong>可预测性：</strong>基于质量分数预测概率</li>
                                    <li><strong>可校正性：</strong>通过统计方法校正</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 系统性错误检测示例</span>
<span class="code-comment"># 1. 序列上下文分析</span>
<span class="code-function">analyze_sequence_context</span>() {
    <span class="code-comment"># 提取错误位点前后5bp序列</span>
    <span class="code-keyword">for</span> error_pos <span class="code-keyword">in</span> error_positions:
        context = sequence[error_pos-5:error_pos+5]
        context_counts[context] += 1

    <span class="code-comment"># 识别高频错误模式</span>
    frequent_patterns = [pattern <span class="code-keyword">for</span> pattern, count <span class="code-keyword">in</span> context_counts.items()
                        <span class="code-keyword">if</span> count > threshold]
}

<span class="code-comment"># 2. 批次效应检测</span>
<span class="code-function">detect_batch_effects</span>() {
    <span class="code-comment"># 比较不同批次的错误谱</span>
    batch1_errors = <span class="code-function">calculate_error_spectrum</span>(batch1_data)
    batch2_errors = <span class="code-function">calculate_error_spectrum</span>(batch2_data)

    correlation = <span class="code-function">pearson_correlation</span>(batch1_errors, batch2_errors)
    <span class="code-keyword">if</span> correlation > 0.8:
        <span class="code-keyword">print</span>(<span class="code-string">"检测到系统性错误模式"</span>)
}
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 错误累积效应：</strong>在基因组组装、长读长比对、变异检测等分析中，单个读长的错误可能累积，需要足够的测序深度和适当的算法来区分真实变异和测序错误。
                    </div>

                    <div class="info-box">
                        <strong>💡 应对策略：</strong>系统性错误需要在实验设计和数据处理阶段预防和校正，随机错误主要通过统计方法和质量过滤处理。
                    </div>
                `
            },
            {
                title: "Phred质量分数体系",
                subtitle: "第二部分：测序数据质量评估指标与方法",
                content: `
                    <h2>Phred质量分数：测序数据质量的核心指标</h2>

                    <div class="highlight-box">
                        <h3>Phred质量分数定义</h3>
                        <p><strong>Q = -10 × log₁₀(P)</strong></p>
                        <p>其中P为碱基调用错误概率，Q值越高表示质量越好</p>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>Phred分数</th>
                                <th>错误概率</th>
                                <th>准确率</th>
                                <th>质量等级</th>
                                <th>应用建议</th>
                            </tr>
                            <tr>
                                <td><strong>Q10</strong></td>
                                <td>10%</td>
                                <td>90%</td>
                                <td>低质量</td>
                                <td>需要过滤</td>
                            </tr>
                            <tr>
                                <td><strong>Q20</strong></td>
                                <td>1%</td>
                                <td>99%</td>
                                <td>中等质量</td>
                                <td>基本可用</td>
                            </tr>
                            <tr>
                                <td><strong>Q30</strong></td>
                                <td>0.1%</td>
                                <td>99.9%</td>
                                <td>高质量</td>
                                <td>推荐标准</td>
                            </tr>
                            <tr>
                                <td><strong>Q40</strong></td>
                                <td>0.01%</td>
                                <td>99.99%</td>
                                <td>极高质量</td>
                                <td>优秀数据</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>编码方式</h3>
                            <div class="platform-card">
                                <h4>📝 Phred+33 (Sanger格式)</h4>
                                <ul>
                                    <li><strong>编码范围：</strong>ASCII 33-126</li>
                                    <li><strong>质量范围：</strong>Q0-Q93</li>
                                    <li><strong>起始字符：</strong>'!' (ASCII 33)</li>
                                    <li><strong>应用：</strong>Illumina 1.8+, 当前主流</li>
                                </ul>

                                <div class="code-block">
<span class="code-comment"># Phred+33编码示例</span>
ASCII字符: !"#$%&'()*+,-./0123456789:;&lt;=&gt;?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\`abcdefghijklmnopqrstuvwxyz{|}~
质量分数: 0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123
         0         1         2         3         4         5         6         7         8         9
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3>质量分数应用</h3>
                            <div class="platform-card">
                                <h4>📊 质量阈值设定</h4>
                                <ul>
                                    <li><strong>Q20阈值：</strong>基本质控标准</li>
                                    <li><strong>Q30阈值：</strong>高质量数据标准</li>
                                    <li><strong>平均质量：</strong>整体数据质量评估</li>
                                    <li><strong>位置质量：</strong>读长质量分布分析</li>
                                </ul>

                                <h4>⚠️ 局限性</h4>
                                <ul>
                                    <li><strong>平均值掩盖：</strong>不能反映质量分布</li>
                                    <li><strong>校准偏差：</strong>实际错误率可能偏离</li>
                                    <li><strong>平台差异：</strong>不同平台校准标准不同</li>
                                    <li><strong>上下文忽略：</strong>不考虑序列背景</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 质量分数计算与转换</span>
<span class="code-function">phred_to_probability</span>(q_score):
    <span class="code-keyword">return</span> 10**(-q_score/10)

<span class="code-function">probability_to_phred</span>(error_prob):
    <span class="code-keyword">return</span> -10 * math.log10(error_prob)

<span class="code-function">ascii_to_phred33</span>(ascii_char):
    <span class="code-keyword">return</span> ord(ascii_char) - 33

<span class="code-comment"># 示例：质量字符串解析</span>
quality_string = <span class="code-string">"IIIIIIIIIIIIIIIIIIIIIIIIIIIIII9IG9IC"</span>
<span class="code-keyword">for</span> char <span class="code-keyword">in</span> quality_string:
    q_score = <span class="code-function">ascii_to_phred33</span>(char)
    error_prob = <span class="code-function">phred_to_probability</span>(q_score)
    <span class="code-keyword">print</span>(f<span class="code-string">"字符: &#123;char&#125;, Q值: &#123;q_score&#125;, 错误率: &#123;error_prob:.4f&#125;"</span>)
                    </div>

                    <div class="info-box">
                        <strong>💡 实用建议：</strong>Q30通常被认为是高质量碱基的标准，但具体阈值应根据应用场景调整。变异检测需要更高质量，而组装可能对质量要求相对宽松。
                    </div>
                `
            },
            {
                title: "基本质量统计指标",
                subtitle: "测序数据质量的多维度评估",
                content: `
                    <h2>测序数据质量评估的关键指标</h2>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>📊 每位置平均质量分数</h4>
                            <ul>
                                <li><strong>计算方法：</strong>每个cycle所有读长的平均Q值</li>
                                <li><strong>典型模式：</strong>5'→3'端逐渐下降</li>
                                <li><strong>异常信号：</strong>急剧下降或周期性波动</li>
                                <li><strong>应用：</strong>识别测序运行问题</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>正常范围：</strong>Q30-Q40 (前80%位置)</li>
                                    <li><strong>警告阈值：</strong>Q20以下区域过多</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📏 序列长度分布</h4>
                            <ul>
                                <li><strong>原始数据：</strong>长度通常一致</li>
                                <li><strong>处理后：</strong>长度变得多样化</li>
                                <li><strong>异常峰：</strong>可能提示接头污染</li>
                                <li><strong>过度剪切：</strong>平均长度显著缩短</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>Illumina：</strong>通常75-300bp</li>
                                    <li><strong>最小长度：</strong>通常设为35bp</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🧬 GC含量分布</h4>
                            <ul>
                                <li><strong>理论分布：</strong>接近物种基因组GC含量</li>
                                <li><strong>正态分布：</strong>随机基因组区域特征</li>
                                <li><strong>异常峰：</strong>可能来自污染序列</li>
                                <li><strong>偏差过大：</strong>存在GC偏好性</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>人类基因组：</strong>~41% GC</li>
                                    <li><strong>大肠杆菌：</strong>~51% GC</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>❓ N含量统计</h4>
                            <ul>
                                <li><strong>N碱基：</strong>表示无法确定的碱基</li>
                                <li><strong>理想情况：</strong>N含量应非常低</li>
                                <li><strong>高N含量：</strong>测序信号模糊</li>
                                <li><strong>位置分布：</strong>检查N在读长中的分布</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>正常水平：</strong>&lt;0.1%</li>
                                    <li><strong>警告阈值：</strong>&gt;1%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔤 每位置碱基组成</h4>
                            <ul>
                                <li><strong>理想状态：</strong>A、T、C、G比例稳定</li>
                                <li><strong>开头偏离：</strong>可能是接头污染</li>
                                <li><strong>引物偏好：</strong>特定位置碱基偏好</li>
                                <li><strong>物种特异：</strong>考虑物种AT/GC偏好</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>随机序列：</strong>各碱基约25%</li>
                                    <li><strong>允许偏差：</strong>±5%范围内</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔄 序列重复率</h4>
                            <ul>
                                <li><strong>PCR重复：</strong>完全相同的序列</li>
                                <li><strong>高重复率：</strong>PCR过度扩增</li>
                                <li><strong>应用相关：</strong>某些应用重复是正常的</li>
                                <li><strong>有效深度：</strong>影响真实测序深度</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>WGS：</strong>重复率&lt;20%</li>
                                    <li><strong>RNA-seq：</strong>可能较高</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 质量统计计算示例</span>
<span class="code-function">calculate_per_base_quality</span>(fastq_file):
    position_qualities = defaultdict(list)

    <span class="code-keyword">for</span> record <span class="code-keyword">in</span> <span class="code-function">parse_fastq</span>(fastq_file):
        <span class="code-keyword">for</span> pos, qual_char <span class="code-keyword">in</span> enumerate(record.quality):
            q_score = ord(qual_char) - 33
            position_qualities[pos].append(q_score)

    <span class="code-comment"># 计算每个位置的平均质量</span>
    avg_qualities = {}
    <span class="code-keyword">for</span> pos, qualities <span class="code-keyword">in</span> position_qualities.items():
        avg_qualities[pos] = sum(qualities) / len(qualities)

    <span class="code-keyword">return</span> avg_qualities

<span class="code-function">calculate_gc_content</span>(sequence):
    gc_count = sequence.count(<span class="code-string">'G'</span>) + sequence.count(<span class="code-string">'C'</span>)
    <span class="code-keyword">return</span> gc_count / len(sequence) * 100
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 综合评估：</strong>单一指标可能误导，需要综合多个指标进行全面评估。例如，平均质量高但N含量也高的数据仍需谨慎处理。
                    </div>
                `
            },
            {
                title: "高级质量评估指标",
                subtitle: "深度质量分析与污染检测",
                content: `
                    <h2>高级质量评估：深入挖掘数据质量问题</h2>

                    <div class="two-column">
                        <div>
                            <h3>K-mer频率分析</h3>
                            <div class="platform-card">
                                <h4>🔍 K-mer分析原理</h4>
                                <ul>
                                    <li><strong>定义：</strong>长度为K的短寡核苷酸</li>
                                    <li><strong>常用K值：</strong>5-7 (平衡特异性和敏感性)</li>
                                    <li><strong>频率分布：</strong>正常应呈泊松分布</li>
                                    <li><strong>异常K-mer：</strong>频率异常高的序列</li>
                                </ul>

                                <h4>🎯 应用场景</h4>
                                <ul>
                                    <li><strong>污染检测：</strong>识别外源序列</li>
                                    <li><strong>重复序列：</strong>发现高频重复元件</li>
                                    <li><strong>技术偏好：</strong>检测测序偏好性</li>
                                    <li><strong>基因组特征：</strong>评估基因组复杂度</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>过表示序列分析</h3>
                            <div class="platform-card">
                                <h4>📈 过表示序列识别</h4>
                                <ul>
                                    <li><strong>阈值设定：</strong>通常&gt;0.1%总读长数</li>
                                    <li><strong>常见来源：</strong>接头、引物、载体</li>
                                    <li><strong>生物来源：</strong>rRNA、高表达基因</li>
                                    <li><strong>技术来源：</strong>PCR产物、污染</li>
                                </ul>

                                <h4>🔬 数据库比对</h4>
                                <ul>
                                    <li><strong>接头数据库：</strong>已知测序接头</li>
                                    <li><strong>载体数据库：</strong>克隆载体序列</li>
                                    <li><strong>污染数据库：</strong>常见污染物</li>
                                    <li><strong>功能数据库：</strong>rRNA、tRNA等</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🧬 接头污染检测</h4>
                            <ul>
                                <li><strong>检测位置：</strong>主要在读长3'端</li>
                                <li><strong>检测方法：</strong>序列比对算法</li>
                                <li><strong>容错匹配：</strong>允许少量错配</li>
                                <li><strong>定量评估：</strong>污染比例统计</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>正常水平：</strong>&lt;1%</li>
                                    <li><strong>需要处理：</strong>&gt;5%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📊 测序偏好性评估</h4>
                            <ul>
                                <li><strong>GC偏好：</strong>不同GC区域覆盖差异</li>
                                <li><strong>位置偏好：</strong>特定位置信号强弱</li>
                                <li><strong>序列基序：</strong>特定序列模式偏好</li>
                                <li><strong>长度偏好：</strong>特定长度片段富集</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>评估方法：</strong>覆盖度均匀性分析</li>
                                    <li><strong>校正策略：</strong>标准化和权重调整</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔄 重复序列深度分析</h4>
                            <ul>
                                <li><strong>精确重复：</strong>完全相同的序列</li>
                                <li><strong>近似重复：</strong>高相似度序列</li>
                                <li><strong>PCR重复：</strong>扩增产生的重复</li>
                                <li><strong>生物重复：</strong>天然存在的重复</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>检测算法：</strong>哈希表、后缀数组</li>
                                    <li><strong>处理策略：</strong>标记vs删除</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># K-mer频率分析示例</span>
<span class="code-function">analyze_kmer_frequency</span>(sequences, k=6):
    kmer_counts = defaultdict(int)

    <span class="code-keyword">for</span> seq <span class="code-keyword">in</span> sequences:
        <span class="code-keyword">for</span> i <span class="code-keyword">in</span> range(len(seq) - k + 1):
            kmer = seq[i:i+k]
            kmer_counts[kmer] += 1

    <span class="code-comment"># 识别过表示的K-mer</span>
    total_kmers = sum(kmer_counts.values())
    threshold = total_kmers * 0.001  <span class="code-comment"># 0.1%阈值</span>

    overrepresented = &#123;kmer: count <span class="code-keyword">for</span> kmer, count <span class="code-keyword">in</span> kmer_counts.items()
                      <span class="code-keyword">if</span> count &gt; threshold&#125;

    <span class="code-keyword">return</span> overrepresented

<span class="code-comment"># 接头污染检测</span>
<span class="code-function">detect_adapter_contamination</span>(sequences, adapter_seq):
    contaminated_count = 0

    <span class="code-keyword">for</span> seq <span class="code-keyword">in</span> sequences:
        <span class="code-keyword">if</span> <span class="code-function">fuzzy_match</span>(seq, adapter_seq, max_errors=2):
            contaminated_count += 1

    contamination_rate = contaminated_count / len(sequences)
    <span class="code-keyword">return</span> contamination_rate
                    </div>

                    <div class="info-box">
                        <strong>💡 实用技巧：</strong>高级质量评估指标能够发现基本指标遗漏的问题，特别是在检测污染、偏好性和技术偏差方面具有重要价值。
                    </div>
                `
            },
            {
                title: "FastQC质量控制工具",
                subtitle: "第三部分：数据过滤与质控策略",
                content: `
                    <h2>FastQC：测序数据质量评估的标准工具</h2>

                    <div class="highlight-box">
                        <h3>FastQC工具概述</h3>
                        <p>FastQC是最广泛使用的测序数据质量控制工具，提供全面的质量评估报告和直观的可视化结果。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要功能模块</h3>
                            <div class="platform-card">
                                <h4>📊 基本统计信息</h4>
                                <ul>
                                    <li><strong>文件名：</strong>输入文件信息</li>
                                    <li><strong>文件类型：</strong>FASTQ格式检测</li>
                                    <li><strong>编码方式：</strong>质量分数编码</li>
                                    <li><strong>序列总数：</strong>读长数量统计</li>
                                    <li><strong>序列长度：</strong>长度范围分布</li>
                                    <li><strong>GC含量：</strong>整体GC百分比</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>📈 每位置质量分数</h4>
                                <ul>
                                    <li><strong>箱线图：</strong>显示质量分布</li>
                                    <li><strong>背景颜色：</strong>质量等级标识</li>
                                    <li><strong>中位数线：</strong>质量趋势展示</li>
                                    <li><strong>异常检测：</strong>质量急剧下降</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>质量评估模块</h3>
                            <div class="platform-card">
                                <h4>🔤 每位置序列内容</h4>
                                <ul>
                                    <li><strong>碱基比例：</strong>A、T、G、C分布</li>
                                    <li><strong>偏差检测：</strong>非随机分布识别</li>
                                    <li><strong>接头污染：</strong>开头位置偏差</li>
                                    <li><strong>引物偏好：</strong>特定位置偏好</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>🧬 GC含量分布</h4>
                                <ul>
                                    <li><strong>理论分布：</strong>正态分布期望</li>
                                    <li><strong>实际分布：</strong>观测到的分布</li>
                                    <li><strong>偏差分析：</strong>污染或偏好性</li>
                                    <li><strong>物种特异：</strong>基因组GC特征</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>❓ N含量分析</h4>
                            <ul>
                                <li><strong>每位置N比例：</strong>无法确定碱基统计</li>
                                <li><strong>质量关联：</strong>低质量区域N增多</li>
                                <li><strong>阈值警告：</strong>N含量过高提醒</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>正常：</strong>N含量&lt;5%</li>
                                    <li><strong>警告：</strong>N含量&gt;20%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📏 序列长度分布</h4>
                            <ul>
                                <li><strong>长度统计：</strong>读长分布直方图</li>
                                <li><strong>一致性检查：</strong>长度变异分析</li>
                                <li><strong>异常检测：</strong>意外长度峰值</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>Illumina：</strong>长度一致</li>
                                    <li><strong>处理后：</strong>长度多样化</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔄 重复序列水平</h4>
                            <ul>
                                <li><strong>重复度统计：</strong>序列出现频次</li>
                                <li><strong>去重比例：</strong>唯一序列百分比</li>
                                <li><strong>PCR偏差：</strong>过度扩增检测</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>理想：</strong>重复率&lt;20%</li>
                                    <li><strong>警告：</strong>重复率&gt;50%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📈 过表示序列</h4>
                            <ul>
                                <li><strong>高频序列：</strong>异常高频出现</li>
                                <li><strong>来源识别：</strong>接头、污染物</li>
                                <li><strong>数据库比对：</strong>已知序列匹配</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>阈值：</strong>&gt;0.1%总序列</li>
                                    <li><strong>处理：</strong>需要去除或标记</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># FastQC使用示例</span>
<span class="code-comment"># 1. 基本用法</span>
fastqc input.fastq.gz -o output_directory

<span class="code-comment"># 2. 批量处理</span>
fastqc *.fastq.gz -o qc_results/ -t 8

<span class="code-comment"># 3. 指定参数</span>
fastqc input.fastq.gz \\
    --outdir results/ \\
    --threads 4 \\
    --format fastq \\
    --quiet

<span class="code-comment"># 4. 结果文件</span>
# input_fastqc.html  - HTML报告
# input_fastqc.zip   - 详细数据
                    </div>

                    <div class="info-box">
                        <strong>💡 使用建议：</strong>FastQC报告应结合具体应用场景解读，某些"失败"项目在特定应用中可能是正常的，如RNA-seq中的GC含量偏差。
                    </div>
                `
            },
            {
                title: "质量过滤策略与参数设置",
                subtitle: "基于质量分数的数据过滤方法",
                content: `
                    <h2>测序数据质量过滤：策略与实践</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">质量评估<br>FastQC分析</div>
                        <div class="flowchart-step">参数设定<br>阈值选择</div>
                        <div class="flowchart-step">数据过滤<br>质控处理</div>
                        <div class="flowchart-step">结果验证<br>效果评估</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>质量过滤策略</h3>
                            <div class="platform-card">
                                <h4>🎯 基于质量分数过滤</h4>
                                <ul>
                                    <li><strong>平均质量：</strong>整条读长平均Q值</li>
                                    <li><strong>滑动窗口：</strong>局部质量评估</li>
                                    <li><strong>末端修剪：</strong>低质量3'端去除</li>
                                    <li><strong>最小长度：</strong>过滤过短序列</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>Q20阈值：</strong>基本质控标准</li>
                                        <li><strong>Q30阈值：</strong>高质量要求</li>
                                        <li><strong>窗口大小：</strong>通常4-10bp</li>
                                        <li><strong>最小长度：</strong>通常35-50bp</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>📏 基于长度过滤</h4>
                                <ul>
                                    <li><strong>最小长度：</strong>保证比对效果</li>
                                    <li><strong>最大长度：</strong>去除异常长序列</li>
                                    <li><strong>长度分布：</strong>维持合理分布</li>
                                    <li><strong>应用相关：</strong>不同应用不同要求</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>高级过滤策略</h3>
                            <div class="platform-card">
                                <h4>🧬 基于序列内容过滤</h4>
                                <ul>
                                    <li><strong>N含量：</strong>过多未确定碱基</li>
                                    <li><strong>低复杂度：</strong>单一碱基重复</li>
                                    <li><strong>接头序列：</strong>污染序列去除</li>
                                    <li><strong>引物序列：</strong>PCR引物残留</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>N比例：</strong>&lt;10%</li>
                                        <li><strong>复杂度：</strong>避免AAAA...等</li>
                                        <li><strong>接头匹配：</strong>允许1-2个错配</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>📊 统计学过滤</h4>
                                <ul>
                                    <li><strong>异常值检测：</strong>统计学异常序列</li>
                                    <li><strong>批次效应：</strong>技术重复一致性</li>
                                    <li><strong>质量分布：</strong>整体质量评估</li>
                                    <li><strong>覆盖均匀性：</strong>偏好性检测</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>应用类型</th>
                                <th>质量阈值</th>
                                <th>最小长度</th>
                                <th>特殊要求</th>
                                <th>推荐工具</th>
                            </tr>
                            <tr>
                                <td><strong>基因组重测序</strong></td>
                                <td>Q20-Q30</td>
                                <td>50bp</td>
                                <td>严格质控</td>
                                <td>Trimmomatic</td>
                            </tr>
                            <tr>
                                <td><strong>RNA-seq</strong></td>
                                <td>Q20</td>
                                <td>35bp</td>
                                <td>保留长度</td>
                                <td>Cutadapt</td>
                            </tr>
                            <tr>
                                <td><strong>ChIP-seq</strong></td>
                                <td>Q15-Q20</td>
                                <td>30bp</td>
                                <td>适度过滤</td>
                                <td>FastP</td>
                            </tr>
                            <tr>
                                <td><strong>宏基因组</strong></td>
                                <td>Q15</td>
                                <td>50bp</td>
                                <td>保留多样性</td>
                                <td>BBDuk</td>
                            </tr>
                            <tr>
                                <td><strong>单细胞RNA-seq</strong></td>
                                <td>Q20</td>
                                <td>30bp</td>
                                <td>UMI处理</td>
                                <td>UMI-tools</td>
                            </tr>
                        </table>
                    </div>

                    <div class="code-block">
<span class="code-comment"># Trimmomatic质量过滤示例</span>
java -jar trimmomatic.jar PE \\
    input_R1.fastq.gz input_R2.fastq.gz \\
    output_R1_paired.fastq.gz output_R1_unpaired.fastq.gz \\
    output_R2_paired.fastq.gz output_R2_unpaired.fastq.gz \\
    ILLUMINACLIP:adapters.fa:2:30:10 \\
    LEADING:3 TRAILING:3 \\
    SLIDINGWINDOW:4:20 \\
    MINLEN:36

<span class="code-comment"># 参数说明：</span>
<span class="code-comment"># ILLUMINACLIP: 接头去除</span>
<span class="code-comment"># LEADING: 5'端低质量碱基去除</span>
<span class="code-comment"># TRAILING: 3'端低质量碱基去除</span>
<span class="code-comment"># SLIDINGWINDOW: 滑动窗口质量过滤</span>
<span class="code-comment"># MINLEN: 最小长度要求</span>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 过滤平衡：</strong>过度严格的质量过滤可能导致有用信息丢失，需要在数据质量和数据量之间找到平衡点。
                    </div>
                `
            },
            {
                title: "接头序列去除原理与方法",
                subtitle: "第四部分：接头去除和低质量序列过滤",
                content: `
                    <h2>接头序列污染：识别、去除与验证</h2>

                    <div class="highlight-box">
                        <h3>接头污染的产生机制</h3>
                        <p>当DNA片段短于读长时，测序会读取到接头序列，形成接头污染。这在短片段文库和降解样本中尤为常见。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>接头污染类型</h3>
                            <div class="platform-card">
                                <h4>🔗 3'端接头污染</h4>
                                <ul>
                                    <li><strong>产生原因：</strong>插入片段短于读长</li>
                                    <li><strong>检测位置：</strong>读长3'端</li>
                                    <li><strong>影响程度：</strong>降低比对质量</li>
                                    <li><strong>处理策略：</strong>末端修剪</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>检测阈值：</strong>连续匹配&gt;10bp</li>
                                        <li><strong>容错率：</strong>允许10-20%错配</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>🔄 接头二聚体</h4>
                                <ul>
                                    <li><strong>产生原因：</strong>接头分子自身连接</li>
                                    <li><strong>序列特征：</strong>完全由接头组成</li>
                                    <li><strong>检测方法：</strong>全长序列比对</li>
                                    <li><strong>处理策略：</strong>完全去除</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>长度特征：</strong>通常120-140bp</li>
                                        <li><strong>比例控制：</strong>&lt;5%可接受</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3>检测算法原理</h3>
                            <div class="platform-card">
                                <h4>🎯 序列比对算法</h4>
                                <ul>
                                    <li><strong>精确匹配：</strong>完全一致的序列</li>
                                    <li><strong>模糊匹配：</strong>允许错配和gap</li>
                                    <li><strong>局部比对：</strong>Smith-Waterman算法</li>
                                    <li><strong>启发式算法：</strong>快速近似匹配</li>
                                </ul>

                                <h4>⚙️ 参数优化</h4>
                                <ul>
                                    <li><strong>最小重叠：</strong>通常3-5bp</li>
                                    <li><strong>错配容忍：</strong>10-20%错误率</li>
                                    <li><strong>gap惩罚：</strong>插入缺失处理</li>
                                    <li><strong>分数阈值：</strong>匹配质量评估</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🛠️ Cutadapt工具</h4>
                            <ul>
                                <li><strong>算法特点：</strong>基于动态规划的精确匹配</li>
                                <li><strong>处理模式：</strong>支持单端和双端数据</li>
                                <li><strong>接头类型：</strong>3'、5'、内部接头</li>
                                <li><strong>质量集成：</strong>结合质量修剪</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>速度：</strong>中等，精确度高</li>
                                    <li><strong>内存：</strong>低内存消耗</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ Trimmomatic工具</h4>
                            <ul>
                                <li><strong>算法特点：</strong>多步骤处理流程</li>
                                <li><strong>接头检测：</strong>种子匹配+扩展</li>
                                <li><strong>质量修剪：</strong>滑动窗口算法</li>
                                <li><strong>并行处理：</strong>多线程支持</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>速度：</strong>快速，适合大数据</li>
                                    <li><strong>功能：</strong>一体化处理</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🚀 FastP工具</h4>
                            <ul>
                                <li><strong>算法特点：</strong>高度优化的C++实现</li>
                                <li><strong>自动检测：</strong>智能接头识别</li>
                                <li><strong>实时报告：</strong>处理过程可视化</li>
                                <li><strong>格式支持：</strong>FASTQ、FASTA</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>速度：</strong>极快，现代化设计</li>
                                    <li><strong>报告：</strong>HTML质量报告</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔧 BBDuk工具</h4>
                            <ul>
                                <li><strong>算法特点：</strong>k-mer匹配算法</li>
                                <li><strong>参考数据库：</strong>内置接头序列库</li>
                                <li><strong>污染去除：</strong>多种污染物处理</li>
                                <li><strong>统计报告：</strong>详细处理统计</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>特色：</strong>BBTools套件组件</li>
                                    <li><strong>应用：</strong>宏基因组学优化</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># Cutadapt接头去除示例</span>
<span class="code-comment"># 1. 单端数据处理</span>
cutadapt -a AGATCGGAAGAGC \\
    -q 20 \\
    --minimum-length 30 \\
    -o output.fastq \\
    input.fastq

<span class="code-comment"># 2. 双端数据处理</span>
cutadapt -a AGATCGGAAGAGC -A AGATCGGAAGAGC \\
    -q 20,20 \\
    --minimum-length 30 \\
    -o output_R1.fastq -p output_R2.fastq \\
    input_R1.fastq input_R2.fastq

<span class="code-comment"># 3. 多个接头序列</span>
cutadapt -a file:adapters.fasta \\
    --times 2 \\
    -e 0.1 \\
    -o clean.fastq \\
    raw.fastq

<span class="code-comment"># 参数说明：</span>
<span class="code-comment"># -a: 3'端接头序列</span>
<span class="code-comment"># -q: 质量修剪阈值</span>
<span class="code-comment"># -e: 错误率容忍度</span>
<span class="code-comment"># --times: 最大去除轮数</span>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 过度修剪风险：</strong>过于严格的接头去除参数可能导致真实序列被误切，需要在去除效果和序列完整性之间平衡。
                    </div>
                `
            },
            {
                title: "低质量序列过滤算法",
                subtitle: "滑动窗口与质量修剪技术",
                content: `
                    <h2>低质量序列过滤：算法原理与实现策略</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">质量评估<br>逐位置分析</div>
                        <div class="flowchart-step">窗口滑动<br>局部质量</div>
                        <div class="flowchart-step">阈值判断<br>修剪决策</div>
                        <div class="flowchart-step">长度检查<br>最终过滤</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>滑动窗口算法</h3>
                            <div class="platform-card">
                                <h4>🪟 算法原理</h4>
                                <ul>
                                    <li><strong>窗口大小：</strong>通常4-10个碱基</li>
                                    <li><strong>滑动步长：</strong>通常1个碱基</li>
                                    <li><strong>质量计算：</strong>窗口内平均质量</li>
                                    <li><strong>修剪策略：</strong>低于阈值即修剪</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>窗口大小：</strong>4bp (Trimmomatic默认)</li>
                                        <li><strong>质量阈值：</strong>Q15-Q20</li>
                                        <li><strong>方向：</strong>通常从3'端开始</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>📊 质量计算方法</h4>
                                <ul>
                                    <li><strong>算术平均：</strong>简单平均质量分数</li>
                                    <li><strong>几何平均：</strong>错误概率的几何平均</li>
                                    <li><strong>加权平均：</strong>位置权重调整</li>
                                    <li><strong>最小值：</strong>窗口内最低质量</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>修剪策略比较</h3>
                            <div class="platform-card">
                                <h4>✂️ 固定长度修剪</h4>
                                <ul>
                                    <li><strong>策略：</strong>修剪固定长度的末端</li>
                                    <li><strong>优点：</strong>简单快速，结果一致</li>
                                    <li><strong>缺点：</strong>可能过度修剪</li>
                                    <li><strong>应用：</strong>质量模式一致的数据</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>🎯 自适应修剪</h4>
                                <ul>
                                    <li><strong>策略：</strong>基于实际质量动态修剪</li>
                                    <li><strong>优点：</strong>精确保留高质量区域</li>
                                    <li><strong>缺点：</strong>计算复杂，结果不一致</li>
                                    <li><strong>应用：</strong>质量变化大的数据</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>修剪算法</th>
                                <th>计算复杂度</th>
                                <th>精确度</th>
                                <th>速度</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>固定长度</strong></td>
                                <td>O(1)</td>
                                <td>低</td>
                                <td>极快</td>
                                <td>质量模式一致</td>
                            </tr>
                            <tr>
                                <td><strong>质量阈值</strong></td>
                                <td>O(n)</td>
                                <td>中等</td>
                                <td>快</td>
                                <td>简单质控</td>
                            </tr>
                            <tr>
                                <td><strong>滑动窗口</strong></td>
                                <td>O(n×w)</td>
                                <td>高</td>
                                <td>中等</td>
                                <td>标准处理</td>
                            </tr>
                            <tr>
                                <td><strong>动态规划</strong></td>
                                <td>O(n²)</td>
                                <td>最高</td>
                                <td>慢</td>
                                <td>高精度要求</td>
                            </tr>
                        </table>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔄 双端数据处理</h4>
                            <ul>
                                <li><strong>独立处理：</strong>R1和R2分别修剪</li>
                                <li><strong>配对保持：</strong>保持读长对应关系</li>
                                <li><strong>长度一致：</strong>可选择保持长度一致</li>
                                <li><strong>孤儿读长：</strong>处理失去配对的读长</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>配对率：</strong>通常&gt;90%</li>
                                    <li><strong>孤儿处理：</strong>单独输出或丢弃</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📈 质量改善评估</h4>
                            <ul>
                                <li><strong>平均质量：</strong>处理前后对比</li>
                                <li><strong>Q30比例：</strong>高质量碱基比例</li>
                                <li><strong>长度分布：</strong>修剪后长度变化</li>
                                <li><strong>数据保留率：</strong>有效数据比例</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>质量提升：</strong>平均Q值+5-10</li>
                                    <li><strong>数据保留：</strong>通常80-95%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>⚖️ 参数优化策略</h4>
                            <ul>
                                <li><strong>试验设计：</strong>多参数组合测试</li>
                                <li><strong>效果评估：</strong>下游分析结果对比</li>
                                <li><strong>成本考虑：</strong>计算时间vs质量提升</li>
                                <li><strong>应用特异：</strong>针对具体应用优化</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>🔍 质量控制验证</h4>
                            <ul>
                                <li><strong>FastQC检查：</strong>处理前后质量对比</li>
                                <li><strong>比对率：</strong>参考基因组比对效果</li>
                                <li><strong>覆盖均匀性：</strong>基因组覆盖分布</li>
                                <li><strong>变异检测：</strong>下游分析准确性</li>
                            </ul>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 滑动窗口算法实现示例</span>
<span class="code-function">def</span> <span class="code-function">sliding_window_trim</span>(sequence, quality, window_size=4, threshold=20):
    <span class="code-string">"""
    滑动窗口质量修剪算法
    """</span>
    length = len(sequence)

    <span class="code-comment"># 从3'端开始检查</span>
    <span class="code-keyword">for</span> i <span class="code-keyword">in</span> range(length - window_size, -1, -1):
        window_qualities = quality[i:i + window_size]
        avg_quality = sum(window_qualities) / window_size

        <span class="code-keyword">if</span> avg_quality &gt;= threshold:
            <span class="code-comment"># 找到合适的修剪位置</span>
            <span class="code-keyword">return</span> sequence[:i + window_size], quality[:i + window_size]

    <span class="code-comment"># 如果整条序列都低质量，返回空</span>
    <span class="code-keyword">return</span> <span class="code-string">""</span>, []

<span class="code-comment"># 使用示例</span>
trimmed_seq, trimmed_qual = <span class="code-function">sliding_window_trim</span>(
    sequence=<span class="code-string">"ATCGATCGATCG"</span>,
    quality=[40, 40, 35, 30, 25, 20, 15, 10, 8, 5, 3, 2],
    window_size=4,
    threshold=20
)
                    </div>

                    <div class="info-box">
                        <strong>💡 最佳实践：</strong>滑动窗口大小应根据测序平台特点调整，Illumina数据通常使用4bp窗口，长读长数据可使用更大窗口。
                    </div>
                `
            },
            {
                title: "测序深度与覆盖度概念",
                subtitle: "第五部分：测序深度与覆盖度的计算及意义",
                content: `
                    <h2>测序深度与覆盖度：核心概念与计算方法</h2>

                    <div class="highlight-box">
                        <h3>基本概念区分</h3>
                        <p><strong>测序深度(Depth)：</strong>特定基因组位点被测序读长覆盖的次数</p>
                        <p><strong>覆盖度(Coverage)：</strong>基因组中被测序读长覆盖的区域比例</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>测序深度分析</h3>
                            <div class="platform-card">
                                <h4>📊 深度计算公式</h4>
                                <ul>
                                    <li><strong>平均深度：</strong>总碱基数 ÷ 基因组大小</li>
                                    <li><strong>有效深度：</strong>去除重复后的深度</li>
                                    <li><strong>目标深度：</strong>特定区域的深度</li>
                                    <li><strong>均一性：</strong>深度分布的均匀程度</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>人类基因组：</strong>~3.2 Gb</li>
                                        <li><strong>30X深度：</strong>~100 Gb数据</li>
                                        <li><strong>有效深度：</strong>通常为名义深度的80-90%</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>📈 深度分布特征</h4>
                                <ul>
                                    <li><strong>泊松分布：</strong>理想随机分布</li>
                                    <li><strong>偏差来源：</strong>GC偏好、重复序列</li>
                                    <li><strong>深度变异：</strong>CV值评估均一性</li>
                                    <li><strong>异常区域：</strong>极高或极低深度</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>覆盖度分析</h3>
                            <div class="platform-card">
                                <h4>🎯 覆盖度类型</h4>
                                <ul>
                                    <li><strong>基因组覆盖度：</strong>整个基因组覆盖比例</li>
                                    <li><strong>外显子覆盖度：</strong>编码区覆盖比例</li>
                                    <li><strong>目标区域覆盖度：</strong>特定区域覆盖</li>
                                    <li><strong>有效覆盖度：</strong>满足最小深度要求</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>WGS覆盖度：</strong>通常&gt;95%</li>
                                        <li><strong>WES覆盖度：</strong>目标区域&gt;90%</li>
                                        <li><strong>最小深度：</strong>通常≥10X</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>🔍 质量评估指标</h4>
                                <ul>
                                    <li><strong>覆盖均匀性：</strong>深度分布的一致性</li>
                                    <li><strong>目标命中率：</strong>目标区域读长比例</li>
                                    <li><strong>重复率：</strong>PCR重复序列比例</li>
                                    <li><strong>比对率：</strong>成功比对的读长比例</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>应用类型</th>
                                <th>推荐深度</th>
                                <th>覆盖度要求</th>
                                <th>关键指标</th>
                                <th>数据量</th>
                            </tr>
                            <tr>
                                <td><strong>WGS (人类)</strong></td>
                                <td>30-50X</td>
                                <td>&gt;95%</td>
                                <td>变异检测准确性</td>
                                <td>100-150 GB</td>
                            </tr>
                            <tr>
                                <td><strong>WES (人类)</strong></td>
                                <td>50-100X</td>
                                <td>&gt;90% (目标区域)</td>
                                <td>外显子覆盖均匀性</td>
                                <td>8-15 GB</td>
                            </tr>
                            <tr>
                                <td><strong>RNA-seq</strong></td>
                                <td>20-50M reads</td>
                                <td>&gt;80% (转录本)</td>
                                <td>基因表达定量</td>
                                <td>5-20 GB</td>
                            </tr>
                            <tr>
                                <td><strong>ChIP-seq</strong></td>
                                <td>10-20M reads</td>
                                <td>基因组范围</td>
                                <td>峰值信噪比</td>
                                <td>2-8 GB</td>
                            </tr>
                            <tr>
                                <td><strong>ATAC-seq</strong></td>
                                <td>25-50M reads</td>
                                <td>开放染色质区域</td>
                                <td>峰值质量</td>
                                <td>5-15 GB</td>
                            </tr>
                        </table>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 测序深度计算示例</span>
<span class="code-comment"># 1. 基本深度计算</span>
<span class="code-function">def</span> <span class="code-function">calculate_depth</span>(total_bases, genome_size):
    <span class="code-string">"""计算平均测序深度"""</span>
    <span class="code-keyword">return</span> total_bases / genome_size

<span class="code-comment"># 2. 有效深度计算</span>
<span class="code-function">def</span> <span class="code-function">calculate_effective_depth</span>(total_bases, genome_size, duplicate_rate):
    <span class="code-string">"""计算去重后的有效深度"""</span>
    effective_bases = total_bases * (1 - duplicate_rate)
    <span class="code-keyword">return</span> effective_bases / genome_size

<span class="code-comment"># 3. 覆盖度计算</span>
<span class="code-function">def</span> <span class="code-function">calculate_coverage</span>(covered_positions, total_positions):
    <span class="code-string">"""计算基因组覆盖度"""</span>
    <span class="code-keyword">return</span> covered_positions / total_positions * 100

<span class="code-comment"># 示例计算</span>
human_genome_size = 3.2e9  <span class="code-comment"># 3.2 Gb</span>
total_bases = 1.0e11       <span class="code-comment"># 100 Gb</span>
duplicate_rate = 0.15      <span class="code-comment"># 15%重复率</span>

avg_depth = <span class="code-function">calculate_depth</span>(total_bases, human_genome_size)
eff_depth = <span class="code-function">calculate_effective_depth</span>(total_bases, human_genome_size, duplicate_rate)

<span class="code-keyword">print</span>(f<span class="code-string">"平均深度: {avg_depth:.1f}X"</span>)
<span class="code-keyword">print</span>(f<span class="code-string">"有效深度: {eff_depth:.1f}X"</span>)
                    </div>

                    <div class="info-box">
                        <strong>💡 实用指导：</strong>测序深度需求取决于具体应用，变异检测需要更高深度，而基因表达分析相对要求较低。覆盖均匀性往往比平均深度更重要。
                    </div>
                `
            },
            {
                title: "深度分布分析与可视化",
                subtitle: "测序深度的统计分析与质量评估",
                content: `
                    <h2>测序深度分布：统计特征与可视化分析</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">深度统计<br>基本指标</div>
                        <div class="flowchart-step">分布分析<br>统计特征</div>
                        <div class="flowchart-step">异常检测<br>质量问题</div>
                        <div class="flowchart-step">可视化<br>结果展示</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>统计指标分析</h3>
                            <div class="platform-card">
                                <h4>📊 基本统计量</h4>
                                <ul>
                                    <li><strong>平均深度：</strong>所有位点深度的算术平均</li>
                                    <li><strong>中位数深度：</strong>深度分布的中位数</li>
                                    <li><strong>标准差：</strong>深度变异程度</li>
                                    <li><strong>变异系数：</strong>CV = σ/μ，均匀性指标</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>理想CV：</strong>&lt;0.3 (高均匀性)</li>
                                        <li><strong>可接受CV：</strong>0.3-0.5</li>
                                        <li><strong>问题CV：</strong>&gt;0.5 (不均匀)</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>📈 分布特征</h4>
                                <ul>
                                    <li><strong>偏度(Skewness)：</strong>分布的对称性</li>
                                    <li><strong>峰度(Kurtosis)：</strong>分布的尖锐程度</li>
                                    <li><strong>分位数：</strong>25%、50%、75%分位点</li>
                                    <li><strong>异常值：</strong>极端深度值识别</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>质量问题诊断</h3>
                            <div class="platform-card">
                                <h4>🔍 常见问题模式</h4>
                                <ul>
                                    <li><strong>GC偏好性：</strong>GC含量相关的深度偏差</li>
                                    <li><strong>重复序列：</strong>高重复区域深度异常</li>
                                    <li><strong>技术偏差：</strong>文库构建或测序偏差</li>
                                    <li><strong>污染影响：</strong>外源序列导致深度异常</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>GC偏差：</strong>极端GC区域深度&lt;50%平均值</li>
                                        <li><strong>重复区域：</strong>深度&gt;3倍平均值</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>⚠️ 异常深度识别</h4>
                                <ul>
                                    <li><strong>零深度区域：</strong>完全未覆盖区域</li>
                                    <li><strong>低深度区域：</strong>&lt;10X覆盖区域</li>
                                    <li><strong>高深度区域：</strong>&gt;3×平均深度区域</li>
                                    <li><strong>深度突变：</strong>相邻区域深度急剧变化</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>📊 深度分布直方图</h4>
                            <ul>
                                <li><strong>X轴：</strong>测序深度值</li>
                                <li><strong>Y轴：</strong>基因组位点数量或比例</li>
                                <li><strong>分布形状：</strong>理想为泊松分布</li>
                                <li><strong>异常峰：</strong>识别技术偏差</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>正常分布：</strong>单峰，右偏</li>
                                    <li><strong>异常分布：</strong>多峰，极端偏斜</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📈 累积分布曲线</h4>
                            <ul>
                                <li><strong>X轴：</strong>测序深度阈值</li>
                                <li><strong>Y轴：</strong>达到该深度的基因组比例</li>
                                <li><strong>关键点：</strong>10X、20X、30X覆盖率</li>
                                <li><strong>质量评估：</strong>曲线陡峭程度</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>高质量：</strong>陡峭上升曲线</li>
                                    <li><strong>低质量：</strong>缓慢上升曲线</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🗺️ 基因组深度图</h4>
                            <ul>
                                <li><strong>X轴：</strong>基因组坐标</li>
                                <li><strong>Y轴：</strong>测序深度</li>
                                <li><strong>分辨率：</strong>窗口大小可调</li>
                                <li><strong>注释：</strong>基因、重复元件标记</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>窗口大小：</strong>1kb-1Mb</li>
                                    <li><strong>平滑处理：</strong>移动平均</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🎯 目标区域分析</h4>
                            <ul>
                                <li><strong>外显子覆盖：</strong>编码区深度分布</li>
                                <li><strong>基因覆盖：</strong>基因水平统计</li>
                                <li><strong>功能区域：</strong>启动子、UTR等</li>
                                <li><strong>临床相关：</strong>疾病相关基因</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>外显子数：</strong>~180,000个</li>
                                    <li><strong>目标覆盖：</strong>&gt;95%外显子</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 深度分布分析示例</span>
<span class="code-keyword">import</span> numpy <span class="code-keyword">as</span> np
<span class="code-keyword">import</span> matplotlib.pyplot <span class="code-keyword">as</span> plt

<span class="code-function">def</span> <span class="code-function">analyze_depth_distribution</span>(depth_array):
    <span class="code-string">"""分析测序深度分布"""</span>

    <span class="code-comment"># 基本统计量</span>
    mean_depth = np.mean(depth_array)
    median_depth = np.median(depth_array)
    std_depth = np.std(depth_array)
    cv = std_depth / mean_depth

    <span class="code-comment"># 覆盖度统计</span>
    coverage_1x = np.sum(depth_array &gt;= 1) / len(depth_array) * 100
    coverage_10x = np.sum(depth_array &gt;= 10) / len(depth_array) * 100
    coverage_30x = np.sum(depth_array &gt;= 30) / len(depth_array) * 100

    <span class="code-comment"># 异常值检测</span>
    q25, q75 = np.percentile(depth_array, [25, 75])
    iqr = q75 - q25
    outlier_threshold = q75 + 1.5 * iqr
    outliers = np.sum(depth_array &gt; outlier_threshold)

    <span class="code-keyword">return</span> {
        <span class="code-string">'mean_depth'</span>: mean_depth,
        <span class="code-string">'median_depth'</span>: median_depth,
        <span class="code-string">'cv'</span>: cv,
        <span class="code-string">'coverage_1x'</span>: coverage_1x,
        <span class="code-string">'coverage_10x'</span>: coverage_10x,
        <span class="code-string">'coverage_30x'</span>: coverage_30x,
        <span class="code-string">'outliers'</span>: outliers
    }

<span class="code-comment"># 可视化深度分布</span>
<span class="code-function">def</span> <span class="code-function">plot_depth_distribution</span>(depth_array):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    <span class="code-comment"># 深度直方图</span>
    ax1.hist(depth_array, bins=50, alpha=0.7, edgecolor=<span class="code-string">'black'</span>)
    ax1.set_xlabel(<span class="code-string">'Sequencing Depth'</span>)
    ax1.set_ylabel(<span class="code-string">'Frequency'</span>)
    ax1.set_title(<span class="code-string">'Depth Distribution'</span>)

    <span class="code-comment"># 累积分布曲线</span>
    sorted_depths = np.sort(depth_array)
    cumulative = np.arange(1, len(sorted_depths) + 1) / len(sorted_depths)
    ax2.plot(sorted_depths, cumulative * 100)
    ax2.set_xlabel(<span class="code-string">'Sequencing Depth'</span>)
    ax2.set_ylabel(<span class="code-string">'Cumulative Coverage (%)'</span>)
    ax2.set_title(<span class="code-string">'Cumulative Coverage'</span>)

    plt.tight_layout()
    plt.show()
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 分析要点：</strong>深度分布分析应结合基因组注释信息，重复序列、GC含量等因素都会影响深度分布，需要综合考虑。
                    </div>
                `
            },
            {
                title: "质控工具综合对比",
                subtitle: "主流质控工具的功能特点与选择指南",
                content: `
                    <h2>测序数据质控工具：全面对比与选择策略</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>工具名称</th>
                                <th>主要功能</th>
                                <th>处理速度</th>
                                <th>内存需求</th>
                                <th>输出格式</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>FastQC</strong></td>
                                <td>质量评估报告</td>
                                <td>快</td>
                                <td>低</td>
                                <td>HTML报告</td>
                                <td>质量检查</td>
                            </tr>
                            <tr>
                                <td><strong>Trimmomatic</strong></td>
                                <td>接头去除+质量修剪</td>
                                <td>快</td>
                                <td>中等</td>
                                <td>FASTQ</td>
                                <td>标准质控</td>
                            </tr>
                            <tr>
                                <td><strong>Cutadapt</strong></td>
                                <td>接头去除专家</td>
                                <td>中等</td>
                                <td>低</td>
                                <td>FASTQ</td>
                                <td>精确接头处理</td>
                            </tr>
                            <tr>
                                <td><strong>FastP</strong></td>
                                <td>一体化质控</td>
                                <td>极快</td>
                                <td>低</td>
                                <td>FASTQ+HTML</td>
                                <td>现代化流程</td>
                            </tr>
                            <tr>
                                <td><strong>BBDuk</strong></td>
                                <td>污染去除+质控</td>
                                <td>快</td>
                                <td>高</td>
                                <td>FASTQ</td>
                                <td>宏基因组</td>
                            </tr>
                            <tr>
                                <td><strong>Skewer</strong></td>
                                <td>接头去除</td>
                                <td>极快</td>
                                <td>低</td>
                                <td>FASTQ</td>
                                <td>高通量处理</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>工具选择策略</h3>
                            <div class="platform-card">
                                <h4>🎯 基于应用场景</h4>
                                <ul>
                                    <li><strong>基因组重测序：</strong>Trimmomatic + FastQC</li>
                                    <li><strong>RNA-seq：</strong>Cutadapt + FastQC</li>
                                    <li><strong>宏基因组：</strong>BBDuk + FastQC</li>
                                    <li><strong>快速流程：</strong>FastP (一体化)</li>
                                    <li><strong>高通量：</strong>Skewer + FastQC</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>⚖️ 性能考虑</h4>
                                <ul>
                                    <li><strong>数据量大：</strong>优先考虑速度</li>
                                    <li><strong>内存限制：</strong>选择低内存工具</li>
                                    <li><strong>精确度要求：</strong>选择专业工具</li>
                                    <li><strong>流程简化：</strong>选择一体化工具</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>工具组合策略</h3>
                            <div class="platform-card">
                                <h4>🔄 标准流程</h4>
                                <ul>
                                    <li><strong>步骤1：</strong>FastQC初步质量检查</li>
                                    <li><strong>步骤2：</strong>选择合适的质控工具</li>
                                    <li><strong>步骤3：</strong>执行质控处理</li>
                                    <li><strong>步骤4：</strong>FastQC验证处理效果</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>🚀 高级流程</h4>
                                <ul>
                                    <li><strong>多工具验证：</strong>关键数据多工具处理</li>
                                    <li><strong>参数优化：</strong>基于数据特点调整</li>
                                    <li><strong>批量处理：</strong>自动化脚本执行</li>
                                    <li><strong>质量监控：</strong>实时质量指标跟踪</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>💰 成本效益分析</h4>
                            <ul>
                                <li><strong>计算成本：</strong>CPU时间 × 核心数</li>
                                <li><strong>存储成本：</strong>中间文件存储需求</li>
                                <li><strong>人力成本：</strong>学习和维护成本</li>
                                <li><strong>质量收益：</strong>下游分析准确性提升</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>ROI评估：</strong>质量提升 vs 成本投入</li>
                                    <li><strong>长期考虑：</strong>可维护性和扩展性</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔧 自动化集成</h4>
                            <ul>
                                <li><strong>工作流管理：</strong>Nextflow、Snakemake</li>
                                <li><strong>容器化：</strong>Docker、Singularity</li>
                                <li><strong>云计算：</strong>AWS、Google Cloud</li>
                                <li><strong>监控报告：</strong>MultiQC集成报告</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>可重现性：</strong>版本控制和环境管理</li>
                                    <li><strong>可扩展性：</strong>并行处理和资源调度</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📊 质量监控指标</h4>
                            <ul>
                                <li><strong>处理效率：</strong>数据保留率</li>
                                <li><strong>质量改善：</strong>Q30比例提升</li>
                                <li><strong>下游影响：</strong>比对率改善</li>
                                <li><strong>一致性：</strong>批次间变异</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>目标保留率：</strong>&gt;85%</li>
                                    <li><strong>质量提升：</strong>Q30+10-20%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🎓 最佳实践建议</h4>
                            <ul>
                                <li><strong>参数记录：</strong>详细记录所有参数</li>
                                <li><strong>版本控制：</strong>软件版本和数据版本</li>
                                <li><strong>质量验证：</strong>多层次质量检查</li>
                                <li><strong>文档完善：</strong>流程和决策记录</li>
                            </ul>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 质控工具性能基准测试</span>
<span class="code-keyword">import</span> time
<span class="code-keyword">import</span> subprocess

<span class="code-function">def</span> <span class="code-function">benchmark_qc_tools</span>(input_file, tools_config):
    <span class="code-string">"""测试不同质控工具的性能"""</span>
    results = {}

    <span class="code-keyword">for</span> tool_name, command <span class="code-keyword">in</span> tools_config.items():
        start_time = time.time()

        <span class="code-comment"># 执行质控命令</span>
        result = subprocess.run(command, shell=True, capture_output=True)

        end_time = time.time()
        processing_time = end_time - start_time

        results[tool_name] = {
            <span class="code-string">'time'</span>: processing_time,
            <span class="code-string">'success'</span>: result.returncode == 0,
            <span class="code-string">'memory_peak'</span>: <span class="code-function">get_memory_usage</span>(result.pid)
        }

    <span class="code-keyword">return</span> results

<span class="code-comment"># 工具配置示例</span>
tools_config = {
    <span class="code-string">'trimmomatic'</span>: <span class="code-string">'java -jar trimmomatic.jar PE input_R1.fq input_R2.fq ...'</span>,
    <span class="code-string">'cutadapt'</span>: <span class="code-string">'cutadapt -a ADAPTER -o output.fq input.fq'</span>,
    <span class="code-string">'fastp'</span>: <span class="code-string">'fastp -i input_R1.fq -I input_R2.fq -o output_R1.fq -O output_R2.fq'</span>
}

<span class="code-comment"># 执行基准测试</span>
benchmark_results = <span class="code-function">benchmark_qc_tools</span>(<span class="code-string">'test_data.fq'</span>, tools_config)
                    </div>

                    <div class="info-box">
                        <strong>💡 选择建议：</strong>没有一个工具适用于所有场景，应根据数据特点、计算资源、精确度要求等因素综合选择最适合的工具组合。
                    </div>
                `
            },
            {
                title: "质控流程实战案例",
                subtitle: "真实数据的质控处理完整流程",
                content: `
                    <h2>实战案例：人类外显子测序数据质控流程</h2>

                    <div class="highlight-box">
                        <h3>案例背景</h3>
                        <p><strong>数据类型：</strong>人类外显子测序 (WES)，双端150bp，目标深度100X</p>
                        <p><strong>样本信息：</strong>临床样本，DNA质量中等，存在轻微降解</p>
                        <p><strong>质控目标：</strong>确保高质量数据用于变异检测分析</p>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">原始数据<br>质量评估</div>
                        <div class="flowchart-step">问题识别<br>策略制定</div>
                        <div class="flowchart-step">质控处理<br>参数优化</div>
                        <div class="flowchart-step">效果验证<br>结果评估</div>
                        <div class="flowchart-step">下游分析<br>准备就绪</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>步骤1：原始数据评估</h3>
                            <div class="platform-card">
                                <h4>📊 FastQC初步分析</h4>
                                <ul>
                                    <li><strong>总读长数：</strong>45M paired reads</li>
                                    <li><strong>平均质量：</strong>Q32 (前100bp), Q25 (后50bp)</li>
                                    <li><strong>GC含量：</strong>42% (正常范围)</li>
                                    <li><strong>N含量：</strong>0.02% (优秀)</li>
                                    <li><strong>序列长度：</strong>150bp (一致)</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>问题发现：</strong>3'端质量下降明显</li>
                                        <li><strong>接头污染：</strong>检测到2.3%接头序列</li>
                                        <li><strong>重复率：</strong>8.5% (可接受)</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>🔍 问题详细分析</h4>
                                <ul>
                                    <li><strong>质量下降：</strong>位置120bp后急剧下降</li>
                                    <li><strong>接头类型：</strong>Illumina TruSeq接头</li>
                                    <li><strong>污染来源：</strong>短插入片段导致</li>
                                    <li><strong>影响评估：</strong>可能影响比对和变异检测</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>步骤2：质控策略制定</h3>
                            <div class="platform-card">
                                <h4>🎯 处理策略</h4>
                                <ul>
                                    <li><strong>接头去除：</strong>使用Cutadapt精确去除</li>
                                    <li><strong>质量修剪：</strong>滑动窗口Q20阈值</li>
                                    <li><strong>长度过滤：</strong>最小长度50bp</li>
                                    <li><strong>配对保持：</strong>保持R1/R2配对关系</li>
                                </ul>

                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>工具选择：</strong>Cutadapt (精确) + Trimmomatic (质量)</li>
                                        <li><strong>参数优化：</strong>基于数据特点调整</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>⚙️ 参数设置</h4>
                                <ul>
                                    <li><strong>接头序列：</strong>AGATCGGAAGAGC (标准TruSeq)</li>
                                    <li><strong>错误容忍：</strong>10% (1-2个错配)</li>
                                    <li><strong>最小重叠：</strong>3bp</li>
                                    <li><strong>质量阈值：</strong>Q20 (99%准确率)</li>
                                    <li><strong>窗口大小：</strong>4bp</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
<span class="code-comment"># 完整质控流程脚本</span>
<span class="code-comment">#!/bin/bash</span>

<span class="code-comment"># 设置变量</span>
INPUT_R1=<span class="code-string">"sample_R1.fastq.gz"</span>
INPUT_R2=<span class="code-string">"sample_R2.fastq.gz"</span>
ADAPTER=<span class="code-string">"AGATCGGAAGAGC"</span>
THREADS=8

<span class="code-comment"># 步骤1: 原始数据质量检查</span>
<span class="code-keyword">echo</span> <span class="code-string">"Step 1: Initial quality assessment"</span>
fastqc $INPUT_R1 $INPUT_R2 -o raw_qc/ -t $THREADS

<span class="code-comment"># 步骤2: 接头去除</span>
<span class="code-keyword">echo</span> <span class="code-string">"Step 2: Adapter removal"</span>
cutadapt -a $ADAPTER -A $ADAPTER \\
    -o trimmed_R1.fastq.gz -p trimmed_R2.fastq.gz \\
    --minimum-length 50 \\
    --error-rate 0.1 \\
    --times 2 \\
    --cores $THREADS \\
    $INPUT_R1 $INPUT_R2

<span class="code-comment"># 步骤3: 质量修剪</span>
<span class="code-keyword">echo</span> <span class="code-string">"Step 3: Quality trimming"</span>
java -jar trimmomatic.jar PE \\
    trimmed_R1.fastq.gz trimmed_R2.fastq.gz \\
    clean_R1_paired.fastq.gz clean_R1_unpaired.fastq.gz \\
    clean_R2_paired.fastq.gz clean_R2_unpaired.fastq.gz \\
    SLIDINGWINDOW:4:20 \\
    LEADING:3 TRAILING:3 \\
    MINLEN:50 \\
    -threads $THREADS

<span class="code-comment"># 步骤4: 处理后质量检查</span>
<span class="code-keyword">echo</span> <span class="code-string">"Step 4: Post-processing quality check"</span>
fastqc clean_R1_paired.fastq.gz clean_R2_paired.fastq.gz \\
    -o clean_qc/ -t $THREADS

<span class="code-comment"># 步骤5: 生成综合报告</span>
<span class="code-keyword">echo</span> <span class="code-string">"Step 5: Generate summary report"</span>
multiqc raw_qc/ clean_qc/ -o final_report/
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>质量指标</th>
                                <th>处理前</th>
                                <th>处理后</th>
                                <th>改善程度</th>
                                <th>评估</th>
                            </tr>
                            <tr>
                                <td><strong>总读长数</strong></td>
                                <td>45M pairs</td>
                                <td>42.3M pairs</td>
                                <td>94.0%保留</td>
                                <td>优秀</td>
                            </tr>
                            <tr>
                                <td><strong>平均质量</strong></td>
                                <td>Q28.5</td>
                                <td>Q34.2</td>
                                <td>+5.7分</td>
                                <td>显著改善</td>
                            </tr>
                            <tr>
                                <td><strong>Q30比例</strong></td>
                                <td>78.3%</td>
                                <td>92.1%</td>
                                <td>+13.8%</td>
                                <td>大幅提升</td>
                            </tr>
                            <tr>
                                <td><strong>接头污染</strong></td>
                                <td>2.3%</td>
                                <td>0.02%</td>
                                <td>-99.1%</td>
                                <td>完全去除</td>
                            </tr>
                            <tr>
                                <td><strong>平均长度</strong></td>
                                <td>150bp</td>
                                <td>142bp</td>
                                <td>-5.3%</td>
                                <td>合理损失</td>
                            </tr>
                        </table>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>✅ 处理效果评估</h4>
                            <ul>
                                <li><strong>数据保留率：</strong>94% (优秀)</li>
                                <li><strong>质量提升：</strong>Q30比例+13.8%</li>
                                <li><strong>污染去除：</strong>接头污染基本清除</li>
                                <li><strong>长度损失：</strong>5.3% (可接受)</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>📈 下游分析预期</h4>
                            <ul>
                                <li><strong>比对率：</strong>预期&gt;95%</li>
                                <li><strong>目标覆盖：</strong>预期&gt;90%外显子</li>
                                <li><strong>变异检测：</strong>假阳性率降低</li>
                                <li><strong>分析可信度：</strong>显著提升</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>💡 经验总结</h4>
                            <ul>
                                <li><strong>参数调优：</strong>基于数据特点微调</li>
                                <li><strong>工具组合：</strong>发挥各工具优势</li>
                                <li><strong>质量监控：</strong>多层次验证效果</li>
                                <li><strong>文档记录：</strong>详细记录处理过程</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>🔄 可重现性保证</h4>
                            <ul>
                                <li><strong>版本记录：</strong>所有软件版本</li>
                                <li><strong>参数保存：</strong>完整命令行记录</li>
                                <li><strong>环境管理：</strong>容器化部署</li>
                                <li><strong>质量标准：</strong>建立质控标准</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 案例启示：</strong>成功的质控需要针对具体数据特点制定策略，合理的参数设置和工具组合能够在保持数据量的同时显著提升数据质量。
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成幻灯片HTML
            const container = document.querySelector('.container');
            container.innerHTML = '';

            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <h1>${slide.title}</h1>
                    <div class="subtitle">${slide.subtitle}</div>
                    ${slide.content}
                `;
                container.appendChild(slideElement);
            });

            // 生成菜单
            generateMenu();
            updateNavigation();
            updateProgress();
        }

        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = '';

            slides.forEach((slide, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = 'menu-item';
                menuItem.textContent = `${index + 1}. ${slide.title}`;
                menuItem.onclick = () => {
                    goToSlide(index);
                    toggleMenu();
                };
                menuDropdown.appendChild(menuItem);
            });
        }

        function showSlide(index) {
            const slides = document.querySelectorAll('.slide');
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });

            updateNavigation();
            updateProgress();
            updateNavbar();
            document.getElementById('currentSlide').textContent = index + 1;
        }

        function updateNavbar() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            
            if (currentSlideIndex >= 0 && currentSlideIndex < navItems.length) {
                navItems[currentSlideIndex]?.classList.add('active');
            }
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                currentSlideIndex++;
                showSlide(currentSlideIndex);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                currentSlideIndex--;
                showSlide(currentSlideIndex);
            }
        }

        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                showSlide(currentSlideIndex);
            }
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.disabled = currentSlideIndex === 0;
            nextBtn.disabled = currentSlideIndex === totalSlides - 1;
        }

        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function toggleMenu() {
            const menu = document.getElementById('menuDropdown');
            menu.classList.toggle('show');
        }

        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay();
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        function resetPresentation() {
            currentSlideIndex = 0;
            showSlide(currentSlideIndex);
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    goToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    goToSlide(totalSlides - 1);
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            const menu = document.getElementById('menuDropdown');
            const menuBtn = document.querySelector('.menu-btn');
            if (!menu.contains(e.target) && !menuBtn.contains(e.target)) {
                menu.classList.remove('show');
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPresentation);
    </script>
</body>
</html>
