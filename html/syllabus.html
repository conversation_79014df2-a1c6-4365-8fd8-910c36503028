<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学大纲 - NGS高通量测序技术课程平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: #667eea;
        }

        /* 主要内容 */
        .main-content {
            padding: 3rem 0;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .course-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .info-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .info-item i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .info-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .lecture-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .lecture-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .lecture-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .lecture-meta h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }

        .lecture-meta .meta-info {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #666;
        }

        .content-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .content-section {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
        }

        .content-section h4 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-section ul {
            list-style: none;
            padding-left: 0;
        }

        .content-section li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .content-section li::before {
            content: '▸';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .assessment-item {
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .assessment-item i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .assessment-item .percentage {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin-bottom: 2rem;
            color: white;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .content-card {
                padding: 1.5rem;
            }

            .lecture-header {
                flex-direction: column;
                text-align: center;
            }

            .course-info-grid,
            .content-sections,
            .assessment-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-dna"></i>
                    NGS课程平台
                </a>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="index.html#courses"><i class="fas fa-book"></i> 课程</a></li>
                    <li><a href="about.html"><i class="fas fa-info-circle"></i> 关于</a></li>
                    <li><a href="syllabus.html" class="active"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                    <li><a href="resources.html"><i class="fas fa-download"></i> 资源</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html">首页</a> / 教学大纲
            </div>

            <!-- 页面头部 -->
            <div class="page-header">
                <h1><i class="fas fa-graduation-cap"></i> 高通量测序原理与数据分析</h1>
                <p>课程教学大纲 - Course Syllabus</p>
            </div>

            <!-- 课程基本信息 -->
            <div class="content-card">
                <h2><i class="fas fa-info-circle"></i> 课程基本信息</h2>
                <div class="course-info-grid">
                    <div class="info-item">
                        <i class="fas fa-id-card"></i>
                        <h4>课程编码</h4>
                        <p>S0904C213</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <h4>总学时</h4>
                        <p>32学时</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-users"></i>
                        <h4>授课对象</h4>
                        <p>硕士研究生</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <h4>教学形式</h4>
                        <p>理论(16h)+实践(16h)</p>
                    </div>
                </div>
                
                <p style="margin-top: 2rem; font-size: 1.1rem; line-height: 1.8;">
                    <strong>课程目标：</strong>使学生全面掌握高通量测序的基本原理、主流技术平台及数据分析方法，能够独立设计实验方案并执行常见类型的高通量测序数据分析流程。
                </p>
            </div>

            <!-- 专题设置 -->
            <div class="content-card">
                <h2><i class="fas fa-list-ol"></i> 专题设置</h2>
                
                <!-- 专题一 -->
                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题一：高通量测序技术概论</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 基础入门</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture1_NGS_Technology_Interactive_Slides.html" class="btn-primary">
                                <i class="fas fa-play"></i> 学习课件
                            </a>
                            <a href="note_pdf/Lecture1_note.pdf" class="btn-primary" target="_blank">
                                <i class="fas fa-download"></i> 下载讲义
                            </a>
                        </div>
                    </div>
                    <div class="content-sections">
                        <div class="content-section">
                            <h4><i class="fas fa-book-open"></i> 理论课内容</h4>
                            <ul>
                                <li>高通量测序技术的发展历史与技术演进</li>
                                <li>第一代、第二代和第三代测序技术原理比较</li>
                                <li>主流测序平台介绍(Illumina, Ion Torrent, PacBio, Oxford Nanopore等)</li>
                                <li>各测序平台的技术特点、优缺点及适用场景</li>
                                <li>测序实验设计与考虑因素</li>
                            </ul>
                        </div>
                        <div class="content-section">
                            <h4><i class="fas fa-code"></i> 实践操作课</h4>
                            <ul>
                                <li>Linux操作系统基本命令介绍</li>
                                <li>生物信息学分析环境配置与测试</li>
                                <li>常用测序数据格式介绍(FASTQ, SAM/BAM, VCF等)</li>
                                <li>公共数据库资源使用(SRA, GEO, ENCODE等)</li>
                                <li>数据传输与管理实践</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 专题二 -->
                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题二：测序数据质量控制与预处理</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 核心技能</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture2_QC_Preprocessing_Interactive_Slides.html" class="btn-primary">
                                <i class="fas fa-play"></i> 学习课件
                            </a>
                            <a href="note_pdf/Lecture2_note.pdf" class="btn-primary" target="_blank">
                                <i class="fas fa-download"></i> 下载讲义
                            </a>
                        </div>
                    </div>
                    <div class="content-sections">
                        <div class="content-section">
                            <h4><i class="fas fa-book-open"></i> 理论课内容</h4>
                            <ul>
                                <li>测序错误来源与类型分析</li>
                                <li>测序数据质量评估指标与方法</li>
                                <li>数据过滤与质控策略</li>
                                <li>接头去除和低质量序列过滤原理</li>
                                <li>测序深度与覆盖度的计算及意义</li>
                            </ul>
                        </div>
                        <div class="content-section">
                            <h4><i class="fas fa-code"></i> 实践操作课</h4>
                            <ul>
                                <li>FastQC工具使用与质量报告解读</li>
                                <li>Trimmomatic/Cutadapt等工具进行数据过滤与清洗</li>
                                <li>MultiQC进行批量质控结果可视化</li>
                                <li>数据预处理自动化流程搭建</li>
                                <li>质控前后数据质量对比分析</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 专题三到八的简化版本 -->
                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题三：基因组测序(DNA-seq)数据分析</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 专业技术</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture3_DNA_seq_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture3_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>掌握基因组比对、变异检测与注释流程，学习BWA、GATK、ANNOVAR等工具，理解SNP/Indel检测原理。</p>
                </div>

                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题四：转录组测序(RNA-seq)数据分析</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 热门应用</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture4_RNA_seq_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture4_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>学习RNA-seq分析完整流程，掌握差异表达分析、功能富集分析方法，熟练使用DESeq2、ClusterProfiler等工具。</p>
                </div>

                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题五：表观基因组测序数据分析</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 前沿技术</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture5_Epigenomics_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture5_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>深入了解表观基因组学，学习ChIP-seq、ATAC-seq、甲基化测序数据分析，掌握MACS2、Bismark等工具使用。</p>
                </div>

                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题六：单细胞测序技术与数据分析</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 前沿技术</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture6_SingleCell_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture6_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>探索单细胞测序前沿技术，学习细胞聚类、轨迹分析方法，熟练使用Seurat、Scanpy进行单细胞RNA-seq分析。</p>
                </div>

                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-bacteria"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题七：宏基因组测序与数据分析</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 生态应用</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture7_Metagenomics_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture7_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>掌握宏基因组学分析方法，学习微生物群落结构分析、功能预测，熟练使用MetaPhlAn、HUMAnN等专业工具。</p>
                </div>

                <div class="lecture-item">
                    <div class="lecture-header">
                        <div class="lecture-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="lecture-meta">
                            <h3>专题八：高通量测序技术在植物保护中的应用</h3>
                            <div class="meta-info">
                                <span><i class="fas fa-book"></i> 理论课(2h)</span>
                                <span><i class="fas fa-laptop-code"></i> 实践课(2h)</span>
                                <span><i class="fas fa-layer-group"></i> 应用专题</span>
                            </div>
                        </div>
                        <div style="margin-left: auto;">
                            <a href="Lecture8_Plant_Protection_Interactive_Slides.html" class="btn-primary">课件</a>
                            <a href="note_pdf/Lecture8_note.pdf" class="btn-primary" target="_blank">讲义</a>
                        </div>
                    </div>
                    <p>探索NGS在植物保护中的创新应用，学习病原体检测、抗病基因挖掘、环境DNA监测等前沿技术应用。</p>
                </div>
            </div>

            <!-- 考核方式 -->
            <div class="content-card">
                <h2><i class="fas fa-tasks"></i> 考核方式</h2>
                <div class="assessment-grid">
                    <div class="assessment-item">
                        <i class="fas fa-user-check"></i>
                        <div class="percentage">20%</div>
                        <h4>平时表现</h4>
                        <p>课堂参与度和实践操作完成情况</p>
                    </div>
                    <div class="assessment-item">
                        <i class="fas fa-project-diagram"></i>
                        <div class="percentage">80%</div>
                        <h4>期末项目</h4>
                        <p>独立完成NGS数据分析项目并撰写技术报告</p>
                    </div>
                </div>
            </div>

            <!-- 参考资料 -->
            <div class="content-card">
                <h2><i class="fas fa-book"></i> 参考资料</h2>
                <div class="content-sections">
                    <div class="content-section">
                        <h4><i class="fas fa-book-open"></i> 主要教材</h4>
                        <ul>
                            <li>《Bioinformatics Data Skills》，Vince Buffalo著</li>
                            <li>《Computational Genome Analysis: An Introduction》，Richard C. Deonier等著</li>
                            <li>《RNA-seq Data Analysis: A Practical Approach》，Eija Korpelainen等著</li>
                        </ul>
                    </div>
                    <div class="content-section">
                        <h4><i class="fas fa-globe"></i> 在线资源</h4>
                        <ul>
                            <li>各测序技术和分析工具的官方文档和教程</li>
                            <li>植物保护与高通量测序相关前沿研究论文集</li>
                            <li>《Plant Pathogen Genomics: Methods and Protocols》</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="content-card">
                <h2><i class="fas fa-compass"></i> 快速导航</h2>
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.html" class="btn-primary">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <a href="about.html" class="btn-primary">
                        <i class="fas fa-info-circle"></i> 课程介绍
                    </a>
                    <a href="index.html#courses" class="btn-primary">
                        <i class="fas fa-book"></i> 开始学习
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html> 