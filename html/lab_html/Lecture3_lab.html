<!DOCTYPE html>
<html>
<head>
<title>Lecture3_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E4%B8%89%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%B5%8B%E5%BA%8Fdna-seq%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题三：基因组测序(DNA-seq)数据分析 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握BWA和Bowtie2比对工具的使用</li>
<li>学会SAMtools进行SAM/BAM文件处理</li>
<li>掌握GATK进行变异检测的流程</li>
<li>学会SnpEff进行变异注释</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE">软件环境配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建专题三专用环境</span>
conda create -n dna_seq_analysis python=3.8 -y
conda activate dna_seq_analysis

<span class="hljs-comment"># 安装核心软件</span>
conda install -c bioconda bwa bowtie2 samtools bcftools gatk4 snpeff picard -y

<span class="hljs-comment"># 验证安装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 软件版本验证 ==="</span>
bwa          <span class="hljs-comment"># 显示帮助信息</span>
bowtie2 --version
samtools --version
gatk --version
bcftools --version

<span class="hljs-built_in">echo</span> <span class="hljs-string">"软件安装完成！"</span>
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p dna_seq_practice/{data/{reference,raw,processed},results/{alignment,variants,annotation},scripts,logs}
<span class="hljs-built_in">cd</span> dna_seq_practice

<span class="hljs-comment"># 创建简化的参考基因组（用于教学演示）</span>
cat &gt; data/reference/chr22_mini.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

<span class="hljs-comment"># 创建模拟双端测序数据</span>
cat &gt; data/raw/sample1_R1.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR001_1/1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_2/1
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR001_3/1
TCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_4/1
CAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat &gt; data/raw/sample1_R2.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR001_1/2
TGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_2/2
ACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR001_3/2
TGCAGACATTCAATTGTTATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR001_4/2
TGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

<span class="hljs-comment"># 创建变异位点文件（用于模拟变异检测）</span>
cat &gt; data/reference/known_sites.vcf &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">##fileformat=VCFv4.2</span>
<span class="hljs-comment">##contig=&lt;ID=chr22&gt;</span>
<span class="hljs-comment">#CHROM	POS	ID	REF	ALT	QUAL	FILTER	INFO</span>
chr22	100	rs001	A	G	60	PASS	.
chr22	200	rs002	T	C	50	PASS	.
chr22	300	rs003	G	A	70	PASS	.
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"实验数据准备完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86bwabowtie2%E5%8F%82%E8%80%83%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">第一部分：BWA/Bowtie2参考基因组比对实践</h2>
<h3 id="1-%E5%8F%82%E8%80%83%E5%9F%BA%E5%9B%A0%E7%BB%84%E5%87%86%E5%A4%87">1. 参考基因组准备</h3>
<ul>
<li>
<p><strong>参考基因组下载</strong></p>
<ul>
<li>从NCBI、Ensembl等数据库下载参考基因组序列（FASTA格式）。</li>
<li>例如，下载人类基因组：<pre class="hljs"><code><div>wget ftp://ftp.ncbi.nlm.nih.gov/genomes/Homo_sapiens/GRCh38_p13/seqs_for_alignment_pipelines.ucsc_ids/Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa.gz
gunzip Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa.gz
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>参考基因组索引构建</strong></p>
<ul>
<li>使用BWA或Bowtie2构建参考基因组索引，用于加速比对过程。</li>
<li>BWA索引构建：<pre class="hljs"><code><div>bwa index Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa
</div></code></pre>
</li>
<li>Bowtie2索引构建：<pre class="hljs"><code><div>bowtie2-build Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa Homo_sapiens.GRCh38_p13
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>参考基因组注释文件准备</strong></p>
<ul>
<li>下载参考基因组的注释文件（GTF/GFF格式），用于后续的变异注释和功能分析。</li>
<li>例如，从Ensembl下载人类基因组注释文件。</li>
</ul>
</li>
</ul>
<h3 id="2-bwa%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">2. BWA比对实践</h3>
<ul>
<li>
<p><strong>BWA安装与配置</strong></p>
<ul>
<li>使用conda安装BWA：<pre class="hljs"><code><div>conda install -c bioconda bwa
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>BWA索引构建命令</strong></p>
<ul>
<li><code>bwa index &lt;reference.fa&gt;</code></li>
<li>例如：<pre class="hljs"><code><div>bwa index Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>BWA-MEM比对命令详解</strong></p>
<ul>
<li><code>bwa mem [options] &lt;reference.fa&gt; &lt;input.fq.gz&gt;</code></li>
<li>常用参数：
<ul>
<li><code>-t &lt;threads&gt;</code>: 指定线程数</li>
<li><code>-M</code>: 标记PCR重复序列</li>
<li><code>-R &lt;read_group&gt;</code>: 指定读段组信息</li>
</ul>
</li>
<li>例如：<pre class="hljs"><code><div>bwa mem -t 8 -M -R <span class="hljs-string">"@RG\tID:sample1\tSM:sample1\tLB:lib1\tPL:illumina"</span> Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa sample1.fastq.gz &gt; sample1.sam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>比对参数优化</strong></p>
<ul>
<li>根据测序数据特点和研究目标，调整BWA算法的参数，以获得最佳的比对效果。</li>
<li>例如，调整<code>-k</code>参数（最小种子长度）、<code>-w</code>参数（Z-dropoff参数）等。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载测序数据（FASTQ格式）。</li>
<li>构建参考基因组索引。</li>
<li>使用BWA-MEM进行比对。</li>
<li>将SAM文件转换为BAM文件。</li>
<li>对比对结果进行排序和索引。</li>
</ol>
</li>
</ul>
<h3 id="3-bowtie2%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">3. Bowtie2比对实践</h3>
<ul>
<li>
<p><strong>Bowtie2安装与配置</strong></p>
<ul>
<li>使用conda安装Bowtie2：<pre class="hljs"><code><div>conda install -c bioconda bowtie2
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Bowtie2索引构建命令</strong></p>
<ul>
<li><code>bowtie2-build &lt;reference.fa&gt; &lt;index_base&gt;</code></li>
<li>例如：<pre class="hljs"><code><div>bowtie2-build Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa Homo_sapiens.GRCh38_p13
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Bowtie2比对命令详解</strong></p>
<ul>
<li><code>bowtie2 [options] -x &lt;index_base&gt; -U &lt;input.fq.gz&gt; -S &lt;output.sam&gt;</code></li>
<li>常用参数：
<ul>
<li><code>-p &lt;threads&gt;</code>: 指定线程数</li>
<li><code>--very-sensitive</code>: 提高比对灵敏度</li>
<li><code>--dovetail</code>: 允许末端到末端的比对</li>
</ul>
</li>
<li>例如：<pre class="hljs"><code><div>bowtie2 -p 8 --very-sensitive -x Homo_sapiens.GRCh38_p13 -U sample1.fastq.gz -S sample1.sam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>比对参数优化</strong></p>
<ul>
<li>根据测序数据特点和研究目标，调整Bowtie2算法的参数，以获得最佳的比对效果。</li>
<li>例如，调整<code>--seed-length</code>参数（种子长度）、<code>--score-min</code>参数（最小比对得分）等。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载测序数据（FASTQ格式）。</li>
<li>构建参考基因组索引。</li>
<li>使用Bowtie2进行比对。</li>
<li>将SAM文件转换为BAM文件。</li>
<li>对比对结果进行排序和索引。</li>
</ol>
</li>
</ul>
<h3 id="4-%E6%AF%94%E5%AF%B9%E7%BB%93%E6%9E%9C%E8%AF%84%E4%BC%B0">4. 比对结果评估</h3>
<ul>
<li>
<p><strong>比对统计信息获取</strong></p>
<ul>
<li>使用SAMtools的<code>flagstat</code>命令获取比对统计信息。</li>
<li>例如：<pre class="hljs"><code><div>samtools flagstat sample1.bam
</div></code></pre>
</li>
<li>常用的统计信息包括：
<ul>
<li>总reads数</li>
<li>比对上的reads数</li>
<li>比对率</li>
<li>唯一比对的reads数</li>
<li>多重比对的reads数</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>比对质量分布分析</strong></p>
<ul>
<li>使用SAMtools的<code>stats</code>命令获取比对质量分布信息。</li>
<li>例如：<pre class="hljs"><code><div>samtools stats sample1.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>覆盖度分析</strong></p>
<ul>
<li>使用SAMtools的<code>depth</code>命令计算每个位置的测序深度。</li>
<li>例如：<pre class="hljs"><code><div>samtools depth sample1.bam &gt; sample1.depth
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>常见问题排查</strong></p>
<ul>
<li>比对率低：
<ul>
<li>可能原因：测序数据质量差、参考基因组污染、物种不匹配等。</li>
<li>解决方案：提高测序数据质量、更换参考基因组、去除污染序列等。</li>
</ul>
</li>
<li>比对结果偏向特定区域：
<ul>
<li>可能原因：GC含量偏好性、重复序列等。</li>
<li>解决方案：使用PCR-free文库构建方法、使用长读长测序技术等。</li>
</ul>
</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>参考基因组比对是基因组测序数据分析的关键步骤。掌握BWA和Bowtie2的使用方法，并对比对结果进行评估，可以为后续的变异检测和功能分析奠定基础。</p>
<h2 id="samtools%E8%BF%9B%E8%A1%8Csambam%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86">SAMtools进行SAM/BAM文件处理</h2>
<h3 id="1-sambam%E6%A0%BC%E5%BC%8F%E8%AF%A6%E8%A7%A3">1. SAM/BAM格式详解</h3>
<ul>
<li>
<p><strong>格式规范回顾</strong></p>
<ul>
<li>SAM (Sequence Alignment/Map) 是一种文本格式，用于存储比对后的测序数据。</li>
<li>BAM (Binary Alignment/Map) 是SAM的二进制压缩格式，占用空间更小，读取速度更快。</li>
</ul>
</li>
<li>
<p><strong>头部信息解读</strong></p>
<ul>
<li>SAM/BAM文件的头部信息以<code>@</code>开头，包含以下信息：
<ul>
<li><code>@HD</code>: 头部信息，包括版本号、排序方式等。</li>
<li><code>@SQ</code>: 参考基因组序列信息，包括序列名称、长度等。</li>
<li><code>@RG</code>: 读段组信息，包括样本ID、文库ID、测序平台等。</li>
<li><code>@PG</code>: 程序信息，包括比对工具名称、版本号等。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>比对记录字段含义</strong></p>
<ul>
<li>每一行代表一个比对结果，包含以下字段：
<ul>
<li><code>QNAME</code>: 序列名称</li>
<li><code>FLAG</code>: 比对标志，表示比对的方向、是否配对、是否唯一比对等信息。</li>
<li><code>RNAME</code>: 参考基因组序列名称</li>
<li><code>POS</code>: 比对起始位置（1-based）</li>
<li><code>MAPQ</code>: 比对质量分数</li>
<li><code>CIGAR</code>: 压缩比对信息，表示比对的匹配、插入、删除等情况。</li>
<li><code>RNEXT</code>: 下一个片段比对的参考基因组序列名称</li>
<li><code>PNEXT</code>: 下一个片段比对的起始位置</li>
<li><code>TLEN</code>: 模板长度</li>
<li><code>SEQ</code>: 序列</li>
<li><code>QUAL</code>: 质量分数</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>FLAG值解读</strong></p>
<ul>
<li>FLAG值是一个二进制数，每一位代表不同的比对状态。</li>
<li>可以使用SAMtools的<code>flag</code>命令解读FLAG值。</li>
<li>例如：<pre class="hljs"><code><div>samtools flag 0x40
</div></code></pre>
<ul>
<li>输出结果表示该序列是read1。</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="2-samtools%E5%9F%BA%E6%9C%AC%E6%93%8D%E4%BD%9C">2. SAMtools基本操作</h3>
<ul>
<li>
<p><strong>格式转换(view)</strong></p>
<ul>
<li>将SAM文件转换为BAM文件：<pre class="hljs"><code><div>samtools view -bS input.sam &gt; output.bam
</div></code></pre>
</li>
<li>将BAM文件转换为SAM文件：<pre class="hljs"><code><div>samtools view -h input.bam &gt; output.sam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>排序(sort)</strong></p>
<ul>
<li>按照比对位置对BAM文件进行排序：<pre class="hljs"><code><div>samtools sort input.bam -o output.sorted.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>索引构建(index)</strong></p>
<ul>
<li>为BAM文件构建索引，用于快速访问：<pre class="hljs"><code><div>samtools index input.sorted.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>合并文件(merge)</strong></p>
<ul>
<li>将多个BAM文件合并成一个BAM文件：<pre class="hljs"><code><div>samtools merge output.merged.bam input1.bam input2.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>子集提取(view -b)</strong></p>
<ul>
<li>提取BAM文件中特定区域的比对结果：<pre class="hljs"><code><div>samtools view -b input.bam chr1:1000000-2000000 &gt; output.subset.bam
</div></code></pre>
</li>
</ul>
</li>
</ul>
<h3 id="3-samtools%E9%AB%98%E7%BA%A7%E5%8A%9F%E8%83%BD">3. SAMtools高级功能</h3>
<ul>
<li>
<p><strong>比对统计(flagstat, idxstats)</strong></p>
<ul>
<li>使用<code>flagstat</code>命令获取比对统计信息：<pre class="hljs"><code><div>samtools flagstat input.bam
</div></code></pre>
</li>
<li>使用<code>idxstats</code>命令获取每个参考基因组序列的比对统计信息：<pre class="hljs"><code><div>samtools idxstats input.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>覆盖度计算(depth, bedcov)</strong></p>
<ul>
<li>使用<code>depth</code>命令计算每个位置的测序深度：<pre class="hljs"><code><div>samtools depth input.bam &gt; output.depth
</div></code></pre>
</li>
<li>使用<code>bedcov</code>命令计算每个BED区域的平均测序深度：<pre class="hljs"><code><div>samtools bedcov regions.bed input.bam &gt; output.bedcov
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>碱基质量统计(stats)</strong></p>
<ul>
<li>使用<code>stats</code>命令获取碱基质量统计信息：<pre class="hljs"><code><div>samtools stats input.bam &gt; output.stats
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>过滤操作(view -q, -F, -f)</strong></p>
<ul>
<li>使用<code>-q</code>参数过滤比对质量分数低于指定值的比对结果：<pre class="hljs"><code><div>samtools view -b -q 20 input.bam &gt; output.filtered.bam
</div></code></pre>
</li>
<li>使用<code>-F</code>参数过滤掉具有指定FLAG值的比对结果：<pre class="hljs"><code><div>samtools view -b -F 0x4 input.bam &gt; output.filtered.bam
</div></code></pre>
</li>
<li>使用<code>-f</code>参数只保留具有指定FLAG值的比对结果：<pre class="hljs"><code><div>samtools view -b -f 0x2 input.bam &gt; output.filtered.bam
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>PCR重复标记(markdup)</strong></p>
<ul>
<li>使用<code>markdup</code>命令标记PCR重复序列：<pre class="hljs"><code><div>samtools markdup input.bam output.marked.bam
</div></code></pre>
</li>
</ul>
</li>
</ul>
<h3 id="4-bam%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5">4. BAM文件处理最佳实践</h3>
<ul>
<li>
<p><strong>处理流程设计</strong></p>
<ol>
<li>比对</li>
<li>排序</li>
<li>标记重复序列</li>
<li>碱基质量重校正</li>
<li>变异检测</li>
</ol>
</li>
<li>
<p><strong>文件命名规范</strong></p>
<ul>
<li><code>sample_id.bam</code>: 比对后的BAM文件</li>
<li><code>sample_id.sorted.bam</code>: 排序后的BAM文件</li>
<li><code>sample_id.marked.bam</code>: 标记重复序列后的BAM文件</li>
<li><code>sample_id.recal.bam</code>: 碱基质量重校正后的BAM文件</li>
</ul>
</li>
<li>
<p><strong>中间文件管理</strong></p>
<ul>
<li>删除不必要的中间文件，节省存储空间。</li>
</ul>
</li>
<li>
<p><strong>并行处理策略</strong></p>
<ul>
<li>使用多线程并行处理BAM文件，提高处理速度。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>使用SAMtools进行排序、索引、统计等操作。</li>
<li>使用SAMtools进行过滤操作。</li>
<li>使用SAMtools标记PCR重复序列。</li>
</ol>
</li>
</ul>
<p><strong>总结</strong></p>
<p>SAMtools是基因组测序数据分析中不可或缺的工具。掌握SAMtools的基本操作和高级功能，可以有效地管理和处理BAM文件，为后续的分析提供高质量的数据。</p>
<h2 id="gatkfreebayes%E7%AD%89%E5%B7%A5%E5%85%B7%E8%BF%9B%E8%A1%8C%E5%8F%98%E5%BC%82%E6%A3%80%E6%B5%8B">GATK/FreeBayes等工具进行变异检测</h2>
<h3 id="1-gatk%E5%8F%98%E5%BC%82%E6%A3%80%E6%B5%8B%E6%B5%81%E7%A8%8B">1. GATK变异检测流程</h3>
<ul>
<li>
<p><strong>GATK安装与配置</strong></p>
<ul>
<li>GATK (Genome Analysis Toolkit) 是一种常用的变异检测工具。</li>
<li>GATK需要Java环境，请确保已安装Java。</li>
<li>从GATK官网下载GATK安装包，并配置环境变量。</li>
<li>或者使用conda安装：<pre class="hljs"><code><div>conda install -c bioconda gatk4
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>数据预处理步骤</strong></p>
<ul>
<li><strong>标记重复序列</strong>
<ul>
<li>使用GATK的<code>MarkDuplicates</code>工具标记PCR重复序列：<pre class="hljs"><code><div>gatk MarkDuplicates -I input.bam -O output.marked.bam -M metrics.txt
</div></code></pre>
</li>
</ul>
</li>
<li><strong>碱基质量重校准(BQSR)</strong>
<ol>
<li>使用<code>BaseRecalibrator</code>工具生成重校准模型：<pre class="hljs"><code><div>gatk BaseRecalibrator -I input.marked.bam -R reference.fa -known-sites dbsnp.vcf.gz -O recal_data.table
</div></code></pre>
</li>
<li>使用<code>ApplyBQSR</code>工具应用重校准模型：<pre class="hljs"><code><div>gatk ApplyBQSR -I input.marked.bam -R reference.fa -bqsr recal_data.table -O output.recal.bam
</div></code></pre>
</li>
</ol>
</li>
</ul>
</li>
<li>
<p><strong>HaplotypeCaller使用</strong></p>
<ul>
<li>
<p>使用<code>HaplotypeCaller</code>工具进行变异检测：</p>
<pre class="hljs"><code><div>gatk HaplotypeCaller -I input.recal.bam -R reference.fa -O output.vcf.gz
</div></code></pre>
</li>
<li>
<p><strong>命令参数详解</strong></p>
<ul>
<li><code>-I</code>: 输入BAM文件</li>
<li><code>-R</code>: 参考基因组文件</li>
<li><code>-O</code>: 输出VCF文件</li>
<li><code>-ERC GVCF</code>: 生成GVCF文件，用于后续的多样本联合分析</li>
</ul>
</li>
<li>
<p><strong>GVCF模式vs标准模式</strong></p>
<ul>
<li>标准模式：直接生成VCF文件，适用于单样本分析。</li>
<li>GVCF模式：生成GVCF文件，适用于多样本联合分析。</li>
</ul>
</li>
<li>
<p><strong>多样本联合分析</strong></p>
<ol>
<li>使用<code>GenomicsDBImport</code>工具将多个GVCF文件导入到GenomicsDB中：<pre class="hljs"><code><div>gatk GenomicsDBImport -v gvcf1.vcf.gz -v gvcf2.vcf.gz -v gvcf3.vcf.gz -genomicsdb-workspace-path my_database
</div></code></pre>
</li>
<li>使用<code>GenotypeGVCFs</code>工具对GenomicsDB中的变异进行基因型推断：<pre class="hljs"><code><div>gatk GenotypeGVCFs -R reference.fa -V gendb://my_database -O output.vcf.gz
</div></code></pre>
</li>
</ol>
</li>
</ul>
</li>
<li>
<p><strong>变异过滤策略</strong></p>
<ul>
<li>
<p><strong>硬过滤参数设置</strong></p>
<ul>
<li>根据GATK官方推荐的硬过滤参数，对变异进行过滤：<pre class="hljs"><code><div>gatk VariantFiltration -V input.vcf.gz -filter <span class="hljs-string">"QD &lt; 2.0 || FS &gt; 60.0 || MQ &lt; 40.0 || MQRankSum &lt; -12.5 || ReadPosRankSum &lt; -8.0 || SOR &gt; 3.0"</span> -O output.filtered.vcf.gz
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>变异质量评分重校正(VQSR)</strong></p>
<ol>
<li>使用<code>VariantRecalibrator</code>工具构建变异质量重校正模型：<pre class="hljs"><code><div>gatk VariantRecalibrator -R reference.fa -V input.vcf.gz -resource:known_sites,known=<span class="hljs-literal">false</span>,training=<span class="hljs-literal">true</span>,truth=<span class="hljs-literal">true</span>,prior=15.0 dbsnp.vcf.gz -an QD -an MQ -an MQRankSum -an ReadPosRankSum -an FS -an SOR -O recal.table
</div></code></pre>
</li>
<li>使用<code>ApplyVQSR</code>工具应用变异质量重校正模型：<pre class="hljs"><code><div>gatk ApplyVQSR -R reference.fa -V input.vcf.gz -ts_filter_level 99.0 -tranche 1000.0to99.0 -recalFile recal.table -O output.vqsr.vcf.gz
</div></code></pre>
</li>
</ol>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>使用GATK进行数据预处理（标记重复序列、碱基质量重校正）。</li>
<li>使用GATK进行变异检测（HaplotypeCaller）。</li>
<li>使用GATK进行变异过滤（硬过滤或VQSR）。</li>
</ol>
</li>
</ul>
<h3 id="2-freebayes%E5%8F%98%E5%BC%82%E6%A3%80%E6%B5%8B">2. FreeBayes变异检测</h3>
<ul>
<li>
<p><strong>FreeBayes安装与配置</strong></p>
<ul>
<li>使用conda安装FreeBayes：<pre class="hljs"><code><div>conda install -c bioconda freebayes
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>基本命令格式</strong></p>
<ul>
<li><code>freebayes -f &lt;reference.fa&gt; &lt;input.bam&gt; &gt; &lt;output.vcf&gt;</code></li>
<li>例如：<pre class="hljs"><code><div>freebayes -f Homo_sapiens.GRCh38_p13.dna.primary_assembly.fa sample1.bam &gt; sample1.vcf
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>关键参数设置</strong></p>
<ul>
<li><code>-f</code>: 参考基因组文件</li>
<li><code>-r</code>: 指定变异检测区域</li>
<li><code>--min-mapping-quality</code>: 最小比对质量分数</li>
<li><code>--min-base-quality</code>: 最小碱基质量分数</li>
</ul>
</li>
<li>
<p><strong>结果过滤策略</strong></p>
<ul>
<li>根据FreeBayes输出的质量分数和深度信息，对变异进行过滤。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>使用FreeBayes进行变异检测。</li>
<li>对FreeBayes输出的VCF文件进行过滤。</li>
</ol>
</li>
</ul>
<h3 id="3-%E7%BB%93%E6%9E%84%E5%8F%98%E5%BC%82%E6%A3%80%E6%B5%8B%E5%AE%9E%E8%B7%B5">3. 结构变异检测实践</h3>
<ul>
<li>
<p><strong>Delly/Lumpy工具使用</strong></p>
<ul>
<li>Delly和Lumpy是常用的结构变异检测工具。</li>
<li>Delly：<pre class="hljs"><code><div>delly call -o output.bcf -g reference.fa input.bam
</div></code></pre>
</li>
<li>Lumpy：需要结合其他工具（如samblaster）使用。</li>
</ul>
</li>
<li>
<p><strong>命令参数设置</strong></p>
<ul>
<li>根据工具的说明文档，设置合适的参数。</li>
</ul>
</li>
<li>
<p><strong>结果过滤与解读</strong></p>
<ul>
<li>根据工具输出的质量分数和支持reads数，对结构变异进行过滤。</li>
</ul>
</li>
<li>
<p><strong>简单演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>使用Delly或Lumpy进行结构变异检测。</li>
<li>对检测结果进行过滤和解读。</li>
</ol>
</li>
</ul>
<h3 id="4-%E5%8F%98%E5%BC%82%E6%A3%80%E6%B5%8B%E7%BB%93%E6%9E%9C%E6%AF%94%E8%BE%83%E4%B8%8E%E5%90%88%E5%B9%B6">4. 变异检测结果比较与合并</h3>
<ul>
<li>
<p><strong>bcftools比较功能</strong></p>
<ul>
<li>使用bcftools的<code>isec</code>命令比较多个VCF文件中的变异：<pre class="hljs"><code><div>bcftools isec -p prefix input1.vcf.gz input2.vcf.gz
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>VCF文件合并方法</strong></p>
<ul>
<li>使用bcftools的<code>merge</code>命令合并多个VCF文件：<pre class="hljs"><code><div>bcftools merge input1.vcf.gz input2.vcf.gz -o output.vcf.gz
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>变异集交集/并集操作</strong></p>
<ul>
<li>使用bcftools的<code>view</code>命令和<code>grep</code>命令，对变异集进行交集和并集操作。</li>
</ul>
</li>
<li>
<p><strong>一致性评估</strong></p>
<ul>
<li>比较不同变异检测工具的结果，评估变异检测的一致性。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>变异检测是基因组测序数据分析的核心内容。掌握GATK、FreeBayes等工具的使用方法，并对检测结果进行比较和评估，可以获得准确可靠的变异信息。</p>
<h2 id="annovarsnpeff%E8%BF%9B%E8%A1%8C%E5%8F%98%E5%BC%82%E6%B3%A8%E9%87%8A%E4%B8%8E%E8%A7%A3%E8%AF%BB">ANNOVAR/SnpEff进行变异注释与解读</h2>
<h3 id="1-annovar%E5%8F%98%E5%BC%82%E6%B3%A8%E9%87%8A">1. ANNOVAR变异注释</h3>
<ul>
<li>
<p><strong>ANNOVAR安装与配置</strong></p>
<ul>
<li>ANNOVAR (Annotate Variation) 是一种常用的变异注释工具，使用Perl语言编写。</li>
<li>从ANNOVAR官网下载ANNOVAR安装包，并解压。</li>
<li>下载ANNOVAR所需的注释数据库。</li>
<li>配置ANNOVAR的环境变量。</li>
</ul>
</li>
<li>
<p><strong>注释数据库下载</strong></p>
<ul>
<li>使用ANNOVAR自带的<code>annotate_variation.pl</code>脚本下载注释数据库：<pre class="hljs"><code><div>perl annotate_variation.pl -buildver hg38 -downdb refGene hg38
perl annotate_variation.pl -buildver hg38 -downdb exac03 hg38
perl annotate_variation.pl -buildver hg38 -downdb gnomad211_genome hg38
</div></code></pre>
</li>
<li>常用的注释数据库包括：
<ul>
<li>refGene：基因信息</li>
<li>exac03：ExAC数据库，人群频率信息</li>
<li>gnomad211_genome：gnomAD数据库，人群频率信息</li>
<li>dbnsfp41a：功能预测信息</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>基本注释命令</strong></p>
<ul>
<li>使用<code>annotate_variation.pl</code>脚本进行变异注释：<pre class="hljs"><code><div>perl annotate_variation.pl -geneanno -dbtype refGene input.vcf.gz hg38 humandb/
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>注释结果解读</strong></p>
<ul>
<li>ANNOVAR输出多个文件，常用的包括：
<ul>
<li><code>input.vcf.gz.exonic_variant_function</code>: 包含外显子区域变异的功能注释信息。</li>
<li><code>input.vcf.gz.variant_function</code>: 包含所有变异的位置注释信息。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载VCF文件。</li>
<li>下载ANNOVAR所需的注释数据库。</li>
<li>使用ANNOVAR进行变异注释。</li>
<li>解读ANNOVAR的输出结果。</li>
</ol>
</li>
</ul>
<h3 id="2-snpeff%E5%8F%98%E5%BC%82%E6%B3%A8%E9%87%8A">2. SnpEff变异注释</h3>
<ul>
<li>
<p><strong>SnpEff安装与配置</strong></p>
<ul>
<li>SnpEff (SNP Effect Predictor) 是一种快速的变异注释工具，使用Java语言编写。</li>
<li>从SnpEff官网下载SnpEff安装包，并解压。</li>
<li>下载SnpEff所需的数据库。</li>
<li>配置SnpEff的环境变量。</li>
</ul>
</li>
<li>
<p><strong>数据库构建与选择</strong></p>
<ul>
<li>下载SnpEff数据库：<pre class="hljs"><code><div>java -jar snpeff.jar download hg38
</div></code></pre>
</li>
<li>常用的数据库包括：
<ul>
<li>hg38：人类基因组hg38版本</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>注释命令详解</strong></p>
<ul>
<li>使用<code>java -jar snpeff.jar</code>命令进行变异注释：<pre class="hljs"><code><div>java -jar snpeff.jar hg38 input.vcf.gz &gt; output.ann.vcf
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>结果文件解读</strong></p>
<ul>
<li>SnpEff输出一个VCF文件，其中包含变异的注释信息。</li>
<li>注释信息位于VCF文件的INFO字段中，以<code>ANN=</code>开头。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载VCF文件。</li>
<li>下载SnpEff所需的数据库。</li>
<li>使用SnpEff进行变异注释。</li>
<li>解读SnpEff的输出结果。</li>
</ol>
</li>
</ul>
<h3 id="3-%E5%8F%98%E5%BC%82%E8%BF%87%E6%BB%A4%E4%B8%8E%E4%BC%98%E5%85%88%E7%BA%A7%E6%8E%92%E5%BA%8F">3. 变异过滤与优先级排序</h3>
<ul>
<li>
<p><strong>基于注释信息的过滤策略</strong></p>
<ul>
<li>根据变异的注释信息，对变异进行过滤。</li>
<li>常用的过滤策略包括：
<ul>
<li>过滤掉位于基因间区的变异</li>
<li>过滤掉同义突变</li>
<li>过滤掉群体频率过高的变异</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>候选变异筛选标准</strong></p>
<ul>
<li>根据研究目标，制定候选变异的筛选标准。</li>
<li>常用的筛选标准包括：
<ul>
<li>变异类型</li>
<li>功能影响</li>
<li>群体频率</li>
<li>保守性</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>自动化过滤脚本示例</strong></p>
<ul>
<li>可以使用Shell脚本、Python脚本等自动化过滤变异。</li>
<li>例如，使用bcftools和awk命令过滤掉群体频率大于0.01的变异：<pre class="hljs"><code><div>bcftools view -i <span class="hljs-string">'INFO/AF&lt;0.01'</span> input.vcf.gz -o output.filtered.vcf.gz
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载VCF文件。</li>
<li>使用ANNOVAR或SnpEff对变异进行注释。</li>
<li>根据注释信息，使用自动化脚本过滤变异。</li>
<li>根据筛选标准，筛选出候选变异。</li>
</ol>
</li>
</ul>
<h3 id="4-%E5%8F%98%E5%BC%82%E7%BB%93%E6%9E%9C%E7%BB%9F%E8%AE%A1%E4%B8%8E%E5%8F%AF%E8%A7%86%E5%8C%96">4. 变异结果统计与可视化</h3>
<ul>
<li>
<p><strong>变异类型分布统计</strong></p>
<ul>
<li>统计不同类型变异（SNP、Indel、SV）的数量和比例。</li>
</ul>
</li>
<li>
<p><strong>变异功能影响统计</strong></p>
<ul>
<li>统计不同功能影响（同义突变、错义突变、无义突变等）的变异数量和比例。</li>
</ul>
</li>
<li>
<p><strong>简单可视化方法</strong></p>
<ul>
<li>使用R语言或Python语言绘制变异类型分布图和变异功能影响图。</li>
</ul>
</li>
<li>
<p><strong>结果导出与分享</strong></p>
<ul>
<li>将统计结果和可视化图表导出为文件，方便分享和交流。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>变异注释和解读是基因组测序数据分析的重要环节。掌握ANNOVAR和SnpEff的使用方法，并根据注释信息对变异进行过滤和优先级排序，可以帮助我们找到与研究目标相关的关键变异。</p>
<h2 id="igv%E7%AD%89%E5%8F%AF%E8%A7%86%E5%8C%96%E5%B7%A5%E5%85%B7%E4%BD%BF%E7%94%A8">IGV等可视化工具使用</h2>
<h3 id="1-igv%E5%B7%A5%E5%85%B7%E4%BB%8B%E7%BB%8D">1. IGV工具介绍</h3>
<ul>
<li>
<p><strong>软件安装与配置</strong></p>
<ul>
<li>IGV (Integrative Genomics Viewer) 是一种常用的基因组可视化工具。</li>
<li>从IGV官网下载IGV安装包，并解压。</li>
<li>IGV需要Java环境，请确保已安装Java。</li>
</ul>
</li>
<li>
<p><strong>界面布局与功能区</strong></p>
<ul>
<li>IGV界面主要包括以下几个区域：
<ul>
<li>参考基因组导航区：用于选择和浏览参考基因组。</li>
<li>数据轨道区：用于显示各种数据，如比对结果、变异信息、基因注释等。</li>
<li>工具栏：包含常用的操作按钮，如放大、缩小、搜索等。</li>
<li>状态栏：显示当前鼠标位置的基因组坐标和数据信息。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>数据加载方法</strong></p>
<ul>
<li>可以通过以下方法加载数据到IGV：
<ul>
<li>File -&gt; Load Data from File：加载本地文件（如BAM、VCF、GTF等）。</li>
<li>File -&gt; Load Data from URL：加载远程文件。</li>
<li>File -&gt; Load Data from Server：从IGV数据服务器加载数据。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>基本操作技巧</strong></p>
<ul>
<li>使用鼠标滚轮放大和缩小。</li>
<li>使用鼠标拖动浏览基因组。</li>
<li>使用搜索框搜索基因或区域。</li>
<li>使用右键菜单进行各种操作，如调整轨道高度、显示/隐藏轨道等。</li>
</ul>
</li>
</ul>
<h3 id="2-igv%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96">2. IGV数据可视化</h3>
<ul>
<li>
<p><strong>参考基因组导航</strong></p>
<ul>
<li>在参考基因组导航区选择要浏览的染色体和区域。</li>
<li>可以使用搜索框快速定位到指定的基因或区域。</li>
<li>支持输入基因名称、染色体坐标或特定变异位点（如chr7:55,191,822）。</li>
<li>使用放大/缩小按钮或鼠标滚轮调整查看的基因组区域范围。</li>
</ul>
<p><img src="../专题三_基因组测序(DNA-seq)数据分析/diagrams/igv_visualization_simple.svg" alt="IGV简化界面示意图"></p>
</li>
<li>
<p><strong>比对数据可视化</strong></p>
<ul>
<li>加载BAM文件，可以查看比对结果。</li>
<li>IGV会显示每个reads的比对位置、比对质量、CIGAR信息等。</li>
<li>可以使用不同的颜色表示不同的比对状态：
<ul>
<li>灰色：完全匹配的reads</li>
<li>彩色：包含错配、插入或删除的reads</li>
<li>紫色：配对reads之间的距离异常</li>
<li>红色/蓝色：表示正向链/反向链比对</li>
</ul>
</li>
<li>右键点击比对轨道可以调整显示设置，如按插入大小着色、按配对方向着色等。</li>
<li>双击任意read可以查看其详细信息，包括序列、质量分数和比对状态。</li>
</ul>
</li>
<li>
<p><strong>变异数据可视化</strong></p>
<ul>
<li>加载VCF文件，可以查看变异信息。</li>
<li>IGV会显示每个变异的位置、类型、基因型、注释信息等。</li>
<li>变异类型颜色编码：
<ul>
<li>紫色：SNP（单核苷酸多态性）</li>
<li>红色：缺失（deletion）</li>
<li>蓝色：插入（insertion）</li>
<li>绿色：复杂变异（complex）</li>
</ul>
</li>
<li>点击变异位点可以查看详细的变异信息，包括变异质量、深度和基因型等。</li>
</ul>
<p><img src="../专题三_基因组测序(DNA-seq)数据分析/diagrams/igv_variant_annotation.svg" alt="IGV变异注释与可视化"></p>
</li>
<li>
<p><strong>注释轨道显示</strong></p>
<ul>
<li>加载GTF/GFF文件，可以查看基因注释信息。</li>
<li>IGV会显示基因的位置、结构、功能等信息。</li>
<li>基因结构表示：
<ul>
<li>矩形：外显子</li>
<li>线条：内含子</li>
<li>箭头：转录方向</li>
</ul>
</li>
<li>可以将注释轨道与比对数据和变异数据结合起来，进行综合分析。</li>
<li>右键点击注释轨道可以调整显示设置，如颜色、高度和标签显示等。</li>
</ul>
</li>
<li>
<p><strong>多样本比较视图</strong></p>
<ul>
<li>同时加载多个样本的BAM文件和VCF文件，可以进行多样本比较分析。</li>
<li>IGV可以显示不同样本在同一区域的比对结果和变异信息，方便进行比较和分析。</li>
<li>使用&quot;File -&gt; New Panel&quot;创建多个面板，可以同时查看不同基因组区域。</li>
<li>使用&quot;View -&gt; Preferences&quot;调整样本显示顺序和分组方式。</li>
<li>特别适用于家系分析、肿瘤-正常样本比较和时间序列样本分析。</li>
</ul>
</li>
</ul>
<h3 id="3-igv%E9%AB%98%E7%BA%A7%E5%8A%9F%E8%83%BD">3. IGV高级功能</h3>
<ul>
<li>
<p><strong>截图与导出</strong></p>
<ul>
<li>使用&quot;File -&gt; Save Image&quot;将当前视图保存为PNG、SVG或PDF格式。</li>
<li>使用快捷键Ctrl+Alt+P（Windows/Linux）或Cmd+Alt+P（Mac）快速截图。</li>
<li>自定义截图设置：
<ul>
<li>调整分辨率和比例</li>
<li>选择是否包含标尺和轨道名称</li>
<li>设置透明背景选项</li>
</ul>
</li>
<li>使用&quot;File -&gt; Export&quot;将数据导出为文本文件，支持导出区域内的变异、覆盖度和注释信息。</li>
<li>批量截图示例：<pre class="hljs"><code><div><span class="hljs-comment"># 使用IGV批处理模式截取多个区域的图像</span>
igv -b batch_commands.txt

<span class="hljs-comment"># batch_commands.txt内容示例</span>
new
load sample.bam
snapshotDirectory ./screenshots
goto chr7:55,191,822
snapshot EGFR_mutation.png
goto chr17:7,674,250
snapshot TP53_mutation.png
<span class="hljs-built_in">exit</span>
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>自定义轨道</strong></p>
<ul>
<li>使用&quot;File -&gt; Load from File&quot;加载自定义数据文件（BED、WIG、BigWig等格式）。</li>
<li>创建自定义注释轨道：
<ul>
<li>基因表达量热图</li>
<li>甲基化水平</li>
<li>保守性得分</li>
<li>调控元件信息</li>
</ul>
</li>
<li>使用&quot;Tracks -&gt; Fit Data to Window&quot;自动调整轨道高度。</li>
<li>右键点击轨道可以调整颜色、高度和渲染方式。</li>
<li>使用&quot;Tracks -&gt; Group By&quot;功能按样本类型或实验条件分组显示轨道。</li>
</ul>
</li>
<li>
<p><strong>批量处理与自动化</strong></p>
<ul>
<li>使用IGV的命令行模式和批处理脚本自动化分析流程：<pre class="hljs"><code><div><span class="hljs-comment"># 启动IGV并执行批处理脚本</span>
igv -b commands.bat
</div></code></pre>
</li>
<li>批处理命令示例：
<ul>
<li><code>load</code> - 加载数据文件</li>
<li><code>goto</code> - 导航到特定位置</li>
<li><code>snapshot</code> - 保存截图</li>
<li><code>maxPanelHeight</code> - 设置面板高度</li>
<li><code>setSleepInterval</code> - 设置命令间隔时间</li>
</ul>
</li>
<li>结合Shell脚本批量处理变异位点：<pre class="hljs"><code><div><span class="hljs-comment"># 为VCF文件中的每个变异生成IGV截图</span>
cat variants.vcf | grep -v <span class="hljs-string">"^#"</span> | cut -f1,2 | <span class="hljs-keyword">while</span> <span class="hljs-built_in">read</span> chr pos; <span class="hljs-keyword">do</span>
  <span class="hljs-built_in">echo</span> <span class="hljs-string">"goto <span class="hljs-variable">$chr</span>:<span class="hljs-variable">$pos</span>"</span> &gt;&gt; commands.bat
  <span class="hljs-built_in">echo</span> <span class="hljs-string">"snapshot <span class="hljs-variable">$chr</span>-<span class="hljs-variable">$pos</span>.png"</span> &gt;&gt; commands.bat
<span class="hljs-keyword">done</span>
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>会话保存与恢复</strong></p>
<ul>
<li>使用&quot;File -&gt; Save Session&quot;将当前的IGV会话保存为.xml或.session文件。</li>
<li>会话文件包含以下信息：
<ul>
<li>加载的数据文件及其路径</li>
<li>当前查看的基因组区域</li>
<li>轨道显示设置（颜色、高度、渲染方式等）</li>
<li>面板布局和分组信息</li>
</ul>
</li>
<li>使用&quot;File -&gt; Open Session&quot;恢复之前保存的会话。</li>
<li>会话文件可以与团队成员共享，确保所有人查看相同的数据视图。</li>
<li>使用相对路径保存会话文件，便于在不同计算机之间迁移。</li>
</ul>
</li>
</ul>
<h3 id="4-%E5%85%B6%E4%BB%96%E5%8F%AF%E8%A7%86%E5%8C%96%E5%B7%A5%E5%85%B7%E7%AE%80%E4%BB%8B">4. 其他可视化工具简介</h3>
<ul>
<li>
<p><strong>UCSC Genome Browser</strong></p>
<ul>
<li>UCSC Genome Browser 是一种功能强大的在线基因组浏览器。</li>
<li>特点与优势：
<ul>
<li>提供丰富的预置注释轨道（基因、变异、保守性等）</li>
<li>支持自定义轨道上传和共享</li>
<li>内置多种分析工具（如Liftover、Table Browser）</li>
<li>适合生成发表级别的基因组可视化图像</li>
</ul>
</li>
<li>使用场景：
<ul>
<li>需要访问综合注释数据库时</li>
<li>共享基因组可视化结果给其他研究者</li>
<li>进行跨物种比较基因组学分析</li>
</ul>
</li>
<li>访问地址：https://genome.ucsc.edu/</li>
</ul>
</li>
<li>
<p><strong>Tablet</strong></p>
<ul>
<li>Tablet 是一种专门用于可视化比对结果的轻量级工具。</li>
<li>特点与优势：
<ul>
<li>高效处理大规模比对数据</li>
<li>内存占用低，适合普通计算机使用</li>
<li>提供多种比对视图模式（碱基、覆盖度、质量等）</li>
<li>支持从头组装结果的可视化</li>
</ul>
</li>
<li>使用场景：
<ul>
<li>检查比对质量和覆盖度</li>
<li>验证变异位点的可靠性</li>
<li>分析从头组装结果</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>JBrowse</strong></p>
<ul>
<li>JBrowse 是一种基于Web的现代基因组浏览器。</li>
<li>特点与优势：
<ul>
<li>快速响应的用户界面，支持大规模数据集</li>
<li>模块化设计，支持插件扩展</li>
<li>可以部署在本地服务器上，便于团队协作</li>
<li>支持多种数据格式和自定义轨道</li>
</ul>
</li>
<li>使用场景：
<ul>
<li>建立实验室或项目专用的基因组浏览平台</li>
<li>需要共享大型基因组数据集</li>
<li>开发自定义基因组可视化功能</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Genome Browser in the Cloud (GBiC)</strong></p>
<ul>
<li>基于云计算的基因组浏览器，适合处理大规模数据。</li>
<li>特点与优势：
<ul>
<li>无需下载大型数据文件</li>
<li>利用云计算资源进行快速处理</li>
<li>支持团队协作和数据共享</li>
</ul>
</li>
<li>使用场景：
<ul>
<li>处理超大型基因组数据集</li>
<li>多人协作的基因组分析项目</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>工具选择建议</strong></p>
<table>
<thead>
<tr>
<th>工具</th>
<th>适用场景</th>
<th>优势</th>
<th>局限性</th>
</tr>
</thead>
<tbody>
<tr>
<td>IGV</td>
<td>本地详细分析，变异验证</td>
<td>功能全面，操作简便，支持多种数据格式</td>
<td>大数据集可能性能受限</td>
</tr>
<tr>
<td>UCSC Genome Browser</td>
<td>整合公共数据，共享结果</td>
<td>注释数据丰富，可视化选项多样</td>
<td>需要网络连接，上传大文件不便</td>
</tr>
<tr>
<td>Tablet</td>
<td>大规模比对数据检查</td>
<td>轻量级，性能优化，专注于比对数据</td>
<td>功能相对单一，注释有限</td>
</tr>
<tr>
<td>JBrowse</td>
<td>团队协作，Web部署</td>
<td>响应快速，可扩展性强</td>
<td>需要一定的配置和维护</td>
</tr>
<tr>
<td>GBiC</td>
<td>超大数据集分析</td>
<td>利用云资源，无需下载数据</td>
<td>可能产生云服务费用</td>
</tr>
</tbody>
</table>
</li>
<li>
<p><strong>选择工具的考虑因素</strong></p>
<ul>
<li>数据规模：大型数据集考虑使用JBrowse或GBiC</li>
<li>分析目的：变异验证选IGV，注释浏览选UCSC</li>
<li>计算资源：资源有限选Tablet，资源充足选IGV</li>
<li>协作需求：团队协作选JBrowse或UCSC</li>
<li>数据隐私：敏感数据选择本地工具如IGV</li>
</ul>
</li>
</ul>
<p><img src="../专题三_基因组测序(DNA-seq)数据分析/diagrams/variant_detection_workflow_detailed.svg" alt="变异检测工作流程详细图"></p>
<p><strong>总结</strong></p>
<p>基因组可视化是基因组测序数据分析的重要手段。掌握IGV等可视化工具的使用方法，可以帮助我们更好地理解和分析基因组数据。选择合适的可视化工具对于提高分析效率和准确性至关重要，应根据具体的研究需求和数据特点进行选择。</p>

</body>
</html>
