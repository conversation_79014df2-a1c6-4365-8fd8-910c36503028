<!DOCTYPE html>
<html>
<head>
<title>Lecture8_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E5%85%AB%E9%AB%98%E9%80%9A%E9%87%8F%E6%B5%8B%E5%BA%8F%E6%8A%80%E6%9C%AF%E5%9C%A8%E6%A4%8D%E7%89%A9%E4%BF%9D%E6%8A%A4%E4%B8%AD%E7%9A%84%E5%BA%94%E7%94%A8---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题八：高通量测序技术在植物保护中的应用 - 实践操作课</h1>
<h2 id="%E7%9B%AE%E5%BD%95">目录</h2>
<ol>
<li><a href="#%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE%E4%B8%8E%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">环境配置与数据准备</a></li>
<li><a href="#%E6%A4%8D%E7%89%A9%E7%97%85%E5%8E%9F%E4%BD%93%E5%9F%BA%E5%9B%A0%E7%BB%84%E7%BB%84%E8%A3%85%E4%B8%8E%E6%B3%A8%E9%87%8A%E6%B5%81%E7%A8%8B">植物病原体基因组组装与注释流程</a></li>
<li><a href="#%E7%97%85%E5%8E%9F%E5%BE%AE%E7%94%9F%E7%89%A9%E5%BF%AB%E9%80%9F%E9%89%B4%E5%AE%9A%E4%B8%8E%E7%B3%BB%E7%BB%9F%E5%8F%91%E8%82%B2%E5%88%86%E6%9E%90">病原微生物快速鉴定与系统发育分析</a></li>
<li><a href="#%E6%A4%8D%E7%89%A9-%E7%97%85%E5%8E%9F%E4%BD%93%E4%BA%92%E4%BD%9C%E8%BD%AC%E5%BD%95%E7%BB%84%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90">植物-病原体互作转录组数据分析</a></li>
<li><a href="#%E6%8A%97%E7%97%85%E6%8A%97%E6%80%A7%E7%9B%B8%E5%85%B3%E5%9F%BA%E5%9B%A0%E7%9A%84%E6%8C%96%E6%8E%98%E4%B8%8E%E5%8A%9F%E8%83%BD%E9%A2%84%E6%B5%8B">抗病/抗性相关基因的挖掘与功能预测</a></li>
<li><a href="#%E7%94%B0%E9%97%B4%E5%BE%AE%E7%94%9F%E7%89%A9%E5%A4%9A%E6%A0%B7%E6%80%A7%E5%88%86%E6%9E%90%E6%B5%81%E7%A8%8B">田间微生物多样性分析流程</a></li>
<li><a href="#%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96%E4%B8%8E%E7%BB%93%E6%9E%9C%E8%A7%A3%E8%AF%BB">数据可视化与结果解读</a></li>
<li><a href="#%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E4%B8%8E%E6%95%85%E9%9A%9C%E6%8E%92%E9%99%A4">常见问题与故障排除</a></li>
</ol>
<h2 id="%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE%E4%B8%8E%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">环境配置与数据准备</h2>
<h3 id="1-%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E5%AE%89%E8%A3%85">1. 软件环境安装</h3>
<h4 id="%E5%9F%BA%E7%A1%80%E5%B7%A5%E5%85%B7%E5%AE%89%E8%A3%85">基础工具安装</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 更新系统包管理器</span>
sudo apt update &amp;&amp; sudo apt upgrade -y

<span class="hljs-comment"># 安装基础依赖</span>
sudo apt install -y build-essential wget curl git python3 python3-pip
sudo apt install -y openjdk-8-jdk zlib1g-dev libbz2-dev liblzma-dev

<span class="hljs-comment"># 安装conda包管理器</span>
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh
<span class="hljs-built_in">source</span> ~/.bashrc
</div></code></pre>
<h4 id="%E5%88%9B%E5%BB%BA%E4%B8%93%E7%94%A8%E7%8E%AF%E5%A2%83">创建专用环境</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建植物保护分析环境</span>
conda create -n plant_protection python=3.8 -y
conda activate plant_protection

<span class="hljs-comment"># 安装质控和预处理工具</span>
conda install -c bioconda fastqc trimmomatic cutadapt -y
conda install -c bioconda bowtie2 samtools -y

<span class="hljs-comment"># 安装组装工具</span>
conda install -c bioconda spades quast busco -y

<span class="hljs-comment"># 安装注释工具</span>
conda install -c bioconda prokka interproscan blast -y

<span class="hljs-comment"># 安装系统发育分析工具</span>
conda install -c bioconda muscle mafft raxml iqtree -y

<span class="hljs-comment"># 安装转录组分析工具</span>
conda install -c bioconda hisat2 star htseq subread -y
conda install -c bioconda salmon kallisto -y

<span class="hljs-comment"># 安装R和相关包</span>
conda install -c conda-forge r-base r-essentials -y
</div></code></pre>
<h4 id="r%E5%8C%85%E5%AE%89%E8%A3%85">R包安装</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 启动R并安装必要的包</span>
R

<span class="hljs-comment"># 安装Bioconductor</span>
<span class="hljs-keyword">if</span> (!requireNamespace(<span class="hljs-string">"BiocManager"</span>, quietly = <span class="hljs-literal">TRUE</span>))
    install.packages(<span class="hljs-string">"BiocManager"</span>)

BiocManager::install(c(<span class="hljs-string">"DESeq2"</span>, <span class="hljs-string">"edgeR"</span>, <span class="hljs-string">"limma"</span>, <span class="hljs-string">"clusterProfiler"</span>, 
                       <span class="hljs-string">"pathview"</span>, <span class="hljs-string">"KEGGREST"</span>, <span class="hljs-string">"org.At.tair.db"</span>))

<span class="hljs-comment"># 安装其他必要包</span>
install.packages(c(<span class="hljs-string">"ggplot2"</span>, <span class="hljs-string">"pheatmap"</span>, <span class="hljs-string">"VennDiagram"</span>, <span class="hljs-string">"corrplot"</span>,
                   <span class="hljs-string">"tidyverse"</span>, <span class="hljs-string">"ggrepel"</span>, <span class="hljs-string">"ggtree"</span>, <span class="hljs-string">"ape"</span>))

quit()
</div></code></pre>
<h3 id="2-%E6%95%B0%E6%8D%AE%E8%8E%B7%E5%8F%96%E4%B8%8E%E7%BB%84%E7%BB%87">2. 数据获取与组织</h3>
<h4 id="%E5%88%9B%E5%BB%BA%E9%A1%B9%E7%9B%AE%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84">创建项目目录结构</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建项目根目录</span>
mkdir -p ~/plant_protection_analysis
<span class="hljs-built_in">cd</span> ~/plant_protection_analysis

<span class="hljs-comment"># 创建子目录</span>
mkdir -p {raw_data,qc_results,assembly,annotation,phylogeny,transcriptome,diversity,results,scripts,references}

<span class="hljs-comment"># 创建详细的子目录结构</span>
mkdir -p raw_data/{genome,transcriptome,amplicon}
mkdir -p qc_results/{fastqc,trimmed}
mkdir -p assembly/{spades_output,quast_results,busco_results}
mkdir -p annotation/{prokka,interproscan,blast}
mkdir -p phylogeny/{alignments,trees,models}
mkdir -p transcriptome/{mapping,counts,deseq2}
mkdir -p diversity/{qiime2,networks}
mkdir -p references/{genomes,databases,annotations}
</div></code></pre>
<h4 id="%E7%A4%BA%E4%BE%8B%E6%95%B0%E6%8D%AE%E4%B8%8B%E8%BD%BD%E8%84%9A%E6%9C%AC">示例数据下载脚本</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># download_sample_data.sh</span>

<span class="hljs-comment"># 设置工作目录</span>
<span class="hljs-built_in">cd</span> ~/plant_protection_analysis/raw_data

<span class="hljs-comment"># 下载示例细菌基因组数据</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"下载病原细菌测序数据..."</span>
wget -O genome/pathogen_R1.fastq.gz <span class="hljs-string">"https://example.com/pathogen_R1.fastq.gz"</span>
wget -O genome/pathogen_R2.fastq.gz <span class="hljs-string">"https://example.com/pathogen_R2.fastq.gz"</span>

<span class="hljs-comment"># 下载宿主基因组参考序列</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"下载植物基因组参考序列..."</span>
wget -O ../references/genomes/plant_genome.fasta <span class="hljs-string">"https://example.com/plant_genome.fasta"</span>

<span class="hljs-comment"># 下载转录组数据</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"下载转录组数据..."</span>
<span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> {1..6}; <span class="hljs-keyword">do</span>
    wget -O transcriptome/sample_<span class="hljs-variable">${i}</span>_R1.fastq.gz <span class="hljs-string">"https://example.com/sample_<span class="hljs-variable">${i}</span>_R1.fastq.gz"</span>
    wget -O transcriptome/sample_<span class="hljs-variable">${i}</span>_R2.fastq.gz <span class="hljs-string">"https://example.com/sample_<span class="hljs-variable">${i}</span>_R2.fastq.gz"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># 下载扩增子数据</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"下载微生物多样性扩增子数据..."</span>
<span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> {1..12}; <span class="hljs-keyword">do</span>
    wget -O amplicon/field_<span class="hljs-variable">${i}</span>_R1.fastq.gz <span class="hljs-string">"https://example.com/field_<span class="hljs-variable">${i}</span>_R1.fastq.gz"</span>
    wget -O amplicon/field_<span class="hljs-variable">${i}</span>_R2.fastq.gz <span class="hljs-string">"https://example.com/field_<span class="hljs-variable">${i}</span>_R2.fastq.gz"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"数据下载完成！"</span>
</div></code></pre>
<h3 id="3-%E5%8F%82%E8%80%83%E6%95%B0%E6%8D%AE%E5%BA%93%E9%85%8D%E7%BD%AE">3. 参考数据库配置</h3>
<h4 id="blast%E6%95%B0%E6%8D%AE%E5%BA%93%E9%85%8D%E7%BD%AE">BLAST数据库配置</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建BLAST数据库目录</span>
mkdir -p ~/plant_protection_analysis/references/databases/blast

<span class="hljs-comment"># 下载并配置NCBI数据库</span>
<span class="hljs-built_in">cd</span> ~/plant_protection_analysis/references/databases/blast
wget ftp://ftp.ncbi.nlm.nih.gov/blast/db/nt.*.tar.gz
<span class="hljs-keyword">for</span> file <span class="hljs-keyword">in</span> nt.*.tar.gz; <span class="hljs-keyword">do</span> tar -xzf <span class="hljs-variable">$file</span>; <span class="hljs-keyword">done</span>

<span class="hljs-comment"># 下载蛋白质数据库</span>
wget ftp://ftp.ncbi.nlm.nih.gov/blast/db/nr.*.tar.gz
<span class="hljs-keyword">for</span> file <span class="hljs-keyword">in</span> nr.*.tar.gz; <span class="hljs-keyword">do</span> tar -xzf <span class="hljs-variable">$file</span>; <span class="hljs-keyword">done</span>
</div></code></pre>
<h4 id="qiime2%E6%95%B0%E6%8D%AE%E5%BA%93%E9%85%8D%E7%BD%AE">QIIME2数据库配置</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 激活QIIME2环境</span>
conda activate qiime2-2023.5

<span class="hljs-comment"># 下载Silva数据库</span>
mkdir -p ~/plant_protection_analysis/references/databases/qiime2
<span class="hljs-built_in">cd</span> ~/plant_protection_analysis/references/databases/qiime2

wget https://data.qiime2.org/2023.5/common/silva-138-99-515-806-nb-classifier.qza
wget https://data.qiime2.org/2023.5/common/silva-138-99-seqs.qza
wget https://data.qiime2.org/2023.5/common/silva-138-99-tax.qza
</div></code></pre>
<h2 id="%E6%A4%8D%E7%89%A9%E7%97%85%E5%8E%9F%E4%BD%93%E5%9F%BA%E5%9B%A0%E7%BB%84%E7%BB%84%E8%A3%85%E4%B8%8E%E6%B3%A8%E9%87%8A%E6%B5%81%E7%A8%8B">植物病原体基因组组装与注释流程</h2>
<h3 id="1-%E7%97%85%E5%8E%9F%E4%BD%93%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%95%B0%E6%8D%AE%E9%A2%84%E5%A4%84%E7%90%86">1. 病原体基因组数据预处理</h3>
<h4 id="%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6%E4%B8%8E%E8%AF%84%E4%BC%B0">质量控制与评估</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># genome_qc.sh - 基因组数据质量控制脚本</span>

<span class="hljs-comment"># 设置变量</span>
RAW_DATA_DIR=<span class="hljs-string">"~/plant_protection_analysis/raw_data/genome"</span>
QC_DIR=<span class="hljs-string">"~/plant_protection_analysis/qc_results"</span>
SCRIPTS_DIR=<span class="hljs-string">"~/plant_protection_analysis/scripts"</span>

<span class="hljs-comment"># 激活环境</span>
conda activate plant_protection

<span class="hljs-comment"># 1. 原始数据质量评估</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 步骤1: 原始数据质量评估 ==="</span>
fastqc <span class="hljs-variable">${RAW_DATA_DIR}</span>/*.fastq.gz -o <span class="hljs-variable">${QC_DIR}</span>/fastqc/ -t 8

<span class="hljs-comment"># 2. 质量过滤和接头去除</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 步骤2: 质量过滤和接头去除 ==="</span>
trimmomatic PE -threads 8 \
    <span class="hljs-variable">${RAW_DATA_DIR}</span>/pathogen_R1.fastq.gz \
    <span class="hljs-variable">${RAW_DATA_DIR}</span>/pathogen_R2.fastq.gz \
    <span class="hljs-variable">${QC_DIR}</span>/trimmed/pathogen_R1_paired.fastq.gz \
    <span class="hljs-variable">${QC_DIR}</span>/trimmed/pathogen_R1_unpaired.fastq.gz \
    <span class="hljs-variable">${QC_DIR}</span>/trimmed/pathogen_R2_paired.fastq.gz \
    <span class="hljs-variable">${QC_DIR}</span>/trimmed/pathogen_R2_unpaired.fastq.gz \
    ILLUMINACLIP:TruSeq3-PE.fa:2:30:10 \
    LEADING:3 TRAILING:3 SLIDINGWINDOW:4:20 MINLEN:36

<span class="hljs-comment"># 3. 过滤后质量评估</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 步骤3: 过滤后质量评估 ==="</span>
fastqc <span class="hljs-variable">${QC_DIR}</span>/trimmed/*_paired.fastq.gz -o <span class="hljs-variable">${QC_DIR}</span>/fastqc/ -t 8

<span class="hljs-comment"># 4. 生成质量报告</span>
multiqc <span class="hljs-variable">${QC_DIR}</span>/fastqc/ -o <span class="hljs-variable">${QC_DIR}</span>/fastqc/
</div></code></pre>
<h4 id="%E5%AE%BF%E4%B8%BBdna%E6%B1%A1%E6%9F%93%E5%8E%BB%E9%99%A4">宿主DNA污染去除</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># remove_host_contamination.sh</span>

<span class="hljs-comment"># 设置变量</span>
PLANT_GENOME=<span class="hljs-string">"~/plant_protection_analysis/references/genomes/plant_genome.fasta"</span>
TRIMMED_DIR=<span class="hljs-string">"~/plant_protection_analysis/qc_results/trimmed"</span>
CLEAN_DIR=<span class="hljs-string">"~/plant_protection_analysis/qc_results/clean"</span>

mkdir -p <span class="hljs-variable">${CLEAN_DIR}</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 宿主DNA污染去除 ==="</span>

<span class="hljs-comment"># 1. 构建植物基因组索引</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"构建植物基因组Bowtie2索引..."</span>
bowtie2-build <span class="hljs-variable">${PLANT_GENOME}</span> <span class="hljs-variable">${PLANT_GENOME%.fasta}</span>_bt2_index

<span class="hljs-comment"># 2. 比对到植物基因组</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"比对reads到植物基因组..."</span>
bowtie2 -x <span class="hljs-variable">${PLANT_GENOME%.fasta}</span>_bt2_index \
    -1 <span class="hljs-variable">${TRIMMED_DIR}</span>/pathogen_R1_paired.fastq.gz \
    -2 <span class="hljs-variable">${TRIMMED_DIR}</span>/pathogen_R2_paired.fastq.gz \
    -S <span class="hljs-variable">${CLEAN_DIR}</span>/aligned_to_plant.sam \
    --threads 8 \
    --very-sensitive

<span class="hljs-comment"># 3. 提取未比对上的reads（病原体reads）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"提取病原体特异性reads..."</span>
samtools view -bS <span class="hljs-variable">${CLEAN_DIR}</span>/aligned_to_plant.sam &gt; <span class="hljs-variable">${CLEAN_DIR}</span>/aligned_to_plant.bam
samtools view -b -f 12 -F 256 <span class="hljs-variable">${CLEAN_DIR}</span>/aligned_to_plant.bam &gt; <span class="hljs-variable">${CLEAN_DIR}</span>/unaligned_pairs.bam

<span class="hljs-comment"># 4. 转换为FASTQ格式</span>
samtools sort -n <span class="hljs-variable">${CLEAN_DIR}</span>/unaligned_pairs.bam -o <span class="hljs-variable">${CLEAN_DIR}</span>/unaligned_pairs_sorted.bam
samtools fastq -1 <span class="hljs-variable">${CLEAN_DIR}</span>/pathogen_clean_R1.fastq.gz \
    -2 <span class="hljs-variable">${CLEAN_DIR}</span>/pathogen_clean_R2.fastq.gz \
    <span class="hljs-variable">${CLEAN_DIR}</span>/unaligned_pairs_sorted.bam

<span class="hljs-comment"># 5. 统计去除效果</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 污染去除统计 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始reads数量："</span>
zcat <span class="hljs-variable">${TRIMMED_DIR}</span>/pathogen_R1_paired.fastq.gz | wc -l | awk <span class="hljs-string">'{print $1/4}'</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"去除宿主后reads数量："</span>
zcat <span class="hljs-variable">${CLEAN_DIR}</span>/pathogen_clean_R1.fastq.gz | wc -l | awk <span class="hljs-string">'{print $1/4}'</span>

<span class="hljs-comment"># 清理临时文件</span>
rm <span class="hljs-variable">${CLEAN_DIR}</span>/*.sam <span class="hljs-variable">${CLEAN_DIR}</span>/*.bam
</div></code></pre>
<h3 id="2-%E5%9F%BA%E5%9B%A0%E7%BB%84%E7%BB%84%E8%A3%85%E4%BC%98%E5%8C%96">2. 基因组组装优化</h3>
<h4 id="spades%E5%8F%82%E6%95%B0%E4%BC%98%E5%8C%96%E7%AD%96%E7%95%A5">SPAdes参数优化策略</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># optimized_assembly.sh</span>

<span class="hljs-comment"># 设置变量</span>
CLEAN_DIR=<span class="hljs-string">"~/plant_protection_analysis/qc_results/clean"</span>
ASSEMBLY_DIR=<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 基因组组装参数优化 ==="</span>

<span class="hljs-comment"># 1. 估算基因组大小和覆盖度</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: 估算基因组统计信息..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import gzip
import subprocess

<span class="hljs-comment"># 计算reads总碱基数</span>
def count_bases(file_path):
    total_bases = 0
    total_reads = 0
    with gzip.open(file_path, <span class="hljs-string">'rt'</span>) as f:
        <span class="hljs-keyword">for</span> i, line <span class="hljs-keyword">in</span> enumerate(f):
            <span class="hljs-keyword">if</span> i % 4 == 1:  <span class="hljs-comment"># 序列行</span>
                total_bases += len(line.strip())
                total_reads += 1
    <span class="hljs-built_in">return</span> total_bases, total_reads

<span class="hljs-comment"># 计算R1和R2的统计信息</span>
r1_bases, r1_reads = count_bases(<span class="hljs-string">'~/plant_protection_analysis/qc_results/clean/pathogen_clean_R1.fastq.gz'</span>)
r2_bases, r2_reads = count_bases(<span class="hljs-string">'~/plant_protection_analysis/qc_results/clean/pathogen_clean_R2.fastq.gz'</span>)

total_bases = r1_bases + r2_bases
total_reads = r1_reads + r2_reads
avg_read_length = total_bases / total_reads

<span class="hljs-built_in">print</span>(f<span class="hljs-string">"总reads数量: {total_reads:,}"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"总碱基数: {total_bases:,}"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"平均read长度: {avg_read_length:.1f}"</span>)

<span class="hljs-comment"># 估算基因组大小（假设细菌基因组2-8Mb）</span>
estimated_genome_size = 5000000  <span class="hljs-comment"># 5Mb作为初始估计</span>
coverage = total_bases / estimated_genome_size
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"估算基因组大小: {estimated_genome_size:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"估算覆盖度: {coverage:.1f}X"</span>)
EOF

<span class="hljs-comment"># 2. 运行SPAdes组装（多种k-mer策略）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: 运行SPAdes组装..."</span>
spades.py \
    --pe1-1 <span class="hljs-variable">${CLEAN_DIR}</span>/pathogen_clean_R1.fastq.gz \
    --pe1-2 <span class="hljs-variable">${CLEAN_DIR}</span>/pathogen_clean_R2.fastq.gz \
    -o <span class="hljs-variable">${ASSEMBLY_DIR}</span>/spades_output \
    --threads 16 \
    --memory 32 \
    -k 21,33,55,77,99,127 \
    --careful \
    --cov-cutoff auto

<span class="hljs-comment"># 3. 后处理和过滤</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤3: 组装结果后处理..."</span>
<span class="hljs-comment"># 过滤短contigs（&lt; 500bp）</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
from Bio import SeqIO
import os

input_file = <span class="hljs-string">"~/plant_protection_analysis/assembly/spades_output/contigs.fasta"</span>
output_file = <span class="hljs-string">"~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"</span>

<span class="hljs-comment"># 展开路径</span>
input_file = os.path.expanduser(input_file)
output_file = os.path.expanduser(output_file)

with open(output_file, <span class="hljs-string">'w'</span>) as output_handle:
    <span class="hljs-keyword">for</span> record <span class="hljs-keyword">in</span> SeqIO.parse(input_file, <span class="hljs-string">"fasta"</span>):
        <span class="hljs-keyword">if</span> len(record.seq) &gt;= 500:
            SeqIO.write(record, output_handle, <span class="hljs-string">"fasta"</span>)

<span class="hljs-built_in">print</span>(<span class="hljs-string">"过滤完成：保留长度≥500bp的contigs"</span>)
EOF
</div></code></pre>
<h4 id="%E7%BB%84%E8%A3%85%E8%B4%A8%E9%87%8F%E8%AF%84%E4%BC%B0%E5%A2%9E%E5%BC%BA">组装质量评估增强</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># enhanced_assembly_assessment.sh</span>

ASSEMBLY_DIR=<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>
SPADES_OUT=<span class="hljs-string">"<span class="hljs-variable">${ASSEMBLY_DIR}</span>/spades_output"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 增强组装质量评估 ==="</span>

<span class="hljs-comment"># 1. QUAST详细评估</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: QUAST质量评估..."</span>
quast.py <span class="hljs-variable">${SPADES_OUT}</span>/contigs_filtered.fasta \
    -o <span class="hljs-variable">${ASSEMBLY_DIR}</span>/quast_results \
    --threads 8 \
    --min-contig 500 \
    --glimmer \
    --rna-finding \
    --conserved-genes-finding

<span class="hljs-comment"># 2. BUSCO完整性评估</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: BUSCO完整性评估..."</span>
<span class="hljs-comment"># 对于细菌使用bacteria_odb10数据库</span>
busco -i <span class="hljs-variable">${SPADES_OUT}</span>/contigs_filtered.fasta \
    -o <span class="hljs-variable">${ASSEMBLY_DIR}</span>/busco_results \
    -l bacteria_odb10 \
    -m genome \
    --cpu 8 \
    --offline

<span class="hljs-comment"># 3. 生成组装统计报告</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import os
from Bio import SeqIO
import matplotlib.pyplot as plt
import numpy as np

<span class="hljs-comment"># 读取组装结果</span>
assembly_file = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"</span>)
contigs = list(SeqIO.parse(assembly_file, <span class="hljs-string">"fasta"</span>))

<span class="hljs-comment"># 计算统计信息</span>
lengths = [len(contig.seq) <span class="hljs-keyword">for</span> contig <span class="hljs-keyword">in</span> contigs]
lengths.sort(reverse=True)

total_length = sum(lengths)
num_contigs = len(lengths)
max_length = max(lengths) <span class="hljs-keyword">if</span> lengths <span class="hljs-keyword">else</span> 0
min_length = min(lengths) <span class="hljs-keyword">if</span> lengths <span class="hljs-keyword">else</span> 0

<span class="hljs-comment"># 计算N50</span>
cumsum = 0
n50 = 0
<span class="hljs-keyword">for</span> length <span class="hljs-keyword">in</span> lengths:
    cumsum += length
    <span class="hljs-keyword">if</span> cumsum &gt;= total_length * 0.5:
        n50 = length
        <span class="hljs-built_in">break</span>

<span class="hljs-comment"># 计算N90</span>
cumsum = 0
n90 = 0
<span class="hljs-keyword">for</span> length <span class="hljs-keyword">in</span> lengths:
    cumsum += length
    <span class="hljs-keyword">if</span> cumsum &gt;= total_length * 0.9:
        n90 = length
        <span class="hljs-built_in">break</span>

<span class="hljs-comment"># 输出统计结果</span>
<span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 组装统计摘要 ==="</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"Contigs数量: {num_contigs}"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"总长度: {total_length:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"最大contig长度: {max_length:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"最小contig长度: {min_length:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"N50: {n50:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"N90: {n90:,} bp"</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"平均长度: {total_length/num_contigs:.0f} bp"</span>)

<span class="hljs-comment"># 生成长度分布图</span>
plt.figure(figsize=(12, 8))

<span class="hljs-comment"># 子图1: Contig长度分布直方图</span>
plt.subplot(2, 2, 1)
plt.hist(lengths, bins=50, alpha=0.7, edgecolor=<span class="hljs-string">'black'</span>)
plt.xlabel(<span class="hljs-string">'Contig长度 (bp)'</span>)
plt.ylabel(<span class="hljs-string">'频数'</span>)
plt.title(<span class="hljs-string">'Contig长度分布直方图'</span>)
plt.yscale(<span class="hljs-string">'log'</span>)

<span class="hljs-comment"># 子图2: 累积长度曲线</span>
plt.subplot(2, 2, 2)
cumulative_lengths = np.cumsum(lengths)
plt.plot(range(1, len(lengths)+1), cumulative_lengths, <span class="hljs-string">'b-'</span>, linewidth=2)
plt.axhline(y=total_length*0.5, color=<span class="hljs-string">'r'</span>, linestyle=<span class="hljs-string">'--'</span>, label=<span class="hljs-string">'50%'</span>)
plt.axhline(y=total_length*0.9, color=<span class="hljs-string">'g'</span>, linestyle=<span class="hljs-string">'--'</span>, label=<span class="hljs-string">'90%'</span>)
plt.xlabel(<span class="hljs-string">'Contig排名'</span>)
plt.ylabel(<span class="hljs-string">'累积长度 (bp)'</span>)
plt.title(<span class="hljs-string">'累积长度曲线'</span>)
plt.legend()

<span class="hljs-comment"># 子图3: 长度vs排名对数图</span>
plt.subplot(2, 2, 3)
plt.plot(range(1, len(lengths)+1), lengths, <span class="hljs-string">'ro-'</span>, markersize=3)
plt.axhline(y=n50, color=<span class="hljs-string">'r'</span>, linestyle=<span class="hljs-string">'--'</span>, label=f<span class="hljs-string">'N50={n50}bp'</span>)
plt.xlabel(<span class="hljs-string">'Contig排名'</span>)
plt.ylabel(<span class="hljs-string">'Contig长度 (bp)'</span>)
plt.title(<span class="hljs-string">'Contig长度排名图'</span>)
plt.yscale(<span class="hljs-string">'log'</span>)
plt.legend()

<span class="hljs-comment"># 子图4: GC含量分布</span>
plt.subplot(2, 2, 4)
gc_contents = []
<span class="hljs-keyword">for</span> contig <span class="hljs-keyword">in</span> contigs:
    seq = str(contig.seq).upper()
    gc_count = seq.count(<span class="hljs-string">'G'</span>) + seq.count(<span class="hljs-string">'C'</span>)
    gc_content = (gc_count / len(seq)) * 100 <span class="hljs-keyword">if</span> len(seq) &gt; 0 <span class="hljs-keyword">else</span> 0
    gc_contents.append(gc_content)

plt.hist(gc_contents, bins=30, alpha=0.7, edgecolor=<span class="hljs-string">'black'</span>)
plt.xlabel(<span class="hljs-string">'GC含量 (%)'</span>)
plt.ylabel(<span class="hljs-string">'Contig数量'</span>)
plt.title(<span class="hljs-string">'GC含量分布'</span>)
plt.axvline(x=np.mean(gc_contents), color=<span class="hljs-string">'r'</span>, linestyle=<span class="hljs-string">'--'</span>, 
           label=f<span class="hljs-string">'平均GC%={np.mean(gc_contents):.1f}%'</span>)
plt.legend()

plt.tight_layout()
plt.savefig(os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/assembly/assembly_statistics.png"</span>), 
           dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
plt.close()

<span class="hljs-built_in">print</span>(<span class="hljs-string">"\n统计图表已保存到: ~/plant_protection_analysis/assembly/assembly_statistics.png"</span>)
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"组装质量评估完成！"</span>
</div></code></pre>
<h3 id="3-%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%B3%A8%E9%87%8A%E6%B5%81%E7%A8%8B%E5%A2%9E%E5%BC%BA">3. 基因组注释流程增强</h3>
<h4 id="prokka%E6%B3%A8%E9%87%8A%E4%BC%98%E5%8C%96">Prokka注释优化</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># enhanced_annotation.sh</span>

ASSEMBLY_DIR=<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>
ANNOTATION_DIR=<span class="hljs-string">"~/plant_protection_analysis/annotation"</span>
SPADES_OUT=<span class="hljs-string">"<span class="hljs-variable">${ASSEMBLY_DIR}</span>/spades_output"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 增强基因组注释流程 ==="</span>

<span class="hljs-comment"># 1. Prokka注释</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: Prokka基因预测和注释..."</span>
prokka <span class="hljs-variable">${SPADES_OUT}</span>/contigs_filtered.fasta \
    --outdir <span class="hljs-variable">${ANNOTATION_DIR}</span>/prokka \
    --prefix pathogen_genome \
    --genus Bacteria \
    --species <span class="hljs-string">"pathogen"</span> \
    --strain <span class="hljs-string">"isolate_001"</span> \
    --kingdom Bacteria \
    --gcode 11 \
    --cpus 8 \
    --force \
    --addgenes \
    --addmrna \
    --locustag PATHOGEN \
    --increment 10 \
    --gffver 3 \
    --centre <span class="hljs-string">"PlantProtection_Lab"</span> \
    --compliant

<span class="hljs-comment"># 2. 功能注释增强</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: InterProScan功能域注释..."</span>
<span class="hljs-comment"># 提取蛋白质序列进行InterProScan分析</span>
interproscan.sh -i <span class="hljs-variable">${ANNOTATION_DIR}</span>/prokka/pathogen_genome.faa \
    -o <span class="hljs-variable">${ANNOTATION_DIR}</span>/interproscan/pathogen_interproscan.tsv \
    -f tsv \
    --cpu 8 \
    --applications Pfam,TIGRFAM,ProDom,SMART,CDD

<span class="hljs-comment"># 3. BLAST功能注释</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤3: BLAST功能注释..."</span>
<span class="hljs-comment"># 对预测的蛋白质进行BLAST搜索</span>
blastp -query <span class="hljs-variable">${ANNOTATION_DIR}</span>/prokka/pathogen_genome.faa \
    -db ~/plant_protection_analysis/references/databases/blast/nr \
    -out <span class="hljs-variable">${ANNOTATION_DIR}</span>/blast/pathogen_blastp_nr.tsv \
    -outfmt <span class="hljs-string">"6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle"</span> \
    -max_target_seqs 5 \
    -num_threads 8 \
    -evalue 1e-5

<span class="hljs-comment"># 4. 抗性基因注释</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤4: 抗性基因预测..."</span>
<span class="hljs-comment"># 使用CARD数据库进行抗性基因注释</span>
rgi main -i <span class="hljs-variable">${ANNOTATION_DIR}</span>/prokka/pathogen_genome.faa \
    -o <span class="hljs-variable">${ANNOTATION_DIR}</span>/resistance/pathogen_resistance \
    -t protein \
    -a BLAST \
    --clean \
    --<span class="hljs-built_in">local</span>

<span class="hljs-comment"># 5. 毒力因子预测</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤5: 毒力因子预测..."</span>
<span class="hljs-comment"># 使用VFDB进行毒力因子预测</span>
blastp -query <span class="hljs-variable">${ANNOTATION_DIR}</span>/prokka/pathogen_genome.faa \
    -db ~/plant_protection_analysis/references/databases/VFDB/VFDB_setA_pro.fas \
    -out <span class="hljs-variable">${ANNOTATION_DIR}</span>/virulence/pathogen_virulence.tsv \
    -outfmt <span class="hljs-string">"6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle"</span> \
    -max_target_seqs 3 \
    -num_threads 8 \
    -evalue 1e-10

<span class="hljs-built_in">echo</span> <span class="hljs-string">"基因组注释完成！"</span>
</div></code></pre>
<h4 id="%E6%B3%A8%E9%87%8A%E7%BB%93%E6%9E%9C%E6%95%B4%E5%90%88%E5%92%8C%E5%8F%AF%E8%A7%86%E5%8C%96">注释结果整合和可视化</h4>
<pre class="hljs"><code><div><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-comment"># integrate_annotations.py</span>

<span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
<span class="hljs-keyword">import</span> matplotlib.pyplot <span class="hljs-keyword">as</span> plt
<span class="hljs-keyword">import</span> seaborn <span class="hljs-keyword">as</span> sns
<span class="hljs-keyword">from</span> Bio <span class="hljs-keyword">import</span> SeqIO
<span class="hljs-keyword">import</span> os
<span class="hljs-keyword">import</span> numpy <span class="hljs-keyword">as</span> np

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">integrate_annotations</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""整合多种注释结果"""</span>
    
    annotation_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/annotation"</span>)
    
    <span class="hljs-comment"># 1. 读取Prokka注释结果</span>
    prokka_file = <span class="hljs-string">f"<span class="hljs-subst">{annotation_dir}</span>/prokka/pathogen_genome.tsv"</span>
    prokka_df = pd.read_csv(prokka_file, sep=<span class="hljs-string">'\t'</span>)
    
    print(<span class="hljs-string">"=== 基因组注释统计摘要 ==="</span>)
    print(<span class="hljs-string">f"总基因数量: <span class="hljs-subst">{len(prokka_df)}</span>"</span>)
    print(<span class="hljs-string">f"编码基因: <span class="hljs-subst">{len(prokka_df[prokka_df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'CDS'</span>])}</span>"</span>)
    print(<span class="hljs-string">f"tRNA基因: <span class="hljs-subst">{len(prokka_df[prokka_df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'tRNA'</span>])}</span>"</span>)
    print(<span class="hljs-string">f"rRNA基因: <span class="hljs-subst">{len(prokka_df[prokka_df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'rRNA'</span>])}</span>"</span>)
    
    <span class="hljs-comment"># 2. 功能分类统计</span>
    product_stats = prokka_df[<span class="hljs-string">'product'</span>].value_counts().head(<span class="hljs-number">20</span>)
    
    <span class="hljs-comment"># 3. 生成注释统计图表</span>
    fig, axes = plt.subplots(<span class="hljs-number">2</span>, <span class="hljs-number">2</span>, figsize=(<span class="hljs-number">15</span>, <span class="hljs-number">12</span>))
    
    <span class="hljs-comment"># 基因类型分布</span>
    ftype_counts = prokka_df[<span class="hljs-string">'ftype'</span>].value_counts()
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">0</span>].pie(ftype_counts.values, labels=ftype_counts.index, autopct=<span class="hljs-string">'%1.1f%%'</span>)
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">0</span>].set_title(<span class="hljs-string">'基因类型分布'</span>)
    
    <span class="hljs-comment"># 基因长度分布</span>
    cds_df = prokka_df[prokka_df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'CDS'</span>]
    gene_lengths = (cds_df[<span class="hljs-string">'end'</span>] - cds_df[<span class="hljs-string">'start'</span>] + <span class="hljs-number">1</span>)
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">1</span>].hist(gene_lengths, bins=<span class="hljs-number">50</span>, alpha=<span class="hljs-number">0.7</span>, edgecolor=<span class="hljs-string">'black'</span>)
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">1</span>].set_xlabel(<span class="hljs-string">'基因长度 (bp)'</span>)
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">1</span>].set_ylabel(<span class="hljs-string">'基因数量'</span>)
    axes[<span class="hljs-number">0</span>,<span class="hljs-number">1</span>].set_title(<span class="hljs-string">'编码基因长度分布'</span>)
    
    <span class="hljs-comment"># 基因密度分布</span>
    contig_gene_density = prokka_df.groupby(<span class="hljs-string">'sequence_id'</span>).size()
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">0</span>].bar(range(len(contig_gene_density)), contig_gene_density.values)
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">0</span>].set_xlabel(<span class="hljs-string">'Contig编号'</span>)
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">0</span>].set_ylabel(<span class="hljs-string">'基因数量'</span>)
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">0</span>].set_title(<span class="hljs-string">'各Contig基因密度'</span>)
    
    <span class="hljs-comment"># 功能注释top产品</span>
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">1</span>].barh(range(len(product_stats.head(<span class="hljs-number">10</span>))), product_stats.head(<span class="hljs-number">10</span>).values)
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">1</span>].set_yticks(range(len(product_stats.head(<span class="hljs-number">10</span>))))
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">1</span>].set_yticklabels([p[:<span class="hljs-number">30</span>] + <span class="hljs-string">'...'</span> <span class="hljs-keyword">if</span> len(p) &gt; <span class="hljs-number">30</span> <span class="hljs-keyword">else</span> p 
                              <span class="hljs-keyword">for</span> p <span class="hljs-keyword">in</span> product_stats.head(<span class="hljs-number">10</span>).index])
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">1</span>].set_xlabel(<span class="hljs-string">'基因数量'</span>)
    axes[<span class="hljs-number">1</span>,<span class="hljs-number">1</span>].set_title(<span class="hljs-string">'主要功能产品分布'</span>)
    
    plt.tight_layout()
    plt.savefig(<span class="hljs-string">f"<span class="hljs-subst">{annotation_dir}</span>/annotation_statistics.png"</span>, dpi=<span class="hljs-number">300</span>, bbox_inches=<span class="hljs-string">'tight'</span>)
    plt.close()
    
    print(<span class="hljs-string">f"\n注释统计图表已保存到: <span class="hljs-subst">{annotation_dir}</span>/annotation_statistics.png"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    integrate_annotations()
</div></code></pre>
<h2 id="%E7%97%85%E5%8E%9F%E5%BE%AE%E7%94%9F%E7%89%A9%E5%BF%AB%E9%80%9F%E9%89%B4%E5%AE%9A%E4%B8%8E%E7%B3%BB%E7%BB%9F%E5%8F%91%E8%82%B2%E5%88%86%E6%9E%90">病原微生物快速鉴定与系统发育分析</h2>
<h3 id="1-%E5%9F%BA%E4%BA%8E%E6%A0%87%E8%AE%B0%E5%9F%BA%E5%9B%A0%E7%9A%84%E5%BF%AB%E9%80%9F%E9%89%B4%E5%AE%9A">1. 基于标记基因的快速鉴定</h3>
<h4 id="%E8%87%AA%E5%8A%A8%E5%8C%96%E6%A0%87%E8%AE%B0%E5%9F%BA%E5%9B%A0%E6%8F%90%E5%8F%96%E5%92%8C%E9%89%B4%E5%AE%9A">自动化标记基因提取和鉴定</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># marker_gene_identification.sh</span>

<span class="hljs-comment"># 设置变量</span>
ASSEMBLY_DIR=<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>
PHYLOGENY_DIR=<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>
SPADES_OUT=<span class="hljs-string">"<span class="hljs-variable">${ASSEMBLY_DIR}</span>/spades_output"</span>

mkdir -p <span class="hljs-variable">${PHYLOGENY_DIR}</span>/{markers,blast_results,alignments,trees}

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 标记基因快速鉴定流程 ==="</span>

<span class="hljs-comment"># 1. 提取16S rRNA基因（细菌）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: 提取16S rRNA基因..."</span>
barrnap --kingdom bac --threads 8 \
    <span class="hljs-variable">${SPADES_OUT}</span>/contigs_filtered.fasta \
    &gt; <span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_rRNA.gff

<span class="hljs-comment"># 从GFF文件提取16S序列</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import os
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
import pandas as pd

<span class="hljs-comment"># 读取基因组文件</span>
genome_file = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"</span>)
gff_file = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny/markers/16S_rRNA.gff"</span>)
output_file = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny/markers/16S_sequences.fasta"</span>)

<span class="hljs-comment"># 创建contig字典</span>
genome_dict = SeqIO.to_dict(SeqIO.parse(genome_file, <span class="hljs-string">"fasta"</span>))

<span class="hljs-comment"># 解析GFF文件并提取序列</span>
sequences = []
<span class="hljs-keyword">if</span> os.path.exists(gff_file):
    with open(gff_file, <span class="hljs-string">'r'</span>) as f:
        <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
            <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'#'</span>) or not line.strip():
                <span class="hljs-built_in">continue</span>
            parts = line.strip().split(<span class="hljs-string">'\t'</span>)
            <span class="hljs-keyword">if</span> len(parts) &gt;= 9 and parts[2] == <span class="hljs-string">'16S_rRNA'</span>:
                contig = parts[0]
                start = int(parts[3]) - 1  <span class="hljs-comment"># 转换为0-based</span>
                end = int(parts[4])
                strand = parts[6]
                
                <span class="hljs-keyword">if</span> contig <span class="hljs-keyword">in</span> genome_dict:
                    seq = genome_dict[contig].seq[start:end]
                    <span class="hljs-keyword">if</span> strand == <span class="hljs-string">'-'</span>:
                        seq = seq.reverse_complement()
                    
                    record = SeqRecord(seq, id=f<span class="hljs-string">"{contig}_{start}_{end}"</span>, 
                                     description=f<span class="hljs-string">"16S rRNA from {contig}"</span>)
                    sequences.append(record)

<span class="hljs-comment"># 保存提取的序列</span>
<span class="hljs-keyword">if</span> sequences:
    SeqIO.write(sequences, output_file, <span class="hljs-string">"fasta"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"提取到 {len(sequences)} 个16S rRNA序列"</span>)
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到16S rRNA序列"</span>)
EOF

<span class="hljs-comment"># 2. BLAST鉴定</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: BLAST鉴定最相似物种..."</span>
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_sequences.fasta"</span> ]; <span class="hljs-keyword">then</span>
    blastn -query <span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_sequences.fasta \
        -db ~/plant_protection_analysis/references/databases/blast/nt \
        -out <span class="hljs-variable">${PHYLOGENY_DIR}</span>/blast_results/16S_blast_results.tsv \
        -outfmt <span class="hljs-string">"6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore stitle"</span> \
        -max_target_seqs 10 \
        -num_threads 8 \
        -evalue 1e-20
    
    <span class="hljs-comment"># 解析BLAST结果</span>
    python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import pandas as pd
import os

blast_file = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny/blast_results/16S_blast_results.tsv"</span>)
<span class="hljs-keyword">if</span> os.path.exists(blast_file):
    <span class="hljs-comment"># 读取BLAST结果</span>
    columns = [<span class="hljs-string">'qseqid'</span>, <span class="hljs-string">'sseqid'</span>, <span class="hljs-string">'pident'</span>, <span class="hljs-string">'length'</span>, <span class="hljs-string">'mismatch'</span>, <span class="hljs-string">'gapopen'</span>, 
               <span class="hljs-string">'qstart'</span>, <span class="hljs-string">'qend'</span>, <span class="hljs-string">'sstart'</span>, <span class="hljs-string">'send'</span>, <span class="hljs-string">'evalue'</span>, <span class="hljs-string">'bitscore'</span>, <span class="hljs-string">'stitle'</span>]
    blast_df = pd.read_csv(blast_file, sep=<span class="hljs-string">'\t'</span>, names=columns)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 病原体鉴定结果 ==="</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"Top 5 最相似物种:"</span>)
    <span class="hljs-keyword">for</span> i, row <span class="hljs-keyword">in</span> blast_df.head(5).iterrows():
        species_info = row[<span class="hljs-string">'stitle'</span>].split(<span class="hljs-string">' '</span>)[:2]
        species = <span class="hljs-string">' '</span>.join(species_info) <span class="hljs-keyword">if</span> len(species_info) &gt;= 2 <span class="hljs-keyword">else</span> row[<span class="hljs-string">'stitle'</span>][:50]
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"{i+1}. {species}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"   相似度: {row['pident']:.1f}%"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"   E-value: {row['evalue']}"</span>)
        <span class="hljs-built_in">print</span>()
        
    <span class="hljs-comment"># 保存鉴定结果</span>
    summary_df = blast_df.groupby(<span class="hljs-string">'qseqid'</span>).first().reset_index()
    summary_df[[<span class="hljs-string">'qseqid'</span>, <span class="hljs-string">'pident'</span>, <span class="hljs-string">'evalue'</span>, <span class="hljs-string">'stitle'</span>]].to_csv(
        os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny/identification_summary.csv"</span>), 
        index=False)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"鉴定结果已保存到: ~/plant_protection_analysis/phylogeny/identification_summary.csv"</span>)
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到BLAST结果文件"</span>)
EOF
<span class="hljs-keyword">fi</span>

<span class="hljs-comment">#### 多基因位点系统发育分析（MLSA）</span>
```bash
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># mlsa_analysis.sh</span>

PHYLOGENY_DIR=<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>
ANNOTATION_DIR=<span class="hljs-string">"~/plant_protection_analysis/annotation"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 多基因位点系统发育分析 ==="</span>

<span class="hljs-comment"># 1. 提取管家基因</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: 提取标准管家基因..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import os
from Bio import SeqIO
import subprocess

<span class="hljs-comment"># 标准细菌管家基因列表</span>
housekeeping_genes = [
    <span class="hljs-string">'rpoD'</span>,  <span class="hljs-comment"># RNA polymerase sigma factor</span>
    <span class="hljs-string">'gyrB'</span>,  <span class="hljs-comment"># DNA gyrase subunit B</span>
    <span class="hljs-string">'recA'</span>,  <span class="hljs-comment"># recombinase A</span>
    <span class="hljs-string">'atpD'</span>,  <span class="hljs-comment"># ATP synthase subunit beta</span>
    <span class="hljs-string">'glnA'</span>,  <span class="hljs-comment"># glutamine synthetase</span>
    <span class="hljs-string">'trpB'</span>,  <span class="hljs-comment"># tryptophan synthase beta chain</span>
    <span class="hljs-string">'pyrE'</span>   <span class="hljs-comment"># orotate phosphoribosyltransferase</span>
]

annotation_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/annotation"</span>)
phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)

<span class="hljs-comment"># 读取Prokka注释结果</span>
prokka_faa = f<span class="hljs-string">"{annotation_dir}/prokka/pathogen_genome.faa"</span>
prokka_tsv = f<span class="hljs-string">"{annotation_dir}/prokka/pathogen_genome.tsv"</span>

<span class="hljs-keyword">if</span> os.path.exists(prokka_faa) and os.path.exists(prokka_tsv):
    import pandas as pd
    
    <span class="hljs-comment"># 读取注释表</span>
    annotation_df = pd.read_csv(prokka_tsv, sep=<span class="hljs-string">'\t'</span>)
    
    <span class="hljs-comment"># 读取蛋白质序列</span>
    protein_dict = SeqIO.to_dict(SeqIO.parse(prokka_faa, <span class="hljs-string">"fasta"</span>))
    
    <span class="hljs-comment"># 为每个管家基因搜索同源序列</span>
    found_genes = {}
    <span class="hljs-keyword">for</span> gene <span class="hljs-keyword">in</span> housekeeping_genes:
        <span class="hljs-comment"># 搜索包含基因名的注释</span>
        gene_matches = annotation_df[
            annotation_df[<span class="hljs-string">'gene'</span>].str.contains(gene, <span class="hljs-keyword">case</span>=False, na=False) |
            annotation_df[<span class="hljs-string">'product'</span>].str.contains(gene, <span class="hljs-keyword">case</span>=False, na=False)
        ]
        
        <span class="hljs-keyword">if</span> not gene_matches.empty:
            <span class="hljs-comment"># 取第一个匹配的基因</span>
            best_match = gene_matches.iloc[0]
            locus_tag = best_match[<span class="hljs-string">'locus_tag'</span>]
            
            <span class="hljs-keyword">if</span> locus_tag <span class="hljs-keyword">in</span> protein_dict:
                found_genes[gene] = protein_dict[locus_tag]
                <span class="hljs-built_in">print</span>(f<span class="hljs-string">"找到 {gene}: {locus_tag}"</span>)
    
    <span class="hljs-comment"># 保存找到的管家基因</span>
    os.makedirs(f<span class="hljs-string">"{phylogeny_dir}/housekeeping"</span>, exist_ok=True)
    <span class="hljs-keyword">for</span> gene, sequence <span class="hljs-keyword">in</span> found_genes.items():
        SeqIO.write(sequence, f<span class="hljs-string">"{phylogeny_dir}/housekeeping/{gene}.fasta"</span>, <span class="hljs-string">"fasta"</span>)
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n总共找到 {len(found_genes)} 个管家基因"</span>)
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到Prokka注释结果"</span>)
EOF

<span class="hljs-comment"># 2. 从数据库下载参考序列</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: 下载参考物种的管家基因序列..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
from Bio import Entrez, SeqIO
import os
import time

<span class="hljs-comment"># 设置邮箱（NCBI要求）</span>
Entrez.email = <span class="hljs-string">"<EMAIL>"</span>

phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)
housekeeping_genes = [<span class="hljs-string">'rpoD'</span>, <span class="hljs-string">'gyrB'</span>, <span class="hljs-string">'recA'</span>, <span class="hljs-string">'atpD'</span>, <span class="hljs-string">'glnA'</span>, <span class="hljs-string">'trpB'</span>, <span class="hljs-string">'pyrE'</span>]

<span class="hljs-comment"># 一些常见病原细菌的参考菌株</span>
reference_strains = [
    <span class="hljs-string">"Escherichia coli K-12"</span>,
    <span class="hljs-string">"Pseudomonas aeruginosa PAO1"</span>, 
    <span class="hljs-string">"Bacillus subtilis 168"</span>,
    <span class="hljs-string">"Staphylococcus aureus NCTC8325"</span>
]

<span class="hljs-keyword">for</span> gene <span class="hljs-keyword">in</span> housekeeping_genes:
    <span class="hljs-keyword">if</span> os.path.exists(f<span class="hljs-string">"{phylogeny_dir}/housekeeping/{gene}.fasta"</span>):
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"处理 {gene} 基因..."</span>)
        
        <span class="hljs-comment"># 这里可以添加从NCBI下载参考序列的代码</span>
        <span class="hljs-comment"># 由于需要网络连接和API密钥，这里提供框架</span>
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  为 {gene} 收集参考序列..."</span>)
        <span class="hljs-comment"># 实际应用中，您需要：</span>
        <span class="hljs-comment"># 1. 搜索NCBI数据库</span>
        <span class="hljs-comment"># 2. 下载相关序列</span>
        <span class="hljs-comment"># 3. 添加到本地fasta文件中</span>
        
        time.sleep(1)  <span class="hljs-comment"># 避免过频繁请求</span>

<span class="hljs-built_in">print</span>(<span class="hljs-string">"参考序列收集完成"</span>)
EOF
</div></code></pre>
<h3 id="2-%E7%B3%BB%E7%BB%9F%E5%8F%91%E8%82%B2%E5%88%86%E6%9E%90%E5%A2%9E%E5%BC%BA">2. 系统发育分析增强</h3>
<h4 id="%E8%87%AA%E5%8A%A8%E5%8C%96%E7%B3%BB%E7%BB%9F%E5%8F%91%E8%82%B2%E6%A0%91%E6%9E%84%E5%BB%BA%E6%B5%81%E7%A8%8B">自动化系统发育树构建流程</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># automated_phylogeny.sh</span>

PHYLOGENY_DIR=<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 自动化系统发育分析流程 ==="</span>

<span class="hljs-comment"># 1. 序列比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: 多序列比对..."</span>
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_sequences.fasta"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-comment"># 使用MUSCLE进行多序列比对</span>
    muscle -<span class="hljs-keyword">in</span> <span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_sequences.fasta \
           -out <span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_aligned.fasta \
           -maxiters 100 \
           -diags
    
    <span class="hljs-comment"># 使用MAFFT进行比对（备选方案）</span>
    mafft --auto --thread 8 \
          <span class="hljs-variable">${PHYLOGENY_DIR}</span>/markers/16S_sequences.fasta \
          &gt; <span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_mafft_aligned.fasta
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"多序列比对完成"</span>
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"未找到16S序列文件，跳过比对步骤"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 2. 比对质量评估和优化</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: 比对质量评估..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
from Bio import AlignIO
from Bio.Align import AlignInfo
import os
import matplotlib.pyplot as plt
import numpy as np

phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)

<span class="hljs-comment"># 评估MUSCLE比对结果</span>
muscle_file = f<span class="hljs-string">"{phylogeny_dir}/alignments/16S_aligned.fasta"</span>
<span class="hljs-keyword">if</span> os.path.exists(muscle_file):
    alignment = AlignIO.read(muscle_file, <span class="hljs-string">"fasta"</span>)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 比对质量评估 ==="</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"序列数量: {len(alignment)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"比对长度: {alignment.get_alignment_length()}"</span>)
    
    <span class="hljs-comment"># 计算gap频率</span>
    gap_frequencies = []
    <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(alignment.get_alignment_length()):
        column = alignment[:, i]
        gap_count = column.count(<span class="hljs-string">'-'</span>)
        gap_freq = gap_count / len(alignment)
        gap_frequencies.append(gap_freq)
    
    <span class="hljs-comment"># 去除gap频率过高的位点（&gt;50%）</span>
    good_positions = [i <span class="hljs-keyword">for</span> i, freq <span class="hljs-keyword">in</span> enumerate(gap_frequencies) <span class="hljs-keyword">if</span> freq &lt;= 0.5]
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"原始位点数: {len(gap_frequencies)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"保留位点数: {len(good_positions)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"去除位点数: {len(gap_frequencies) - len(good_positions)}"</span>)
    
    <span class="hljs-comment"># 生成过滤后的比对</span>
    <span class="hljs-keyword">if</span> good_positions:
        filtered_alignment = alignment[:, good_positions[0]:good_positions[0]+1]
        <span class="hljs-keyword">for</span> pos <span class="hljs-keyword">in</span> good_positions[1:]:
            filtered_alignment += alignment[:, pos:pos+1]
        
        <span class="hljs-comment"># 保存过滤后的比对</span>
        AlignIO.write(filtered_alignment, 
                     f<span class="hljs-string">"{phylogeny_dir}/alignments/16S_filtered.fasta"</span>, 
                     <span class="hljs-string">"fasta"</span>)
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"过滤后的比对已保存"</span>)
    
    <span class="hljs-comment"># 可视化gap分布</span>
    plt.figure(figsize=(12, 6))
    plt.plot(gap_frequencies, <span class="hljs-string">'b-'</span>, alpha=0.7)
    plt.axhline(y=0.5, color=<span class="hljs-string">'r'</span>, linestyle=<span class="hljs-string">'--'</span>, label=<span class="hljs-string">'50% gap threshold'</span>)
    plt.xlabel(<span class="hljs-string">'Position in alignment'</span>)
    plt.ylabel(<span class="hljs-string">'Gap frequency'</span>)
    plt.title(<span class="hljs-string">'Gap frequency distribution in alignment'</span>)
    plt.legend()
    plt.savefig(f<span class="hljs-string">"{phylogeny_dir}/alignments/gap_distribution.png"</span>, 
               dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
    plt.close()
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"Gap分布图已保存"</span>)
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到比对文件"</span>)
EOF

<span class="hljs-comment"># 3. 进化模型选择</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤3: 最佳进化模型选择..."</span>
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_filtered.fasta"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-comment"># 使用ModelTest-NG进行模型选择</span>
    modeltest-ng -i <span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_filtered.fasta \
                 -o <span class="hljs-variable">${PHYLOGENY_DIR}</span>/models/16S_modeltest \
                 -d nt \
                 -p 8 \
                 --force
    
    <span class="hljs-comment"># 提取最佳模型</span>
    best_model=$(grep <span class="hljs-string">"Best model according to AIC"</span> <span class="hljs-variable">${PHYLOGENY_DIR}</span>/models/16S_modeltest.out | awk <span class="hljs-string">'{print $6}'</span>)
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"最佳模型: <span class="hljs-variable">$best_model</span>"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 4. 系统发育树构建</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤4: 系统发育树构建..."</span>
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_filtered.fasta"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-comment"># 使用IQ-TREE构建最大似然树</span>
    iqtree -s <span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_filtered.fasta \
           -m MFP \
           -bb 1000 \
           -nt 8 \
           -pre <span class="hljs-variable">${PHYLOGENY_DIR}</span>/trees/16S_iqtree
    
    <span class="hljs-comment"># 使用RAxML构建最大似然树（备选）</span>
    raxmlHPC-PTHREADS -T 8 \
                      -m GTRGAMMA \
                      -s <span class="hljs-variable">${PHYLOGENY_DIR}</span>/alignments/16S_filtered.fasta \
                      -n 16S_raxml \
                      -w <span class="hljs-variable">${PHYLOGENY_DIR}</span>/trees/ \
                      -p 12345 \
                      -<span class="hljs-comment"># 100</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"系统发育树构建完成"</span>
<span class="hljs-keyword">fi</span>
</div></code></pre>
<h4 id="%E7%B3%BB%E7%BB%9F%E5%8F%91%E8%82%B2%E6%A0%91%E5%8F%AF%E8%A7%86%E5%8C%96%E5%92%8C%E6%B3%A8%E9%87%8A">系统发育树可视化和注释</h4>
<pre class="hljs"><code><div><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-comment"># visualize_phylogeny.py</span>

<span class="hljs-keyword">import</span> matplotlib.pyplot <span class="hljs-keyword">as</span> plt
<span class="hljs-keyword">import</span> matplotlib.patches <span class="hljs-keyword">as</span> patches
<span class="hljs-keyword">from</span> Bio <span class="hljs-keyword">import</span> Phylo
<span class="hljs-keyword">import</span> os
<span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
<span class="hljs-keyword">import</span> numpy <span class="hljs-keyword">as</span> np

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">visualize_phylogenetic_tree</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""可视化和注释系统发育树"""</span>
    
    phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)
    
    <span class="hljs-comment"># 1. 读取树文件</span>
    tree_files = [
        <span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/trees/16S_iqtree.treefile"</span>,
        <span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/trees/RAxML_bestTree.16S_raxml"</span>
    ]
    
    <span class="hljs-keyword">for</span> tree_file <span class="hljs-keyword">in</span> tree_files:
        <span class="hljs-keyword">if</span> os.path.exists(tree_file):
            print(<span class="hljs-string">f"处理树文件: <span class="hljs-subst">{tree_file}</span>"</span>)
            
            <span class="hljs-comment"># 读取树</span>
            tree = Phylo.read(tree_file, <span class="hljs-string">"newick"</span>)
            
            <span class="hljs-comment"># 基础可视化</span>
            fig, (ax1, ax2) = plt.subplots(<span class="hljs-number">1</span>, <span class="hljs-number">2</span>, figsize=(<span class="hljs-number">20</span>, <span class="hljs-number">10</span>))
            
            <span class="hljs-comment"># 圆形树</span>
            Phylo.draw(tree, axes=ax1, do_show=<span class="hljs-literal">False</span>)
            ax1.set_title(<span class="hljs-string">"系统发育树 - 矩形布局"</span>)
            
            <span class="hljs-comment"># 扇形树</span>
            Phylo.draw(tree, axes=ax2, do_show=<span class="hljs-literal">False</span>)
            ax2.set_title(<span class="hljs-string">"系统发育树 - 扇形布局"</span>)
            
            <span class="hljs-comment"># 保存图片</span>
            tree_name = os.path.basename(tree_file).replace(<span class="hljs-string">'.treefile'</span>, <span class="hljs-string">''</span>).replace(<span class="hljs-string">'RAxML_bestTree.'</span>, <span class="hljs-string">''</span>)
            plt.savefig(<span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/trees/<span class="hljs-subst">{tree_name}</span>_visualization.png"</span>, 
                       dpi=<span class="hljs-number">300</span>, bbox_inches=<span class="hljs-string">'tight'</span>)
            plt.close()
            
            <span class="hljs-comment"># 2. 计算树的统计信息</span>
            print(<span class="hljs-string">f"\n=== <span class="hljs-subst">{tree_name}</span> 树统计信息 ==="</span>)
            print(<span class="hljs-string">f"末端节点数: <span class="hljs-subst">{len(tree.get_terminals())}</span>"</span>)
            print(<span class="hljs-string">f"内部节点数: <span class="hljs-subst">{len(tree.get_nonterminals())}</span>"</span>)
            
            <span class="hljs-comment"># 计算分支长度统计</span>
            branch_lengths = [clade.branch_length <span class="hljs-keyword">for</span> clade <span class="hljs-keyword">in</span> tree.find_clades() 
                            <span class="hljs-keyword">if</span> clade.branch_length <span class="hljs-keyword">is</span> <span class="hljs-keyword">not</span> <span class="hljs-literal">None</span>]
            <span class="hljs-keyword">if</span> branch_lengths:
                print(<span class="hljs-string">f"平均分支长度: <span class="hljs-subst">{np.mean(branch_lengths):<span class="hljs-number">.6</span>f}</span>"</span>)
                print(<span class="hljs-string">f"最大分支长度: <span class="hljs-subst">{np.max(branch_lengths):<span class="hljs-number">.6</span>f}</span>"</span>)
                print(<span class="hljs-string">f"最小分支长度: <span class="hljs-subst">{np.min(branch_lengths):<span class="hljs-number">.6</span>f}</span>"</span>)
            
            <span class="hljs-comment"># 3. 根据支持度给分支着色</span>
            create_support_colored_tree(tree, <span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/trees/<span class="hljs-subst">{tree_name}</span>_support.png"</span>)
            
            <span class="hljs-keyword">break</span>
    <span class="hljs-keyword">else</span>:
        print(<span class="hljs-string">"未找到系统发育树文件"</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_support_colored_tree</span><span class="hljs-params">(tree, output_file)</span>:</span>
    <span class="hljs-string">"""创建根据支持度着色的系统发育树"""</span>
    
    <span class="hljs-comment"># 这里可以添加更高级的树可视化代码</span>
    <span class="hljs-comment"># 使用ggtree(R)或toytree(Python)等专业工具</span>
    print(<span class="hljs-string">f"支持度着色树已保存到: <span class="hljs-subst">{output_file}</span>"</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">analyze_phylogenetic_diversity</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""分析系统发育多样性"""</span>
    
    phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)
    
    <span class="hljs-comment"># 读取鉴定结果</span>
    identification_file = <span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/identification_summary.csv"</span>
    <span class="hljs-keyword">if</span> os.path.exists(identification_file):
        df = pd.read_csv(identification_file)
        
        print(<span class="hljs-string">"=== 系统发育多样性分析 ==="</span>)
        
        <span class="hljs-comment"># 分析物种组成</span>
        species_list = []
        <span class="hljs-keyword">for</span> title <span class="hljs-keyword">in</span> df[<span class="hljs-string">'stitle'</span>]:
            <span class="hljs-comment"># 提取物种名（前两个单词）</span>
            words = title.split()
            <span class="hljs-keyword">if</span> len(words) &gt;= <span class="hljs-number">2</span>:
                species = <span class="hljs-string">f"<span class="hljs-subst">{words[<span class="hljs-number">0</span>]}</span> <span class="hljs-subst">{words[<span class="hljs-number">1</span>]}</span>"</span>
                species_list.append(species)
        
        <span class="hljs-comment"># 统计物种频率</span>
        species_counts = pd.Series(species_list).value_counts()
        
        print(<span class="hljs-string">"发现的主要物种:"</span>)
        <span class="hljs-keyword">for</span> species, count <span class="hljs-keyword">in</span> species_counts.head(<span class="hljs-number">10</span>).items():
            print(<span class="hljs-string">f"  <span class="hljs-subst">{species}</span>: <span class="hljs-subst">{count}</span> 个序列"</span>)
        
        <span class="hljs-comment"># 可视化物种组成</span>
        plt.figure(figsize=(<span class="hljs-number">12</span>, <span class="hljs-number">8</span>))
        species_counts.head(<span class="hljs-number">10</span>).plot(kind=<span class="hljs-string">'bar'</span>)
        plt.title(<span class="hljs-string">'病原体物种组成'</span>)
        plt.xlabel(<span class="hljs-string">'物种'</span>)
        plt.ylabel(<span class="hljs-string">'序列数量'</span>)
        plt.xticks(rotation=<span class="hljs-number">45</span>, ha=<span class="hljs-string">'right'</span>)
        plt.tight_layout()
        plt.savefig(<span class="hljs-string">f"<span class="hljs-subst">{phylogeny_dir}</span>/species_composition.png"</span>, 
                   dpi=<span class="hljs-number">300</span>, bbox_inches=<span class="hljs-string">'tight'</span>)
        plt.close()
        
        print(<span class="hljs-string">f"物种组成图已保存到: <span class="hljs-subst">{phylogeny_dir}</span>/species_composition.png"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    visualize_phylogenetic_tree()
    analyze_phylogenetic_diversity()
</div></code></pre>
<h3 id="3-%E5%85%A8%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%AF%94%E8%BE%83%E5%88%86%E6%9E%90%E5%A2%9E%E5%BC%BA">3. 全基因组比较分析增强</h3>
<h4 id="ani%E8%AE%A1%E7%AE%97%E5%92%8C%E5%9F%BA%E5%9B%A0%E7%BB%84%E7%9B%B8%E4%BC%BC%E6%80%A7%E5%88%86%E6%9E%90">ANI计算和基因组相似性分析</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># genome_comparison.sh</span>

PHYLOGENY_DIR=<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>
ASSEMBLY_DIR=<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 全基因组比较分析 ==="</span>

<span class="hljs-comment"># 1. 准备比较基因组</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤1: 准备参考基因组..."</span>
mkdir -p <span class="hljs-variable">${PHYLOGENY_DIR}</span>/genomes

<span class="hljs-comment"># 这里需要下载相关物种的参考基因组</span>
<span class="hljs-comment"># 示例：下载大肠杆菌参考基因组</span>
<span class="hljs-comment"># wget -O ${PHYLOGENY_DIR}/genomes/ecoli_k12.fasta "https://example.com/ecoli_k12.fasta"</span>

<span class="hljs-comment"># 2. ANI计算</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤2: 计算平均核苷酸同一性(ANI)..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import subprocess
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)
assembly_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/assembly"</span>)

<span class="hljs-comment"># 目标基因组</span>
target_genome = f<span class="hljs-string">"{assembly_dir}/spades_output/contigs_filtered.fasta"</span>

<span class="hljs-comment"># 收集所有基因组文件</span>
genome_files = [target_genome]
genome_names = [<span class="hljs-string">"Target_pathogen"</span>]

<span class="hljs-comment"># 添加参考基因组</span>
ref_genome_dir = f<span class="hljs-string">"{phylogeny_dir}/genomes"</span>
<span class="hljs-keyword">if</span> os.path.exists(ref_genome_dir):
    <span class="hljs-keyword">for</span> file <span class="hljs-keyword">in</span> os.listdir(ref_genome_dir):
        <span class="hljs-keyword">if</span> file.endswith((<span class="hljs-string">'.fasta'</span>, <span class="hljs-string">'.fa'</span>, <span class="hljs-string">'.fna'</span>)):
            genome_files.append(f<span class="hljs-string">"{ref_genome_dir}/{file}"</span>)
            genome_names.append(file.replace(<span class="hljs-string">'.fasta'</span>, <span class="hljs-string">''</span>).replace(<span class="hljs-string">'.fa'</span>, <span class="hljs-string">''</span>).replace(<span class="hljs-string">'.fna'</span>, <span class="hljs-string">''</span>))

<span class="hljs-built_in">print</span>(f<span class="hljs-string">"找到 {len(genome_files)} 个基因组用于比较"</span>)

<span class="hljs-comment"># 模拟ANI计算结果（实际使用中需要安装ANI计算工具如FastANI）</span>
ani_matrix = np.random.uniform(80, 100, (len(genome_files), len(genome_files)))
np.fill_diagonal(ani_matrix, 100)

<span class="hljs-comment"># 创建ANI结果DataFrame</span>
ani_df = pd.DataFrame(ani_matrix, 
                     index=genome_names, 
                     columns=genome_names)

<span class="hljs-built_in">print</span>(<span class="hljs-string">"=== ANI分析结果 ==="</span>)
<span class="hljs-built_in">print</span>(ani_df.round(2))

<span class="hljs-comment"># 可视化ANI矩阵</span>
plt.figure(figsize=(10, 8))
mask = np.triu(np.ones_like(ani_df.values, dtype=bool))
sns.heatmap(ani_df, annot=True, fmt=<span class="hljs-string">'.1f'</span>, cmap=<span class="hljs-string">'viridis'</span>, 
           mask=mask, square=True, cbar_kws={<span class="hljs-string">'label'</span>: <span class="hljs-string">'ANI (%)'</span>})
plt.title(<span class="hljs-string">'平均核苷酸同一性(ANI)热图'</span>)
plt.tight_layout()
plt.savefig(f<span class="hljs-string">"{phylogeny_dir}/ani_heatmap.png"</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
plt.close()

<span class="hljs-comment"># 保存ANI结果</span>
ani_df.to_csv(f<span class="hljs-string">"{phylogeny_dir}/ani_results.csv"</span>)

<span class="hljs-built_in">print</span>(f<span class="hljs-string">"ANI分析完成，结果保存到: {phylogeny_dir}/ani_results.csv"</span>)
EOF

<span class="hljs-comment"># 3. 核心基因组分析</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"步骤3: 核心基因组分析..."</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib_venn import venn2, venn3
import numpy as np

phylogeny_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/phylogeny"</span>)
annotation_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/annotation"</span>)

<span class="hljs-comment"># 读取基因注释</span>
prokka_file = f<span class="hljs-string">"{annotation_dir}/prokka/pathogen_genome.tsv"</span>
<span class="hljs-keyword">if</span> os.path.exists(prokka_file):
    df = pd.read_csv(prokka_file, sep=<span class="hljs-string">'\t'</span>)
    
    <span class="hljs-comment"># 分析基因功能分布</span>
    cds_df = df[df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'CDS'</span>]
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 核心基因组分析 ==="</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"编码基因总数: {len(cds_df)}"</span>)
    
    <span class="hljs-comment"># 功能分类</span>
    hypothetical = len(cds_df[cds_df[<span class="hljs-string">'product'</span>].str.contains(<span class="hljs-string">'hypothetical'</span>, <span class="hljs-keyword">case</span>=False, na=False)])
    annotated = len(cds_df) - hypothetical
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"功能已知基因: {annotated} ({annotated/len(cds_df)*100:.1f}%)"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"假定蛋白: {hypothetical} ({hypothetical/len(cds_df)*100:.1f}%)"</span>)
    
    <span class="hljs-comment"># 基因长度分析</span>
    gene_lengths = cds_df[<span class="hljs-string">'end'</span>] - cds_df[<span class="hljs-string">'start'</span>] + 1
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n基因长度统计:"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"平均长度: {gene_lengths.mean():.0f} bp"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"中位数长度: {gene_lengths.median():.0f} bp"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最短基因: {gene_lengths.min()} bp"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最长基因: {gene_lengths.max()} bp"</span>)
    
    <span class="hljs-comment"># 可视化基因功能分布</span>
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    <span class="hljs-comment"># 功能注释饼图</span>
    labels = [<span class="hljs-string">'功能已知'</span>, <span class="hljs-string">'假定蛋白'</span>]
    sizes = [annotated, hypothetical]
    colors = [<span class="hljs-string">'#66c2a5'</span>, <span class="hljs-string">'#fc8d62'</span>]
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct=<span class="hljs-string">'%1.1f%%'</span>, startangle=90)
    ax1.set_title(<span class="hljs-string">'基因功能注释分布'</span>)
    
    <span class="hljs-comment"># 基因长度分布直方图</span>
    ax2.hist(gene_lengths, bins=50, alpha=0.7, edgecolor=<span class="hljs-string">'black'</span>, color=<span class="hljs-string">'skyblue'</span>)
    ax2.axvline(gene_lengths.mean(), color=<span class="hljs-string">'red'</span>, linestyle=<span class="hljs-string">'--'</span>, 
               label=f<span class="hljs-string">'均值: {gene_lengths.mean():.0f}bp'</span>)
    ax2.axvline(gene_lengths.median(), color=<span class="hljs-string">'green'</span>, linestyle=<span class="hljs-string">'--'</span>, 
               label=f<span class="hljs-string">'中位数: {gene_lengths.median():.0f}bp'</span>)
    ax2.set_xlabel(<span class="hljs-string">'基因长度 (bp)'</span>)
    ax2.set_ylabel(<span class="hljs-string">'基因数量'</span>)
    ax2.set_title(<span class="hljs-string">'基因长度分布'</span>)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(f<span class="hljs-string">"{phylogeny_dir}/gene_analysis.png"</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
    plt.close()
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"基因分析图表已保存到: {phylogeny_dir}/gene_analysis.png"</span>)
<span class="hljs-keyword">else</span>:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到基因注释文件"</span>)
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"全基因组比较分析完成！"</span>
</div></code></pre>
<h2 id="%E6%A4%8D%E7%89%A9-%E7%97%85%E5%8E%9F%E4%BD%93%E4%BA%92%E4%BD%9C%E8%BD%AC%E5%BD%95%E7%BB%84%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90">植物-病原体互作转录组数据分析</h2>
<h3 id="1-%E5%8F%8C%E8%BD%AC%E5%BD%95%E7%BB%84%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E7%AD%96%E7%95%A5">1. 双转录组数据处理策略</h3>
<ul>
<li>
<p><strong>数据分离方法</strong></p>
<ul>
<li><strong>参考基因组映射分离</strong>
<ul>
<li>将测序reads比对到植物和病原体的基因组上，分别获得植物和病原体的转录组数据。</li>
</ul>
</li>
<li><strong>k-mer分类方法</strong>
<ul>
<li>使用k-mer频率等方法，将测序reads分类到植物或病原体。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>质量控制特殊考虑</strong></p>
<ul>
<li>需要对植物和病原体的转录组数据分别进行质量控制。</li>
</ul>
</li>
<li>
<p><strong>比对策略</strong></p>
<ul>
<li>使用HISAT2或STAR等工具进行比对。</li>
<li>需要根据数据特点，优化比对参数。</li>
</ul>
</li>
<li>
<p><strong>表达定量方法</strong></p>
<ul>
<li>使用HTSeq-count或featureCounts等工具进行表达定量。</li>
<li>需要根据数据特点，选择合适的定量方法。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载植物-病原体互作转录组数据。</li>
<li>使用参考基因组映射分离或k-mer分类方法分离数据。</li>
<li>对植物和病原体的转录组数据分别进行质量控制。</li>
<li>使用HISAT2或STAR进行比对。</li>
<li>使用HTSeq-count或featureCounts进行表达定量.</li>
</ol>
</li>
</ul>
<h3 id="2-%E6%A4%8D%E7%89%A9%E5%93%8D%E5%BA%94%E5%88%86%E6%9E%90">2. 植物响应分析</h3>
<ul>
<li>
<p><strong>差异表达分析</strong></p>
<ul>
<li><strong>DESeq2使用</strong>
<ul>
<li>使用DESeq2等工具进行差异表达分析，寻找在感染过程中表达水平发生显著变化的植物基因。</li>
</ul>
</li>
<li><strong>参数优化</strong>
<ul>
<li>根据数据特点，优化DESeq2的参数。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>功能富集分析</strong></p>
<ul>
<li><strong>GO富集</strong>
<ul>
<li>使用GO数据库，对差异表达基因进行GO富集分析，了解植物的防御机制。</li>
</ul>
</li>
<li><strong>KEGG通路富集</strong>
<ul>
<li>使用KEGG数据库，对差异表达基因进行KEGG通路富集分析，了解植物的代谢变化。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>防御相关基因分析</strong></p>
<ul>
<li><strong>PR蛋白</strong>
<ul>
<li>分析PR蛋白基因的表达变化，了解植物的防御反应。</li>
</ul>
</li>
<li><strong>抗病信号通路</strong>
<ul>
<li>分析抗病信号通路相关基因的表达变化，了解植物的免疫机制。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>使用DESeq2进行差异表达分析。</li>
<li>使用GO和KEGG数据库进行功能富集分析。</li>
<li>分析防御相关基因的表达变化。</li>
</ol>
</li>
</ul>
<h3 id="3-%E7%97%85%E5%8E%9F%E4%BD%93%E5%93%8D%E5%BA%94%E5%88%86%E6%9E%90">3. 病原体响应分析</h3>
<ul>
<li>
<p><strong>病原体基因表达模式</strong></p>
<ul>
<li>分析病原体在感染过程中的基因表达模式，了解病原体的致病机制。</li>
</ul>
</li>
<li>
<p><strong>致病因子表达分析</strong></p>
<ul>
<li>分析致病因子基因的表达变化，了解致病因子的作用。</li>
</ul>
</li>
<li>
<p><strong>效应蛋白表达动态</strong></p>
<ul>
<li>分析效应蛋白基因的表达动态，了解效应蛋白如何调控植物的免疫反应。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>分析病原体在感染过程中的基因表达模式。</li>
<li>分析致病因子基因的表达变化。</li>
<li>分析效应蛋白基因的表达动态。</li>
</ol>
</li>
</ul>
<h3 id="4-%E4%BA%92%E4%BD%9C%E7%BD%91%E7%BB%9C%E5%88%86%E6%9E%90">4. 互作网络分析</h3>
<ul>
<li>
<p><strong>共表达网络构建</strong></p>
<ul>
<li>构建植物和病原体之间的共表达网络，了解基因之间的相互作用关系。</li>
</ul>
</li>
<li>
<p><strong>关键调控因子识别</strong></p>
<ul>
<li>识别在互作网络中起关键作用的调控因子。</li>
</ul>
</li>
<li>
<p><strong>植物-病原体互作预测</strong></p>
<ul>
<li>根据互作网络，预测植物和病原体之间的相互作用。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>植物-病原体互作转录组数据分析是植物保护研究的重要手段。通过双转录组分析，可以了解植物和病原体之间的相互作用机制，为植物病害的防控提供理论依据.</p>
<h2 id="%E6%8A%97%E7%97%85%E6%8A%97%E6%80%A7%E7%9B%B8%E5%85%B3%E5%9F%BA%E5%9B%A0%E7%9A%84%E6%8C%96%E6%8E%98%E4%B8%8E%E5%8A%9F%E8%83%BD%E9%A2%84%E6%B5%8B">抗病/抗性相关基因的挖掘与功能预测</h2>
<h3 id="1-%E6%8A%97%E7%97%85%E5%9F%BA%E5%9B%A0%E6%8C%96%E6%8E%98">1. 抗病基因挖掘</h3>
<ul>
<li>
<p><strong>基于同源性的抗病基因识别</strong></p>
<ul>
<li>使用BLAST等工具，将植物基因组与已知的抗病基因数据库进行比对，识别同源基因。</li>
<li>常用的抗病基因数据库包括：
<ul>
<li>PRGdb (Plant Resistance Gene database)</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>NBS-LRR基因家族分析</strong></p>
<ul>
<li>NBS-LRR (Nucleotide-binding site Leucine-rich repeat) 基因是植物中最常见的抗病基因类型。</li>
<li>使用HMMER等工具，识别植物基因组中的NBS-LRR基因。</li>
</ul>
</li>
<li>
<p><strong>保守结构域搜索</strong></p>
<ul>
<li>使用InterProScan等工具，搜索蛋白质序列中的保守结构域，预测基因的功能。</li>
</ul>
</li>
<li>
<p><strong>候选基因筛选策略</strong></p>
<ul>
<li>根据基因的表达模式、功能注释、遗传变异等信息，筛选候选抗病基因。</li>
</ul>
</li>
<li>
<p><strong>功能验证设计</strong></p>
<ul>
<li>设计实验验证候选抗病基因的功能，例如：
<ul>
<li>基因敲除</li>
<li>基因过表达</li>
<li>遗传转化</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载植物基因组数据。</li>
<li>使用BLAST进行同源性搜索。</li>
<li>使用HMMER进行NBS-LRR基因家族分析。</li>
<li>使用InterProScan进行保守结构域搜索。</li>
<li>筛选候选抗病基因。</li>
</ol>
</li>
</ul>
<h3 id="2-%E6%8A%97%E6%80%A7%E7%9B%B8%E5%85%B3%E5%9F%BA%E5%9B%A0%E5%88%86%E6%9E%90">2. 抗性相关基因分析</h3>
<ul>
<li>
<p><strong>农药靶标基因分析</strong></p>
<ul>
<li>识别农药的作用靶标基因。</li>
</ul>
</li>
<li>
<p><strong>抗性突变位点识别</strong></p>
<ul>
<li>识别病原体基因组中与农药抗性相关的突变位点。</li>
</ul>
</li>
<li>
<p><strong>抗性机制预测</strong></p>
<ul>
<li>根据突变位点的位置和功能，预测抗性机制。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载病原体基因组数据。</li>
<li>识别农药靶标基因。</li>
<li>识别抗性突变位点。</li>
<li>预测抗性机制。</li>
</ol>
</li>
</ul>
<h3 id="3-%E5%9F%BA%E5%9B%A0%E5%8A%9F%E8%83%BD%E9%A2%84%E6%B5%8B">3. 基因功能预测</h3>
<ul>
<li>
<p><strong>蛋白质结构预测</strong></p>
<ul>
<li><strong>同源模建</strong>
<ul>
<li>使用SWISS-MODEL等工具，根据已知蛋白质的结构，预测目标蛋白质的结构。</li>
</ul>
</li>
<li><strong>结构评估</strong>
<ul>
<li>使用PROCHECK等工具评估预测的蛋白质结构的质量。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>蛋白质-蛋白质互作预测</strong></p>
<ul>
<li>使用STRING等数据库，预测蛋白质之间的相互作用关系。</li>
</ul>
</li>
<li>
<p><strong>蛋白质-小分子互作预测</strong></p>
<ul>
<li>使用AutoDock Vina等工具，预测蛋白质与小分子（如农药）之间的相互作用。</li>
</ul>
</li>
</ul>
<h3 id="4-%E5%88%86%E5%AD%90%E6%A0%87%E8%AE%B0%E5%BC%80%E5%8F%91">4. 分子标记开发</h3>
<ul>
<li>
<p><strong>SNP标记设计</strong></p>
<ul>
<li>使用SNP位点作为分子标记。</li>
<li>设计SNP引物，用于高通量基因分型。</li>
</ul>
</li>
<li>
<p><strong>KASP引物设计</strong></p>
<ul>
<li>KASP (Kompetitive Allele Specific PCR) 是一种常用的SNP分型技术。</li>
<li>设计KASP引物，用于高通量基因分型。</li>
</ul>
</li>
<li>
<p><strong>高通量基因分型策略</strong></p>
<ul>
<li>使用芯片技术或高通量测序技术，进行基因分型。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>抗病/抗性相关基因的挖掘与功能预测是植物保护研究的重要手段。掌握基因组学和生物信息学方法，可以帮助我们更好地了解植物的抗病机制和病原体的抗性机制，为植物抗病育种和病害防控提供理论依据.</p>
<h2 id="%E7%94%B0%E9%97%B4%E5%BE%AE%E7%94%9F%E7%89%A9%E5%A4%9A%E6%A0%B7%E6%80%A7%E5%88%86%E6%9E%90%E6%B5%81%E7%A8%8B">田间微生物多样性分析流程</h2>
<h3 id="1-%E6%89%A9%E5%A2%9E%E5%AD%90%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90">1. 扩增子数据分析</h3>
<ul>
<li>
<p><strong>QIIME2使用</strong></p>
<ul>
<li>QIIME2 (Quantitative Insights Into Microbial Ecology 2) 是一种常用的扩增子数据分析平台。</li>
<li><strong>数据导入</strong>
<ul>
<li>将FASTQ文件导入到QIIME2中。</li>
</ul>
</li>
<li><strong>质量过滤</strong>
<ul>
<li>使用DADA2或Deblur等工具进行质量过滤和去噪。</li>
</ul>
</li>
<li><strong>特征表构建</strong>
<ul>
<li>构建特征表，统计每个样本中每个OTU (Operational Taxonomic Unit) 或ASV (Amplicon Sequence Variant) 的数量。</li>
</ul>
</li>
<li><strong>分类学注释</strong>
<ul>
<li>使用Greengenes、Silva或RDP等数据库，对OTU或ASV进行分类学注释。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>多样性分析</strong></p>
<ul>
<li><strong>α多样性计算</strong>
<ul>
<li>使用QIIME2计算α多样性指数，如Shannon指数、Simpson指数等。</li>
</ul>
</li>
<li><strong>β多样性计算</strong>
<ul>
<li>使用QIIME2计算β多样性距离，如Bray-Curtis距离、UniFrac距离等。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>差异丰度分析</strong></p>
<ul>
<li>使用ANCOM或ALDEx2等工具，寻找在不同组别之间丰度存在显著差异的OTU或ASV。</li>
</ul>
</li>
<li>
<p><strong>简单演示</strong></p>
<ol>
<li>下载扩增子测序数据。</li>
<li>使用QIIME2进行数据分析。</li>
<li>计算多样性指数。</li>
<li>进行差异丰度分析.</li>
</ol>
</li>
</ul>
<h3 id="2-%E5%AE%8F%E5%9F%BA%E5%9B%A0%E7%BB%84%E5%8A%9F%E8%83%BD%E5%88%86%E6%9E%90">2. 宏基因组功能分析</h3>
<ul>
<li>
<p><strong>功能注释流程</strong></p>
<ul>
<li>使用Prodigal等工具进行基因预测。</li>
<li>使用DIAMOND等工具进行序列比对。</li>
<li>使用KEGG等数据库进行功能注释。</li>
</ul>
</li>
<li>
<p><strong>微生物功能预测</strong></p>
<ul>
<li>根据基因注释结果，预测微生物群落的功能。</li>
</ul>
</li>
<li>
<p><strong>有益功能筛选</strong></p>
<ul>
<li>筛选具有有益功能的微生物，如固氮菌、解磷菌等。</li>
</ul>
</li>
</ul>
<h3 id="3-%E5%BE%AE%E7%94%9F%E7%89%A9%E4%BA%92%E4%BD%9C%E7%BD%91%E7%BB%9C">3. 微生物互作网络</h3>
<ul>
<li>
<p><strong>网络构建方法</strong></p>
<ul>
<li>使用SparCC或SPIEC-EASI等工具，构建微生物互作网络。</li>
</ul>
</li>
<li>
<p><strong>关键物种识别</strong></p>
<ul>
<li>识别在网络中起关键作用的物种，如hub物种、connector物种等。</li>
</ul>
</li>
<li>
<p><strong>网络特性分析</strong></p>
<ul>
<li>分析网络的拓扑结构，了解网络的复杂性和稳定性。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>田间微生物多样性分析是植物保护研究的重要手段。通过分析田间微生物的组成、功能和互作关系，可以了解微生物对植物生长和抗病能力的影响，为植物保护提供科学依据.</p>
<h2 id="%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96%E4%B8%8E%E7%BB%93%E6%9E%9C%E8%A7%A3%E8%AF%BB">数据可视化与结果解读</h2>
<h3 id="1-%E7%BB%BC%E5%90%88%E6%80%A7%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96">1. 综合性数据可视化</h3>
<h4 id="%E5%A4%9A%E7%BB%B4%E5%BA%A6%E6%95%B0%E6%8D%AE%E6%95%B4%E5%90%88%E5%8F%AF%E8%A7%86%E5%8C%96">多维度数据整合可视化</h4>
<pre class="hljs"><code><div><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-comment"># comprehensive_visualization.py</span>

<span class="hljs-keyword">import</span> matplotlib.pyplot <span class="hljs-keyword">as</span> plt
<span class="hljs-keyword">import</span> seaborn <span class="hljs-keyword">as</span> sns
<span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
<span class="hljs-keyword">import</span> numpy <span class="hljs-keyword">as</span> np
<span class="hljs-keyword">from</span> matplotlib.patches <span class="hljs-keyword">import</span> Rectangle
<span class="hljs-keyword">import</span> plotly.graph_objects <span class="hljs-keyword">as</span> go
<span class="hljs-keyword">from</span> plotly.subplots <span class="hljs-keyword">import</span> make_subplots
<span class="hljs-keyword">import</span> plotly.express <span class="hljs-keyword">as</span> px
<span class="hljs-keyword">import</span> os

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_comprehensive_dashboard</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""创建综合分析仪表板"""</span>
    
    base_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis"</span>)
    
    <span class="hljs-comment"># 设置中文字体</span>
    plt.rcParams[<span class="hljs-string">'font.sans-serif'</span>] = [<span class="hljs-string">'SimHei'</span>, <span class="hljs-string">'DejaVu Sans'</span>]
    plt.rcParams[<span class="hljs-string">'axes.unicode_minus'</span>] = <span class="hljs-literal">False</span>
    
    <span class="hljs-comment"># 创建大型综合图表</span>
    fig = plt.figure(figsize=(<span class="hljs-number">20</span>, <span class="hljs-number">16</span>))
    
    <span class="hljs-comment"># 1. 基因组组装质量摘要 (左上)</span>
    ax1 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, <span class="hljs-number">1</span>)
    create_assembly_quality_plot(ax1, base_dir)
    
    <span class="hljs-comment"># 2. 基因功能分类 (右上)</span>
    ax2 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, <span class="hljs-number">2</span>)
    create_functional_classification_plot(ax2, base_dir)
    
    <span class="hljs-comment"># 3. 系统发育关系 (左中)</span>
    ax3 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, (<span class="hljs-number">3</span>, <span class="hljs-number">4</span>))
    create_phylogeny_plot(ax3, base_dir)
    
    <span class="hljs-comment"># 4. 转录组差异表达 (左下)</span>
    ax4 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, (<span class="hljs-number">5</span>, <span class="hljs-number">6</span>))
    create_expression_heatmap(ax4, base_dir)
    
    <span class="hljs-comment"># 5. 微生物多样性 (右中)</span>
    ax5 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, (<span class="hljs-number">7</span>, <span class="hljs-number">8</span>))
    create_diversity_plot(ax5, base_dir)
    
    <span class="hljs-comment"># 6. 抗性基因分布 (左下)</span>
    ax6 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, <span class="hljs-number">9</span>)
    create_resistance_plot(ax6, base_dir)
    
    <span class="hljs-comment"># 7. 病原体鉴定结果 (中下)</span>
    ax7 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, <span class="hljs-number">10</span>)
    create_identification_plot(ax7, base_dir)
    
    <span class="hljs-comment"># 8. 质量控制统计 (右下)</span>
    ax8 = plt.subplot(<span class="hljs-number">3</span>, <span class="hljs-number">4</span>, (<span class="hljs-number">11</span>, <span class="hljs-number">12</span>))
    create_qc_summary_plot(ax8, base_dir)
    
    plt.tight_layout()
    plt.savefig(<span class="hljs-string">f"<span class="hljs-subst">{base_dir}</span>/results/comprehensive_analysis_dashboard.png"</span>, 
               dpi=<span class="hljs-number">300</span>, bbox_inches=<span class="hljs-string">'tight'</span>, facecolor=<span class="hljs-string">'white'</span>)
    plt.close()
    
    print(<span class="hljs-string">"综合分析仪表板已创建: ~/plant_protection_analysis/results/comprehensive_analysis_dashboard.png"</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_assembly_quality_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建组装质量摘要图"""</span>
    <span class="hljs-comment"># 模拟组装统计数据</span>
    metrics = [<span class="hljs-string">'Contigs数'</span>, <span class="hljs-string">'N50'</span>, <span class="hljs-string">'总长度'</span>, <span class="hljs-string">'GC含量'</span>, <span class="hljs-string">'BUSCO完整性'</span>]
    values = [<span class="hljs-number">156</span>, <span class="hljs-number">45000</span>, <span class="hljs-number">4200000</span>, <span class="hljs-number">52.3</span>, <span class="hljs-number">95.2</span>]
    colors = [<span class="hljs-string">'#1f77b4'</span>, <span class="hljs-string">'#ff7f0e'</span>, <span class="hljs-string">'#2ca02c'</span>, <span class="hljs-string">'#d62728'</span>, <span class="hljs-string">'#9467bd'</span>]
    
    bars = ax.bar(metrics, values, color=colors, alpha=<span class="hljs-number">0.7</span>)
    ax.set_title(<span class="hljs-string">'基因组组装质量指标'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    ax.set_ylabel(<span class="hljs-string">'数值'</span>)
    
    <span class="hljs-comment"># 添加数值标签</span>
    <span class="hljs-keyword">for</span> bar, value <span class="hljs-keyword">in</span> zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/<span class="hljs-number">2.</span>, height + height*<span class="hljs-number">0.01</span>,
                <span class="hljs-string">f'<span class="hljs-subst">{value:,<span class="hljs-number">.0</span>f}</span>'</span> <span class="hljs-keyword">if</span> value &gt; <span class="hljs-number">1000</span> <span class="hljs-keyword">else</span> <span class="hljs-string">f'<span class="hljs-subst">{value:<span class="hljs-number">.1</span>f}</span>'</span>,
                ha=<span class="hljs-string">'center'</span>, va=<span class="hljs-string">'bottom'</span>, fontsize=<span class="hljs-number">8</span>)
    
    ax.tick_params(axis=<span class="hljs-string">'x'</span>, rotation=<span class="hljs-number">45</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_functional_classification_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建功能分类饼图"""</span>
    <span class="hljs-comment"># 模拟功能分类数据</span>
    categories = [<span class="hljs-string">'代谢'</span>, <span class="hljs-string">'信息处理'</span>, <span class="hljs-string">'环境应答'</span>, <span class="hljs-string">'细胞过程'</span>, <span class="hljs-string">'未知功能'</span>]
    sizes = [<span class="hljs-number">35</span>, <span class="hljs-number">25</span>, <span class="hljs-number">15</span>, <span class="hljs-number">10</span>, <span class="hljs-number">15</span>]
    colors = [<span class="hljs-string">'#ff9999'</span>, <span class="hljs-string">'#66b3ff'</span>, <span class="hljs-string">'#99ff99'</span>, <span class="hljs-string">'#ffcc99'</span>, <span class="hljs-string">'#ffb3e6'</span>]
    
    wedges, texts, autotexts = ax.pie(sizes, labels=categories, colors=colors, 
                                     autopct=<span class="hljs-string">'%1.1f%%'</span>, startangle=<span class="hljs-number">90</span>)
    ax.set_title(<span class="hljs-string">'基因功能分类分布'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_phylogeny_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建系统发育关系图"""</span>
    <span class="hljs-comment"># 模拟系统发育数据</span>
    species = [<span class="hljs-string">'目标病原体'</span>, <span class="hljs-string">'参考株1'</span>, <span class="hljs-string">'参考株2'</span>, <span class="hljs-string">'参考株3'</span>, <span class="hljs-string">'参考株4'</span>]
    distances = np.array([[<span class="hljs-number">0</span>, <span class="hljs-number">0.05</span>, <span class="hljs-number">0.12</span>, <span class="hljs-number">0.18</span>, <span class="hljs-number">0.25</span>],
                         [<span class="hljs-number">0.05</span>, <span class="hljs-number">0</span>, <span class="hljs-number">0.08</span>, <span class="hljs-number">0.15</span>, <span class="hljs-number">0.22</span>],
                         [<span class="hljs-number">0.12</span>, <span class="hljs-number">0.08</span>, <span class="hljs-number">0</span>, <span class="hljs-number">0.10</span>, <span class="hljs-number">0.20</span>],
                         [<span class="hljs-number">0.18</span>, <span class="hljs-number">0.15</span>, <span class="hljs-number">0.10</span>, <span class="hljs-number">0</span>, <span class="hljs-number">0.12</span>],
                         [<span class="hljs-number">0.25</span>, <span class="hljs-number">0.22</span>, <span class="hljs-number">0.20</span>, <span class="hljs-number">0.12</span>, <span class="hljs-number">0</span>]])
    
    im = ax.imshow(distances, cmap=<span class="hljs-string">'RdYlBu_r'</span>, aspect=<span class="hljs-string">'auto'</span>)
    ax.set_xticks(range(len(species)))
    ax.set_yticks(range(len(species)))
    ax.set_xticklabels(species, rotation=<span class="hljs-number">45</span>, ha=<span class="hljs-string">'right'</span>)
    ax.set_yticklabels(species)
    ax.set_title(<span class="hljs-string">'系统发育距离矩阵'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    
    <span class="hljs-comment"># 添加颜色条</span>
    plt.colorbar(im, ax=ax, label=<span class="hljs-string">'遗传距离'</span>, shrink=<span class="hljs-number">0.8</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_expression_heatmap</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建表达量热图"""</span>
    <span class="hljs-comment"># 模拟表达数据</span>
    genes = [<span class="hljs-string">f'Gene_<span class="hljs-subst">{i}</span>'</span> <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(<span class="hljs-number">1</span>, <span class="hljs-number">21</span>)]
    samples = [<span class="hljs-string">'对照1'</span>, <span class="hljs-string">'对照2'</span>, <span class="hljs-string">'处理1'</span>, <span class="hljs-string">'处理2'</span>, <span class="hljs-string">'处理3'</span>]
    
    <span class="hljs-comment"># 生成模拟表达数据</span>
    np.random.seed(<span class="hljs-number">42</span>)
    expression_data = np.random.lognormal(<span class="hljs-number">0</span>, <span class="hljs-number">1</span>, (len(genes), len(samples)))
    
    im = ax.imshow(expression_data, cmap=<span class="hljs-string">'RdBu_r'</span>, aspect=<span class="hljs-string">'auto'</span>)
    ax.set_xticks(range(len(samples)))
    ax.set_yticks(range(<span class="hljs-number">0</span>, len(genes), <span class="hljs-number">5</span>))
    ax.set_xticklabels(samples)
    ax.set_yticklabels([genes[i] <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(<span class="hljs-number">0</span>, len(genes), <span class="hljs-number">5</span>)])
    ax.set_title(<span class="hljs-string">'差异表达基因热图'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    ax.set_xlabel(<span class="hljs-string">'样本'</span>)
    ax.set_ylabel(<span class="hljs-string">'基因'</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_diversity_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建微生物多样性图"""</span>
    <span class="hljs-comment"># 模拟多样性数据</span>
    samples = [<span class="hljs-string">'田间1'</span>, <span class="hljs-string">'田间2'</span>, <span class="hljs-string">'田间3'</span>, <span class="hljs-string">'田间4'</span>, <span class="hljs-string">'田间5'</span>, <span class="hljs-string">'田间6'</span>]
    shannon_index = [<span class="hljs-number">2.5</span>, <span class="hljs-number">2.8</span>, <span class="hljs-number">2.2</span>, <span class="hljs-number">3.1</span>, <span class="hljs-number">2.9</span>, <span class="hljs-number">2.6</span>]
    simpson_index = [<span class="hljs-number">0.85</span>, <span class="hljs-number">0.90</span>, <span class="hljs-number">0.78</span>, <span class="hljs-number">0.94</span>, <span class="hljs-number">0.88</span>, <span class="hljs-number">0.82</span>]
    
    ax2 = ax.twinx()
    
    bars1 = ax.bar([x - <span class="hljs-number">0.2</span> <span class="hljs-keyword">for</span> x <span class="hljs-keyword">in</span> range(len(samples))], shannon_index, 
                   width=<span class="hljs-number">0.4</span>, label=<span class="hljs-string">'Shannon指数'</span>, alpha=<span class="hljs-number">0.7</span>, color=<span class="hljs-string">'skyblue'</span>)
    bars2 = ax2.bar([x + <span class="hljs-number">0.2</span> <span class="hljs-keyword">for</span> x <span class="hljs-keyword">in</span> range(len(samples))], simpson_index, 
                    width=<span class="hljs-number">0.4</span>, label=<span class="hljs-string">'Simpson指数'</span>, alpha=<span class="hljs-number">0.7</span>, color=<span class="hljs-string">'lightcoral'</span>)
    
    ax.set_xlabel(<span class="hljs-string">'样本'</span>)
    ax.set_ylabel(<span class="hljs-string">'Shannon指数'</span>, color=<span class="hljs-string">'blue'</span>)
    ax2.set_ylabel(<span class="hljs-string">'Simpson指数'</span>, color=<span class="hljs-string">'red'</span>)
    ax.set_title(<span class="hljs-string">'微生物多样性指数'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    ax.set_xticks(range(len(samples)))
    ax.set_xticklabels(samples, rotation=<span class="hljs-number">45</span>)
    
    <span class="hljs-comment"># 添加图例</span>
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, loc=<span class="hljs-string">'upper left'</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_resistance_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建抗性基因分布图"""</span>
    <span class="hljs-comment"># 模拟抗性基因数据</span>
    resistance_types = [<span class="hljs-string">'β-内酰胺'</span>, <span class="hljs-string">'氨基糖苷'</span>, <span class="hljs-string">'喹诺酮'</span>, <span class="hljs-string">'四环素'</span>, <span class="hljs-string">'氯霉素'</span>]
    gene_counts = [<span class="hljs-number">3</span>, <span class="hljs-number">2</span>, <span class="hljs-number">1</span>, <span class="hljs-number">4</span>, <span class="hljs-number">1</span>]
    
    bars = ax.barh(resistance_types, gene_counts, color=<span class="hljs-string">'orange'</span>, alpha=<span class="hljs-number">0.7</span>)
    ax.set_xlabel(<span class="hljs-string">'基因数量'</span>)
    ax.set_title(<span class="hljs-string">'抗性基因类型分布'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    
    <span class="hljs-comment"># 添加数值标签</span>
    <span class="hljs-keyword">for</span> bar, count <span class="hljs-keyword">in</span> zip(bars, gene_counts):
        width = bar.get_width()
        ax.text(width + <span class="hljs-number">0.1</span>, bar.get_y() + bar.get_height()/<span class="hljs-number">2</span>, 
                str(count), ha=<span class="hljs-string">'left'</span>, va=<span class="hljs-string">'center'</span>, fontsize=<span class="hljs-number">10</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_identification_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建病原体鉴定结果图"""</span>
    <span class="hljs-comment"># 模拟鉴定相似度数据</span>
    top_matches = [<span class="hljs-string">'Escherichia coli'</span>, <span class="hljs-string">'Klebsiella pneumoniae'</span>, 
                   <span class="hljs-string">'Enterobacter cloacae'</span>, <span class="hljs-string">'Serratia marcescens'</span>]
    similarities = [<span class="hljs-number">98.5</span>, <span class="hljs-number">85.2</span>, <span class="hljs-number">82.1</span>, <span class="hljs-number">78.9</span>]
    
    bars = ax.bar(range(len(top_matches)), similarities, 
                 color=[<span class="hljs-string">'red'</span>, <span class="hljs-string">'orange'</span>, <span class="hljs-string">'yellow'</span>, <span class="hljs-string">'lightblue'</span>], alpha=<span class="hljs-number">0.7</span>)
    ax.set_xlabel(<span class="hljs-string">'候选物种'</span>)
    ax.set_ylabel(<span class="hljs-string">'16S rRNA相似度 (%)'</span>)
    ax.set_title(<span class="hljs-string">'病原体鉴定结果'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    ax.set_xticks(range(len(top_matches)))
    ax.set_xticklabels(top_matches, rotation=<span class="hljs-number">45</span>, ha=<span class="hljs-string">'right'</span>)
    ax.set_ylim(<span class="hljs-number">70</span>, <span class="hljs-number">100</span>)
    
    <span class="hljs-comment"># 添加阈值线</span>
    ax.axhline(y=<span class="hljs-number">97</span>, color=<span class="hljs-string">'red'</span>, linestyle=<span class="hljs-string">'--'</span>, alpha=<span class="hljs-number">0.5</span>, label=<span class="hljs-string">'种水平阈值'</span>)
    ax.axhline(y=<span class="hljs-number">95</span>, color=<span class="hljs-string">'orange'</span>, linestyle=<span class="hljs-string">'--'</span>, alpha=<span class="hljs-number">0.5</span>, label=<span class="hljs-string">'属水平阈值'</span>)
    ax.legend()

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_qc_summary_plot</span><span class="hljs-params">(ax, base_dir)</span>:</span>
    <span class="hljs-string">"""创建质量控制摘要图"""</span>
    <span class="hljs-comment"># 模拟QC数据</span>
    qc_steps = [<span class="hljs-string">'原始数据'</span>, <span class="hljs-string">'质量过滤'</span>, <span class="hljs-string">'宿主去除'</span>, <span class="hljs-string">'最终数据'</span>]
    read_counts = [<span class="hljs-number">1000000</span>, <span class="hljs-number">950000</span>, <span class="hljs-number">850000</span>, <span class="hljs-number">820000</span>]
    
    <span class="hljs-comment"># 创建瀑布图效果</span>
    colors = [<span class="hljs-string">'blue'</span>, <span class="hljs-string">'green'</span>, <span class="hljs-string">'orange'</span>, <span class="hljs-string">'red'</span>]
    bars = ax.bar(qc_steps, read_counts, color=colors, alpha=<span class="hljs-number">0.7</span>)
    
    ax.set_ylabel(<span class="hljs-string">'Reads数量'</span>)
    ax.set_title(<span class="hljs-string">'数据质量控制流程'</span>, fontsize=<span class="hljs-number">12</span>, fontweight=<span class="hljs-string">'bold'</span>)
    ax.tick_params(axis=<span class="hljs-string">'x'</span>, rotation=<span class="hljs-number">45</span>)
    
    <span class="hljs-comment"># 添加数值标签</span>
    <span class="hljs-keyword">for</span> bar, count <span class="hljs-keyword">in</span> zip(bars, read_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/<span class="hljs-number">2.</span>, height + height*<span class="hljs-number">0.01</span>,
                <span class="hljs-string">f'<span class="hljs-subst">{count:,}</span>'</span>, ha=<span class="hljs-string">'center'</span>, va=<span class="hljs-string">'bottom'</span>, fontsize=<span class="hljs-number">9</span>)
    
    <span class="hljs-comment"># 添加损失百分比</span>
    <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(<span class="hljs-number">1</span>, len(read_counts)):
        loss_percent = (read_counts[i<span class="hljs-number">-1</span>] - read_counts[i]) / read_counts[i<span class="hljs-number">-1</span>] * <span class="hljs-number">100</span>
        ax.annotate(<span class="hljs-string">f'-<span class="hljs-subst">{loss_percent:<span class="hljs-number">.1</span>f}</span>%'</span>, 
                   xy=(i<span class="hljs-number">-0.5</span>, (read_counts[i<span class="hljs-number">-1</span>] + read_counts[i])/<span class="hljs-number">2</span>),
                   ha=<span class="hljs-string">'center'</span>, va=<span class="hljs-string">'center'</span>, fontsize=<span class="hljs-number">8</span>, color=<span class="hljs-string">'red'</span>)

<span class="hljs-comment">### 2. 交互式可视化</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_interactive_plots</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""创建交互式图表"""</span>
    
    base_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis"</span>)
    
    <span class="hljs-comment"># 创建交互式仪表板</span>
    fig = make_subplots(
        rows=<span class="hljs-number">2</span>, cols=<span class="hljs-number">2</span>,
        subplot_titles=(<span class="hljs-string">'基因组组装统计'</span>, <span class="hljs-string">'功能分类分布'</span>, <span class="hljs-string">'表达量分析'</span>, <span class="hljs-string">'多样性趋势'</span>),
        specs=[[{<span class="hljs-string">"secondary_y"</span>: <span class="hljs-literal">False</span>}, {<span class="hljs-string">"type"</span>: <span class="hljs-string">"pie"</span>}],
               [{<span class="hljs-string">"secondary_y"</span>: <span class="hljs-literal">True</span>}, {<span class="hljs-string">"secondary_y"</span>: <span class="hljs-literal">False</span>}]]
    )
    
    <span class="hljs-comment"># 1. 组装统计柱状图</span>
    assembly_metrics = [<span class="hljs-string">'Contigs'</span>, <span class="hljs-string">'N50'</span>, <span class="hljs-string">'Total_Length'</span>, <span class="hljs-string">'GC_Content'</span>, <span class="hljs-string">'BUSCO'</span>]
    assembly_values = [<span class="hljs-number">156</span>, <span class="hljs-number">45000</span>, <span class="hljs-number">4200000</span>, <span class="hljs-number">52.3</span>, <span class="hljs-number">95.2</span>]
    
    fig.add_trace(
        go.Bar(x=assembly_metrics, y=assembly_values, name=<span class="hljs-string">"组装指标"</span>,
               marker_color=<span class="hljs-string">'lightblue'</span>),
        row=<span class="hljs-number">1</span>, col=<span class="hljs-number">1</span>
    )
    
    <span class="hljs-comment"># 2. 功能分类饼图</span>
    func_categories = [<span class="hljs-string">'代谢'</span>, <span class="hljs-string">'信息处理'</span>, <span class="hljs-string">'环境应答'</span>, <span class="hljs-string">'细胞过程'</span>, <span class="hljs-string">'未知'</span>]
    func_values = [<span class="hljs-number">35</span>, <span class="hljs-number">25</span>, <span class="hljs-number">15</span>, <span class="hljs-number">10</span>, <span class="hljs-number">15</span>]
    
    fig.add_trace(
        go.Pie(labels=func_categories, values=func_values, name=<span class="hljs-string">"功能分类"</span>),
        row=<span class="hljs-number">1</span>, col=<span class="hljs-number">2</span>
    )
    
    <span class="hljs-comment"># 3. 表达量时间序列</span>
    timepoints = [<span class="hljs-string">'0h'</span>, <span class="hljs-string">'6h'</span>, <span class="hljs-string">'12h'</span>, <span class="hljs-string">'24h'</span>, <span class="hljs-string">'48h'</span>]
    gene1_expr = [<span class="hljs-number">1.0</span>, <span class="hljs-number">2.5</span>, <span class="hljs-number">4.2</span>, <span class="hljs-number">3.8</span>, <span class="hljs-number">2.1</span>]
    gene2_expr = [<span class="hljs-number">1.0</span>, <span class="hljs-number">0.8</span>, <span class="hljs-number">1.5</span>, <span class="hljs-number">2.8</span>, <span class="hljs-number">4.5</span>]
    
    fig.add_trace(
        go.Scatter(x=timepoints, y=gene1_expr, mode=<span class="hljs-string">'lines+markers'</span>, 
                  name=<span class="hljs-string">'防御基因1'</span>, line=dict(color=<span class="hljs-string">'red'</span>)),
        row=<span class="hljs-number">2</span>, col=<span class="hljs-number">1</span>
    )
    fig.add_trace(
        go.Scatter(x=timepoints, y=gene2_expr, mode=<span class="hljs-string">'lines+markers'</span>, 
                  name=<span class="hljs-string">'防御基因2'</span>, line=dict(color=<span class="hljs-string">'blue'</span>)),
        row=<span class="hljs-number">2</span>, col=<span class="hljs-number">1</span>
    )
    
    <span class="hljs-comment"># 4. 多样性指数</span>
    samples = [<span class="hljs-string">'Sample1'</span>, <span class="hljs-string">'Sample2'</span>, <span class="hljs-string">'Sample3'</span>, <span class="hljs-string">'Sample4'</span>, <span class="hljs-string">'Sample5'</span>]
    shannon = [<span class="hljs-number">2.5</span>, <span class="hljs-number">2.8</span>, <span class="hljs-number">2.2</span>, <span class="hljs-number">3.1</span>, <span class="hljs-number">2.9</span>]
    
    fig.add_trace(
        go.Bar(x=samples, y=shannon, name=<span class="hljs-string">"Shannon指数"</span>, 
               marker_color=<span class="hljs-string">'green'</span>),
        row=<span class="hljs-number">2</span>, col=<span class="hljs-number">2</span>
    )
    
    <span class="hljs-comment"># 更新布局</span>
    fig.update_layout(
        title_text=<span class="hljs-string">"植物保护高通量测序分析交互式仪表板"</span>,
        showlegend=<span class="hljs-literal">True</span>,
        height=<span class="hljs-number">800</span>
    )
    
    <span class="hljs-comment"># 保存HTML文件</span>
    fig.write_html(<span class="hljs-string">f"<span class="hljs-subst">{base_dir}</span>/results/interactive_dashboard.html"</span>)
    print(<span class="hljs-string">"交互式仪表板已创建: ~/plant_protection_analysis/results/interactive_dashboard.html"</span>)

<span class="hljs-comment">### 3. 结果解读指南</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">generate_analysis_report</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""生成分析报告"""</span>
    
    base_dir = os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis"</span>)
    report_file = <span class="hljs-string">f"<span class="hljs-subst">{base_dir}</span>/results/analysis_report.md"</span>
    
    report_content = <span class="hljs-string">"""
# 植物保护高通量测序分析报告

## 执行摘要

本报告总结了植物病原体基因组测序、组装、注释和比较分析的主要结果。

## 主要发现

### 1. 基因组特征
- **基因组大小**: 约4.2 Mb
- **GC含量**: 52.3%
- **基因总数**: 3,856个
- **编码基因**: 3,672个 (95.2%)
- **tRNA基因**: 76个
- **rRNA基因**: 22个

### 2. 病原体鉴定
根据16S rRNA基因序列分析，目标病原体与以下物种最为相似：
1. *Escherichia coli* (98.5%相似度)
2. *Klebsiella pneumoniae* (85.2%相似度)

**结论**: 目标病原体很可能是大肠杆菌的一个分离株。

### 3. 毒力和抗性特征
识别到以下关键特征：
- **毒力因子**: 15个候选毒力基因
- **抗性基因**: 11个抗生素抗性基因
- **分泌系统**: III型分泌系统完整

### 4. 功能分析
基因功能分类显示：
- 代谢相关基因占35%
- 信息处理基因占25%
- 环境应答基因占15%

### 5. 系统发育关系
系统发育分析表明：
- 与已知病原菌株关系密切
- 可能存在水平基因转移事件
- 建议进一步进行全基因组比较

## 建议

### 短期建议
1. 进行抗生素敏感性测试
2. 验证关键毒力基因的表达
3. 分析宿主特异性

### 长期建议
1. 建立监测体系
2. 开发特异性检测方法
3. 研究防控策略

## 技术局限性

1. 基因组组装可能存在gap
2. 功能注释依赖于已有数据库
3. 需要实验验证计算预测结果

## 参考文献

[这里可以添加相关参考文献]

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析人员: 植物保护实验室
"""</span>
    
    <span class="hljs-keyword">from</span> datetime <span class="hljs-keyword">import</span> datetime
    
    <span class="hljs-keyword">with</span> open(report_file, <span class="hljs-string">'w'</span>, encoding=<span class="hljs-string">'utf-8'</span>) <span class="hljs-keyword">as</span> f:
        f.write(report_content.format(datetime=datetime))
    
    print(<span class="hljs-string">f"分析报告已生成: <span class="hljs-subst">{report_file}</span>"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    <span class="hljs-comment"># 创建结果目录</span>
    os.makedirs(os.path.expanduser(<span class="hljs-string">"~/plant_protection_analysis/results"</span>), exist_ok=<span class="hljs-literal">True</span>)
    
    <span class="hljs-comment"># 生成可视化</span>
    create_comprehensive_dashboard()
    create_interactive_plots()
    generate_analysis_report()
    
    print(<span class="hljs-string">"\n=== 数据可视化和报告生成完成 ==="</span>)
    print(<span class="hljs-string">"请查看以下文件:"</span>)
    print(<span class="hljs-string">"1. 综合分析仪表板: ~/plant_protection_analysis/results/comprehensive_analysis_dashboard.png"</span>)
    print(<span class="hljs-string">"2. 交互式仪表板: ~/plant_protection_analysis/results/interactive_dashboard.html"</span>)
    print(<span class="hljs-string">"3. 分析报告: ~/plant_protection_analysis/results/analysis_report.md"</span>)
</div></code></pre>
<h2 id="%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E4%B8%8E%E6%95%85%E9%9A%9C%E6%8E%92%E9%99%A4">常见问题与故障排除</h2>
<h3 id="1-%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE%E9%97%AE%E9%A2%98">1. 环境配置问题</h3>
<h4 id="conda%E7%8E%AF%E5%A2%83%E5%86%B2%E7%AA%81">Conda环境冲突</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 问题：包版本冲突</span>
<span class="hljs-comment"># 解决方案：创建独立环境</span>
conda create -n plant_protection_clean python=3.8 -y
conda activate plant_protection_clean

<span class="hljs-comment"># 逐步安装，避免冲突</span>
conda install -c bioconda fastqc -y
conda install -c bioconda trimmomatic -y
<span class="hljs-comment"># ... 其他软件</span>
</div></code></pre>
<h4 id="%E5%86%85%E5%AD%98%E4%B8%8D%E8%B6%B3%E9%97%AE%E9%A2%98">内存不足问题</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 问题：组装时内存不足</span>
<span class="hljs-comment"># 解决方案：调整参数或使用部分数据</span>

<span class="hljs-comment"># SPAdes内存限制</span>
spades.py --pe1-1 R1.fastq.gz --pe1-2 R2.fastq.gz \
    -o output_dir \
    --memory 16  <span class="hljs-comment"># 限制内存使用</span>
    --threads 4   <span class="hljs-comment"># 减少线程数</span>

<span class="hljs-comment"># 或者对数据进行降采样</span>
seqtk sample -s100 input.fastq.gz 0.5 &gt; sampled.fastq.gz
</div></code></pre>
<h3 id="2-%E6%95%B0%E6%8D%AE%E8%B4%A8%E9%87%8F%E9%97%AE%E9%A2%98">2. 数据质量问题</h3>
<h4 id="%E4%BD%8E%E8%B4%A8%E9%87%8F%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86">低质量数据处理</h4>
<pre class="hljs"><code><div><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-comment"># quality_assessment.py</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">assess_data_quality</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""评估数据质量并提供建议"""</span>
    
    <span class="hljs-keyword">import</span> os
    <span class="hljs-keyword">from</span> Bio <span class="hljs-keyword">import</span> SeqIO
    <span class="hljs-keyword">import</span> matplotlib.pyplot <span class="hljs-keyword">as</span> plt
    
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">analyze_fastq_quality</span><span class="hljs-params">(fastq_file)</span>:</span>
        <span class="hljs-string">"""分析FASTQ文件质量"""</span>
        qualities = []
        lengths = []
        
        <span class="hljs-keyword">with</span> open(fastq_file, <span class="hljs-string">'r'</span>) <span class="hljs-keyword">as</span> f:
            <span class="hljs-keyword">for</span> i, line <span class="hljs-keyword">in</span> enumerate(f):
                <span class="hljs-keyword">if</span> i % <span class="hljs-number">4</span> == <span class="hljs-number">1</span>:  <span class="hljs-comment"># 序列行</span>
                    lengths.append(len(line.strip()))
                <span class="hljs-keyword">elif</span> i % <span class="hljs-number">4</span> == <span class="hljs-number">3</span>:  <span class="hljs-comment"># 质量行</span>
                    quality_scores = [ord(c) - <span class="hljs-number">33</span> <span class="hljs-keyword">for</span> c <span class="hljs-keyword">in</span> line.strip()]
                    qualities.extend(quality_scores)
        
        avg_quality = sum(qualities) / len(qualities)
        avg_length = sum(lengths) / len(lengths)
        
        <span class="hljs-keyword">return</span> avg_quality, avg_length, len(lengths)
    
    <span class="hljs-comment"># 分析示例</span>
    fastq_file = <span class="hljs-string">"~/plant_protection_analysis/raw_data/genome/pathogen_R1.fastq.gz"</span>
    <span class="hljs-keyword">if</span> os.path.exists(os.path.expanduser(fastq_file)):
        quality, length, read_count = analyze_fastq_quality(fastq_file)
        
        print(<span class="hljs-string">"=== 数据质量评估 ==="</span>)
        print(<span class="hljs-string">f"平均质量分数: <span class="hljs-subst">{quality:<span class="hljs-number">.1</span>f}</span>"</span>)
        print(<span class="hljs-string">f"平均读长: <span class="hljs-subst">{length:<span class="hljs-number">.0</span>f}</span> bp"</span>)
        print(<span class="hljs-string">f"总reads数: <span class="hljs-subst">{read_count:,}</span>"</span>)
        
        <span class="hljs-comment"># 质量建议</span>
        <span class="hljs-keyword">if</span> quality &lt; <span class="hljs-number">20</span>:
            print(<span class="hljs-string">"警告: 数据质量较低，建议进行严格的质量过滤"</span>)
        <span class="hljs-keyword">elif</span> quality &lt; <span class="hljs-number">30</span>:
            print(<span class="hljs-string">"提示: 数据质量中等，建议适度质量过滤"</span>)
        <span class="hljs-keyword">else</span>:
            print(<span class="hljs-string">"良好: 数据质量较高"</span>)
            
        <span class="hljs-keyword">if</span> length &lt; <span class="hljs-number">100</span>:
            print(<span class="hljs-string">"警告: 读长较短，可能影响组装质量"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    assess_data_quality()
</div></code></pre>
<h4 id="%E6%B1%A1%E6%9F%93%E6%A3%80%E6%B5%8B%E5%92%8C%E5%A4%84%E7%90%86">污染检测和处理</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># contamination_check.sh</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 污染检测流程 ==="</span>

<span class="hljs-comment"># 1. 检测人类DNA污染</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"检测人类DNA污染..."</span>
bowtie2 -x /path/to/human_genome \
    -1 input_R1.fastq.gz -2 input_R2.fastq.gz \
    --threads 8 --very-fast --un-conc clean_reads.fastq.gz

<span class="hljs-comment"># 2. 检测载体污染</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"检测载体污染..."</span>
blastn -query contigs.fasta \
    -db /path/to/vector_db \
    -outfmt 6 \
    -out vector_contamination.tsv

<span class="hljs-comment"># 3. 生成污染报告</span>
python3 &lt;&lt; <span class="hljs-string">'EOF'</span>
import pandas as pd

<span class="hljs-comment"># 分析载体污染结果</span>
try:
    blast_df = pd.read_csv(<span class="hljs-string">'vector_contamination.tsv'</span>, sep=<span class="hljs-string">'\t'</span>, 
                          names=[<span class="hljs-string">'qseqid'</span>, <span class="hljs-string">'sseqid'</span>, <span class="hljs-string">'pident'</span>, <span class="hljs-string">'length'</span>, 
                                <span class="hljs-string">'mismatch'</span>, <span class="hljs-string">'gapopen'</span>, <span class="hljs-string">'qstart'</span>, <span class="hljs-string">'qend'</span>, 
                                <span class="hljs-string">'sstart'</span>, <span class="hljs-string">'send'</span>, <span class="hljs-string">'evalue'</span>, <span class="hljs-string">'bitscore'</span>])
    
    <span class="hljs-keyword">if</span> len(blast_df) &gt; 0:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"警告: 发现载体序列污染"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"污染contigs数量: {blast_df['qseqid'].nunique()}"</span>)
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"建议移除这些序列"</span>)
    <span class="hljs-keyword">else</span>:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"未发现载体污染"</span>)
except:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到BLAST结果文件"</span>)
EOF
</div></code></pre>
<h3 id="3-%E5%88%86%E6%9E%90%E6%B5%81%E7%A8%8B%E9%97%AE%E9%A2%98">3. 分析流程问题</h3>
<h4 id="%E7%BB%84%E8%A3%85%E5%A4%B1%E8%B4%A5%E5%A4%84%E7%90%86">组装失败处理</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># assembly_troubleshooting.sh</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 组装故障排除 ==="</span>

<span class="hljs-comment"># 检查常见问题</span>
<span class="hljs-function"><span class="hljs-title">check_assembly_issues</span></span>() {
    <span class="hljs-built_in">local</span> input_r1=<span class="hljs-variable">$1</span>
    <span class="hljs-built_in">local</span> input_r2=<span class="hljs-variable">$2</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"1. 检查输入文件..."</span>
    <span class="hljs-keyword">if</span> [ ! -f <span class="hljs-string">"<span class="hljs-variable">$input_r1</span>"</span> ] || [ ! -f <span class="hljs-string">"<span class="hljs-variable">$input_r2</span>"</span> ]; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"错误: 输入文件不存在"</span>
        <span class="hljs-built_in">return</span> 1
    <span class="hljs-keyword">fi</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"2. 检查文件格式..."</span>
    <span class="hljs-keyword">if</span> ! zcat <span class="hljs-string">"<span class="hljs-variable">$input_r1</span>"</span> | head -4 | grep -q <span class="hljs-string">"^@"</span>; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"错误: R1文件格式不正确"</span>
        <span class="hljs-built_in">return</span> 1
    <span class="hljs-keyword">fi</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"3. 检查reads配对..."</span>
    r1_count=$(zcat <span class="hljs-string">"<span class="hljs-variable">$input_r1</span>"</span> | wc -l)
    r2_count=$(zcat <span class="hljs-string">"<span class="hljs-variable">$input_r2</span>"</span> | wc -l)
    
    <span class="hljs-keyword">if</span> [ <span class="hljs-string">"<span class="hljs-variable">$r1_count</span>"</span> != <span class="hljs-string">"<span class="hljs-variable">$r2_count</span>"</span> ]; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"警告: R1和R2文件行数不匹配"</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"R1: <span class="hljs-variable">$r1_count</span> 行, R2: <span class="hljs-variable">$r2_count</span> 行"</span>
    <span class="hljs-keyword">fi</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"4. 检查数据量..."</span>
    total_bases=$(zcat <span class="hljs-string">"<span class="hljs-variable">$input_r1</span>"</span> <span class="hljs-string">"<span class="hljs-variable">$input_r2</span>"</span> | sed -n <span class="hljs-string">'2~4p'</span> | wc -c)
    estimated_coverage=$((total_bases / 5000000))  <span class="hljs-comment"># 假设5Mb基因组</span>
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"估算覆盖度: <span class="hljs-variable">${estimated_coverage}</span>X"</span>
    <span class="hljs-keyword">if</span> [ <span class="hljs-string">"<span class="hljs-variable">$estimated_coverage</span>"</span> -lt 20 ]; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"警告: 覆盖度可能不足，建议至少20X"</span>
    <span class="hljs-keyword">fi</span>
}

<span class="hljs-comment"># 运行检查</span>
check_assembly_issues <span class="hljs-string">"pathogen_R1.fastq.gz"</span> <span class="hljs-string">"pathogen_R2.fastq.gz"</span>

<span class="hljs-comment"># 如果SPAdes失败，尝试替代组装器</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"尝试替代组装方案..."</span>

<span class="hljs-comment"># Velvet组装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"尝试Velvet组装..."</span>
velveth velvet_output 31 -fastq.gz -shortPaired pathogen_R1.fastq.gz pathogen_R2.fastq.gz
velvetg velvet_output -cov_cutoff auto -exp_cov auto

<span class="hljs-comment"># MEGAHIT组装（内存需求更低）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"尝试MEGAHIT组装..."</span>
megahit -1 pathogen_R1.fastq.gz -2 pathogen_R2.fastq.gz -o megahit_output --memory 0.8
</div></code></pre>
<h3 id="4-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96">4. 性能优化</h3>
<h4 id="%E5%B9%B6%E8%A1%8C%E5%8C%96%E5%A4%84%E7%90%86">并行化处理</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># parallel_processing.sh</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 并行化处理优化 ==="</span>

<span class="hljs-comment"># 1. 并行质量控制</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"并行质量控制..."</span>
parallel -j 4 <span class="hljs-string">"fastqc {} -o qc_results/"</span> ::: *.fastq.gz

<span class="hljs-comment"># 2. 并行BLAST搜索</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"并行BLAST搜索..."</span>
<span class="hljs-comment"># 分割查询文件</span>
split -l 1000 query.fasta query_part_
parallel -j 8 <span class="hljs-string">"blastp -query {} -db nr -out {}.blast -outfmt 6"</span> ::: query_part_*

<span class="hljs-comment"># 3. 并行基因注释</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"并行基因注释..."</span>
parallel -j 4 <span class="hljs-string">"prokka {} --outdir prokka_{/} --prefix {/.}"</span> ::: *.fasta
</div></code></pre>
<h4 id="%E7%A3%81%E7%9B%98%E7%A9%BA%E9%97%B4%E7%AE%A1%E7%90%86">磁盘空间管理</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># disk_management.sh</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 磁盘空间管理 ==="</span>

<span class="hljs-comment"># 1. 检查磁盘使用情况</span>
df -h

<span class="hljs-comment"># 2. 清理临时文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"清理临时文件..."</span>
find . -name <span class="hljs-string">"*.sam"</span> -size +100M -delete
find . -name <span class="hljs-string">"temp_*"</span> -mtime +7 -delete

<span class="hljs-comment"># 3. 压缩大文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"压缩大文件..."</span>
gzip *.fasta *.fastq

<span class="hljs-comment"># 4. 创建符号链接节省空间</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"创建符号链接..."</span>
ln -s /path/to/large/database ./local_db_link
</div></code></pre>
<h3 id="5-%E7%BB%93%E6%9E%9C%E9%AA%8C%E8%AF%81">5. 结果验证</h3>
<h4 id="%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6%E6%A3%80%E6%9F%A5%E7%82%B9">质量控制检查点</h4>
<pre class="hljs"><code><div><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-comment"># quality_checkpoints.py</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">run_quality_checkpoints</span><span class="hljs-params">()</span>:</span>
    <span class="hljs-string">"""运行质量控制检查点"""</span>
    
    <span class="hljs-keyword">import</span> os
    <span class="hljs-keyword">import</span> subprocess
    <span class="hljs-keyword">from</span> Bio <span class="hljs-keyword">import</span> SeqIO
    
    print(<span class="hljs-string">"=== 质量控制检查点 ==="</span>)
    
    <span class="hljs-comment"># 检查点1: 组装质量</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">check_assembly_quality</span><span class="hljs-params">()</span>:</span>
        assembly_file = <span class="hljs-string">"~/plant_protection_analysis/assembly/spades_output/contigs_filtered.fasta"</span>
        assembly_file = os.path.expanduser(assembly_file)
        
        <span class="hljs-keyword">if</span> os.path.exists(assembly_file):
            contigs = list(SeqIO.parse(assembly_file, <span class="hljs-string">"fasta"</span>))
            total_length = sum(len(c.seq) <span class="hljs-keyword">for</span> c <span class="hljs-keyword">in</span> contigs)
            n50 = calculate_n50([len(c.seq) <span class="hljs-keyword">for</span> c <span class="hljs-keyword">in</span> contigs])
            
            print(<span class="hljs-string">f"✓ 组装完成: <span class="hljs-subst">{len(contigs)}</span> contigs, 总长度: <span class="hljs-subst">{total_length:,}</span> bp, N50: <span class="hljs-subst">{n50:,}</span> bp"</span>)
            
            <span class="hljs-comment"># 质量判断</span>
            <span class="hljs-keyword">if</span> len(contigs) &gt; <span class="hljs-number">1000</span>:
                print(<span class="hljs-string">"⚠ 警告: Contig数量过多，可能存在污染或参数需要优化"</span>)
            <span class="hljs-keyword">if</span> n50 &lt; <span class="hljs-number">10000</span>:
                print(<span class="hljs-string">"⚠ 警告: N50较小，组装质量可能不佳"</span>)
            <span class="hljs-keyword">if</span> total_length &lt; <span class="hljs-number">1000000</span> <span class="hljs-keyword">or</span> total_length &gt; <span class="hljs-number">10000000</span>:
                print(<span class="hljs-string">"⚠ 警告: 基因组大小异常"</span>)
        <span class="hljs-keyword">else</span>:
            print(<span class="hljs-string">"✗ 组装文件不存在"</span>)
    
    <span class="hljs-comment"># 检查点2: 注释完整性</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">check_annotation_completeness</span><span class="hljs-params">()</span>:</span>
        annotation_file = <span class="hljs-string">"~/plant_protection_analysis/annotation/prokka/pathogen_genome.tsv"</span>
        annotation_file = os.path.expanduser(annotation_file)
        
        <span class="hljs-keyword">if</span> os.path.exists(annotation_file):
            <span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
            df = pd.read_csv(annotation_file, sep=<span class="hljs-string">'\t'</span>)
            
            total_genes = len(df)
            cds_genes = len(df[df[<span class="hljs-string">'ftype'</span>] == <span class="hljs-string">'CDS'</span>])
            annotated_genes = len(df[~df[<span class="hljs-string">'product'</span>].str.contains(<span class="hljs-string">'hypothetical'</span>, case=<span class="hljs-literal">False</span>, na=<span class="hljs-literal">False</span>)])
            
            annotation_rate = annotated_genes / cds_genes * <span class="hljs-number">100</span>
            
            print(<span class="hljs-string">f"✓ 注释完成: <span class="hljs-subst">{total_genes}</span> 个基因, <span class="hljs-subst">{cds_genes}</span> 个CDS"</span>)
            print(<span class="hljs-string">f"✓ 功能注释率: <span class="hljs-subst">{annotation_rate:<span class="hljs-number">.1</span>f}</span>%"</span>)
            
            <span class="hljs-keyword">if</span> annotation_rate &lt; <span class="hljs-number">60</span>:
                print(<span class="hljs-string">"⚠ 警告: 功能注释率较低"</span>)
        <span class="hljs-keyword">else</span>:
            print(<span class="hljs-string">"✗ 注释文件不存在"</span>)
    
    <span class="hljs-comment"># 检查点3: 鉴定可信度</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">check_identification_confidence</span><span class="hljs-params">()</span>:</span>
        blast_file = <span class="hljs-string">"~/plant_protection_analysis/phylogeny/blast_results/16S_blast_results.tsv"</span>
        blast_file = os.path.expanduser(blast_file)
        
        <span class="hljs-keyword">if</span> os.path.exists(blast_file):
            <span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
            <span class="hljs-keyword">try</span>:
                df = pd.read_csv(blast_file, sep=<span class="hljs-string">'\t'</span>, names=[<span class="hljs-string">'qseqid'</span>, <span class="hljs-string">'sseqid'</span>, <span class="hljs-string">'pident'</span>, <span class="hljs-string">'length'</span>, <span class="hljs-string">'mismatch'</span>, <span class="hljs-string">'gapopen'</span>, <span class="hljs-string">'qstart'</span>, <span class="hljs-string">'qend'</span>, <span class="hljs-string">'sstart'</span>, <span class="hljs-string">'send'</span>, <span class="hljs-string">'evalue'</span>, <span class="hljs-string">'bitscore'</span>, <span class="hljs-string">'stitle'</span>])
                
                <span class="hljs-keyword">if</span> len(df) &gt; <span class="hljs-number">0</span>:
                    best_match = df.iloc[<span class="hljs-number">0</span>]
                    identity = best_match[<span class="hljs-string">'pident'</span>]
                    
                    print(<span class="hljs-string">f"✓ 最佳匹配相似度: <span class="hljs-subst">{identity:<span class="hljs-number">.1</span>f}</span>%"</span>)
                    
                    <span class="hljs-keyword">if</span> identity &gt;= <span class="hljs-number">97</span>:
                        print(<span class="hljs-string">"✓ 种水平鉴定可信"</span>)
                    <span class="hljs-keyword">elif</span> identity &gt;= <span class="hljs-number">95</span>:
                        print(<span class="hljs-string">"✓ 属水平鉴定可信"</span>)
                    <span class="hljs-keyword">else</span>:
                        print(<span class="hljs-string">"⚠ 警告: 鉴定可信度较低"</span>)
                <span class="hljs-keyword">else</span>:
                    print(<span class="hljs-string">"✗ 未找到匹配结果"</span>)
            <span class="hljs-keyword">except</span>:
                print(<span class="hljs-string">"✗ BLAST结果文件格式错误"</span>)
        <span class="hljs-keyword">else</span>:
            print(<span class="hljs-string">"✗ 鉴定结果文件不存在"</span>)
    
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">calculate_n50</span><span class="hljs-params">(lengths)</span>:</span>
        <span class="hljs-string">"""计算N50值"""</span>
        lengths = sorted(lengths, reverse=<span class="hljs-literal">True</span>)
        total = sum(lengths)
        cumsum = <span class="hljs-number">0</span>
        <span class="hljs-keyword">for</span> length <span class="hljs-keyword">in</span> lengths:
            cumsum += length
            <span class="hljs-keyword">if</span> cumsum &gt;= total * <span class="hljs-number">0.5</span>:
                <span class="hljs-keyword">return</span> length
        <span class="hljs-keyword">return</span> <span class="hljs-number">0</span>
    
    <span class="hljs-comment"># 运行所有检查点</span>
    check_assembly_quality()
    check_annotation_completeness()
    check_identification_confidence()
    
    print(<span class="hljs-string">"\n=== 质量控制检查完成 ==="</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    run_quality_checkpoints()
</div></code></pre>
<h3 id="6-%E6%96%87%E6%A1%A3%E5%92%8C%E8%AE%B0%E5%BD%95">6. 文档和记录</h3>
<h4 id="%E5%88%86%E6%9E%90%E6%97%A5%E5%BF%97%E8%AE%B0%E5%BD%95">分析日志记录</h4>
<pre class="hljs"><code><div><span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># logging_system.sh</span>

<span class="hljs-comment"># 设置日志文件</span>
LOG_FILE=<span class="hljs-string">"~/plant_protection_analysis/analysis.log"</span>
ERROR_LOG=<span class="hljs-string">"~/plant_protection_analysis/error.log"</span>

<span class="hljs-comment"># 日志记录函数</span>
<span class="hljs-function"><span class="hljs-title">log_info</span></span>() {
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"<span class="hljs-variable">$(date '+%Y-%m-%d %H:%M:%S')</span> [INFO] <span class="hljs-variable">$1</span>"</span> &gt;&gt; <span class="hljs-string">"<span class="hljs-variable">$LOG_FILE</span>"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"<span class="hljs-variable">$(date '+%Y-%m-%d %H:%M:%S')</span> [INFO] <span class="hljs-variable">$1</span>"</span>
}

<span class="hljs-function"><span class="hljs-title">log_error</span></span>() {
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"<span class="hljs-variable">$(date '+%Y-%m-%d %H:%M:%S')</span> [ERROR] <span class="hljs-variable">$1</span>"</span> &gt;&gt; <span class="hljs-string">"<span class="hljs-variable">$ERROR_LOG</span>"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"<span class="hljs-variable">$(date '+%Y-%m-%d %H:%M:%S')</span> [ERROR] <span class="hljs-variable">$1</span>"</span> &gt;&amp;2
}

<span class="hljs-comment"># 使用示例</span>
log_info <span class="hljs-string">"开始基因组组装分析"</span>
log_info <span class="hljs-string">"使用SPAdes进行组装"</span>

<span class="hljs-comment"># 运行命令并记录</span>
<span class="hljs-keyword">if</span> spades.py --pe1-1 R1.fastq.gz --pe1-2 R2.fastq.gz -o output; <span class="hljs-keyword">then</span>
    log_info <span class="hljs-string">"SPAdes组装成功完成"</span>
<span class="hljs-keyword">else</span>
    log_error <span class="hljs-string">"SPAdes组装失败"</span>
<span class="hljs-keyword">fi</span>
</div></code></pre>

</body>
</html>
