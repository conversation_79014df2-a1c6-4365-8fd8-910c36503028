<!DOCTYPE html>
<html>
<head>
<title>Lecture5_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E4%BA%94%E8%A1%A8%E8%A7%82%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%B5%8B%E5%BA%8F%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题五：表观基因组测序数据分析 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握ChIP-seq数据处理的完整流程</li>
<li>学会使用MACS2进行峰检测和参数优化</li>
<li>掌握ATAC-seq数据分析和开放染色质区域鉴定</li>
<li>学会差异结合分析和功能注释方法</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE">软件环境配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建专题五专用环境</span>
conda create -n chipseq_analysis python=3.8 -y
conda activate chipseq_analysis

<span class="hljs-comment"># 安装核心软件</span>
conda install -c bioconda bowtie2 samtools bedtools macs2 -y
conda install -c bioconda picard fastqc trimmomatic -y
conda install -c bioconda deeptools homer -y
conda install -c r r-base r-diffbind r-chipseeker r-clusterProfiler -y

<span class="hljs-comment"># 验证安装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 软件版本验证 ==="</span>
bowtie2 --version
samtools --version
macs2 --version
bedtools --version

<span class="hljs-built_in">echo</span> <span class="hljs-string">"软件安装完成！"</span>
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p chipseq_practice/{data/{reference,raw,processed},results/{alignment,peaks,qc,diff_analysis},scripts,logs}
<span class="hljs-built_in">cd</span> chipseq_practice

<span class="hljs-comment"># 创建简化的参考基因组（用于教学演示）</span>
cat &gt; data/reference/chr22_mini.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

<span class="hljs-comment"># 创建基因注释文件</span>
cat &gt; data/reference/chr22_mini.bed &lt;&lt; <span class="hljs-string">'EOF'</span>
chr22	100	300	GENE1	0	+
chr22	500	700	GENE2	0	-
chr22	800	1000	GENE3	0	+
EOF

<span class="hljs-comment"># 创建模拟ChIP-seq数据（转录因子结合）</span>
cat &gt; data/raw/treatment.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@chipseq_read_1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@chipseq_read_2
CGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTAT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@chipseq_read_3
TTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@chipseq_read_4
AATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCC
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

<span class="hljs-comment"># 创建对照样本（Input DNA）</span>
cat &gt; data/raw/control.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@input_read_1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@input_read_2
CGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTAT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@input_read_3
TTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"实验数据准备完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86chip-seq%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E5%AE%8C%E6%95%B4%E6%B5%81%E7%A8%8B">第一部分：ChIP-seq数据处理完整流程</h2>
<h3 id="11-%E6%95%B0%E6%8D%AE%E8%B4%A8%E6%8E%A7%E4%B8%8E%E9%A2%84%E5%A4%84%E7%90%86">1.1 数据质控与预处理</h3>
<h4 id="111-fastqc%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6">1.1.1 FastQC质量控制</h4>
<p><strong>实验目标：</strong> 评估原始ChIP-seq数据质量，识别潜在问题</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== ChIP-seq数据质量控制 ==="</span>

<span class="hljs-comment"># 1. 对原始数据进行质量控制</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"执行FastQC质量控制..."</span>
fastqc data/raw/treatment.fastq -o results/qc/
fastqc data/raw/control.fastq -o results/qc/

<span class="hljs-comment"># 2. 检查质量控制结果</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 质量控制结果概览 ==="</span>
ls -la results/qc/

<span class="hljs-comment"># 3. 创建质量评估脚本</span>
cat &gt; scripts/assess_chip_quality.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
ChIP-seq数据质量评估脚本
评估测序数据的基本统计信息和质量分布
"</span><span class="hljs-string">""</span>

def count_fastq_reads(fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"统计FASTQ文件中的reads数量"</span><span class="hljs-string">""</span>
    try:
        with open(fastq_file, <span class="hljs-string">'r'</span>) as f:
            count = 0
            <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
                <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'@'</span>):
                    count += 1
            <span class="hljs-built_in">return</span> count
    except FileNotFoundError:
        <span class="hljs-built_in">return</span> 0

def calculate_sequence_stats(fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"计算序列基本统计信息"</span><span class="hljs-string">""</span>
    try:
        total_length = 0
        gc_count = 0
        total_bases = 0
        read_count = 0
        
        with open(fastq_file, <span class="hljs-string">'r'</span>) as f:
            lines = f.readlines()
            
        <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(1, len(lines), 4):  <span class="hljs-comment"># 序列行</span>
            <span class="hljs-keyword">if</span> i &lt; len(lines):
                seq = lines[i].strip()
                total_length += len(seq)
                gc_count += seq.count(<span class="hljs-string">'G'</span>) + seq.count(<span class="hljs-string">'C'</span>)
                total_bases += len(seq)
                read_count += 1
        
        <span class="hljs-built_in">return</span> {
            <span class="hljs-string">'read_count'</span>: read_count,
            <span class="hljs-string">'avg_length'</span>: total_length / read_count <span class="hljs-keyword">if</span> read_count &gt; 0 <span class="hljs-keyword">else</span> 0,
            <span class="hljs-string">'gc_content'</span>: (gc_count / total_bases * 100) <span class="hljs-keyword">if</span> total_bases &gt; 0 <span class="hljs-keyword">else</span> 0,
            <span class="hljs-string">'total_bases'</span>: total_bases
        }
    except:
        <span class="hljs-built_in">return</span> None

def generate_quality_report(sample_name, fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"生成质量报告"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== {sample_name} 质量报告 ==="</span>)
    
    stats = calculate_sequence_stats(fastq_file)
    <span class="hljs-keyword">if</span> stats:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"reads数量: {stats['read_count']:,}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"平均序列长度: {stats['avg_length']:.1f} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"GC含量: {stats['gc_content']:.1f}%"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"总碱基数: {stats['total_bases']:,}"</span>)
        
        <span class="hljs-comment"># 质量评估建议</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n质量评估:"</span>)
        <span class="hljs-keyword">if</span> stats[<span class="hljs-string">'read_count'</span>] &lt; 1000000:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"⚠️  reads数量较少，可能影响后续分析"</span>)
        <span class="hljs-keyword">else</span>:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"✅ reads数量充足"</span>)
            
        <span class="hljs-keyword">if</span> 40 &lt;= stats[<span class="hljs-string">'gc_content'</span>] &lt;= 60:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"✅ GC含量正常"</span>)
        <span class="hljs-keyword">else</span>:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"⚠️  GC含量异常，可能存在污染或偏好性"</span>)
    <span class="hljs-keyword">else</span>:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"❌ 无法读取文件"</span>)

<span class="hljs-comment"># 分析样本</span>
generate_quality_report(<span class="hljs-string">"Treatment (ChIP)"</span>, <span class="hljs-string">"data/raw/treatment.fastq"</span>)
generate_quality_report(<span class="hljs-string">"Control (Input)"</span>, <span class="hljs-string">"data/raw/control.fastq"</span>)

<span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== ChIP-seq特殊质控建议 ==="</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"1. 检查片段长度分布：应该反映转录因子保护的DNA片段"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"2. 检查重复序列比例：ChIP-seq中重复序列比例应该较低"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"3. 比较ChIP和Input样本：ChIP样本应在特定区域有富集"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"4. 评估文库复杂度：避免PCR过度扩增"</span>)
EOF

python3 scripts/assess_chip_quality.py
</div></code></pre>
<h4 id="112-%E5%BA%8F%E5%88%97%E8%BF%87%E6%BB%A4%E5%92%8C%E4%BF%AE%E5%89%AA">1.1.2 序列过滤和修剪</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 序列质量过滤 ==="</span>

<span class="hljs-comment"># 1. 使用Trimmomatic进行序列修剪</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"执行序列修剪..."</span>
trimmomatic SE \
    data/raw/treatment.fastq \
    data/processed/treatment_trimmed.fastq \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:30

trimmomatic SE \
    data/raw/control.fastq \
    data/processed/control_trimmed.fastq \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:30

<span class="hljs-comment"># 2. 比较修剪前后的统计信息</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 修剪效果对比 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Treatment样本:"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始reads: <span class="hljs-variable">$(grep -c '^@' data/raw/treatment.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"修剪后reads: <span class="hljs-variable">$(grep -c '^@' data/processed/treatment_trimmed.fastq)</span>"</span>

<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\nControl样本:"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始reads: <span class="hljs-variable">$(grep -c '^@' data/raw/control.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"修剪后reads: <span class="hljs-variable">$(grep -c '^@' data/processed/control_trimmed.fastq)</span>"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"序列过滤完成！"</span>
</div></code></pre>
<h3 id="12-%E5%8F%82%E8%80%83%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%AF%94%E5%AF%B9">1.2 参考基因组比对</h3>
<h4 id="121-%E6%9E%84%E5%BB%BAbowtie2%E7%B4%A2%E5%BC%95">1.2.1 构建Bowtie2索引</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 构建Bowtie2索引 ==="</span>

<span class="hljs-comment"># 1. 为参考基因组构建索引</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"构建参考基因组索引..."</span>
bowtie2-build data/reference/chr22_mini.fa data/reference/chr22_bowtie2_index

<span class="hljs-comment"># 2. 验证索引文件</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n索引文件列表:"</span>
ls -la data/reference/chr22_bowtie2_index*

<span class="hljs-built_in">echo</span> <span class="hljs-string">"Bowtie2索引构建完成！"</span>
</div></code></pre>
<h4 id="122-%E5%BA%8F%E5%88%97%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">1.2.2 序列比对实践</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== ChIP-seq序列比对 ==="</span>

<span class="hljs-comment"># 1. Treatment样本比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"比对Treatment样本..."</span>
bowtie2 -x data/reference/chr22_bowtie2_index \
    -U data/processed/treatment_trimmed.fastq \
    -S results/alignment/treatment.sam \
    -p 2 \
    --very-sensitive

<span class="hljs-comment"># 2. Control样本比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"比对Control样本..."</span>
bowtie2 -x data/reference/chr22_bowtie2_index \
    -U data/processed/control_trimmed.fastq \
    -S results/alignment/control.sam \
    -p 2 \
    --very-sensitive

<span class="hljs-comment"># 3. SAM转BAM并排序</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 处理比对结果 ==="</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> treatment control; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"处理<span class="hljs-variable">${sample}</span>样本..."</span>
    
    <span class="hljs-comment"># SAM转BAM</span>
    samtools view -bS results/alignment/<span class="hljs-variable">${sample}</span>.sam &gt; results/alignment/<span class="hljs-variable">${sample}</span>.bam
    
    <span class="hljs-comment"># 排序</span>
    samtools sort results/alignment/<span class="hljs-variable">${sample}</span>.bam -o results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam
    
    <span class="hljs-comment"># 建立索引</span>
    samtools index results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam
    
    <span class="hljs-comment"># 生成比对统计</span>
    samtools flagstat results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam &gt; results/alignment/<span class="hljs-variable">${sample}</span>_flagstat.txt
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">${sample}</span>样本处理完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"序列比对完成！"</span>
</div></code></pre>
<h3 id="13-pcr%E9%87%8D%E5%A4%8D%E5%8E%BB%E9%99%A4%E4%B8%8E%E8%B4%A8%E9%87%8F%E8%BF%87%E6%BB%A4">1.3 PCR重复去除与质量过滤</h3>
<h4 id="131-pcr%E9%87%8D%E5%A4%8D%E6%A0%87%E8%AE%B0">1.3.1 PCR重复标记</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== PCR重复去除 ==="</span>

<span class="hljs-comment"># 1. 使用Picard标记重复序列</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"标记PCR重复序列..."</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> treatment control; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"处理<span class="hljs-variable">${sample}</span>样本..."</span>
    
    picard MarkDuplicates \
        I=results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam \
        O=results/alignment/<span class="hljs-variable">${sample}</span>.marked.bam \
        M=results/alignment/<span class="hljs-variable">${sample}</span>_dup_metrics.txt \
        REMOVE_DUPLICATES=<span class="hljs-literal">true</span>
    
    <span class="hljs-comment"># 重新排序和索引</span>
    samtools sort results/alignment/<span class="hljs-variable">${sample}</span>.marked.bam -o results/alignment/<span class="hljs-variable">${sample}</span>.final.bam
    samtools index results/alignment/<span class="hljs-variable">${sample}</span>.final.bam
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">${sample}</span>重复去除完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># 2. 质量过滤</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 质量过滤 ==="</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> treatment control; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"质量过滤<span class="hljs-variable">${sample}</span>样本..."</span>
    
    samtools view -b -q 20 -F 1028 \
        results/alignment/<span class="hljs-variable">${sample}</span>.final.bam &gt; \
        results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam
    
    samtools index results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">${sample}</span>质量过滤完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># 3. 比对质量统计对比</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 处理效果统计 ==="</span>
cat &gt; scripts/alignment_stats.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"样本\t阶段\t总reads\t比对reads\t比对率\t唯一比对"</span>

<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> treatment control; <span class="hljs-keyword">do</span>
    <span class="hljs-comment"># 原始比对结果</span>
    <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam"</span> ]; <span class="hljs-keyword">then</span>
        total=$(samtools view -c results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam)
        mapped=$(samtools view -c -F 4 results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam)
        unique=$(samtools view -c -q 1 results/alignment/<span class="hljs-variable">${sample}</span>.sorted.bam)
        rate=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$mapped</span> * 100 / <span class="hljs-variable">$total</span>"</span> | bc -l)
        <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"<span class="hljs-variable">$sample</span>\t原始\t<span class="hljs-variable">$total</span>\t<span class="hljs-variable">$mapped</span>\t<span class="hljs-variable">${rate}</span>%\t<span class="hljs-variable">$unique</span>"</span>
    <span class="hljs-keyword">fi</span>
    
    <span class="hljs-comment"># 过滤后结果</span>
    <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam"</span> ]; <span class="hljs-keyword">then</span>
        total=$(samtools view -c results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam)
        mapped=$(samtools view -c -F 4 results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam)
        unique=$(samtools view -c -q 1 results/alignment/<span class="hljs-variable">${sample}</span>.filtered.bam)
        rate=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$mapped</span> * 100 / <span class="hljs-variable">$total</span>"</span> | bc -l)
        <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"<span class="hljs-variable">$sample</span>\t过滤后\t<span class="hljs-variable">$total</span>\t<span class="hljs-variable">$mapped</span>\t<span class="hljs-variable">${rate}</span>%\t<span class="hljs-variable">$unique</span>"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>
EOF

chmod +x scripts/alignment_stats.sh
./scripts/alignment_stats.sh

<span class="hljs-built_in">echo</span> <span class="hljs-string">"数据预处理完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86macs2%E5%B3%B0%E6%A3%80%E6%B5%8B%E8%AF%A6%E7%BB%86%E5%AE%9E%E8%B7%B5">第二部分：MACS2峰检测详细实践</h2>
<h3 id="21-macs2%E5%9F%BA%E7%A1%80%E5%B3%B0%E6%A3%80%E6%B5%8B">2.1 MACS2基础峰检测</h3>
<h4 id="211-narrow%E5%B3%B0%E6%A3%80%E6%B5%8B%E8%BD%AC%E5%BD%95%E5%9B%A0%E5%AD%90">2.1.1 narrow峰检测（转录因子）</h4>
<p><strong>实验目标：</strong> 学会使用MACS2检测sharp peaks，理解参数设置</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== MACS2 narrow峰检测 ==="</span>

<span class="hljs-comment"># 1. 基本峰检测</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"执行narrow峰检测..."</span>
macs2 callpeak \
    -t results/alignment/treatment.filtered.bam \
    -c results/alignment/control.filtered.bam \
    -f BAM \
    -g 1000 \
    -n treatment_vs_control \
    -q 0.05 \
    --outdir results/peaks/ \
    2&gt; results/peaks/macs2_narrow.log

<span class="hljs-comment"># 2. 检查输出文件</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== MACS2输出文件 ==="</span>
ls -la results/peaks/treatment_vs_control*

<span class="hljs-comment"># 参数解释</span>
cat &lt;&lt; <span class="hljs-string">'EOF'</span>

=== MACS2参数详解 ===
-t: Treatment文件（ChIP样本）
-c: Control文件（Input样本）
-f: 输入文件格式
-g: 有效基因组大小
-n: 输出文件前缀
-q: q值阈值（FDR校正后的p值）
--outdir: 输出目录
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"narrow峰检测完成！"</span>
</div></code></pre>
<h4 id="212-broad%E5%B3%B0%E6%A3%80%E6%B5%8B%E7%BB%84%E8%9B%8B%E7%99%BD%E4%BF%AE%E9%A5%B0">2.1.2 broad峰检测（组蛋白修饰）</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== MACS2 broad峰检测 ==="</span>

<span class="hljs-comment"># 1. broad峰检测</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"执行broad峰检测..."</span>
macs2 callpeak \
    -t results/alignment/treatment.filtered.bam \
    -c results/alignment/control.filtered.bam \
    -f BAM \
    -g 1000 \
    -n treatment_broad \
    --broad \
    --broad-cutoff 0.1 \
    -q 0.05 \
    --outdir results/peaks/ \
    2&gt; results/peaks/macs2_broad.log

<span class="hljs-built_in">echo</span> <span class="hljs-string">"broad峰检测完成！"</span>
</div></code></pre>
<h3 id="22-%E5%B3%B0%E6%A3%80%E6%B5%8B%E7%BB%93%E6%9E%9C%E5%88%86%E6%9E%90">2.2 峰检测结果分析</h3>
<h4 id="221-%E5%B3%B0%E6%96%87%E4%BB%B6%E6%A0%BC%E5%BC%8F%E8%A7%A3%E8%AF%BB">2.2.1 峰文件格式解读</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建峰文件分析脚本</span>
cat &gt; scripts/analyze_peaks.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
MACS2峰检测结果分析脚本
解析和统计峰检测结果
"</span><span class="hljs-string">""</span>

import pandas as pd
import numpy as np

def analyze_narrow_peaks(peak_file):
    <span class="hljs-string">""</span><span class="hljs-string">"分析narrow峰文件"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"=== Narrow峰分析 ==="</span>)
    
    try:
        <span class="hljs-comment"># 读取narrowPeak文件</span>
        columns = [<span class="hljs-string">'chr'</span>, <span class="hljs-string">'start'</span>, <span class="hljs-string">'end'</span>, <span class="hljs-string">'name'</span>, <span class="hljs-string">'score'</span>, <span class="hljs-string">'strand'</span>, 
                  <span class="hljs-string">'signalValue'</span>, <span class="hljs-string">'pValue'</span>, <span class="hljs-string">'qValue'</span>, <span class="hljs-string">'peak'</span>]
        
        df = pd.read_csv(peak_file, sep=<span class="hljs-string">'\t'</span>, header=None, names=columns)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"检测到峰数量: {len(df)}"</span>)
        
        <span class="hljs-comment"># 峰长度分析</span>
        df[<span class="hljs-string">'length'</span>] = df[<span class="hljs-string">'end'</span>] - df[<span class="hljs-string">'start'</span>]
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"峰长度统计:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  平均长度: {df['length'].mean():.1f} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  中位数长度: {df['length'].median():.1f} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最短峰: {df['length'].min()} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最长峰: {df['length'].max()} bp"</span>)
        
        <span class="hljs-comment"># 信号强度分析</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n信号强度统计:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  平均信号值: {df['signalValue'].mean():.2f}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最高信号值: {df['signalValue'].max():.2f}"</span>)
        
        <span class="hljs-comment"># 显著性分析</span>
        significant_peaks = len(df[df[<span class="hljs-string">'qValue'</span>] &gt; 2])  <span class="hljs-comment"># -log10(0.01) ≈ 2</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n显著性统计:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  高显著性峰 (q&lt;0.01): {significant_peaks}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  高显著性比例: {significant_peaks/len(df)*100:.1f}%"</span>)
        
        <span class="hljs-comment"># 显示前5个最显著的峰</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n最显著的峰 (前5个):"</span>)
        top_peaks = df.nlargest(5, <span class="hljs-string">'qValue'</span>)[[<span class="hljs-string">'chr'</span>, <span class="hljs-string">'start'</span>, <span class="hljs-string">'end'</span>, <span class="hljs-string">'signalValue'</span>, <span class="hljs-string">'qValue'</span>]]
        <span class="hljs-built_in">print</span>(top_peaks.to_string(index=False))
        
        <span class="hljs-built_in">return</span> df
        
    except FileNotFoundError:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"错误: 文件 {peak_file} 不存在"</span>)
        <span class="hljs-built_in">return</span> None
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析过程中出错: {e}"</span>)
        <span class="hljs-built_in">return</span> None

def analyze_broad_peaks(peak_file):
    <span class="hljs-string">""</span><span class="hljs-string">"分析broad峰文件"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== Broad峰分析 ==="</span>)
    
    try:
        <span class="hljs-comment"># broadPeak文件格式</span>
        columns = [<span class="hljs-string">'chr'</span>, <span class="hljs-string">'start'</span>, <span class="hljs-string">'end'</span>, <span class="hljs-string">'name'</span>, <span class="hljs-string">'score'</span>, <span class="hljs-string">'strand'</span>, 
                  <span class="hljs-string">'signalValue'</span>, <span class="hljs-string">'pValue'</span>, <span class="hljs-string">'qValue'</span>]
        
        df = pd.read_csv(peak_file, sep=<span class="hljs-string">'\t'</span>, header=None, names=columns)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"检测到broad峰数量: {len(df)}"</span>)
        
        <span class="hljs-comment"># 峰长度分析</span>
        df[<span class="hljs-string">'length'</span>] = df[<span class="hljs-string">'end'</span>] - df[<span class="hljs-string">'start'</span>]
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Broad峰长度统计:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  平均长度: {df['length'].mean():.1f} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  中位数长度: {df['length'].median():.1f} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最短峰: {df['length'].min()} bp"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最长峰: {df['length'].max()} bp"</span>)
        
        <span class="hljs-built_in">return</span> df
        
    except FileNotFoundError:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"错误: 文件 {peak_file} 不存在"</span>)
        <span class="hljs-built_in">return</span> None
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析过程中出错: {e}"</span>)
        <span class="hljs-built_in">return</span> None

def compare_peak_types(narrow_df, broad_df):
    <span class="hljs-string">""</span><span class="hljs-string">"比较narrow和broad峰的特征"</span><span class="hljs-string">""</span>
    <span class="hljs-keyword">if</span> narrow_df is None or broad_df is None:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"无法进行比较：缺少数据"</span>)
        <span class="hljs-built_in">return</span>
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== Narrow vs Broad峰比较 ==="</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Narrow峰数量: {len(narrow_df)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Broad峰数量: {len(broad_df)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Narrow峰平均长度: {narrow_df['length'].mean():.1f} bp"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Broad峰平均长度: {broad_df['length'].mean():.1f} bp"</span>)
    
    <span class="hljs-comment"># 长度分布比较</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n长度分布比较:"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Narrow峰 &lt;500bp: {len(narrow_df[narrow_df['length'] &lt; 500])}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Narrow峰 &gt;1000bp: {len(narrow_df[narrow_df['length'] &gt; 1000])}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Broad峰 &lt;500bp: {len(broad_df[broad_df['length'] &lt; 500])}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Broad峰 &gt;1000bp: {len(broad_df[broad_df['length'] &gt; 1000])}"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    <span class="hljs-comment"># 分析narrow峰</span>
    narrow_df = analyze_narrow_peaks(<span class="hljs-string">"results/peaks/treatment_vs_control_peaks.narrowPeak"</span>)
    
    <span class="hljs-comment"># 分析broad峰  </span>
    broad_df = analyze_broad_peaks(<span class="hljs-string">"results/peaks/treatment_broad_peaks.broadPeak"</span>)
    
    <span class="hljs-comment"># 比较分析</span>
    compare_peak_types(narrow_df, broad_df)
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== 峰检测质量评估建议 ==="</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"1. 峰数量: 转录因子通常1000-10000个峰，组蛋白修饰可能更多"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"2. 峰长度: 转录因子峰通常&lt;500bp，组蛋白修饰峰可能&gt;1000bp"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"3. 信号强度: 高质量峰应有较高的信号值和显著性"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"4. 与已知位点比较: 验证峰是否在预期的基因组区域"</span>)
EOF

python3 scripts/analyze_peaks.py
</div></code></pre>

</body>
</html>
