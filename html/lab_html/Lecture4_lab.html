<!DOCTYPE html>
<html>
<head>
<title>Lecture4_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E5%9B%9B%E8%BD%AC%E5%BD%95%E7%BB%84%E6%B5%8B%E5%BA%8Frna-seq%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题四：转录组测序(RNA-seq)数据分析 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握HISAT2和STAR转录组比对工具的使用</li>
<li>学会StringTie和featureCounts进行转录本定量</li>
<li>掌握DESeq2和edgeR进行差异表达分析</li>
<li>学会功能富集分析和可视化方法</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE">软件环境配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建专题四专用环境</span>
conda create -n rna_seq_analysis python=3.8 -y
conda activate rna_seq_analysis

<span class="hljs-comment"># 安装核心软件</span>
conda install -c bioconda hisat2 star stringtie subread samtools bcftools -y
conda install -c bioconda rseqc gffutils -y
conda install -c r r-deseq2 r-edger r-ggplot2 r-clusterProfiler -y

<span class="hljs-comment"># 验证安装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 软件版本验证 ==="</span>
hisat2 --version
STAR --version
stringtie --version
featureCounts -v
samtools --version

<span class="hljs-built_in">echo</span> <span class="hljs-string">"软件安装完成！"</span>
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p rna_seq_practice/{data/{reference,raw,processed},results/{alignment,assembly,quantification,diff_analysis},scripts,logs}
<span class="hljs-built_in">cd</span> rna_seq_practice

<span class="hljs-comment"># 创建简化的参考基因组（用于教学演示）</span>
cat &gt; data/reference/chr22_mini.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

<span class="hljs-comment"># 创建简化的基因注释文件</span>
cat &gt; data/reference/chr22_mini.gtf &lt;&lt; <span class="hljs-string">'EOF'</span>
chr22	ensembl	gene	100	400	.	+	.	gene_id <span class="hljs-string">"ENSG00000001"</span>; gene_name <span class="hljs-string">"GENE1"</span>; gene_biotype <span class="hljs-string">"protein_coding"</span>;
chr22	ensembl	transcript	100	400	.	+	.	gene_id <span class="hljs-string">"ENSG00000001"</span>; transcript_id <span class="hljs-string">"ENST00000001"</span>; gene_name <span class="hljs-string">"GENE1"</span>;
chr22	ensembl	exon	100	200	.	+	.	gene_id <span class="hljs-string">"ENSG00000001"</span>; transcript_id <span class="hljs-string">"ENST00000001"</span>; exon_number <span class="hljs-string">"1"</span>;
chr22	ensembl	exon	300	400	.	+	.	gene_id <span class="hljs-string">"ENSG00000001"</span>; transcript_id <span class="hljs-string">"ENST00000001"</span>; exon_number <span class="hljs-string">"2"</span>;
chr22	ensembl	gene	500	800	.	-	.	gene_id <span class="hljs-string">"ENSG00000002"</span>; gene_name <span class="hljs-string">"GENE2"</span>; gene_biotype <span class="hljs-string">"protein_coding"</span>;
chr22	ensembl	transcript	500	800	.	-	.	gene_id <span class="hljs-string">"ENSG00000002"</span>; transcript_id <span class="hljs-string">"ENST00000002"</span>; gene_name <span class="hljs-string">"GENE2"</span>;
chr22	ensembl	exon	500	600	.	-	.	gene_id <span class="hljs-string">"ENSG00000002"</span>; transcript_id <span class="hljs-string">"ENST00000002"</span>; exon_number <span class="hljs-string">"1"</span>;
chr22	ensembl	exon	700	800	.	-	.	gene_id <span class="hljs-string">"ENSG00000002"</span>; transcript_id <span class="hljs-string">"ENST00000002"</span>; exon_number <span class="hljs-string">"2"</span>;
EOF

<span class="hljs-comment"># 创建模拟RNA-seq双端测序数据（正常表达样本）</span>
cat &gt; data/raw/control_R1.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR_control_1/1
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_2/1
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR_control_3/1
TCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_4/1
CAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat &gt; data/raw/control_R2.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR_control_1/2
TGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_2/2
ACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@SRR_control_3/2
TGCAGACATTCAATTGTTATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR_control_4/2
TGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

<span class="hljs-comment"># 创建处理组样本（模拟差异表达）</span>
cp data/raw/control_R1.fastq data/raw/treated_R1.fastq
cp data/raw/control_R2.fastq data/raw/treated_R2.fastq

<span class="hljs-built_in">echo</span> <span class="hljs-string">"实验数据准备完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86hisat2star%E8%BD%AC%E5%BD%95%E7%BB%84%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">第一部分：HISAT2/STAR转录组比对实践</h2>
<h3 id="11-%E5%8F%82%E8%80%83%E5%9F%BA%E5%9B%A0%E7%BB%84%E5%87%86%E5%A4%87%E4%B8%8E%E7%B4%A2%E5%BC%95%E6%9E%84%E5%BB%BA">1.1 参考基因组准备与索引构建</h3>
<h4 id="111-hisat2%E7%B4%A2%E5%BC%95%E6%9E%84%E5%BB%BA%E5%AE%9E%E8%B7%B5">1.1.1 HISAT2索引构建实践</h4>
<p><strong>实验目标：</strong> 学会构建HISAT2索引，理解剪接位点提取的重要性</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== HISAT2索引构建实践 ==="</span>

<span class="hljs-comment"># 1. 检查参考基因组文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"检查参考基因组文件格式..."</span>
head -5 data/reference/chr22_mini.fa
<span class="hljs-built_in">echo</span> <span class="hljs-string">"序列长度: <span class="hljs-variable">$(grep -v "^&gt;" data/reference/chr22_mini.fa | wc -c)</span>"</span>

<span class="hljs-comment"># 2. 检查GTF注释文件</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n检查GTF注释文件..."</span>
head -5 data/reference/chr22_mini.gtf
<span class="hljs-built_in">echo</span> <span class="hljs-string">"注释条目数: <span class="hljs-variable">$(wc -l &lt; data/reference/chr22_mini.gtf)</span>"</span>

<span class="hljs-comment"># 3. 提取剪接位点信息</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 提取剪接位点信息 ==="</span>
extract_splice_sites.py data/reference/chr22_mini.gtf &gt; data/reference/splice_sites.txt

<span class="hljs-comment"># 检查剪接位点文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"已知剪接位点:"</span>
cat data/reference/splice_sites.txt

<span class="hljs-comment"># 4. 提取外显子信息</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 提取外显子信息 ==="</span>
extract_exons.py data/reference/chr22_mini.gtf &gt; data/reference/exons.txt

<span class="hljs-comment"># 检查外显子文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"外显子信息:"</span>
cat data/reference/exons.txt

<span class="hljs-comment"># 5. 构建HISAT2索引</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 构建HISAT2索引 ==="</span>
hisat2-build \
    data/reference/chr22_mini.fa \
    data/reference/chr22_hisat2_index \
    --ss data/reference/splice_sites.txt \
    --exon data/reference/exons.txt \
    -p 2

<span class="hljs-comment"># 验证索引文件</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n索引文件列表:"</span>
ls -la data/reference/chr22_hisat2_index*

<span class="hljs-built_in">echo</span> <span class="hljs-string">"HISAT2索引构建完成！"</span>
</div></code></pre>
<h4 id="112-star%E7%B4%A2%E5%BC%95%E6%9E%84%E5%BB%BA%E5%AE%9E%E8%B7%B5">1.1.2 STAR索引构建实践</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== STAR索引构建实践 ==="</span>

<span class="hljs-comment"># 1. 创建STAR索引目录</span>
mkdir -p data/reference/star_index

<span class="hljs-comment"># 2. 构建STAR索引（针对小基因组调整参数）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"构建STAR索引..."</span>
STAR --runMode genomeGenerate \
    --genomeDir data/reference/star_index \
    --genomeFastaFiles data/reference/chr22_mini.fa \
    --sjdbGTFfile data/reference/chr22_mini.gtf \
    --genomeSAindexNbases 4 \
    --runThreadN 2

<span class="hljs-comment"># 参数解释：</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== STAR索引参数解释 ==="</span>
cat &lt;&lt; <span class="hljs-string">'EOF'</span>
--runMode genomeGenerate: 索引构建模式
--genomeDir: 索引输出目录
--genomeFastaFiles: 参考基因组FASTA文件
--sjdbGTFfile: 基因注释GTF文件
--genomeSAindexNbases: 索引参数（小基因组设为4）
--runThreadN: 线程数
EOF

<span class="hljs-comment"># 3. 验证索引文件</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n索引文件列表:"</span>
ls -la data/reference/star_index/

<span class="hljs-built_in">echo</span> <span class="hljs-string">"STAR索引构建完成！"</span>
</div></code></pre>
<h3 id="12-hisat2%E6%AF%94%E5%AF%B9%E8%AF%A6%E7%BB%86%E6%93%8D%E4%BD%9C">1.2 HISAT2比对详细操作</h3>
<h4 id="121-%E5%9F%BA%E6%9C%AC%E6%AF%94%E5%AF%B9%E6%B5%81%E7%A8%8B">1.2.1 基本比对流程</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== HISAT2比对实践 ==="</span>

<span class="hljs-comment"># 1. 单样本双端比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"正在比对control样本..."</span>
hisat2 -x data/reference/chr22_hisat2_index \
    -1 data/raw/control_R1.fastq \
    -2 data/raw/control_R2.fastq \
    -S results/alignment/control_hisat2.sam \
    --summary-file results/alignment/control_hisat2_summary.txt \
    -p 2

<span class="hljs-comment"># 2. 比对treated样本</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"正在比对treated样本..."</span>
hisat2 -x data/reference/chr22_hisat2_index \
    -1 data/raw/treated_R1.fastq \
    -2 data/raw/treated_R2.fastq \
    -S results/alignment/treated_hisat2.sam \
    --summary-file results/alignment/treated_hisat2_summary.txt \
    -p 2

<span class="hljs-built_in">echo</span> <span class="hljs-string">"HISAT2比对完成！"</span>
</div></code></pre>
<h4 id="122-%E6%AF%94%E5%AF%B9%E7%BB%93%E6%9E%9C%E5%A4%84%E7%90%86%E5%92%8C%E7%BB%9F%E8%AE%A1">1.2.2 比对结果处理和统计</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建比对结果处理脚本</span>
cat &gt; scripts/process_alignment.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># RNA-seq比对结果处理脚本</span>

sample_name=<span class="hljs-variable">$1</span>
input_sam=<span class="hljs-variable">$2</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 处理样本: <span class="hljs-variable">$sample_name</span> ==="</span>

<span class="hljs-comment"># 1. SAM转BAM</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"SAM文件转换为BAM..."</span>
samtools view -bS <span class="hljs-variable">$input_sam</span> &gt; results/alignment/<span class="hljs-variable">${sample_name}</span>.bam

<span class="hljs-comment"># 2. 排序</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"BAM文件排序..."</span>
samtools sort results/alignment/<span class="hljs-variable">${sample_name}</span>.bam -o results/alignment/<span class="hljs-variable">${sample_name}</span>.sorted.bam

<span class="hljs-comment"># 3. 建立索引</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"建立BAM索引..."</span>
samtools index results/alignment/<span class="hljs-variable">${sample_name}</span>.sorted.bam

<span class="hljs-comment"># 4. 比对统计</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"生成比对统计信息..."</span>
samtools flagstat results/alignment/<span class="hljs-variable">${sample_name}</span>.sorted.bam &gt; results/alignment/<span class="hljs-variable">${sample_name}</span>_flagstat.txt

<span class="hljs-comment"># 5. 基本统计信息</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"样本: <span class="hljs-variable">$sample_name</span>"</span> &gt; results/alignment/<span class="hljs-variable">${sample_name}</span>_stats.txt
<span class="hljs-built_in">echo</span> <span class="hljs-string">"总reads数: <span class="hljs-variable">$(samtools view -c results/alignment/${sample_name}.sorted.bam)</span>"</span> &gt;&gt; results/alignment/<span class="hljs-variable">${sample_name}</span>_stats.txt
<span class="hljs-built_in">echo</span> <span class="hljs-string">"比对上的reads数: <span class="hljs-variable">$(samtools view -c -F 4 results/alignment/${sample_name}.sorted.bam)</span>"</span> &gt;&gt; results/alignment/<span class="hljs-variable">${sample_name}</span>_stats.txt

<span class="hljs-comment"># 计算比对率</span>
total_reads=$(samtools view -c results/alignment/<span class="hljs-variable">${sample_name}</span>.sorted.bam)
mapped_reads=$(samtools view -c -F 4 results/alignment/<span class="hljs-variable">${sample_name}</span>.sorted.bam)
<span class="hljs-keyword">if</span> [ <span class="hljs-variable">$total_reads</span> -gt 0 ]; <span class="hljs-keyword">then</span>
    mapping_rate=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$mapped_reads</span> * 100 / <span class="hljs-variable">$total_reads</span>"</span> | bc)
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"比对率: <span class="hljs-variable">${mapping_rate}</span>%"</span> &gt;&gt; results/alignment/<span class="hljs-variable">${sample_name}</span>_stats.txt
<span class="hljs-keyword">fi</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">$sample_name</span> 处理完成"</span>
EOF

chmod +x scripts/process_alignment.sh

<span class="hljs-comment"># 处理比对结果</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 处理HISAT2比对结果 ==="</span>
./scripts/process_alignment.sh control_hisat2 results/alignment/control_hisat2.sam
./scripts/process_alignment.sh treated_hisat2 results/alignment/treated_hisat2.sam

<span class="hljs-comment"># 显示比对统计结果</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 比对结果统计 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Control样本统计:"</span>
cat results/alignment/control_hisat2_stats.txt

<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\nTreated样本统计:"</span>
cat results/alignment/treated_hisat2_stats.txt
</div></code></pre>
<h3 id="13-star%E6%AF%94%E5%AF%B9%E8%AF%A6%E7%BB%86%E6%93%8D%E4%BD%9C">1.3 STAR比对详细操作</h3>
<h4 id="131-star%E6%AF%94%E5%AF%B9%E5%AE%9E%E8%B7%B5">1.3.1 STAR比对实践</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== STAR比对实践 ==="</span>

<span class="hljs-comment"># 1. Control样本STAR比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"STAR比对control样本..."</span>
STAR --genomeDir data/reference/star_index \
    --readFilesIn data/raw/control_R1.fastq data/raw/control_R2.fastq \
    --outFileNamePrefix results/alignment/control_star_ \
    --outSAMtype BAM SortedByCoordinate \
    --runThreadN 2

<span class="hljs-comment"># 2. Treated样本STAR比对</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"STAR比对treated样本..."</span>
STAR --genomeDir data/reference/star_index \
    --readFilesIn data/raw/treated_R1.fastq data/raw/treated_R2.fastq \
    --outFileNamePrefix results/alignment/treated_star_ \
    --outSAMtype BAM SortedByCoordinate \
    --runThreadN 2

<span class="hljs-built_in">echo</span> <span class="hljs-string">"STAR比对完成！"</span>
</div></code></pre>
<h4 id="132-star%E7%BB%93%E6%9E%9C%E5%88%86%E6%9E%90">1.3.2 STAR结果分析</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建STAR结果分析脚本</span>
cat &gt; scripts/analyze_star_results.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
STAR比对结果分析脚本
分析STAR输出的日志文件，提取关键比对统计信息
"</span><span class="hljs-string">""</span>

import os
import re

def parse_star_log(log_file):
    <span class="hljs-string">""</span><span class="hljs-string">"解析STAR日志文件"</span><span class="hljs-string">""</span>
    stats = {}
    
    <span class="hljs-keyword">if</span> not os.path.exists(log_file):
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"警告: 日志文件 {log_file} 不存在"</span>)
        <span class="hljs-built_in">return</span> stats
    
    with open(log_file, <span class="hljs-string">'r'</span>) as f:
        content = f.read()
        
        <span class="hljs-comment"># 提取关键统计信息</span>
        patterns = {
            <span class="hljs-string">'total_reads'</span>: r<span class="hljs-string">'Number of input reads \|\s+(\d+)'</span>,
            <span class="hljs-string">'mapped_reads'</span>: r<span class="hljs-string">'Uniquely mapped reads number \|\s+(\d+)'</span>,
            <span class="hljs-string">'mapped_percent'</span>: r<span class="hljs-string">'Uniquely mapped reads % \|\s+([\d.]+)%'</span>,
            <span class="hljs-string">'splice_junctions'</span>: r<span class="hljs-string">'Number of splices: Total \|\s+(\d+)'</span>,
            <span class="hljs-string">'novel_junctions'</span>: r<span class="hljs-string">'Number of splices: Novel \|\s+(\d+)'</span>
        }
        
        <span class="hljs-keyword">for</span> key, pattern <span class="hljs-keyword">in</span> patterns.items():
            match = re.search(pattern, content)
            <span class="hljs-keyword">if</span> match:
                stats[key] = match.group(1)
    
    <span class="hljs-built_in">return</span> stats

def generate_report(sample_name, stats):
    <span class="hljs-string">""</span><span class="hljs-string">"生成比对报告"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== STAR比对报告: {sample_name} ==="</span>)
    
    <span class="hljs-keyword">if</span> not stats:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"无法获取统计信息"</span>)
        <span class="hljs-built_in">return</span>
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"总输入reads数: {stats.get('total_reads', 'N/A')}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"唯一比对reads数: {stats.get('mapped_reads', 'N/A')}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"唯一比对率: {stats.get('mapped_percent', 'N/A')}%"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"剪接位点总数: {stats.get('splice_junctions', 'N/A')}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"新发现剪接位点: {stats.get('novel_junctions', 'N/A')}"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    <span class="hljs-comment"># 分析control样本</span>
    control_stats = parse_star_log(<span class="hljs-string">"results/alignment/control_star_Log.final.out"</span>)
    generate_report(<span class="hljs-string">"Control"</span>, control_stats)
    
    <span class="hljs-comment"># 分析treated样本  </span>
    treated_stats = parse_star_log(<span class="hljs-string">"results/alignment/treated_star_Log.final.out"</span>)
    generate_report(<span class="hljs-string">"Treated"</span>, treated_stats)
    
    <span class="hljs-comment"># 比较分析</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== 样本比较 ==="</span>)
    <span class="hljs-keyword">if</span> control_stats and treated_stats:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"样本\t总reads\t比对reads\t比对率\t剪接位点"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Control\t{control_stats.get('total_reads', 'N/A')}\t{control_stats.get('mapped_reads', 'N/A')}\t{control_stats.get('mapped_percent', 'N/A')}%\t{control_stats.get('splice_junctions', 'N/A')}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Treated\t{treated_stats.get('total_reads', 'N/A')}\t{treated_stats.get('mapped_reads', 'N/A')}\t{treated_stats.get('mapped_percent', 'N/A')}%\t{treated_stats.get('splice_junctions', 'N/A')}"</span>)
EOF

chmod +x scripts/analyze_star_results.py

<span class="hljs-comment"># 运行STAR结果分析</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 分析STAR比对结果 ==="</span>
python3 scripts/analyze_star_results.py

<span class="hljs-comment"># 为STAR输出的BAM文件建立索引</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 为STAR输出建立索引 ==="</span>
<span class="hljs-keyword">for</span> bam <span class="hljs-keyword">in</span> results/alignment/*_star_Aligned.sortedByCoord.out.bam; <span class="hljs-keyword">do</span>
    <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">$bam</span>"</span> ]; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"为 <span class="hljs-variable">$(basename $bam)</span> 建立索引..."</span>
        samtools index <span class="hljs-string">"<span class="hljs-variable">$bam</span>"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>
</div></code></pre>
<h3 id="14-%E6%AF%94%E5%AF%B9%E5%B7%A5%E5%85%B7%E6%80%A7%E8%83%BD%E6%AF%94%E8%BE%83">1.4 比对工具性能比较</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建比对工具比较脚本</span>
cat &gt; scripts/compare_aligners.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># 比较HISAT2和STAR比对结果</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 比对工具性能比较 ==="</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"工具\t样本\t总reads\t比对reads\t比对率"</span>

<span class="hljs-comment"># HISAT2结果分析</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> control treated; <span class="hljs-keyword">do</span>
    <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"results/alignment/<span class="hljs-variable">${sample}</span>_hisat2.sorted.bam"</span> ]; <span class="hljs-keyword">then</span>
        total=$(samtools view -c results/alignment/<span class="hljs-variable">${sample}</span>_hisat2.sorted.bam)
        mapped=$(samtools view -c -F 4 results/alignment/<span class="hljs-variable">${sample}</span>_hisat2.sorted.bam)
        <span class="hljs-keyword">if</span> [ <span class="hljs-variable">$total</span> -gt 0 ]; <span class="hljs-keyword">then</span>
            rate=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$mapped</span> * 100 / <span class="hljs-variable">$total</span>"</span> | bc)
        <span class="hljs-keyword">else</span>
            rate=<span class="hljs-string">"0"</span>
        <span class="hljs-keyword">fi</span>
        <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"HISAT2\t<span class="hljs-variable">$sample</span>\t<span class="hljs-variable">$total</span>\t<span class="hljs-variable">$mapped</span>\t<span class="hljs-variable">${rate}</span>%"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># STAR结果分析</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> control treated; <span class="hljs-keyword">do</span>
    bam_file=<span class="hljs-string">"results/alignment/<span class="hljs-variable">${sample}</span>_star_Aligned.sortedByCoord.out.bam"</span>
    <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">$bam_file</span>"</span> ]; <span class="hljs-keyword">then</span>
        total=$(samtools view -c <span class="hljs-string">"<span class="hljs-variable">$bam_file</span>"</span>)
        mapped=$(samtools view -c -F 4 <span class="hljs-string">"<span class="hljs-variable">$bam_file</span>"</span>)
        <span class="hljs-keyword">if</span> [ <span class="hljs-variable">$total</span> -gt 0 ]; <span class="hljs-keyword">then</span>
            rate=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$mapped</span> * 100 / <span class="hljs-variable">$total</span>"</span> | bc)
        <span class="hljs-keyword">else</span>
            rate=<span class="hljs-string">"0"</span>
        <span class="hljs-keyword">fi</span>
        <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"STAR\t<span class="hljs-variable">$sample</span>\t<span class="hljs-variable">$total</span>\t<span class="hljs-variable">$mapped</span>\t<span class="hljs-variable">${rate}</span>%"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 建议 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"1. HISAT2: 内存需求低，适合标准RNA-seq分析"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"2. STAR: 速度快，剪接位点检测精确，适合大规模数据"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"3. 选择标准: 根据计算资源和精度要求进行选择"</span>
EOF

chmod +x scripts/compare_aligners.sh
./scripts/compare_aligners.sh &gt; results/alignment/aligner_comparison.txt

<span class="hljs-built_in">echo</span> <span class="hljs-string">"比对工具比较结果:"</span>
cat results/alignment/aligner_comparison.txt
</div></code></pre>
<h2 id="stringtiecufflinks%E8%BF%9B%E8%A1%8C%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E4%B8%8E%E5%AE%9A%E9%87%8F">StringTie/Cufflinks进行转录本组装与定量</h2>
<h3 id="1-stringtie%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E4%B8%8E%E5%AE%9A%E9%87%8F">1. StringTie转录本组装与定量</h3>
<ul>
<li>
<p><strong>StringTie安装与配置</strong></p>
<ul>
<li>使用conda安装StringTie：<pre class="hljs"><code><div>conda install -c bioconda stringtie
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>基本命令格式</strong></p>
<ul>
<li><code>stringtie [options] &lt;input.bam&gt;</code></li>
</ul>
</li>
<li>
<p><strong>参考指导组装</strong></p>
<ul>
<li>使用参考基因组和基因注释文件进行转录本组装。</li>
<li><strong>参数设置</strong>
<ul>
<li><code>-G &lt;reference.gtf&gt;</code>: 指定参考基因组注释文件</li>
<li><code>-o &lt;output.gtf&gt;</code>: 指定输出GTF文件</li>
<li><code>-p &lt;threads&gt;</code>: 指定线程数</li>
</ul>
</li>
<li><strong>GTF文件使用</strong>
<ul>
<li>GTF文件包含基因、转录本、外显子等信息。</li>
</ul>
</li>
<li>例如：<pre class="hljs"><code><div>stringtie -G Homo_sapiens.GRCh38.104.gtf -o transcripts.gtf Aligned.sortedByCoord.out.bam -p 8
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>从头组装模式</strong></p>
<ul>
<li>在没有参考基因组的情况下，进行转录本组装。</li>
<li>需要使用<code>-l</code>参数指定平均片段长度。</li>
<li>例如：<pre class="hljs"><code><div>stringtie -l 200 -o transcripts.gtf Aligned.sortedByCoord.out.bam -p 8
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>多样本合并策略</strong></p>
<ul>
<li>将多个样本的转录本组装结果合并成一个文件。</li>
<li><strong>stringtie --merge使用</strong><pre class="hljs"><code><div>stringtie --merge -G Homo_sapiens.GRCh38.104.gtf -o merged_transcripts.gtf list.txt
</div></code></pre>
<ul>
<li><code>list.txt</code> 文件包含所有样本的GTF文件路径。</li>
</ul>
</li>
<li><strong>参考转录本整合</strong>
<ul>
<li>将组装结果与参考转录本进行整合，提高组装的准确性。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>表达量估计</strong></p>
<ul>
<li>StringTie可以同时进行转录本组装和表达量估计。</li>
<li><strong>TPM/FPKM输出</strong>
<ul>
<li>StringTie输出TPM (Transcripts Per Million) 和 FPKM (Fragments Per Kilobase per Million) 两种表达量。</li>
</ul>
</li>
<li><strong>覆盖度文件生成</strong>
<ul>
<li>StringTie可以生成覆盖度文件，用于可视化转录本结构。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>下载参考基因组和基因注释文件。</li>
<li>使用StringTie进行转录本组装和表达量估计。</li>
<li>将多个样本的转录本组装结果合并成一个文件。</li>
</ol>
</li>
</ul>
<h3 id="2-cufflinks%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E4%B8%8E%E5%AE%9A%E9%87%8F">2. Cufflinks转录本组装与定量</h3>
<ul>
<li>
<p><strong>Cufflinks安装与配置</strong></p>
<ul>
<li>使用conda安装Cufflinks：<pre class="hljs"><code><div>conda install -c bioconda cufflinks
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>基本命令格式</strong></p>
<ul>
<li><code>cufflinks [options] &lt;input.bam&gt;</code></li>
</ul>
</li>
<li>
<p><strong>关键参数设置</strong></p>
<ul>
<li><code>-g &lt;reference.gtf&gt;</code>: 指定参考基因组注释文件</li>
<li><code>-o &lt;output_dir&gt;</code>: 指定输出目录</li>
<li><code>-p &lt;threads&gt;</code>: 指定线程数</li>
</ul>
</li>
<li>
<p><strong>转录本组装</strong></p>
<ul>
<li>使用Cufflinks进行转录本组装：<pre class="hljs"><code><div>cufflinks -g Homo_sapiens.GRCh38.104.gtf -o cufflinks_out Aligned.sortedByCoord.out.bam -p 8
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>表达量估计</strong></p>
<ul>
<li>Cufflinks可以同时进行转录本组装和表达量估计。</li>
<li>Cufflinks输出FPKM (Fragments Per Kilobase per Million) 表达量。</li>
</ul>
</li>
<li>
<p><strong>Cuffmerge使用</strong></p>
<ul>
<li>将多个样本的转录本组装结果合并成一个文件：<pre class="hljs"><code><div>cuffmerge -g Homo_sapiens.GRCh38.104.gtf -s Homo_sapiens.GRCh38.dna.primary_assembly.fa assembly_list.txt
</div></code></pre>
<ul>
<li><code>assembly_list.txt</code> 文件包含所有样本的GTF文件路径。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>下载BAM文件。</li>
<li>下载参考基因组和基因注释文件。</li>
<li>使用Cufflinks进行转录本组装和表达量估计。</li>
<li>将多个样本的转录本组装结果合并成一个文件。</li>
</ol>
</li>
</ul>
<h3 id="3-%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E8%B4%A8%E9%87%8F%E8%AF%84%E4%BC%B0">3. 转录本组装质量评估</h3>
<ul>
<li>
<p><strong>与参考注释比较</strong></p>
<ul>
<li>将组装结果与参考注释进行比较，评估组装的准确性。</li>
</ul>
</li>
<li>
<p><strong>新转录本评估</strong></p>
<ul>
<li>评估新发现的转录本的可靠性。</li>
</ul>
</li>
<li>
<p><strong>GffCompare工具使用</strong></p>
<ul>
<li>GffCompare 是一种常用的转录本组装质量评估工具。</li>
<li>GffCompare可以将组装结果与参考注释进行比较，并生成评估报告。</li>
</ul>
</li>
<li>
<p><strong>组装可视化检查</strong></p>
<ul>
<li>使用IGV等可视化工具检查组装结果，评估组装的准确性。</li>
</ul>
</li>
</ul>
<h3 id="4-%E8%A1%A8%E8%BE%BE%E7%9F%A9%E9%98%B5%E7%94%9F%E6%88%90">4. 表达矩阵生成</h3>
<ul>
<li>
<p><strong>StringTie表达矩阵提取</strong></p>
<ul>
<li>使用StringTie自带的<code>prepDE.py</code>脚本提取表达矩阵。<pre class="hljs"><code><div>prepDE.py -i gtf_file_list.txt
</div></code></pre>
<ul>
<li><code>gtf_file_list.txt</code> 文件包含所有样本的GTF文件路径。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>表达矩阵格式转换</strong></p>
<ul>
<li>将表达矩阵转换为适合后续分析的格式。</li>
</ul>
</li>
<li>
<p><strong>表达数据初步探索</strong></p>
<ul>
<li>对表达数据进行初步探索，例如：
<ul>
<li>绘制表达量分布图</li>
<li>进行PCA分析</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>使用StringTie或Cufflinks进行转录本组装和表达量估计。</li>
<li>使用<code>prepDE.py</code>脚本提取表达矩阵。</li>
<li>对表达数据进行初步探索。</li>
</ol>
</li>
</ul>
<p><strong>总结</strong></p>
<p>转录本组装和定量是RNA-seq数据分析的重要步骤。掌握StringTie和Cufflinks的使用方法，并对组装结果进行质量评估，可以为后续的差异表达分析奠定基础。</p>
<h2 id="deseq2edger%E8%BF%9B%E8%A1%8C%E5%B7%AE%E5%BC%82%E8%A1%A8%E8%BE%BE%E5%88%86%E6%9E%90">DESeq2/edgeR进行差异表达分析</h2>
<h3 id="1-r%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">1. R环境准备</h3>
<ul>
<li>
<p><strong>R/RStudio安装</strong></p>
<ul>
<li>从R官网下载R安装包，并安装。</li>
<li>从RStudio官网下载RStudio安装包，并安装。</li>
</ul>
</li>
<li>
<p><strong>Bioconductor配置</strong></p>
<ul>
<li>Bioconductor是一个R包的集合，专门用于生物信息学分析。</li>
<li>使用以下命令安装Bioconductor：<pre class="hljs"><code><div><span class="hljs-keyword">if</span> (!requireNamespace(<span class="hljs-string">"BiocManager"</span>, quietly = <span class="hljs-literal">TRUE</span>))
    install.packages(<span class="hljs-string">"BiocManager"</span>)
BiocManager::install(version = <span class="hljs-string">"3.14"</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>必要包安装</strong></p>
<ul>
<li>安装DESeq2和edgeR等R包：<pre class="hljs"><code><div>BiocManager::install(<span class="hljs-string">"DESeq2"</span>)
BiocManager::install(<span class="hljs-string">"edgeR"</span>)
BiocManager::install(<span class="hljs-string">"ggplot2"</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>工作环境设置</strong></p>
<ul>
<li>设置R的工作目录，方便管理文件。<pre class="hljs"><code><div>setwd(<span class="hljs-string">"/path/to/your/working/directory"</span>)
</div></code></pre>
</li>
</ul>
</li>
</ul>
<h3 id="2-deseq2%E5%B7%AE%E5%BC%82%E8%A1%A8%E8%BE%BE%E5%88%86%E6%9E%90">2. DESeq2差异表达分析</h3>
<ul>
<li>
<p><strong>数据导入</strong></p>
<ul>
<li><strong>计数矩阵格式要求</strong>
<ul>
<li>计数矩阵是一个表格，行表示基因或转录本，列表示样本，每个元素表示该基因或转录本在该样本中的读段计数。</li>
</ul>
</li>
<li><strong>样本信息表格式</strong>
<ul>
<li>样本信息表是一个表格，包含样本的各种信息，如组别、处理方式等。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>DESeqDataSet对象创建</strong></p>
<ul>
<li>使用<code>DESeqDataSetFromMatrix</code>函数创建DESeqDataSet对象：<pre class="hljs"><code><div>dds &lt;- DESeqDataSetFromMatrix(countData = countData,
                              colData = sampleInfo,
                              design = ~ condition)
</div></code></pre>
<ul>
<li><code>countData</code>: 计数矩阵</li>
<li><code>sampleInfo</code>: 样本信息表</li>
<li><code>design</code>: 实验设计公式</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>数据预处理</strong></p>
<ul>
<li><strong>低表达基因过滤</strong>
<ul>
<li>去除低表达基因，减少假阳性。</li>
</ul>
<pre class="hljs"><code><div>keep &lt;- rowSums(counts(dds)) &gt;= <span class="hljs-number">10</span>
dds &lt;- dds[keep,]
</div></code></pre>
</li>
<li><strong>数据转换</strong>
<ul>
<li>使用<code>vst</code>函数或<code>rlog</code>函数对数据进行转换，使其满足线性模型的假设。</li>
</ul>
<pre class="hljs"><code><div>vsd &lt;- vst(dds, blind = <span class="hljs-literal">FALSE</span>)
</div></code></pre>
</li>
<li><strong>异常值检测</strong>
<ul>
<li>使用PCA等方法检测异常值。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>差异表达分析</strong></p>
<ul>
<li><strong>设计矩阵构建</strong>
<ul>
<li>根据实验设计，构建设计矩阵。</li>
</ul>
<pre class="hljs"><code><div>design(dds) &lt;- ~ condition
</div></code></pre>
</li>
<li><strong>DESeq函数运行</strong>
<ul>
<li>使用<code>DESeq</code>函数进行差异表达分析。</li>
</ul>
<pre class="hljs"><code><div>dds &lt;- DESeq(dds)
</div></code></pre>
</li>
<li><strong>结果提取与过滤</strong>
<ul>
<li>使用<code>results</code>函数提取差异表达分析结果。</li>
</ul>
<pre class="hljs"><code><div>res &lt;- results(dds)
res &lt;- res[order(res$padj),]
</div></code></pre>
<ul>
<li>根据P值和log2FoldChange等指标，对结果进行过滤。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>批次效应校正</strong></p>
<ul>
<li>如果存在批次效应，可以使用<code>removeBatchEffect</code>函数进行校正。</li>
</ul>
</li>
<li>
<p><strong>结果可视化</strong></p>
<ul>
<li><strong>MA图</strong><pre class="hljs"><code><div>plotMA(res, ylim=c(-<span class="hljs-number">5</span>,<span class="hljs-number">5</span>))
</div></code></pre>
</li>
<li><strong>火山图</strong><pre class="hljs"><code><div>EnhancedVolcano(res,
                lab = rownames(res),
                x = <span class="hljs-string">'log2FoldChange'</span>,
                y = <span class="hljs-string">'padj'</span>,
                pCutoff = <span class="hljs-number">0.05</span>,
                FCcutoff = <span class="hljs-number">1.0</span>)
</div></code></pre>
</li>
<li><strong>热图</strong><pre class="hljs"><code><div>pheatmap(assay(vsd)[head(rownames(res),<span class="hljs-number">20</span>),], cluster_rows=<span class="hljs-literal">TRUE</span>, show_rownames=<span class="hljs-literal">TRUE</span>,
         cluster_cols=<span class="hljs-literal">TRUE</span>, annotation_col=as.data.frame(colData(dds)[,<span class="hljs-string">"condition"</span>]))
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备计数矩阵和样本信息表。</li>
<li>使用DESeq2进行差异表达分析。</li>
<li>对结果进行过滤和可视化。</li>
</ol>
</li>
</ul>
<h3 id="3-edger%E5%B7%AE%E5%BC%82%E8%A1%A8%E8%BE%BE%E5%88%86%E6%9E%90">3. edgeR差异表达分析</h3>
<ul>
<li>
<p><strong>数据导入与预处理</strong></p>
<ul>
<li>使用<code>readDGE</code>函数导入计数矩阵和样本信息表。<pre class="hljs"><code><div>dge &lt;- readDGE(countData, group=factor(sampleInfo$condition))
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>DGEList对象创建</strong></p>
<ul>
<li>创建DGEList对象。</li>
</ul>
</li>
<li>
<p><strong>数据标准化</strong></p>
<ul>
<li>使用<code>calcNormFactors</code>函数进行数据标准化。<pre class="hljs"><code><div>dge &lt;- calcNormFactors(dge)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>离散度估计</strong></p>
<ul>
<li>使用<code>estimateDisp</code>函数估计离散度。<pre class="hljs"><code><div>dge &lt;- estimateDisp(dge, design)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>差异表达分析</strong></p>
<ul>
<li><strong>精确检验</strong><pre class="hljs"><code><div>et &lt;- exactTest(dge)
</div></code></pre>
</li>
<li><strong>广义线性模型</strong><pre class="hljs"><code><div>fit &lt;- glmQLFit(dge, design)
qlf &lt;- glmQLFTest(fit, coef=<span class="hljs-number">2</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>结果提取与过滤</strong></p>
<ul>
<li>使用<code>topTags</code>函数提取差异表达分析结果。<pre class="hljs"><code><div>topTags(et)
</div></code></pre>
</li>
<li>根据P值和logFC等指标，对结果进行过滤。</li>
</ul>
</li>
<li>
<p><strong>结果可视化</strong></p>
<ul>
<li>使用<code>plotMD</code>函数绘制MA图。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备计数矩阵和样本信息表。</li>
<li>使用edgeR进行差异表达分析。</li>
<li>对结果进行过滤和可视化。</li>
</ol>
</li>
</ul>
<h3 id="4-%E5%B7%AE%E5%BC%82%E8%A1%A8%E8%BE%BE%E7%BB%93%E6%9E%9C%E8%A7%A3%E8%AF%BB">4. 差异表达结果解读</h3>
<ul>
<li>
<p><strong>结果表格字段解释</strong></p>
<ul>
<li>logFC：log2倍数变化</li>
<li>logCPM：log2 counts per million</li>
<li>LR：似然比统计量</li>
<li>PValue：P值</li>
<li>FDR：FDR校正后的P值</li>
</ul>
</li>
<li>
<p><strong>显著性阈值设定</strong></p>
<ul>
<li>常用的显著性阈值包括：
<ul>
<li>P值 &lt; 0.05</li>
<li>FDR &lt; 0.05</li>
<li>log2FoldChange &gt; 1 或 log2FoldChange &lt; -1</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>上调/下调基因筛选</strong></p>
<ul>
<li>根据log2FoldChange的值，筛选出上调基因和下调基因。</li>
</ul>
</li>
<li>
<p><strong>结果导出与保存</strong></p>
<ul>
<li>将差异表达分析结果导出为文件，方便后续分析。</li>
</ul>
</li>
<li>
<p><strong>与DESeq2结果比较</strong></p>
<ul>
<li>比较DESeq2和edgeR的差异表达分析结果，评估结果的一致性。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>差异表达分析是RNA-seq数据分析的核心内容。掌握DESeq2和edgeR的使用方法，并对结果进行解读和验证, 可以获得准确可靠的差异表达基因信息.</p>
<h2 id="clusterprofiler%E8%BF%9B%E8%A1%8C%E5%8A%9F%E8%83%BD%E5%AF%8C%E9%9B%86%E5%88%86%E6%9E%90">ClusterProfiler进行功能富集分析</h2>
<h3 id="1-clusterprofiler%E5%AE%89%E8%A3%85%E4%B8%8E%E9%85%8D%E7%BD%AE">1. ClusterProfiler安装与配置</h3>
<ul>
<li>
<p><strong>包安装</strong></p>
<ul>
<li>ClusterProfiler是一个R包，用于进行功能富集分析。</li>
<li>使用BiocManager安装ClusterProfiler：<pre class="hljs"><code><div>BiocManager::install(<span class="hljs-string">"clusterProfiler"</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>注释数据库准备</strong></p>
<ul>
<li>ClusterProfiler需要使用注释数据库进行功能富集分析。</li>
<li>常用的注释数据库包括：
<ul>
<li>GO (Gene Ontology)</li>
<li>KEGG (Kyoto Encyclopedia of Genes and Genomes)</li>
<li>Reactome</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>物种设置</strong></p>
<ul>
<li>根据研究物种，设置相应的物种信息。</li>
<li>例如，对于人类基因组，可以使用<code>org.Hs.eg.db</code>包：<pre class="hljs"><code><div>BiocManager::install(<span class="hljs-string">"org.Hs.eg.db"</span>)
<span class="hljs-keyword">library</span>(org.Hs.eg.db)
</div></code></pre>
</li>
</ul>
</li>
</ul>
<h3 id="2-go%E5%AF%8C%E9%9B%86%E5%88%86%E6%9E%90">2. GO富集分析</h3>
<ul>
<li>
<p><strong>基因ID转换</strong></p>
<ul>
<li>将基因ID转换为Entrez ID，用于GO富集分析。<pre class="hljs"><code><div>gene &lt;- bitr(geneList, fromType=<span class="hljs-string">"SYMBOL"</span>,
              toType=<span class="hljs-string">"ENTREZID"</span>,
              Orgdb=<span class="hljs-string">"org.Hs.eg.db"</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>enrichGO函数使用</strong></p>
<ul>
<li>使用<code>enrichGO</code>函数进行GO富集分析。<pre class="hljs"><code><div>ego &lt;- enrichGO(gene          = gene$ENTREZID,
                Orgdb         = org.Hs.eg.db,
                ont           = <span class="hljs-string">"BP"</span>,
                pAdjustMethod = <span class="hljs-string">"BH"</span>,
                pvalueCutoff  = <span class="hljs-number">0.05</span>,
                qvalueCutoff  = <span class="hljs-number">0.05</span>,
                readable      = <span class="hljs-literal">TRUE</span>)
</div></code></pre>
</li>
<li><strong>参数设置与优化</strong>
<ul>
<li><code>gene</code>: 基因ID列表</li>
<li><code>Orgdb</code>: 物种信息</li>
<li><code>ont</code>: GO类别（BP, CC, MF）</li>
<li><code>pAdjustMethod</code>: 多重检验校正方法</li>
<li><code>pvalueCutoff</code>: P值阈值</li>
<li><code>qvalueCutoff</code>: Q值阈值</li>
<li><code>readable</code>: 是否显示基因名称</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>结果解读</strong></p>
<ul>
<li><code>ID</code>: GO term ID</li>
<li><code>Description</code>: GO term描述</li>
<li><code>GeneRatio</code>: 差异表达基因中，属于该GO term的基因比例</li>
<li><code>BgRatio</code>: 所有基因中，属于该GO term的基因比例</li>
<li><code>pvalue</code>: P值</li>
<li><code>p.adjust</code>: 校正后的P值</li>
<li><code>qvalue</code>: Q值</li>
</ul>
</li>
<li>
<p><strong>可视化方法</strong></p>
<ul>
<li><strong>条形图</strong><pre class="hljs"><code><div>barplot(ego, showCategory=<span class="hljs-number">20</span>)
</div></code></pre>
</li>
<li><strong>点图</strong><pre class="hljs"><code><div>dotplot(ego, showCategory=<span class="hljs-number">20</span>)
</div></code></pre>
</li>
<li><strong>网络图</strong><pre class="hljs"><code><div>plotGOgraph(ego)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备差异表达基因列表。</li>
<li>将基因ID转换为Entrez ID。</li>
<li>使用<code>enrichGO</code>函数进行GO富集分析。</li>
<li>解读GO富集分析结果。</li>
<li>使用条形图、点图等可视化方法展示富集结果。</li>
</ol>
</li>
</ul>
<h3 id="3-kegg%E9%80%9A%E8%B7%AF%E5%AF%8C%E9%9B%86%E5%88%86%E6%9E%90">3. KEGG通路富集分析</h3>
<ul>
<li>
<p><strong>enrichKEGG函数使用</strong></p>
<ul>
<li>使用<code>enrichKEGG</code>函数进行KEGG通路富集分析。<pre class="hljs"><code><div>kk &lt;- enrichKEGG(gene          = gene$ENTREZID,
                 organism      = <span class="hljs-string">'hsa'</span>,
                 pvalueCutoff  = <span class="hljs-number">0.05</span>,
                 qvalueCutoff  = <span class="hljs-number">0.05</span>)
</div></code></pre>
</li>
<li><strong>参数设置与优化</strong>
<ul>
<li><code>gene</code>: 基因ID列表</li>
<li><code>organism</code>: 物种信息（hsa：人类）</li>
<li><code>pvalueCutoff</code>: P值阈值</li>
<li><code>qvalueCutoff</code>: Q值阈值</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>结果解读</strong></p>
<ul>
<li><code>ID</code>: KEGG通路ID</li>
<li><code>Description</code>: KEGG通路描述</li>
<li><code>GeneRatio</code>: 差异表达基因中，属于该KEGG通路的基因比例</li>
<li><code>BgRatio</code>: 所有基因中，属于该KEGG通路的基因比例</li>
<li><code>pvalue</code>: P值</li>
<li><code>p.adjust</code>: 校正后的P值</li>
<li><code>qvalue</code>: Q值</li>
</ul>
</li>
<li>
<p><strong>通路图可视化</strong></p>
<ul>
<li>使用<code>pathview</code>包可视化KEGG通路。<pre class="hljs"><code><div>pathview(gene.data  = geneList,
         pathway.id = <span class="hljs-string">"hsa04110"</span>,
         species    = <span class="hljs-string">"hsa"</span>,
         limit      = list(gene=max(abs(geneList)), cpd=<span class="hljs-number">1</span>))
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备差异表达基因列表。</li>
<li>将基因ID转换为Entrez ID。</li>
<li>使用<code>enrichKEGG</code>函数进行KEGG通路富集分析。</li>
<li>解读KEGG通路富集分析结果。</li>
<li>使用<code>pathview</code>包可视化KEGG通路。</li>
</ol>
</li>
</ul>
<h3 id="4-%E5%9F%BA%E5%9B%A0%E9%9B%86%E5%AF%8C%E9%9B%86%E5%88%86%E6%9E%90gsea">4. 基因集富集分析(GSEA)</h3>
<ul>
<li>
<p><strong>排序列表准备</strong></p>
<ul>
<li>准备一个包含所有基因的排序列表，按照差异表达倍数排序。</li>
</ul>
</li>
<li>
<p><strong>GSEA函数使用</strong></p>
<ul>
<li>使用<code>gseGO</code>函数进行GSEA分析。<pre class="hljs"><code><div>gse &lt;- gseGO(geneList     = geneList,
             Orgdb        = org.Hs.eg.db,
             ont          = <span class="hljs-string">"BP"</span>,
             nPerm        = <span class="hljs-number">10000</span>,
             pvalueCutoff = <span class="hljs-number">0.05</span>,
             verbose      = <span class="hljs-literal">FALSE</span>)
</div></code></pre>
</li>
<li><strong>结果解读</strong>
<ul>
<li>NES (Normalized Enrichment Score)：标准化富集分数</li>
<li>pvalue：P值</li>
<li>FDR：FDR校正后的P值</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>富集图可视化</strong></p>
<ul>
<li>使用<code>gseaplot2</code>函数可视化GSEA结果。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备差异表达基因列表。</li>
<li>将基因ID转换为Entrez ID。</li>
<li>使用<code>gseGO</code>函数进行GSEA分析。</li>
<li>解读GSEA结果。</li>
<li>使用<code>gseaplot2</code>函数可视化GSEA结果。</li>
</ol>
</li>
</ul>
<h3 id="5-%E5%AF%8C%E9%9B%86%E7%BB%93%E6%9E%9C%E6%95%B4%E5%90%88%E4%B8%8E%E8%A7%A3%E8%AF%BB">5. 富集结果整合与解读</h3>
<ul>
<li>
<p><strong>多种富集结果比较</strong></p>
<ul>
<li>比较GO富集分析、KEGG通路富集分析和GSEA的结果，寻找一致的结论。</li>
</ul>
</li>
<li>
<p><strong>生物学意义解释</strong></p>
<ul>
<li>结合文献资料和实验数据，对富集结果进行生物学解释。</li>
</ul>
</li>
<li>
<p><strong>结果导出与报告</strong></p>
<ul>
<li>将富集结果导出为文件，方便分享和交流。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>功能富集分析是RNA-seq数据分析的重要步骤。掌握ClusterProfiler的使用方法，并对富集结果进行解读和验证，可以帮助我们了解差异表达基因的功能和参与的通路，为后续的实验验证提供依据.</p>
<h2 id="%E8%A1%A8%E8%BE%BE%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96">表达数据可视化</h2>
<h3 id="1-%E7%83%AD%E5%9B%BE%E7%BB%98%E5%88%B6">1. 热图绘制</h3>
<ul>
<li>
<p><strong>pheatmap包使用</strong></p>
<ul>
<li>pheatmap是一个R包，用于绘制热图。</li>
<li>使用BiocManager安装pheatmap：<pre class="hljs"><code><div>BiocManager::install(<span class="hljs-string">"pheatmap"</span>)
<span class="hljs-keyword">library</span>(pheatmap)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>数据准备与转换</strong></p>
<ul>
<li>准备表达矩阵，行表示基因或转录本，列表示样本。</li>
<li>对数据进行log2转换。</li>
</ul>
</li>
<li>
<p><strong>聚类参数设置</strong></p>
<ul>
<li>设置聚类参数，例如：
<ul>
<li><code>cluster_rows</code>: 是否对行进行聚类</li>
<li><code>cluster_cols</code>: 是否对列进行聚类</li>
<li><code>clustering_distance_rows</code>: 行聚类距离计算方法</li>
<li><code>clustering_method</code>: 聚类方法</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>注释信息添加</strong></p>
<ul>
<li>添加注释信息，例如：
<ul>
<li>样本组别</li>
<li>基因功能</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>配色方案选择</strong></p>
<ul>
<li>选择合适的配色方案，使热图更易于解读。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<pre class="hljs"><code><div><span class="hljs-comment"># 准备数据</span>
data &lt;- read.table(<span class="hljs-string">"expression_matrix.txt"</span>, header=<span class="hljs-literal">TRUE</span>, row.names=<span class="hljs-number">1</span>)
data &lt;- log2(data + <span class="hljs-number">1</span>)

<span class="hljs-comment"># 准备样本信息</span>
annotation &lt;- data.frame(condition = factor(sampleInfo$condition))
rownames(annotation) &lt;- colnames(data)

<span class="hljs-comment"># 绘制热图</span>
pheatmap(data,
         cluster_rows = <span class="hljs-literal">TRUE</span>,
         cluster_cols = <span class="hljs-literal">TRUE</span>,
         annotation_col = annotation,
         color = colorRampPalette(c(<span class="hljs-string">"blue"</span>, <span class="hljs-string">"white"</span>, <span class="hljs-string">"red"</span>))(<span class="hljs-number">100</span>))
</div></code></pre>
</li>
</ul>
<h3 id="2-%E7%81%AB%E5%B1%B1%E5%9B%BE%E7%BB%98%E5%88%B6">2. 火山图绘制</h3>
<ul>
<li>
<p><strong>EnhancedVolcano包使用</strong></p>
<ul>
<li>EnhancedVolcano是一个R包，用于绘制火山图。</li>
<li>使用BiocManager安装EnhancedVolcano：<pre class="hljs"><code><div>BiocManager::install(<span class="hljs-string">"EnhancedVolcano"</span>)
<span class="hljs-keyword">library</span>(EnhancedVolcano)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>数据准备</strong></p>
<ul>
<li>准备差异表达分析结果，包括log2FoldChange和P值。</li>
</ul>
</li>
<li>
<p><strong>参数设置</strong></p>
<ul>
<li>设置火山图参数，例如：
<ul>
<li><code>x</code>: log2FoldChange</li>
<li><code>y</code>: P值</li>
<li><code>pCutoff</code>: P值阈值</li>
<li><code>FCcutoff</code>: log2FoldChange阈值</li>
<li><code>lab</code>: 基因名称</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>标签添加</strong></p>
<ul>
<li>添加基因名称标签，突出显示重要的基因。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<pre class="hljs"><code><div><span class="hljs-comment"># 准备数据</span>
res &lt;- read.table(<span class="hljs-string">"deseq2_results.txt"</span>, header=<span class="hljs-literal">TRUE</span>, row.names=<span class="hljs-number">1</span>)

<span class="hljs-comment"># 绘制火山图</span>
EnhancedVolcano(res,
                lab = rownames(res),
                x = <span class="hljs-string">'log2FoldChange'</span>,
                y = <span class="hljs-string">'padj'</span>,
                pCutoff = <span class="hljs-number">0.05</span>,
                FCcutoff = <span class="hljs-number">1.0</span>)
</div></code></pre>
</li>
</ul>
<h3 id="3-pca%E5%88%86%E6%9E%90%E4%B8%8E%E5%8F%AF%E8%A7%86%E5%8C%96">3. PCA分析与可视化</h3>
<ul>
<li>
<p><strong>PCA原理简介</strong></p>
<ul>
<li>PCA (Principal Component Analysis) 是一种常用的降维方法。</li>
<li>PCA可以将高维数据转换为低维数据，同时保留数据的主要特征。</li>
</ul>
</li>
<li>
<p><strong>prcomp函数使用</strong></p>
<ul>
<li>使用R语言的<code>prcomp</code>函数进行PCA分析。<pre class="hljs"><code><div>pca &lt;- prcomp(t(data), scale. = <span class="hljs-literal">TRUE</span>)
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>ggplot2绘图</strong></p>
<ul>
<li>使用ggplot2包绘制PCA散点图。<pre class="hljs"><code><div><span class="hljs-keyword">library</span>(ggplot2)
pcaData &lt;- data.frame(PC1=pca$x[,<span class="hljs-number">1</span>], PC2=pca$x[,<span class="hljs-number">2</span>], condition=sampleInfo$condition)
ggplot(pcaData, aes(PC1, PC2, color=condition)) +
  geom_point(size=<span class="hljs-number">3</span>) +
  xlab(paste0(<span class="hljs-string">"PC1: "</span>,percentVar[<span class="hljs-number">1</span>],<span class="hljs-string">"% variance"</span>)) +
  ylab(paste0(<span class="hljs-string">"PC2: "</span>,percentVar[<span class="hljs-number">2</span>],<span class="hljs-string">"% variance"</span>)) +
  coord_fixed()
</div></code></pre>
</li>
</ul>
</li>
<li>
<p><strong>结果解读</strong></p>
<ul>
<li>PCA散点图可以展示样本之间的相似性和差异性。</li>
<li>PC1和PC2分别表示第一主成分和第二主成分，解释了数据中最大的变异。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ol>
<li>准备表达矩阵。</li>
<li>使用<code>prcomp</code>函数进行PCA分析。</li>
<li>使用ggplot2绘制PCA散点图。</li>
</ol>
</li>
</ul>
<h3 id="4-%E5%85%B6%E4%BB%96%E5%8F%AF%E8%A7%86%E5%8C%96%E6%96%B9%E6%B3%95">4. 其他可视化方法</h3>
<ul>
<li>
<p><strong>箱线图</strong></p>
<ul>
<li>用于展示基因在不同组别之间的表达水平分布。</li>
</ul>
</li>
<li>
<p><strong>小提琴图</strong></p>
<ul>
<li>用于展示基因在不同组别之间的表达水平分布，比箱线图更详细。</li>
</ul>
</li>
<li>
<p><strong>MA图</strong></p>
<ul>
<li>用于展示差异表达分析结果，以平均表达量为横坐标，以log2FoldChange为纵坐标。</li>
</ul>
</li>
<li>
<p><strong>基因表达趋势图</strong></p>
<ul>
<li>用于展示基因在不同时间点或不同处理条件下的表达趋势。</li>
</ul>
</li>
<li>
<p><strong>实际操作演示</strong></p>
<ul>
<li>使用ggplot2等R包绘制各种可视化图表。</li>
</ul>
</li>
</ul>
<p><strong>总结</strong></p>
<p>表达数据可视化是RNA-seq数据分析的重要手段。掌握各种可视化方法，可以帮助我们更好地理解和解释RNA-seq数据。</p>
<hr>
<h2 id="%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86stringtie%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E4%B8%8E%E5%AE%9A%E9%87%8F%E5%AE%9E%E8%B7%B5">第二部分：StringTie转录本组装与定量实践</h2>
<h3 id="21-stringtie%E5%9F%BA%E7%A1%80%E6%93%8D%E4%BD%9C">2.1 StringTie基础操作</h3>
<h4 id="211-%E5%8F%82%E8%80%83%E6%8C%87%E5%AF%BC%E7%9A%84%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85">2.1.1 参考指导的转录本组装</h4>
<p><strong>实验目标：</strong> 使用参考注释进行转录本组装，理解已知转录本的定量</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== StringTie参考指导组装实践 ==="</span>

<span class="hljs-comment"># 1. 使用HISAT2比对结果进行StringTie组装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Control样本转录本组装..."</span>
stringtie results/alignment/control_hisat2.sorted.bam \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/control_stringtie.gtf \
    -A results/assembly/control_gene_abundances.tab \
    -e \
    -B \
    -p 2

<span class="hljs-built_in">echo</span> <span class="hljs-string">"Treated样本转录本组装..."</span>
stringtie results/alignment/treated_hisat2.sorted.bam \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/treated_stringtie.gtf \
    -A results/assembly/treated_gene_abundances.tab \
    -e \
    -B \
    -p 2

<span class="hljs-comment"># 参数解释：</span>
cat &lt;&lt; <span class="hljs-string">'EOF'</span>
=== StringTie参数详解 ===
-G: 参考注释GTF文件
-o: 输出GTF文件
-A: 基因丰度输出文件
-e: 只估计参考转录本的表达量（不组装新转录本）
-B: 输出Ballgown格式文件用于下游分析
-p: 线程数
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"StringTie组装完成！"</span>
</div></code></pre>
<h4 id="212-%E8%BD%AC%E5%BD%95%E6%9C%AC%E7%BB%84%E8%A3%85%E7%BB%93%E6%9E%9C%E5%88%86%E6%9E%90">2.1.2 转录本组装结果分析</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建StringTie结果分析脚本</span>
cat &gt; scripts/analyze_stringtie_results.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
StringTie结果分析脚本
分析转录本组装和基因表达定量结果
"</span><span class="hljs-string">""</span>

import pandas as pd
import numpy as np

def analyze_gene_abundances(file_path, sample_name):
    <span class="hljs-string">""</span><span class="hljs-string">"分析基因丰度文件"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== {sample_name} 基因表达分析 ==="</span>)
    
    try:
        <span class="hljs-comment"># 读取基因丰度文件</span>
        df = pd.read_csv(file_path, sep=<span class="hljs-string">'\t'</span>)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"检测到基因数量: {len(df)}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"表达基因数量 (FPKM&gt;0): {len(df[df['FPKM'] &gt; 0])}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"高表达基因数量 (FPKM&gt;1): {len(df[df['FPKM'] &gt; 1])}"</span>)
        
        <span class="hljs-comment"># 基本统计</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\nFPKM统计:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最大值: {df['FPKM'].max():.2f}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最小值: {df['FPKM'].min():.2f}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"平均值: {df['FPKM'].mean():.2f}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"中位数: {df['FPKM'].median():.2f}"</span>)
        
        <span class="hljs-comment"># 显示表达量最高的基因</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n表达量最高的基因:"</span>)
        top_genes = df.nlargest(5, <span class="hljs-string">'FPKM'</span>)[[<span class="hljs-string">'Gene Name'</span>, <span class="hljs-string">'FPKM'</span>, <span class="hljs-string">'TPM'</span>]]
        <span class="hljs-built_in">print</span>(top_genes.to_string(index=False))
        
        <span class="hljs-built_in">return</span> df
        
    except FileNotFoundError:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"错误: 文件 {file_path} 不存在"</span>)
        <span class="hljs-built_in">return</span> None
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析文件时出错: {e}"</span>)
        <span class="hljs-built_in">return</span> None

def compare_samples(control_df, treated_df):
    <span class="hljs-string">""</span><span class="hljs-string">"比较两个样本的表达情况"</span><span class="hljs-string">""</span>
    <span class="hljs-keyword">if</span> control_df is None or treated_df is None:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"无法进行样本比较：缺少数据"</span>)
        <span class="hljs-built_in">return</span>
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== 样本比较分析 ==="</span>)
    
    <span class="hljs-comment"># 合并数据进行比较</span>
    merged = pd.merge(control_df, treated_df, on=<span class="hljs-string">'Gene ID'</span>, suffixes=(<span class="hljs-string">'_control'</span>, <span class="hljs-string">'_treated'</span>))
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"共同检测到的基因: {len(merged)}"</span>)
    
    <span class="hljs-comment"># 计算差异表达基因数量（简单倍数变化）</span>
    merged[<span class="hljs-string">'fold_change'</span>] = np.log2((merged[<span class="hljs-string">'FPKM_treated'</span>] + 0.1) / (merged[<span class="hljs-string">'FPKM_control'</span>] + 0.1))
    
    <span class="hljs-comment"># 统计差异表达基因</span>
    upregulated = len(merged[merged[<span class="hljs-string">'fold_change'</span>] &gt; 1])  <span class="hljs-comment"># &gt;2倍上调</span>
    downregulated = len(merged[merged[<span class="hljs-string">'fold_change'</span>] &lt; -1])  <span class="hljs-comment"># &gt;2倍下调</span>
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"上调基因数量 (&gt;2倍): {upregulated}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"下调基因数量 (&gt;2倍): {downregulated}"</span>)
    
    <span class="hljs-comment"># 显示变化最大的基因</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n上调最显著的基因:"</span>)
    top_up = merged.nlargest(3, <span class="hljs-string">'fold_change'</span>)[[<span class="hljs-string">'Gene Name_control'</span>, <span class="hljs-string">'FPKM_control'</span>, <span class="hljs-string">'FPKM_treated'</span>, <span class="hljs-string">'fold_change'</span>]]
    <span class="hljs-built_in">print</span>(top_up.to_string(index=False))
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n下调最显著的基因:"</span>)
    top_down = merged.nsmallest(3, <span class="hljs-string">'fold_change'</span>)[[<span class="hljs-string">'Gene Name_control'</span>, <span class="hljs-string">'FPKM_control'</span>, <span class="hljs-string">'FPKM_treated'</span>, <span class="hljs-string">'fold_change'</span>]]
    <span class="hljs-built_in">print</span>(top_down.to_string(index=False))

def analyze_gtf_assembly(gtf_file, sample_name):
    <span class="hljs-string">""</span><span class="hljs-string">"分析GTF组装文件"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== {sample_name} 转录本组装分析 ==="</span>)
    
    try:
        with open(gtf_file, <span class="hljs-string">'r'</span>) as f:
            lines = f.readlines()
        
        <span class="hljs-comment"># 统计各种特征类型</span>
        feature_counts = {}
        <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> lines:
            <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'#'</span>):
                <span class="hljs-built_in">continue</span>
            fields = line.strip().split(<span class="hljs-string">'\t'</span>)
            <span class="hljs-keyword">if</span> len(fields) &gt;= 3:
                feature = fields[2]
                feature_counts[feature] = feature_counts.get(feature, 0) + 1
        
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"组装特征统计:"</span>)
        <span class="hljs-keyword">for</span> feature, count <span class="hljs-keyword">in</span> feature_counts.items():
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"{feature}: {count}"</span>)
            
    except FileNotFoundError:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"错误: GTF文件 {gtf_file} 不存在"</span>)
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析GTF文件时出错: {e}"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    <span class="hljs-comment"># 分析基因表达结果</span>
    control_df = analyze_gene_abundances(<span class="hljs-string">"results/assembly/control_gene_abundances.tab"</span>, <span class="hljs-string">"Control"</span>)
    treated_df = analyze_gene_abundances(<span class="hljs-string">"results/assembly/treated_gene_abundances.tab"</span>, <span class="hljs-string">"Treated"</span>)
    
    <span class="hljs-comment"># 比较样本</span>
    compare_samples(control_df, treated_df)
    
    <span class="hljs-comment"># 分析转录本组装结果</span>
    analyze_gtf_assembly(<span class="hljs-string">"results/assembly/control_stringtie.gtf"</span>, <span class="hljs-string">"Control"</span>)
    analyze_gtf_assembly(<span class="hljs-string">"results/assembly/treated_stringtie.gtf"</span>, <span class="hljs-string">"Treated"</span>)
EOF

chmod +x scripts/analyze_stringtie_results.py
python3 scripts/analyze_stringtie_results.py
</div></code></pre>
<h3 id="22-featurecounts%E5%AE%9A%E9%87%8F%E5%88%86%E6%9E%90">2.2 featureCounts定量分析</h3>
<h4 id="221-%E5%9F%BA%E4%BA%8E%E5%9F%BA%E5%9B%A0%E7%9A%84%E8%AF%BB%E6%95%B0%E8%AE%A1%E6%95%B0">2.2.1 基于基因的读数计数</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== featureCounts基因计数分析 ==="</span>

<span class="hljs-comment"># 1. 对HISAT2比对结果进行基因计数</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"使用featureCounts进行基因计数..."</span>
featureCounts -a data/reference/chr22_mini.gtf \
    -o results/quantification/gene_counts.txt \
    -g gene_id \
    -t exon \
    -T 2 \
    results/alignment/control_hisat2.sorted.bam \
    results/alignment/treated_hisat2.sorted.bam

<span class="hljs-comment"># 2. 检查计数结果</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== featureCounts结果概览 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"计数文件前10行:"</span>
head -10 results/quantification/gene_counts.txt

<span class="hljs-comment"># 3. 提取计数统计信息</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n计数统计信息:"</span>
tail -n +2 results/quantification/gene_counts.txt.summary

<span class="hljs-comment"># 4. 创建清理后的计数矩阵</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 创建表达矩阵 ==="</span>
cat &gt; scripts/process_counts.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
处理featureCounts输出，创建表达矩阵
"</span><span class="hljs-string">""</span>

import pandas as pd

def process_feature_counts(input_file, output_file):
    <span class="hljs-string">""</span><span class="hljs-string">"处理featureCounts输出文件"</span><span class="hljs-string">""</span>
    
    <span class="hljs-comment"># 读取featureCounts输出</span>
    df = pd.read_csv(input_file, sep=<span class="hljs-string">'\t'</span>, comment=<span class="hljs-string">'#'</span>)
    
    <span class="hljs-comment"># 提取基因ID和计数列</span>
    gene_counts = df[[<span class="hljs-string">'Geneid'</span>] + [col <span class="hljs-keyword">for</span> col <span class="hljs-keyword">in</span> df.columns <span class="hljs-keyword">if</span> col.endswith(<span class="hljs-string">'.bam'</span>)]]
    
    <span class="hljs-comment"># 重命名列</span>
    new_columns = [<span class="hljs-string">'gene_id'</span>]
    <span class="hljs-keyword">for</span> col <span class="hljs-keyword">in</span> gene_counts.columns[1:]:
        <span class="hljs-comment"># 从路径中提取样本名称</span>
        sample_name = col.split(<span class="hljs-string">'/'</span>)[-1].replace(<span class="hljs-string">'_hisat2.sorted.bam'</span>, <span class="hljs-string">''</span>)
        new_columns.append(sample_name)
    
    gene_counts.columns = new_columns
    
    <span class="hljs-comment"># 保存处理后的矩阵</span>
    gene_counts.to_csv(output_file, sep=<span class="hljs-string">'\t'</span>, index=False)
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"表达矩阵已保存到: {output_file}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"基因数量: {len(gene_counts)}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"样本数量: {len(gene_counts.columns) - 1}"</span>)
    
    <span class="hljs-comment"># 显示矩阵概览</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n表达矩阵概览:"</span>)
    <span class="hljs-built_in">print</span>(gene_counts.head())
    
    <span class="hljs-comment"># 基本统计</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n表达统计:"</span>)
    <span class="hljs-keyword">for</span> col <span class="hljs-keyword">in</span> gene_counts.columns[1:]:
        total_reads = gene_counts[col].sum()
        expressed_genes = len(gene_counts[gene_counts[col] &gt; 0])
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"{col}: 总reads={total_reads}, 表达基因={expressed_genes}"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    process_feature_counts(<span class="hljs-string">"results/quantification/gene_counts.txt"</span>, 
                          <span class="hljs-string">"results/quantification/gene_expression_matrix.txt"</span>)
EOF

python3 scripts/process_counts.py
</div></code></pre>
<h3 id="23-%E5%A4%9A%E6%A0%B7%E6%9C%AC%E8%BD%AC%E5%BD%95%E6%9C%AC%E5%90%88%E5%B9%B6">2.3 多样本转录本合并</h3>
<h4 id="231-%E5%88%9B%E5%BB%BA%E6%A0%B7%E6%9C%AC%E5%88%97%E8%A1%A8%E5%92%8C%E5%90%88%E5%B9%B6%E8%BD%AC%E5%BD%95%E6%9C%AC">2.3.1 创建样本列表和合并转录本</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 多样本转录本合并 ==="</span>

<span class="hljs-comment"># 1. 创建样本列表文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"创建样本列表..."</span>
cat &gt; results/assembly/sample_list.txt &lt;&lt; <span class="hljs-string">'EOF'</span>
results/assembly/control_stringtie.gtf
results/assembly/treated_stringtie.gtf
EOF

<span class="hljs-comment"># 2. 合并所有样本的转录本</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"合并转录本..."</span>
stringtie --merge \
    -G data/reference/chr22_mini.gtf \
    -o results/assembly/merged_transcripts.gtf \
    results/assembly/sample_list.txt

<span class="hljs-comment"># 3. 检查合并结果</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 合并转录本分析 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"合并前后转录本统计:"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"参考注释转录本数: <span class="hljs-variable">$(grep -c "transcript" data/reference/chr22_mini.gtf)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Control组装转录本数: <span class="hljs-variable">$(grep -c "transcript" results/assembly/control_stringtie.gtf)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Treated组装转录本数: <span class="hljs-variable">$(grep -c "transcript" results/assembly/treated_stringtie.gtf)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"合并后转录本数: <span class="hljs-variable">$(grep -c "transcript" results/assembly/merged_transcripts.gtf)</span>"</span>

<span class="hljs-comment"># 4. 使用合并转录本重新定量</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n=== 使用合并转录本重新定量 ==="</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> control treated; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"重新定量样本: <span class="hljs-variable">$sample</span>"</span>
    stringtie results/alignment/<span class="hljs-variable">${sample}</span>_hisat2.sorted.bam \
        -G results/assembly/merged_transcripts.gtf \
        -o results/assembly/<span class="hljs-variable">${sample}</span>_reprocessed.gtf \
        -A results/assembly/<span class="hljs-variable">${sample}</span>_reprocessed_abundances.tab \
        -e \
        -B \
        -p 2
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"转录本合并和重新定量完成！"</span>
</div></code></pre>
<h3 id="24-%E8%A1%A8%E8%BE%BE%E9%87%8F%E6%A0%87%E5%87%86%E5%8C%96%E6%96%B9%E6%B3%95%E6%AF%94%E8%BE%83">2.4 表达量标准化方法比较</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建表达量标准化比较脚本</span>
cat &gt; scripts/compare_normalization.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
比较不同表达量标准化方法
比较Raw counts, FPKM, TPM等不同标准化方法
"</span><span class="hljs-string">""</span>

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_fpkm(counts, gene_lengths, total_mapped_reads):
    <span class="hljs-string">""</span><span class="hljs-string">"计算FPKM值"</span><span class="hljs-string">""</span>
    <span class="hljs-comment"># FPKM = (reads * 1000 * 1,000,000) / (gene_length * total_mapped_reads)</span>
    fpkm = (counts * 1000 * 1000000) / (gene_lengths * total_mapped_reads)
    <span class="hljs-built_in">return</span> fpkm

def calculate_tpm(counts, gene_lengths):
    <span class="hljs-string">""</span><span class="hljs-string">"计算TPM值"</span><span class="hljs-string">""</span>
    <span class="hljs-comment"># 第一步：计算每个基因的RPK (Reads Per Kilobase)</span>
    rpk = counts / (gene_lengths / 1000)
    
    <span class="hljs-comment"># 第二步：计算每个样本的scaling factor</span>
    scaling_factor = rpk.sum() / 1000000
    
    <span class="hljs-comment"># 第三步：计算TPM</span>
    tpm = rpk / scaling_factor
    <span class="hljs-built_in">return</span> tpm

def analyze_normalization_methods():
    <span class="hljs-string">""</span><span class="hljs-string">"分析不同标准化方法的效果"</span><span class="hljs-string">""</span>
    
    <span class="hljs-comment"># 读取原始计数矩阵</span>
    try:
        counts_df = pd.read_csv(<span class="hljs-string">"results/quantification/gene_expression_matrix.txt"</span>, sep=<span class="hljs-string">'\t'</span>)
        
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 表达量标准化方法比较 ==="</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"基因数量: {len(counts_df)}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"样本数量: {len(counts_df.columns) - 1}"</span>)
        
        <span class="hljs-comment"># 模拟基因长度（实际应用中从GTF文件获取）</span>
        np.random.seed(42)  <span class="hljs-comment"># 保证结果可重复</span>
        gene_lengths = np.random.normal(2000, 500, len(counts_df))
        gene_lengths = np.maximum(gene_lengths, 500)  <span class="hljs-comment"># 最小长度500bp</span>
        
        <span class="hljs-comment"># 为每个样本计算不同标准化方法</span>
        <span class="hljs-keyword">for</span> col <span class="hljs-keyword">in</span> counts_df.columns[1:]:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n--- 样本: {col} ---"</span>)
            
            raw_counts = counts_df[col]
            total_mapped = raw_counts.sum()
            
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"原始计数总和: {total_mapped}"</span>)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"表达基因数 (counts&gt;0): {len(raw_counts[raw_counts &gt; 0])}"</span>)
            
            <span class="hljs-comment"># 计算FPKM</span>
            fpkm = calculate_fpkm(raw_counts, gene_lengths, total_mapped)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"FPKM平均值: {fpkm.mean():.2f}"</span>)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"FPKM中位数: {fpkm.median():.2f}"</span>)
            
            <span class="hljs-comment"># 计算TPM</span>
            tpm = calculate_tpm(raw_counts, gene_lengths)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"TPM总和: {tpm.sum():.0f} (理论值应为1,000,000)"</span>)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"TPM平均值: {tpm.mean():.2f}"</span>)
            
            <span class="hljs-comment"># 添加到结果DataFrame</span>
            counts_df[f<span class="hljs-string">'{col}_FPKM'</span>] = fpkm
            counts_df[f<span class="hljs-string">'{col}_TPM'</span>] = tpm
        
        <span class="hljs-comment"># 保存标准化结果</span>
        output_file = <span class="hljs-string">"results/quantification/normalized_expression.txt"</span>
        counts_df.to_csv(output_file, sep=<span class="hljs-string">'\t'</span>, index=False)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n标准化结果已保存到: {output_file}"</span>)
        
        <span class="hljs-comment"># 比较标准化前后的相关性</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== 标准化方法相关性分析 ==="</span>)
        control_raw = counts_df[<span class="hljs-string">'control'</span>]
        treated_raw = counts_df[<span class="hljs-string">'treated'</span>]
        
        <span class="hljs-comment"># 过滤掉零值进行相关性计算</span>
        mask = (control_raw &gt; 0) &amp; (treated_raw &gt; 0)
        
        <span class="hljs-keyword">if</span> mask.sum() &gt; 1:
            <span class="hljs-comment"># Raw counts相关性</span>
            raw_corr = np.corrcoef(control_raw[mask], treated_raw[mask])[0,1]
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"Raw counts相关性: {raw_corr:.3f}"</span>)
            
            <span class="hljs-comment"># FPKM相关性</span>
            fpkm_corr = np.corrcoef(counts_df[<span class="hljs-string">'control_FPKM'</span>][mask], 
                                   counts_df[<span class="hljs-string">'treated_FPKM'</span>][mask])[0,1]
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"FPKM相关性: {fpkm_corr:.3f}"</span>)
            
            <span class="hljs-comment"># TPM相关性</span>
            tpm_corr = np.corrcoef(counts_df[<span class="hljs-string">'control_TPM'</span>][mask], 
                                  counts_df[<span class="hljs-string">'treated_TPM'</span>][mask])[0,1]
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"TPM相关性: {tpm_corr:.3f}"</span>)
        
        <span class="hljs-built_in">return</span> counts_df
        
    except FileNotFoundError:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"错误: 找不到基因表达矩阵文件"</span>)
        <span class="hljs-built_in">return</span> None
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析过程中出错: {e}"</span>)
        <span class="hljs-built_in">return</span> None

def summarize_normalization_recommendations():
    <span class="hljs-string">""</span><span class="hljs-string">"总结标准化方法选择建议"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== 标准化方法选择建议 ==="</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"1. Raw Counts:"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 用途: 差异表达分析（DESeq2, edgeR）"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 优点: 保留原始统计分布，适合统计模型"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 缺点: 不能直接比较不同基因或样本"</span>)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n2. FPKM (Fragments Per Kilobase per Million):"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 用途: 样本内基因表达比较"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 优点: 校正基因长度和测序深度"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 缺点: 样本间比较存在偏差"</span>)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n3. TPM (Transcripts Per Million):"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 用途: 样本间基因表达比较"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 优点: 样本间总和一致，便于比较"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 缺点: 不适合差异表达统计分析"</span>)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n4. 使用建议:"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 可视化和比较: 使用TPM"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 差异表达分析: 使用Raw counts"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"   - 功能富集分析: 可使用FPKM或TPM"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    result_df = analyze_normalization_methods()
    summarize_normalization_recommendations()
EOF

python3 scripts/compare_normalization.py
</div></code></pre>

</body>
</html>
