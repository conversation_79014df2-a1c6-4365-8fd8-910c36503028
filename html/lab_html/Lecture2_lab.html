<!DOCTYPE html>
<html>
<head>
<title>Lecture2_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E4%BA%8C%E6%B5%8B%E5%BA%8F%E6%95%B0%E6%8D%AE%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6%E4%B8%8E%E9%A2%84%E5%A4%84%E7%90%86---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题二：测序数据质量控制与预处理 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握FastQC工具的使用和质量报告解读</li>
<li>学会使用Trimmomatic、Cutadapt等工具进行数据过滤</li>
<li>理解测序数据质量控制的原理和最佳实践</li>
<li>建立完整的数据预处理自动化流程</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E5%AE%89%E8%A3%85%E9%AA%8C%E8%AF%81">软件安装验证</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 激活分析环境</span>
conda activate ngs_analysis

<span class="hljs-comment"># 验证软件安装</span>
fastqc --version          <span class="hljs-comment"># 应显示 FastQC v0.x.x</span>
trimmomatic -version      <span class="hljs-comment"># 应显示 0.39</span>
cutadapt --version        <span class="hljs-comment"># 应显示版本信息</span>
multiqc --version         <span class="hljs-comment"># 应显示版本信息</span>

<span class="hljs-comment"># 如果未安装，执行以下命令</span>
conda install -c bioconda fastqc trimmomatic cutadapt multiqc -y
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p qc_practice/{data/{raw,processed},results/{fastqc,trimmed,reports},scripts}
<span class="hljs-built_in">cd</span> qc_practice

<span class="hljs-comment"># 下载示例数据（如果网络允许）</span>
<span class="hljs-comment"># wget ftp://ftp.sra.ebi.ac.uk/vol1/fastq/SRR000/SRR000001/SRR000001.fastq.gz</span>
<span class="hljs-comment"># 或创建模拟数据用于练习</span>

<span class="hljs-comment"># 创建模拟FASTQ数据</span>
cat &gt; data/raw/sample1_R1.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR000001.1 length=36
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+SRR000001.1 length=36
!<span class="hljs-string">''</span>*((((***++<span class="hljs-string">""</span>%%<span class="hljs-string">''</span>++<span class="hljs-string">"++)()...+()''*(((***+"</span>))<span class="hljs-string">""</span>*(((***++<span class="hljs-string">""</span><span class="hljs-string">""</span>*))(***++<span class="hljs-string">""</span>
@SRR000001.2 length=36
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+SRR000001.2 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII<span class="hljs-comment">###</span>
@SRR000001.3 length=36
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+SRR000001.3 length=36
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
@SRR000001.4 length=36
AGATCGGAAGAGCACACGTCTGAACTCCAGTCACNNNNNNATCTCGTATGCCGTCTTCTGCTTGAAAAA
+SRR000001.4 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII!!!IIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR000001.5 length=36
GATCGGAAGAGCACACGTCTGAACTCCAGTCACNNNNNNATCTCGTATGCCGTCTTCTGCTTGAAAAAA
+SRR000001.5 length=36
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ!!!JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

<span class="hljs-comment"># 创建配对的R2文件</span>
cat &gt; data/raw/sample1_R2.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SRR000001.1 length=36
CATCATCATCATCATCATCATCATCATCATTATTTACGATATGCTGTTTGAACCCCAAATCGATTTGGGGTT
+SRR000001.1 length=36
!<span class="hljs-string">''</span>*((((***++<span class="hljs-string">""</span>%%<span class="hljs-string">''</span>++<span class="hljs-string">"++)()...+()''*(((***+"</span>))<span class="hljs-string">""</span>*(((***++<span class="hljs-string">""</span><span class="hljs-string">""</span>*))(***++<span class="hljs-string">""</span>
@SRR000001.2 length=36
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+SRR000001.2 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII<span class="hljs-comment">###</span>
@SRR000001.3 length=36
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+SRR000001.3 length=36
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
@SRR000001.4 length=36
TTTTTTCAAGCAGAACGGCATACGAGATNNNNNGTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
+SRR000001.4 length=36
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIII!!!IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SRR000001.5 length=36
TTTTTTTCAAGCAGAACGGCATACGAGATNNNNNGTGACTGGAGTTCAGACGTGTGCTCTTCCGATC
+SRR000001.5 length=36
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ!!!JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

<span class="hljs-comment"># 压缩文件（模拟真实数据）</span>
gzip data/raw/sample1_R*.fastq

<span class="hljs-built_in">echo</span> <span class="hljs-string">"实验数据准备完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86fastqc%E5%B7%A5%E5%85%B7%E4%BD%BF%E7%94%A8%E4%B8%8E%E8%B4%A8%E9%87%8F%E6%8A%A5%E5%91%8A%E8%A7%A3%E8%AF%BB">第一部分：FastQC工具使用与质量报告解读</h2>
<h3 id="11-fastqc%E5%9F%BA%E7%A1%80%E6%93%8D%E4%BD%9C">1.1 FastQC基础操作</h3>
<h4 id="111-%E5%8D%95%E6%96%87%E4%BB%B6%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6%E5%88%86%E6%9E%90">1.1.1 单文件质量控制分析</h4>
<p><strong>实验目标：</strong> 学会使用FastQC进行单个FASTQ文件的质量分析</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 基本FastQC分析</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 执行FastQC质量控制分析 ==="</span>
fastqc data/raw/sample1_R1.fastq.gz -o results/fastqc/

<span class="hljs-comment"># 检查输出文件</span>
ls -la results/fastqc/
<span class="hljs-comment"># 应该看到：</span>
<span class="hljs-comment"># sample1_R1_fastqc.html  - HTML报告文件</span>
<span class="hljs-comment"># sample1_R1_fastqc.zip   - 压缩的详细结果</span>

<span class="hljs-comment"># 2. 带参数的FastQC分析</span>
fastqc data/raw/sample1_R1.fastq.gz \
    -o results/fastqc/ \
    -t 2 \
    --extract \
    --nogroup

<span class="hljs-comment"># 参数说明：</span>
<span class="hljs-comment"># -o: 指定输出目录</span>
<span class="hljs-comment"># -t: 指定线程数</span>
<span class="hljs-comment"># --extract: 自动解压结果文件</span>
<span class="hljs-comment"># --nogroup: 不将碱基位置分组</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"FastQC分析完成，请查看results/fastqc/目录"</span>
</div></code></pre>
<h4 id="112-%E6%89%B9%E9%87%8F%E5%A4%84%E7%90%86%E5%A4%9A%E4%B8%AA%E6%96%87%E4%BB%B6">1.1.2 批量处理多个文件</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 简单批量处理</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 批量处理多个FASTQ文件 ==="</span>
fastqc data/raw/*.fastq.gz -o results/fastqc/ -t 4

<span class="hljs-comment"># 2. 使用循环进行更精确的控制</span>
<span class="hljs-keyword">for</span> file <span class="hljs-keyword">in</span> data/raw/*.fastq.gz; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"正在处理: <span class="hljs-variable">$file</span>"</span>
    filename=$(basename <span class="hljs-string">"<span class="hljs-variable">$file</span>"</span> .fastq.gz)
    
    fastqc <span class="hljs-string">"<span class="hljs-variable">$file</span>"</span> \
        -o results/fastqc/ \
        -t 2 \
        --extract
    
    <span class="hljs-keyword">if</span> [ $? -eq 0 ]; <span class="hljs-keyword">then</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">$filename</span> 分析成功"</span>
    <span class="hljs-keyword">else</span>
        <span class="hljs-built_in">echo</span> <span class="hljs-string">"✗ <span class="hljs-variable">$filename</span> 分析失败"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># 3. 并行处理（如果数据量大）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 并行处理示例 ==="</span>
find data/raw/ -name <span class="hljs-string">"*.fastq.gz"</span> | \
parallel -j 4 fastqc {} -o results/fastqc/ -t 1

<span class="hljs-built_in">echo</span> <span class="hljs-string">"批量处理完成！"</span>
</div></code></pre>
<h3 id="12-fastqc%E8%B4%A8%E9%87%8F%E6%8A%A5%E5%91%8A%E6%B7%B1%E5%BA%A6%E8%A7%A3%E8%AF%BB">1.2 FastQC质量报告深度解读</h3>
<h4 id="121-%E5%88%9B%E5%BB%BA%E6%8A%A5%E5%91%8A%E8%A7%A3%E8%AF%BB%E8%84%9A%E6%9C%AC">1.2.1 创建报告解读脚本</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建FastQC报告解读脚本</span>
cat &gt; scripts/parse_fastqc.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
FastQC报告解读脚本
解析FastQC输出文件，提取关键质量指标
"</span><span class="hljs-string">""</span>

import os
import re
import sys
from pathlib import Path

def parse_fastqc_data(fastqc_dir):
    <span class="hljs-string">""</span><span class="hljs-string">"解析FastQC数据文件"</span><span class="hljs-string">""</span>
    data_file = Path(fastqc_dir) / <span class="hljs-string">"fastqc_data.txt"</span>
    
    <span class="hljs-keyword">if</span> not data_file.exists():
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"错误: 找不到文件 {data_file}"</span>)
        <span class="hljs-built_in">return</span> None
    
    results = {}
    current_module = None
    
    with open(data_file, <span class="hljs-string">'r'</span>) as f:
        <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
            line = line.strip()
            
            <span class="hljs-comment"># 基本统计信息</span>
            <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">"Filename"</span>):
                results[<span class="hljs-string">'filename'</span>] = line.split(<span class="hljs-string">'\t'</span>)[1]
            <span class="hljs-keyword">elif</span> line.startswith(<span class="hljs-string">"Total Sequences"</span>):
                results[<span class="hljs-string">'total_sequences'</span>] = int(line.split(<span class="hljs-string">'\t'</span>)[1])
            <span class="hljs-keyword">elif</span> line.startswith(<span class="hljs-string">"Sequence length"</span>):
                results[<span class="hljs-string">'sequence_length'</span>] = line.split(<span class="hljs-string">'\t'</span>)[1]
            <span class="hljs-keyword">elif</span> line.startswith(<span class="hljs-string">"%GC"</span>):
                results[<span class="hljs-string">'gc_content'</span>] = int(line.split(<span class="hljs-string">'\t'</span>)[1])
            
            <span class="hljs-comment"># 模块信息</span>
            <span class="hljs-keyword">elif</span> line.startswith(<span class="hljs-string">"&gt;&gt;"</span>) and not line.startswith(<span class="hljs-string">"&gt;&gt;END"</span>):
                parts = line.split(<span class="hljs-string">'\t'</span>)
                module_name = parts[0][2:]
                status = parts[1] <span class="hljs-keyword">if</span> len(parts) &gt; 1 <span class="hljs-keyword">else</span> <span class="hljs-string">"UNKNOWN"</span>
                
                <span class="hljs-keyword">if</span> <span class="hljs-string">'modules'</span> not <span class="hljs-keyword">in</span> results:
                    results[<span class="hljs-string">'modules'</span>] = {}
                results[<span class="hljs-string">'modules'</span>][module_name] = status
                current_module = module_name
    
    <span class="hljs-built_in">return</span> results

def analyze_quality_scores(fastqc_dir):
    <span class="hljs-string">""</span><span class="hljs-string">"分析质量分数分布"</span><span class="hljs-string">""</span>
    data_file = Path(fastqc_dir) / <span class="hljs-string">"fastqc_data.txt"</span>
    
    quality_scores = []
    in_quality_section = False
    
    with open(data_file, <span class="hljs-string">'r'</span>) as f:
        <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
            line = line.strip()
            
            <span class="hljs-keyword">if</span> line == <span class="hljs-string">"&gt;&gt;Per base sequence quality"</span>:
                in_quality_section = True
                <span class="hljs-built_in">continue</span>
            <span class="hljs-keyword">elif</span> line.startswith(<span class="hljs-string">"&gt;&gt;END_MODULE"</span>):
                in_quality_section = False
                <span class="hljs-built_in">continue</span>
            <span class="hljs-keyword">elif</span> in_quality_section and line and not line.startswith(<span class="hljs-string">"#"</span>):
                parts = line.split(<span class="hljs-string">'\t'</span>)
                <span class="hljs-keyword">if</span> len(parts) &gt;= 2:
                    position = parts[0]
                    mean_quality = <span class="hljs-built_in">float</span>(parts[1])
                    quality_scores.append((position, mean_quality))
    
    <span class="hljs-built_in">return</span> quality_scores

def generate_report(sample_name, results, quality_scores):
    <span class="hljs-string">""</span><span class="hljs-string">"生成质量报告"</span><span class="hljs-string">""</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n=== FastQC质量报告: {sample_name} ==="</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"文件名: {results.get('filename', 'N/A')}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"总序列数: {results.get('total_sequences', 'N/A'):,}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"序列长度: {results.get('sequence_length', 'N/A')}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"GC含量: {results.get('gc_content', 'N/A')}%"</span>)
    
    <span class="hljs-comment"># 模块状态统计</span>
    <span class="hljs-keyword">if</span> <span class="hljs-string">'modules'</span> <span class="hljs-keyword">in</span> results:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n模块状态统计:"</span>)
        status_count = {<span class="hljs-string">'PASS'</span>: 0, <span class="hljs-string">'WARN'</span>: 0, <span class="hljs-string">'FAIL'</span>: 0}
        
        <span class="hljs-keyword">for</span> module, status <span class="hljs-keyword">in</span> results[<span class="hljs-string">'modules'</span>].items():
            <span class="hljs-keyword">if</span> status <span class="hljs-keyword">in</span> status_count:
                status_count[status] += 1
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  {module}: {status}"</span>)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n状态汇总:"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  通过 (PASS): {status_count['PASS']}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  警告 (WARN): {status_count['WARN']}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  失败 (FAIL): {status_count['FAIL']}"</span>)
    
    <span class="hljs-comment"># 质量分数分析</span>
    <span class="hljs-keyword">if</span> quality_scores:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n质量分数分析:"</span>)
        avg_quality = sum(score[1] <span class="hljs-keyword">for</span> score <span class="hljs-keyword">in</span> quality_scores) / len(quality_scores)
        min_quality = min(score[1] <span class="hljs-keyword">for</span> score <span class="hljs-keyword">in</span> quality_scores)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  平均质量分数: {avg_quality:.2f}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  最低质量分数: {min_quality:.2f}"</span>)
        
        <span class="hljs-comment"># 识别低质量区域</span>
        low_quality_positions = [pos <span class="hljs-keyword">for</span> pos, qual <span class="hljs-keyword">in</span> quality_scores <span class="hljs-keyword">if</span> qual &lt; 20]
        <span class="hljs-keyword">if</span> low_quality_positions:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  低质量区域 (Q&lt;20): {len(low_quality_positions)} 个位置"</span>)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  低质量位置: {', '.join(low_quality_positions[:10])}"</span> + 
                  (<span class="hljs-string">"..."</span> <span class="hljs-keyword">if</span> len(low_quality_positions) &gt; 10 <span class="hljs-keyword">else</span> <span class="hljs-string">""</span>))
    
    <span class="hljs-comment"># 质量建议</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n质量评估建议:"</span>)
    <span class="hljs-keyword">if</span> <span class="hljs-string">'modules'</span> <span class="hljs-keyword">in</span> results:
        fail_modules = [m <span class="hljs-keyword">for</span> m, s <span class="hljs-keyword">in</span> results[<span class="hljs-string">'modules'</span>].items() <span class="hljs-keyword">if</span> s == <span class="hljs-string">'FAIL'</span>]
        warn_modules = [m <span class="hljs-keyword">for</span> m, s <span class="hljs-keyword">in</span> results[<span class="hljs-string">'modules'</span>].items() <span class="hljs-keyword">if</span> s == <span class="hljs-string">'WARN'</span>]
        
        <span class="hljs-keyword">if</span> fail_modules:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  ❌ 严重问题: {', '.join(fail_modules)}"</span>)
            <span class="hljs-keyword">if</span> <span class="hljs-string">'Adapter Content'</span> <span class="hljs-keyword">in</span> fail_modules:
                <span class="hljs-built_in">print</span>(f<span class="hljs-string">"     建议: 使用Trimmomatic或Cutadapt去除接头序列"</span>)
            <span class="hljs-keyword">if</span> <span class="hljs-string">'Per base sequence quality'</span> <span class="hljs-keyword">in</span> fail_modules:
                <span class="hljs-built_in">print</span>(f<span class="hljs-string">"     建议: 使用质量过滤，建议SLIDINGWINDOW:4:20"</span>)
        
        <span class="hljs-keyword">if</span> warn_modules:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  ⚠️  需要注意: {', '.join(warn_modules)}"</span>)
        
        <span class="hljs-keyword">if</span> not fail_modules and not warn_modules:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"  ✅ 数据质量良好，可以直接用于下游分析"</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    <span class="hljs-keyword">if</span> len(sys.argv) != 2:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"用法: python parse_fastqc.py &lt;fastqc_output_directory&gt;"</span>)
        sys.exit(1)
    
    fastqc_dir = sys.argv[1]
    sample_name = os.path.basename(fastqc_dir).replace(<span class="hljs-string">'_fastqc'</span>, <span class="hljs-string">''</span>)
    
    results = parse_fastqc_data(fastqc_dir)
    <span class="hljs-keyword">if</span> results:
        quality_scores = analyze_quality_scores(fastqc_dir)
        generate_report(sample_name, results, quality_scores)
EOF

chmod +x scripts/parse_fastqc.py

<span class="hljs-comment"># 运行报告解读</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 运行FastQC报告解读 ==="</span>
<span class="hljs-keyword">for</span> dir <span class="hljs-keyword">in</span> results/fastqc/*_fastqc/; <span class="hljs-keyword">do</span>
    <span class="hljs-keyword">if</span> [ -d <span class="hljs-string">"<span class="hljs-variable">$dir</span>"</span> ]; <span class="hljs-keyword">then</span>
        python3 scripts/parse_fastqc.py <span class="hljs-string">"<span class="hljs-variable">$dir</span>"</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>
</div></code></pre>
<h4 id="122-%E8%B4%A8%E9%87%8F%E6%8C%87%E6%A0%87%E8%AF%A6%E7%BB%86%E5%88%86%E6%9E%90">1.2.2 质量指标详细分析</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建质量指标提取脚本</span>
cat &gt; scripts/extract_quality_metrics.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># 从FastQC结果中提取关键质量指标</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 提取FastQC质量指标 ==="</span>
<span class="hljs-built_in">echo</span> -e <span class="hljs-string">"样本名\t总序列数\t序列长度\tGC含量\t通过模块数\t警告模块数\t失败模块数"</span>

<span class="hljs-keyword">for</span> fastqc_dir <span class="hljs-keyword">in</span> results/fastqc/*_fastqc/; <span class="hljs-keyword">do</span>
    <span class="hljs-keyword">if</span> [ -d <span class="hljs-string">"<span class="hljs-variable">$fastqc_dir</span>"</span> ]; <span class="hljs-keyword">then</span>
        data_file=<span class="hljs-string">"<span class="hljs-variable">$fastqc_dir</span>/fastqc_data.txt"</span>
        sample_name=$(basename <span class="hljs-string">"<span class="hljs-variable">$fastqc_dir</span>"</span> _fastqc)
        
        <span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> ]; <span class="hljs-keyword">then</span>
            <span class="hljs-comment"># 提取基本信息</span>
            total_seq=$(grep <span class="hljs-string">"Total Sequences"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | cut -f2)
            seq_length=$(grep <span class="hljs-string">"Sequence length"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | cut -f2)
            gc_content=$(grep <span class="hljs-string">"%GC"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | cut -f2)
            
            <span class="hljs-comment"># 统计模块状态</span>
            pass_count=$(grep -E <span class="hljs-string">"^&gt;&gt;.*\tPASS"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | wc -l)
            warn_count=$(grep -E <span class="hljs-string">"^&gt;&gt;.*\tWARN"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | wc -l)
            fail_count=$(grep -E <span class="hljs-string">"^&gt;&gt;.*\tFAIL"</span> <span class="hljs-string">"<span class="hljs-variable">$data_file</span>"</span> | wc -l)
            
            <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"<span class="hljs-variable">$sample_name</span>\t<span class="hljs-variable">$total_seq</span>\t<span class="hljs-variable">$seq_length</span>\t<span class="hljs-variable">$gc_content</span>\t<span class="hljs-variable">$pass_count</span>\t<span class="hljs-variable">$warn_count</span>\t<span class="hljs-variable">$fail_count</span>"</span>
        <span class="hljs-keyword">fi</span>
    <span class="hljs-keyword">fi</span>
<span class="hljs-keyword">done</span>
EOF

chmod +x scripts/extract_quality_metrics.sh
./scripts/extract_quality_metrics.sh &gt; results/quality_summary.txt

<span class="hljs-built_in">echo</span> <span class="hljs-string">"质量指标汇总保存到: results/quality_summary.txt"</span>
cat results/quality_summary.txt
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86trimmomaticcutadapt%E6%95%B0%E6%8D%AE%E8%BF%87%E6%BB%A4%E5%AE%9E%E8%B7%B5">第二部分：Trimmomatic/Cutadapt数据过滤实践</h2>
<h3 id="21-trimmomatic%E5%AE%9E%E9%99%85%E6%93%8D%E4%BD%9C">2.1 Trimmomatic实际操作</h3>
<h4 id="211-%E5%8D%95%E7%AB%AF%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86">2.1.1 单端数据处理</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 准备Illumina通用接头文件</span>
mkdir -p adapters
cat &gt; adapters/TruSeq3-SE.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;TruSeq3_IndexedAdapter
AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC
&gt;TruSeq3_UniversalAdapter
AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA
EOF

<span class="hljs-comment"># 单端数据过滤示例</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== Trimmomatic单端数据处理 ==="</span>

<span class="hljs-comment"># 解压文件用于演示</span>
gunzip -c data/raw/sample1_R1.fastq.gz &gt; data/raw/sample1_R1_temp.fastq

trimmomatic SE \
    data/raw/sample1_R1_temp.fastq \
    data/processed/sample1_R1_trimmed.fastq \
    ILLUMINACLIP:adapters/TruSeq3-SE.fa:2:30:10 \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:15 \
    MINLEN:36 \
    -phred33 \
    -trimlog data/processed/sample1_trim.log

<span class="hljs-comment"># 参数详解：</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== Trimmomatic参数详解 ==="</span>
cat &lt;&lt; <span class="hljs-string">'EOF'</span>
ILLUMINACLIP:adapters/TruSeq3-SE.fa:2:30:10
- 接头文件路径
- 2: 最大mismatch数
- 30: palindrome clip threshold
- 10: simple clip threshold

LEADING:3
- 去除5<span class="hljs-string">'端质量值低于3的碱基

TRAILING:3  
- 去除3'</span>端质量值低于3的碱基

SLIDINGWINDOW:4:15
- 滑动窗口大小4bp
- 平均质量值阈值15

MINLEN:36
- 最小序列长度36bp

-phred33
- 质量值编码格式
EOF

<span class="hljs-comment"># 检查处理结果</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 处理结果统计 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始序列数: <span class="hljs-variable">$(grep -c '^@' data/raw/sample1_R1_temp.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"过滤后序列数: <span class="hljs-variable">$(grep -c '^@' data/processed/sample1_R1_trimmed.fastq)</span>"</span>

<span class="hljs-comment"># 清理临时文件</span>
rm data/raw/sample1_R1_temp.fastq
</div></code></pre>
<h4 id="212-%E5%8F%8C%E7%AB%AF%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E8%AF%A6%E8%A7%A3">2.1.2 双端数据处理详解</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== Trimmomatic双端数据处理 ==="</span>

<span class="hljs-comment"># 准备双端接头文件</span>
cat &gt; adapters/TruSeq3-PE.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;PrefixPE/1
TACACTCTTTCCCTACACGACGCTCTTCCGATCT
&gt;PrefixPE/2
GTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
&gt;PE1
TACACTCTTTCCCTACACGACGCTCTTCCGATCT
&gt;PE1_rc
AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA
&gt;PE2
GTGACTGGAGTTCAGACGTGTGCTCTTCCGATCT
&gt;PE2_rc
AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC
EOF

<span class="hljs-comment"># 解压双端数据</span>
gunzip -c data/raw/sample1_R1.fastq.gz &gt; data/raw/sample1_R1_temp.fastq
gunzip -c data/raw/sample1_R2.fastq.gz &gt; data/raw/sample1_R2_temp.fastq

<span class="hljs-comment"># 执行双端数据过滤</span>
trimmomatic PE \
    data/raw/sample1_R1_temp.fastq data/raw/sample1_R2_temp.fastq \
    data/processed/sample1_R1_paired.fastq data/processed/sample1_R1_unpaired.fastq \
    data/processed/sample1_R2_paired.fastq data/processed/sample1_R2_unpaired.fastq \
    ILLUMINACLIP:adapters/TruSeq3-PE.fa:2:30:10:2:keepBothReads \
    LEADING:3 \
    TRAILING:3 \
    SLIDINGWINDOW:4:20 \
    MINLEN:36 \
    -threads 4 \
    -trimlog data/processed/sample1_PE_trim.log \
    -summary data/processed/sample1_PE_summary.txt

<span class="hljs-comment"># 分析处理结果</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 双端数据处理结果 ==="</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始R1序列数: <span class="hljs-variable">$(grep -c '^@' data/raw/sample1_R1_temp.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始R2序列数: <span class="hljs-variable">$(grep -c '^@' data/raw/sample1_R2_temp.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"配对保留R1: <span class="hljs-variable">$(grep -c '^@' data/processed/sample1_R1_paired.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"配对保留R2: <span class="hljs-variable">$(grep -c '^@' data/processed/sample1_R2_paired.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"未配对R1: <span class="hljs-variable">$(grep -c '^@' data/processed/sample1_R1_unpaired.fastq)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"未配对R2: <span class="hljs-variable">$(grep -c '^@' data/processed/sample1_R2_unpaired.fastq)</span>"</span>

<span class="hljs-comment"># 显示汇总信息</span>
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"data/processed/sample1_PE_summary.txt"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> -e <span class="hljs-string">"\n处理汇总信息:"</span>
    cat data/processed/sample1_PE_summary.txt
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 清理临时文件</span>
rm data/raw/sample1_R*_temp.fastq
</div></code></pre>
<h3 id="22-cutadapt%E5%AE%9E%E9%99%85%E6%93%8D%E4%BD%9C">2.2 Cutadapt实际操作</h3>
<h4 id="221-%E6%8E%A5%E5%A4%B4%E5%8E%BB%E9%99%A4%E5%AE%9E%E8%B7%B5">2.2.1 接头去除实践</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== Cutadapt接头去除实践 ==="</span>

<span class="hljs-comment"># 重新解压文件用于cutadapt演示</span>
gunzip -c data/raw/sample1_R1.fastq.gz &gt; data/raw/sample1_R1_temp.fastq

<span class="hljs-comment"># 单端接头去除</span>
cutadapt \
    -a AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC \
    -q 20 \
    -m 30 \
    --trim-n \
    -o data/processed/sample1_R1_cutadapt.fastq \
    data/raw/sample1_R1_temp.fastq \
    &gt; data/processed/cutadapt_report.txt

<span class="hljs-comment"># 参数说明：</span>
cat &lt;&lt; <span class="hljs-string">'EOF'</span>
=== Cutadapt参数详解 ===
-a: 3<span class="hljs-string">'端接头序列
-q: 质量值修剪阈值
-m: 最小序列长度
--trim-n: 去除N碱基
-o: 输出文件
EOF

# 显示处理报告
echo "=== Cutadapt处理报告 ==="
cat data/processed/cutadapt_report.txt

# 双端数据处理
echo -e "\n=== Cutadapt双端数据处理 ==="
gunzip -c data/raw/sample1_R2.fastq.gz &gt; data/raw/sample1_R2_temp.fastq

cutadapt \
    -a AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC \
    -A AGATCGGAAGAGCGTCGTGTAGGGAAAGAGTGTA \
    -q 20,20 \
    -m 30 \
    --trim-n \
    -o data/processed/sample1_R1_cutadapt_PE.fastq \
    -p data/processed/sample1_R2_cutadapt_PE.fastq \
    data/raw/sample1_R1_temp.fastq \
    data/raw/sample1_R2_temp.fastq \
    &gt; data/processed/cutadapt_PE_report.txt

echo "双端处理报告:"
cat data/processed/cutadapt_PE_report.txt

# 清理临时文件
rm data/raw/sample1_R*_temp.fastq
</span></div></code></pre>
<h3 id="23-%E8%B4%A8%E9%87%8F%E8%BF%87%E6%BB%A4%E6%95%88%E6%9E%9C%E5%AF%B9%E6%AF%94">2.3 质量过滤效果对比</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建质量过滤效果对比脚本</span>
cat &gt; scripts/compare_filtering_results.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
比较不同过滤工具的效果
"</span><span class="hljs-string">""</span>

def count_sequences(fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"统计FASTQ文件中的序列数量"</span><span class="hljs-string">""</span>
    try:
        with open(fastq_file, <span class="hljs-string">'r'</span>) as f:
            count = 0
            <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
                <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'@'</span>):
                    count += 1
            <span class="hljs-built_in">return</span> count
    except FileNotFoundError:
        <span class="hljs-built_in">return</span> 0

def calculate_average_length(fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"计算平均序列长度"</span><span class="hljs-string">""</span>
    try:
        total_length = 0
        count = 0
        with open(fastq_file, <span class="hljs-string">'r'</span>) as f:
            lines = f.readlines()
            <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(1, len(lines), 4):  <span class="hljs-comment"># 序列行</span>
                total_length += len(lines[i].strip())
                count += 1
        <span class="hljs-built_in">return</span> total_length / count <span class="hljs-keyword">if</span> count &gt; 0 <span class="hljs-keyword">else</span> 0
    except FileNotFoundError:
        <span class="hljs-built_in">return</span> 0

def calculate_gc_content(fastq_file):
    <span class="hljs-string">""</span><span class="hljs-string">"计算GC含量"</span><span class="hljs-string">""</span>
    try:
        gc_count = 0
        total_bases = 0
        with open(fastq_file, <span class="hljs-string">'r'</span>) as f:
            lines = f.readlines()
            <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(1, len(lines), 4):  <span class="hljs-comment"># 序列行</span>
                seq = lines[i].strip()
                gc_count += seq.count(<span class="hljs-string">'G'</span>) + seq.count(<span class="hljs-string">'C'</span>)
                total_bases += len(seq)
        <span class="hljs-built_in">return</span> (gc_count / total_bases * 100) <span class="hljs-keyword">if</span> total_bases &gt; 0 <span class="hljs-keyword">else</span> 0
    except FileNotFoundError:
        <span class="hljs-built_in">return</span> 0

<span class="hljs-comment"># 分析文件列表</span>
files_to_analyze = [
    (<span class="hljs-string">"原始R1"</span>, <span class="hljs-string">"data/raw/sample1_R1.fastq.gz"</span>),
    (<span class="hljs-string">"Trimmomatic-SE"</span>, <span class="hljs-string">"data/processed/sample1_R1_trimmed.fastq"</span>),
    (<span class="hljs-string">"Trimmomatic-PE-R1"</span>, <span class="hljs-string">"data/processed/sample1_R1_paired.fastq"</span>),
    (<span class="hljs-string">"Cutadapt-SE"</span>, <span class="hljs-string">"data/processed/sample1_R1_cutadapt.fastq"</span>),
    (<span class="hljs-string">"Cutadapt-PE-R1"</span>, <span class="hljs-string">"data/processed/sample1_R1_cutadapt_PE.fastq"</span>)
]

<span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 质量过滤效果对比 ==="</span>)
<span class="hljs-built_in">print</span>(f<span class="hljs-string">"{'文件类型':&lt;20} {'序列数':&lt;10} {'平均长度':&lt;10} {'GC含量%':&lt;10} {'保留率%':&lt;10}"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"-"</span> * 70)

original_count = None

<span class="hljs-keyword">for</span> file_type, file_path <span class="hljs-keyword">in</span> files_to_analyze:
    <span class="hljs-keyword">if</span> file_path.endswith(<span class="hljs-string">'.gz'</span>):
        import gzip
        <span class="hljs-comment"># 处理压缩文件</span>
        try:
            with gzip.open(file_path, <span class="hljs-string">'rt'</span>) as f:
                count = sum(1 <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'@'</span>))
                
            <span class="hljs-comment"># 重新打开计算其他统计信息</span>
            with gzip.open(file_path, <span class="hljs-string">'rt'</span>) as f:
                lines = f.readlines()
                total_length = sum(len(lines[i].strip()) <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(1, len(lines), 4))
                avg_length = total_length / count <span class="hljs-keyword">if</span> count &gt; 0 <span class="hljs-keyword">else</span> 0
                
                gc_count = sum(lines[i].strip().count(<span class="hljs-string">'G'</span>) + lines[i].strip().count(<span class="hljs-string">'C'</span>) 
                              <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> range(1, len(lines), 4))
                gc_content = (gc_count / total_length * 100) <span class="hljs-keyword">if</span> total_length &gt; 0 <span class="hljs-keyword">else</span> 0
        except:
            count = avg_length = gc_content = 0
    <span class="hljs-keyword">else</span>:
        count = count_sequences(file_path)
        avg_length = calculate_average_length(file_path)
        gc_content = calculate_gc_content(file_path)
    
    <span class="hljs-keyword">if</span> original_count is None:
        original_count = count
    
    retention_rate = (count / original_count * 100) <span class="hljs-keyword">if</span> original_count &gt; 0 <span class="hljs-keyword">else</span> 0
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"{file_type:&lt;20} {count:&lt;10} {avg_length:&lt;10.1f} {gc_content:&lt;10.1f} {retention_rate:&lt;10.1f}"</span>)

<span class="hljs-built_in">print</span>(<span class="hljs-string">"\n=== 结论和建议 ==="</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"1. 比较不同工具的序列保留率"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"2. 观察平均序列长度的变化"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"3. 检查GC含量是否有显著变化"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"4. 根据下游分析需求选择合适的过滤策略"</span>)
EOF

python3 scripts/compare_filtering_results.py
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%89%E9%83%A8%E5%88%86multiqc%E7%BB%93%E6%9E%9C%E6%B1%87%E6%80%BB%E4%B8%8E%E5%8F%AF%E8%A7%86%E5%8C%96">第三部分：MultiQC结果汇总与可视化</h2>
<h3 id="31-multiqc%E5%9F%BA%E7%A1%80%E4%BD%BF%E7%94%A8">3.1 MultiQC基础使用</h3>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== MultiQC结果汇总 ==="</span>

<span class="hljs-comment"># 首先对过滤后的数据运行FastQC</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"对过滤后数据运行FastQC..."</span>
fastqc data/processed/*.fastq -o results/fastqc/ -t 4

<span class="hljs-comment"># 运行MultiQC汇总所有结果</span>
multiqc results/fastqc/ -o results/reports/ --title <span class="hljs-string">"QC Practice Report"</span> --filename <span class="hljs-string">"qc_summary_report"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"MultiQC报告生成完成: results/reports/qc_summary_report.html"</span>

<span class="hljs-comment"># 检查输出文件</span>
ls -la results/reports/
</div></code></pre>
<h3 id="32-%E8%87%AA%E5%AE%9A%E4%B9%89multiqc%E9%85%8D%E7%BD%AE">3.2 自定义MultiQC配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建MultiQC配置文件</span>
cat &gt; multiqc_config.yaml &lt;&lt; <span class="hljs-string">'EOF'</span>
title: <span class="hljs-string">"NGS数据质量控制报告"</span>
subtitle: <span class="hljs-string">"专题二实践课程"</span>
intro_text: <span class="hljs-string">"本报告汇总了FastQC和数据过滤工具的结果"</span>

report_header_info:
    - Contact E-mail: <span class="hljs-string">'<EMAIL>'</span>
    - Application Type: <span class="hljs-string">'NGS Quality Control'</span>
    - Project Type: <span class="hljs-string">'Training Exercise'</span>

<span class="hljs-comment"># 自定义样本名称</span>
sample_names_rename:
    - [<span class="hljs-string">'_R1'</span>, <span class="hljs-string">' R1'</span>]
    - [<span class="hljs-string">'_R2'</span>, <span class="hljs-string">' R2'</span>]
    - [<span class="hljs-string">'_trimmed'</span>, <span class="hljs-string">' (Trimmed)'</span>]
    - [<span class="hljs-string">'_cutadapt'</span>, <span class="hljs-string">' (Cutadapt)'</span>]

<span class="hljs-comment"># 模块顺序</span>
module_order:
    - fastqc
    - trimmomatic
    - cutadapt

<span class="hljs-comment"># 图表配置</span>
plots_flat: <span class="hljs-literal">false</span>
plots_force_flat: <span class="hljs-literal">false</span>

<span class="hljs-comment"># 输出设置</span>
make_data_dir: <span class="hljs-literal">true</span>
zip_data_dir: <span class="hljs-literal">true</span>
EOF

<span class="hljs-comment"># 使用自定义配置运行MultiQC</span>
multiqc results/fastqc/ data/processed/ \
    -o results/reports/ \
    -c multiqc_config.yaml \
    --title <span class="hljs-string">"定制化QC报告"</span> \
    --filename <span class="hljs-string">"custom_qc_report"</span> \
    --force

<span class="hljs-built_in">echo</span> <span class="hljs-string">"定制化MultiQC报告生成完成!"</span>
</div></code></pre>

</body>
</html>
