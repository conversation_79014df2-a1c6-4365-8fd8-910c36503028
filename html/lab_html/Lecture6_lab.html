<!DOCTYPE html>
<html>
<head>
<title>Lecture6_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E5%85%AD%E5%8D%95%E7%BB%86%E8%83%9E%E6%B5%8B%E5%BA%8F%E6%8A%80%E6%9C%AF%E4%B8%8E%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题六：单细胞测序技术与数据分析 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握Seurat和Scanpy单细胞数据分析框架</li>
<li>学会单细胞数据质控、标准化和批次效应校正</li>
<li>掌握细胞聚类、细胞类型注释和轨迹分析</li>
<li>学会差异表达分析和功能富集分析</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE">软件环境配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建专题六专用环境</span>
conda create -n single_cell_analysis python=3.8 r-base=4.1 -y
conda activate single_cell_analysis

<span class="hljs-comment"># 安装Python包</span>
conda install -c conda-forge scanpy pandas numpy scipy matplotlib seaborn -y
conda install -c conda-forge scikit-learn anndata -y

<span class="hljs-comment"># 安装R包管理器</span>
conda install -c conda-forge r-devtools r-biocmanager -y

<span class="hljs-comment"># 启动R进行R包安装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"安装Seurat及相关R包..."</span>
R --slave &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment"># 安装Bioconductor包管理器</span>
<span class="hljs-keyword">if</span> (!require(<span class="hljs-string">"BiocManager"</span>, quietly = TRUE))
    install.packages(<span class="hljs-string">"BiocManager"</span>)

<span class="hljs-comment"># 安装Seurat和相关包</span>
BiocManager::install(c(<span class="hljs-string">"Seurat"</span>, <span class="hljs-string">"ggplot2"</span>, <span class="hljs-string">"patchwork"</span>, <span class="hljs-string">"dplyr"</span>, <span class="hljs-string">"tidyr"</span>))
BiocManager::install(c(<span class="hljs-string">"SingleR"</span>, <span class="hljs-string">"celldex"</span>, <span class="hljs-string">"scater"</span>, <span class="hljs-string">"scran"</span>))

<span class="hljs-comment"># 验证安装</span>
library(Seurat)
library(ggplot2)
cat(<span class="hljs-string">"R包安装完成！Seurat版本:"</span>, as.character(packageVersion(<span class="hljs-string">"Seurat"</span>)), <span class="hljs-string">"\n"</span>)
EOF

<span class="hljs-comment"># 验证Python环境</span>
python3 -c <span class="hljs-string">"
import scanpy as sc
import pandas as pd
import numpy as np
print('Python包安装完成！Scanpy版本:', sc.__version__)
"</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"软件环境配置完成！"</span>
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p single_cell_practice/{data/{raw,processed},results/{qc,analysis,plots},scripts,logs}
<span class="hljs-built_in">cd</span> single_cell_practice

<span class="hljs-comment"># 创建模拟10x Genomics Cell Ranger输出格式的数据</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 创建模拟单细胞数据 ==="</span>

<span class="hljs-comment"># 1. 创建基因列表文件</span>
cat &gt; data/raw/features.tsv &lt;&lt; <span class="hljs-string">'EOF'</span>
ENSG00000001	GENE1	Gene Expression
ENSG00000002	GENE2	Gene Expression
ENSG00000003	GENE3	Gene Expression
ENSG00000004	GENE4	Gene Expression
ENSG00000005	MT-GENE1	Gene Expression
ENSG00000006	GENE6	Gene Expression
ENSG00000007	GENE7	Gene Expression
ENSG00000008	GENE8	Gene Expression
ENSG00000009	RPS1	Gene Expression
ENSG00000010	RPS2	Gene Expression
EOF

<span class="hljs-comment"># 2. 创建细胞条形码文件</span>
cat &gt; data/raw/barcodes.tsv &lt;&lt; <span class="hljs-string">'EOF'</span>
AAACATACAACCAC-1
AAACATTGAGCTAC-1
AAACATTGATCAGC-1
AAACCGTGCTTCCG-1
AAACCGTGTATGCG-1
AAACGCACTGGTAC-1
AAACGCTGACCAGT-1
AAACGCTGGTTCTT-1
AAACGCTGTAGCCA-1
AAACGCTGTTTCTG-1
AAACTTGAAAAACG-1
AAACTTGATCCAGA-1
AAAGAGACGAGATA-1
AAAGAGACGCGAGA-1
AAAGAGACGGACTT-1
AAAGAGACGGGTAG-1
AAAGAGACGTGGAG-1
AAAGAGACGTTTGG-1
AAAGAGACTGAAAC-1
AAAGAGACTGTAGA-1
EOF

<span class="hljs-comment"># 3. 创建稀疏表达矩阵</span>
cat &gt; data/raw/matrix.mtx &lt;&lt; <span class="hljs-string">'EOF'</span>
%%MatrixMarket matrix coordinate <span class="hljs-built_in">integer</span> general
%
10 20 40
1 1 5
1 2 3
2 1 8
2 3 4
3 2 12
3 4 7
4 1 15
4 5 9
5 6 2
5 7 25
6 8 6
6 9 11
7 10 3
7 11 8
8 12 14
8 13 5
9 14 7
9 15 19
10 16 4
10 17 6
1 18 3
2 19 5
3 20 8
4 1 2
5 2 7
6 3 12
7 4 5
8 5 9
9 6 6
10 7 11
1 8 4
2 9 7
3 10 13
4 11 6
5 12 8
6 13 10
7 14 5
8 15 7
9 16 9
EOF

<span class="hljs-comment"># 4. 创建高质量细胞数据集（用于对比）</span>
mkdir -p data/processed/high_quality
cp data/raw/* data/processed/high_quality/

<span class="hljs-built_in">echo</span> <span class="hljs-string">"模拟数据创建完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86seuratscanpy%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E5%9F%BA%E7%A1%80">第一部分：Seurat/Scanpy数据处理基础</h2>
<h3 id="11-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%92%8C%E5%88%9D%E6%AD%A5%E6%8E%A2%E7%B4%A2">1.1 数据导入和初步探索</h3>
<h4 id="111-seurat%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%AE%9E%E8%B7%B5">1.1.1 Seurat数据导入实践</h4>
<p><strong>实验目标：</strong> 学会使用Seurat读取10x Genomics数据，创建Seurat对象</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建Seurat数据导入脚本</span>
cat &gt; scripts/seurat_data_import.R &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env Rscript</span>
<span class="hljs-comment"># Seurat数据导入和初步分析脚本</span>

library(Seurat)
library(ggplot2)
library(patchwork)
library(dplyr)

<span class="hljs-comment"># 1. 读取10x Genomics数据</span>
cat(<span class="hljs-string">"=== 读取10x Genomics数据 ===\n"</span>)
data_dir &lt;- <span class="hljs-string">"data/raw"</span>

<span class="hljs-comment"># 检查数据文件</span>
cat(<span class="hljs-string">"检查数据文件:\n"</span>)
cat(<span class="hljs-string">"- features.tsv:"</span>, file.exists(file.path(data_dir, <span class="hljs-string">"features.tsv"</span>)), <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"- barcodes.tsv:"</span>, file.exists(file.path(data_dir, <span class="hljs-string">"barcodes.tsv"</span>)), <span class="hljs-string">"\n"</span>) 
cat(<span class="hljs-string">"- matrix.mtx:"</span>, file.exists(file.path(data_dir, <span class="hljs-string">"matrix.mtx"</span>)), <span class="hljs-string">"\n"</span>)

<span class="hljs-comment"># 读取数据</span>
expression_matrix &lt;- Read10X(data.dir = data_dir)
cat(<span class="hljs-string">"表达矩阵维度:"</span>, dim(expression_matrix), <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"基因数量:"</span>, nrow(expression_matrix), <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"细胞数量:"</span>, ncol(expression_matrix), <span class="hljs-string">"\n"</span>)

<span class="hljs-comment"># 2. 创建Seurat对象</span>
cat(<span class="hljs-string">"\n=== 创建Seurat对象 ===\n"</span>)
seurat_obj &lt;- CreateSeuratObject(
    counts = expression_matrix,
    project = <span class="hljs-string">"SingleCell_Practice"</span>,
    min.cells = 1,    <span class="hljs-comment"># 至少在1个细胞中表达的基因</span>
    min.features = 1  <span class="hljs-comment"># 至少表达1个基因的细胞</span>
)

<span class="hljs-comment"># 显示Seurat对象信息</span>
cat(<span class="hljs-string">"Seurat对象信息:\n"</span>)
<span class="hljs-built_in">print</span>(seurat_obj)

<span class="hljs-comment"># 3. 初步数据探索</span>
cat(<span class="hljs-string">"\n=== 初步数据探索 ===\n"</span>)

<span class="hljs-comment"># 计算线粒体基因比例</span>
seurat_obj[[<span class="hljs-string">"percent.mt"</span>]] &lt;- PercentageFeatureSet(seurat_obj, pattern = <span class="hljs-string">"^MT-"</span>)

<span class="hljs-comment"># 计算核糖体基因比例</span>
seurat_obj[[<span class="hljs-string">"percent.ribo"</span>]] &lt;- PercentageFeatureSet(seurat_obj, pattern = <span class="hljs-string">"^RP[SL]"</span>)

<span class="hljs-comment"># 显示细胞元数据</span>
cat(<span class="hljs-string">"细胞元数据概览:\n"</span>)
head(<EMAIL>)

<span class="hljs-comment"># 4. 基本统计信息</span>
cat(<span class="hljs-string">"\n=== 基本统计信息 ===\n"</span>)
meta_data &lt;- <EMAIL>

cat(<span class="hljs-string">"每个细胞检测到的基因数统计:\n"</span>)
<span class="hljs-built_in">print</span>(summary(meta_data<span class="hljs-variable">$nFeature_RNA</span>))

cat(<span class="hljs-string">"\n每个细胞的UMI计数统计:\n"</span>)
<span class="hljs-built_in">print</span>(summary(meta_data<span class="hljs-variable">$nCount_RNA</span>))

cat(<span class="hljs-string">"\n线粒体基因比例统计:\n"</span>)
<span class="hljs-built_in">print</span>(summary(meta_data<span class="hljs-variable">$percent</span>.mt))

<span class="hljs-comment"># 5. 保存Seurat对象</span>
saveRDS(seurat_obj, file = <span class="hljs-string">"results/analysis/seurat_initial.rds"</span>)
cat(<span class="hljs-string">"\nSeurat对象已保存到: results/analysis/seurat_initial.rds\n"</span>)

<span class="hljs-comment"># 6. 生成初步可视化</span>
cat(<span class="hljs-string">"\n=== 生成初步可视化 ===\n"</span>)

<span class="hljs-comment"># 创建质量指标小提琴图</span>
p1 &lt;- VlnPlot(seurat_obj, 
              features = c(<span class="hljs-string">"nFeature_RNA"</span>, <span class="hljs-string">"nCount_RNA"</span>, <span class="hljs-string">"percent.mt"</span>), 
              ncol = 3,
              pt.size = 0.1)

<span class="hljs-comment"># 保存图片</span>
ggsave(<span class="hljs-string">"results/plots/initial_qc_violin.png"</span>, plot = p1, width = 12, height = 4)
cat(<span class="hljs-string">"质量控制小提琴图已保存到: results/plots/initial_qc_violin.png\n"</span>)

<span class="hljs-comment"># 创建特征散点图</span>
p2 &lt;- FeatureScatter(seurat_obj, feature1 = <span class="hljs-string">"nCount_RNA"</span>, feature2 = <span class="hljs-string">"percent.mt"</span>) +
      ggtitle(<span class="hljs-string">"UMI计数 vs 线粒体基因比例"</span>)

p3 &lt;- FeatureScatter(seurat_obj, feature1 = <span class="hljs-string">"nCount_RNA"</span>, feature2 = <span class="hljs-string">"nFeature_RNA"</span>) +
      ggtitle(<span class="hljs-string">"UMI计数 vs 检测基因数"</span>)

combined_scatter &lt;- p2 + p3
ggsave(<span class="hljs-string">"results/plots/initial_scatter.png"</span>, plot = combined_scatter, width = 12, height = 6)
cat(<span class="hljs-string">"特征散点图已保存到: results/plots/initial_scatter.png\n"</span>)

cat(<span class="hljs-string">"\n=== Seurat数据导入完成 ===\n"</span>)
EOF

<span class="hljs-comment"># 运行Seurat数据导入</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 运行Seurat数据导入 ==="</span>
Rscript scripts/seurat_data_import.R
</div></code></pre>
<h4 id="112-scanpy%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%AE%9E%E8%B7%B5">1.1.2 Scanpy数据导入实践</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建Scanpy数据导入脚本</span>
cat &gt; scripts/scanpy_data_import.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
Scanpy数据导入和初步分析脚本
"</span><span class="hljs-string">""</span>

import scanpy as sc
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

<span class="hljs-comment"># 设置scanpy参数</span>
sc.settings.verbosity = 3  <span class="hljs-comment"># 详细输出</span>
sc.settings.set_figure_params(dpi=80, facecolor=<span class="hljs-string">'white'</span>)
sc.settings.figdir = <span class="hljs-string">'results/plots/'</span>

def main():
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== Scanpy数据导入和分析 ==="</span>)
    
    <span class="hljs-comment"># 1. 读取10x Genomics数据</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n1. 读取10x Genomics数据"</span>)
    try:
        adata = sc.read_10x_mtx(
            <span class="hljs-string">'data/raw/'</span>,  <span class="hljs-comment"># 数据目录</span>
            var_names=<span class="hljs-string">'gene_symbols'</span>,  <span class="hljs-comment"># 使用基因符号作为变量名</span>
            cache=True  <span class="hljs-comment"># 缓存结果</span>
        )
        
        <span class="hljs-comment"># 使基因名唯一</span>
        adata.var_names_make_unique()
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"AnnData对象维度: {adata.shape}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"基因数量: {adata.n_vars}"</span>)
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"细胞数量: {adata.n_obs}"</span>)
        
    except Exception as e:
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"读取数据时出错: {e}"</span>)
        <span class="hljs-built_in">return</span>
    
    <span class="hljs-comment"># 2. 数据结构探索</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n2. 数据结构探索"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"AnnData对象信息:"</span>)
    <span class="hljs-built_in">print</span>(adata)
    
    <span class="hljs-comment"># 显示基因信息</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n基因信息 (前5个):"</span>)
    <span class="hljs-built_in">print</span>(adata.var.head())
    
    <span class="hljs-comment"># 显示细胞信息</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n细胞信息 (前5个):"</span>)
    <span class="hljs-built_in">print</span>(adata.obs.head())
    
    <span class="hljs-comment"># 3. 计算质量控制指标</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n3. 计算质量控制指标"</span>)
    
    <span class="hljs-comment"># 标记线粒体基因</span>
    adata.var[<span class="hljs-string">'mt'</span>] = adata.var_names.str.startswith(<span class="hljs-string">'MT-'</span>)
    
    <span class="hljs-comment"># 标记核糖体基因</span>
    adata.var[<span class="hljs-string">'ribo'</span>] = adata.var_names.str.startswith((<span class="hljs-string">'RPS'</span>, <span class="hljs-string">'RPL'</span>))
    
    <span class="hljs-comment"># 计算QC指标</span>
    sc.pp.calculate_qc_metrics(
        adata, 
        percent_top=None, 
        log1p=False, 
        inplace=True,
        qc_vars=[<span class="hljs-string">'mt'</span>, <span class="hljs-string">'ribo'</span>]
    )
    
    <span class="hljs-comment"># 显示计算的QC指标</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"计算的QC指标:"</span>)
    <span class="hljs-built_in">print</span>(adata.obs.columns.tolist())
    
    <span class="hljs-comment"># 4. 基本统计信息</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n4. 基本统计信息"</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"每个细胞检测到的基因数统计:"</span>)
    <span class="hljs-built_in">print</span>(adata.obs[<span class="hljs-string">'n_genes_by_counts'</span>].describe())
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n每个细胞的总UMI计数统计:"</span>)
    <span class="hljs-built_in">print</span>(adata.obs[<span class="hljs-string">'total_counts'</span>].describe())
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n线粒体基因比例统计:"</span>)
    <span class="hljs-built_in">print</span>(adata.obs[<span class="hljs-string">'pct_counts_mt'</span>].describe())
    
    <span class="hljs-comment"># 5. 生成初步可视化</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n5. 生成初步可视化"</span>)
    
    <span class="hljs-comment"># 小提琴图</span>
    fig, axes = plt.subplots(1, 3, figsize=(12, 4))
    
    <span class="hljs-comment"># 基因数量</span>
    sc.pl.violin(adata, <span class="hljs-string">'n_genes_by_counts'</span>, 
                jitter=0.4, ax=axes[0], show=False)
    axes[0].set_title(<span class="hljs-string">'检测基因数'</span>)
    
    <span class="hljs-comment"># UMI计数</span>
    sc.pl.violin(adata, <span class="hljs-string">'total_counts'</span>, 
                jitter=0.4, ax=axes[1], show=False)
    axes[1].set_title(<span class="hljs-string">'总UMI计数'</span>)
    
    <span class="hljs-comment"># 线粒体基因比例</span>
    sc.pl.violin(adata, <span class="hljs-string">'pct_counts_mt'</span>, 
                jitter=0.4, ax=axes[2], show=False)
    axes[2].set_title(<span class="hljs-string">'线粒体基因比例(%)'</span>)
    
    plt.tight_layout()
    plt.savefig(<span class="hljs-string">'results/plots/scanpy_initial_qc_violin.png'</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
    plt.close()
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"质量控制小提琴图已保存到: results/plots/scanpy_initial_qc_violin.png"</span>)
    
    <span class="hljs-comment"># 散点图</span>
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    <span class="hljs-comment"># UMI vs 基因数</span>
    sc.pl.scatter(adata, x=<span class="hljs-string">'total_counts'</span>, y=<span class="hljs-string">'n_genes_by_counts'</span>, 
                 ax=axes[0], show=False)
    axes[0].set_title(<span class="hljs-string">'总UMI计数 vs 检测基因数'</span>)
    
    <span class="hljs-comment"># UMI vs 线粒体比例</span>
    sc.pl.scatter(adata, x=<span class="hljs-string">'total_counts'</span>, y=<span class="hljs-string">'pct_counts_mt'</span>, 
                 ax=axes[1], show=False)
    axes[1].set_title(<span class="hljs-string">'总UMI计数 vs 线粒体基因比例'</span>)
    
    plt.tight_layout()
    plt.savefig(<span class="hljs-string">'results/plots/scanpy_initial_scatter.png'</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
    plt.close()
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"特征散点图已保存到: results/plots/scanpy_initial_scatter.png"</span>)
    
    <span class="hljs-comment"># 6. 保存AnnData对象</span>
    adata.write(<span class="hljs-string">'results/analysis/scanpy_initial.h5ad'</span>)
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\nAnnData对象已保存到: results/analysis/scanpy_initial.h5ad"</span>)
    
    <span class="hljs-comment"># 7. 生成数据概览报告</span>
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n=== 数据概览报告 ==="</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"数据集包含 {adata.n_obs} 个细胞和 {adata.n_vars} 个基因"</span>)
    
    <span class="hljs-comment"># 质量阈值建议</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"\n质量阈值建议:"</span>)
    gene_threshold = adata.obs[<span class="hljs-string">'n_genes_by_counts'</span>].quantile(0.05)
    umi_threshold = adata.obs[<span class="hljs-string">'total_counts'</span>].quantile(0.05)
    mt_threshold = adata.obs[<span class="hljs-string">'pct_counts_mt'</span>].quantile(0.95)
    
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最少基因数阈值 (5%分位数): {gene_threshold:.0f}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最少UMI数阈值 (5%分位数): {umi_threshold:.0f}"</span>)
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"最大线粒体比例阈值 (95%分位数): {mt_threshold:.1f}%"</span>)
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n=== Scanpy数据导入完成 ==="</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    main()
EOF

<span class="hljs-comment"># 运行Scanpy数据导入</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 运行Scanpy数据导入 ==="</span>
python3 scripts/scanpy_data_import.py
</div></code></pre>
<h3 id="12-%E6%95%B0%E6%8D%AE%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6%E8%AF%A6%E7%BB%86%E5%AE%9E%E8%B7%B5">1.2 数据质量控制详细实践</h3>
<h4 id="121-%E8%B4%A8%E9%87%8F%E6%8C%87%E6%A0%87%E6%B7%B1%E5%BA%A6%E5%88%86%E6%9E%90">1.2.1 质量指标深度分析</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建质量控制分析脚本</span>
cat &gt; scripts/quality_control_analysis.R &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env Rscript</span>
<span class="hljs-comment"># 单细胞数据质量控制详细分析</span>

library(Seurat)
library(ggplot2)
library(dplyr)
library(gridExtra)

<span class="hljs-comment"># 读取之前保存的Seurat对象</span>
seurat_obj &lt;- readRDS(<span class="hljs-string">"results/analysis/seurat_initial.rds"</span>)

cat(<span class="hljs-string">"=== 单细胞数据质量控制详细分析 ===\n"</span>)

<span class="hljs-comment"># 1. 详细质量指标计算</span>
cat(<span class="hljs-string">"\n1. 计算额外质量指标\n"</span>)

<span class="hljs-comment"># 计算基因类型比例</span>
seurat_obj[[<span class="hljs-string">"percent.ribo"</span>]] &lt;- PercentageFeatureSet(seurat_obj, pattern = <span class="hljs-string">"^RP[SL]"</span>)

<span class="hljs-comment"># 计算细胞复杂度（log10(基因数)/log10(UMI数)）</span>
seurat_obj<span class="hljs-variable">$complexity</span> &lt;- log10(seurat_obj<span class="hljs-variable">$nFeature_RNA</span>) / log10(seurat_obj<span class="hljs-variable">$nCount_RNA</span>)

<span class="hljs-comment"># 显示所有质量指标</span>
cat(<span class="hljs-string">"质量指标概览:\n"</span>)
<span class="hljs-built_in">print</span>(head(<EMAIL>))

<span class="hljs-comment"># 2. 质量阈值确定</span>
cat(<span class="hljs-string">"\n2. 质量阈值分析\n"</span>)

meta_data &lt;- <EMAIL>

<span class="hljs-comment"># 基因数量阈值</span>
gene_lower &lt;- quantile(meta_data<span class="hljs-variable">$nFeature_RNA</span>, 0.05)
gene_upper &lt;- quantile(meta_data<span class="hljs-variable">$nFeature_RNA</span>, 0.95)

<span class="hljs-comment"># UMI计数阈值</span>
umi_lower &lt;- quantile(meta_data<span class="hljs-variable">$nCount_RNA</span>, 0.05)
umi_upper &lt;- quantile(meta_data<span class="hljs-variable">$nCount_RNA</span>, 0.95)

<span class="hljs-comment"># 线粒体基因比例阈值</span>
mt_upper &lt;- quantile(meta_data<span class="hljs-variable">$percent</span>.mt, 0.95)

cat(<span class="hljs-string">"建议的质量阈值:\n"</span>)
cat(<span class="hljs-string">"基因数量:"</span>, gene_lower, <span class="hljs-string">"-"</span>, gene_upper, <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"UMI计数:"</span>, umi_lower, <span class="hljs-string">"-"</span>, umi_upper, <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"线粒体基因比例: &lt;"</span>, mt_upper, <span class="hljs-string">"%\n"</span>)

<span class="hljs-comment"># 3. 质量分布可视化</span>
cat(<span class="hljs-string">"\n3. 生成详细质量分布图\n"</span>)

<span class="hljs-comment"># 创建多面板质量图</span>
p1 &lt;- ggplot(meta_data, aes(x = nFeature_RNA)) +
      geom_histogram(bins = 50, fill = <span class="hljs-string">"skyblue"</span>, alpha = 0.7) +
      geom_vline(xintercept = c(gene_lower, gene_upper), color = <span class="hljs-string">"red"</span>, linetype = <span class="hljs-string">"dashed"</span>) +
      labs(title = <span class="hljs-string">"基因数量分布"</span>, x = <span class="hljs-string">"检测基因数"</span>, y = <span class="hljs-string">"细胞数量"</span>) +
      theme_minimal()

p2 &lt;- ggplot(meta_data, aes(x = nCount_RNA)) +
      geom_histogram(bins = 50, fill = <span class="hljs-string">"lightgreen"</span>, alpha = 0.7) +
      geom_vline(xintercept = c(umi_lower, umi_upper), color = <span class="hljs-string">"red"</span>, linetype = <span class="hljs-string">"dashed"</span>) +
      labs(title = <span class="hljs-string">"UMI计数分布"</span>, x = <span class="hljs-string">"总UMI计数"</span>, y = <span class="hljs-string">"细胞数量"</span>) +
      theme_minimal()

p3 &lt;- ggplot(meta_data, aes(x = percent.mt)) +
      geom_histogram(bins = 50, fill = <span class="hljs-string">"orange"</span>, alpha = 0.7) +
      geom_vline(xintercept = mt_upper, color = <span class="hljs-string">"red"</span>, linetype = <span class="hljs-string">"dashed"</span>) +
      labs(title = <span class="hljs-string">"线粒体基因比例分布"</span>, x = <span class="hljs-string">"线粒体基因比例(%)"</span>, y = <span class="hljs-string">"细胞数量"</span>) +
      theme_minimal()

p4 &lt;- ggplot(meta_data, aes(x = complexity)) +
      geom_histogram(bins = 50, fill = <span class="hljs-string">"purple"</span>, alpha = 0.7) +
      labs(title = <span class="hljs-string">"细胞复杂度分布"</span>, x = <span class="hljs-string">"复杂度"</span>, y = <span class="hljs-string">"细胞数量"</span>) +
      theme_minimal()

combined_plot &lt;- grid.arrange(p1, p2, p3, p4, ncol = 2)
ggsave(<span class="hljs-string">"results/plots/detailed_qc_distributions.png"</span>, plot = combined_plot, 
       width = 12, height = 10)
cat(<span class="hljs-string">"详细质量分布图已保存到: results/plots/detailed_qc_distributions.png\n"</span>)

<span class="hljs-comment"># 4. 质量筛选效果预览</span>
cat(<span class="hljs-string">"\n4. 质量筛选效果预览\n"</span>)

<span class="hljs-comment"># 应用质量筛选条件</span>
cells_before &lt;- ncol(seurat_obj)
filtered_cells &lt;- meta_data %&gt;%
  filter(nFeature_RNA &gt;= gene_lower &amp; nFeature_RNA &lt;= gene_upper &amp;
         nCount_RNA &gt;= umi_lower &amp; nCount_RNA &lt;= umi_upper &amp;
         percent.mt &lt;= mt_upper)

cells_after &lt;- nrow(filtered_cells)
retention_rate &lt;- (cells_after / cells_before) * 100

cat(<span class="hljs-string">"筛选前细胞数:"</span>, cells_before, <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"筛选后细胞数:"</span>, cells_after, <span class="hljs-string">"\n"</span>)
cat(<span class="hljs-string">"细胞保留率:"</span>, round(retention_rate, 1), <span class="hljs-string">"%\n"</span>)

<span class="hljs-comment"># 5. 基因表达统计</span>
cat(<span class="hljs-string">"\n5. 基因表达统计\n"</span>)

<span class="hljs-comment"># 计算每个基因在多少细胞中表达</span>
gene_expression_stats &lt;- data.frame(
  gene = rownames(seurat_obj),
  cells_expressed = rowSums(GetAssayData(seurat_obj, slot = <span class="hljs-string">"counts"</span>) &gt; 0),
  total_expression = rowSums(GetAssayData(seurat_obj, slot = <span class="hljs-string">"counts"</span>))
)

gene_expression_stats<span class="hljs-variable">$expression_frequency</span> &lt;- 
  gene_expression_stats<span class="hljs-variable">$cells_expressed</span> / ncol(seurat_obj) * 100

cat(<span class="hljs-string">"基因表达频率统计:\n"</span>)
<span class="hljs-built_in">print</span>(summary(gene_expression_stats<span class="hljs-variable">$expression_frequency</span>))

<span class="hljs-comment"># 显示高表达基因</span>
cat(<span class="hljs-string">"\n表达频率最高的基因 (前10个):\n"</span>)
top_genes &lt;- gene_expression_stats %&gt;% 
  arrange(desc(expression_frequency)) %&gt;% 
  head(10)
<span class="hljs-built_in">print</span>(top_genes)

<span class="hljs-comment"># 6. 保存质量控制报告</span>
cat(<span class="hljs-string">"\n6. 保存质量控制分析结果\n"</span>)

<span class="hljs-comment"># 保存质量统计</span>
write.csv(meta_data, <span class="hljs-string">"results/analysis/cell_quality_metrics.csv"</span>, row.names = TRUE)
write.csv(gene_expression_stats, <span class="hljs-string">"results/analysis/gene_expression_stats.csv"</span>, row.names = FALSE)

cat(<span class="hljs-string">"质量控制分析完成！\n"</span>)
cat(<span class="hljs-string">"- 细胞质量指标: results/analysis/cell_quality_metrics.csv\n"</span>)
cat(<span class="hljs-string">"- 基因表达统计: results/analysis/gene_expression_stats.csv\n"</span>)
EOF

<span class="hljs-comment"># 运行质量控制分析</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 运行质量控制分析 ==="</span>
Rscript scripts/quality_control_analysis.R
</div></code></pre>

</body>
</html>
