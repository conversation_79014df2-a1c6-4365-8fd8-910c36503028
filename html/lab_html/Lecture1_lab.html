<!DOCTYPE html>
<html>
<head>
<title>Lecture1_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E4%B8%80%E9%AB%98%E9%80%9A%E9%87%8F%E6%B5%8B%E5%BA%8F%E6%8A%80%E6%9C%AF%E6%A6%82%E8%AE%BA---%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题一：高通量测序技术概论 - 实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本实践课程旨在帮助学生：</p>
<ol>
<li>掌握Linux操作系统基本命令和生物信息学分析环境</li>
<li>了解常用测序数据格式和特点</li>
<li>学会配置生物信息学软件环境</li>
<li>通过实际操作加深对高通量测序技术的理解</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E8%A6%81%E6%B1%82">实验环境要求</h2>
<ul>
<li>Linux操作系统（Ubuntu 20.04+ 或 CentOS 7+）</li>
<li>至少4GB内存，20GB可用磁盘空间</li>
<li>网络连接（用于下载软件包和测试数据）</li>
</ul>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86linux%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F%E5%9F%BA%E6%9C%AC%E5%91%BD%E4%BB%A4%E5%AE%9E%E8%B7%B5">第一部分：Linux操作系统基本命令实践</h2>
<h3 id="11-linux%E7%B3%BB%E7%BB%9F%E5%9F%BA%E7%A1%80%E6%93%8D%E4%BD%9C">1.1 Linux系统基础操作</h3>
<h4 id="111-%E6%96%87%E4%BB%B6%E7%B3%BB%E7%BB%9F%E5%AF%BC%E8%88%AA%E7%BB%83%E4%B9%A0">1.1.1 文件系统导航练习</h4>
<p><strong>实验目标：</strong> 熟悉Linux文件系统结构，掌握基本导航命令</p>
<p><strong>操作步骤：</strong></p>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 查看当前目录</span>
<span class="hljs-built_in">pwd</span>
<span class="hljs-comment"># 预期输出：/home/<USER>/span>

<span class="hljs-comment"># 2. 查看根目录结构</span>
ls /
<span class="hljs-comment"># 预期输出：bin  boot  dev  etc  home  lib  lib64  ...</span>

<span class="hljs-comment"># 3. 详细查看当前目录内容</span>
ls -la
<span class="hljs-comment"># 解释：-l 显示详细信息，-a 显示隐藏文件</span>
<span class="hljs-comment"># 输出说明：</span>
<span class="hljs-comment"># drwxr-xr-x  2 <USER> <GROUP>  4096 Oct 15 10:30 Documents</span>
<span class="hljs-comment"># d=目录，rwx=权限，user=所有者，group=群组，4096=大小，时间=修改时间</span>

<span class="hljs-comment"># 4. 创建实验目录结构</span>
mkdir -p ngs_course/data/{raw,processed,results}
mkdir -p ngs_course/scripts
mkdir -p ngs_course/logs

<span class="hljs-comment"># 验证目录结构</span>
tree ngs_course
<span class="hljs-comment"># 如果没有tree命令，使用：</span>
ls -R ngs_course
</div></code></pre>
<p><strong>实际练习任务：</strong></p>
<pre class="hljs"><code><div><span class="hljs-comment"># 任务1：创建以下目录结构</span>
<span class="hljs-comment"># project/</span>
<span class="hljs-comment"># ├── data/</span>
<span class="hljs-comment"># │   ├── fastq/</span>
<span class="hljs-comment"># │   ├── reference/</span>
<span class="hljs-comment"># │   └── results/</span>
<span class="hljs-comment"># │       ├── qc/</span>
<span class="hljs-comment"># │       ├── alignment/</span>
<span class="hljs-comment"># │       └── variants/</span>
<span class="hljs-comment"># ├── scripts/</span>
<span class="hljs-comment"># └── docs/</span>

mkdir -p project/data/{fastq,reference,results/{qc,alignment,variants}}
mkdir -p project/{scripts,docs}
</div></code></pre>
<h4 id="112-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C%E5%AE%9E%E8%B7%B5">1.1.2 文件操作实践</h4>
<p><strong>实验目标：</strong> 掌握文件和目录的基本操作</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 切换到工作目录</span>
<span class="hljs-built_in">cd</span> ngs_course

<span class="hljs-comment"># 1. 创建示例文件</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Sample_ID,Platform,Read_Length,Total_Reads"</span> &gt; sample_info.csv
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Sample_001,Illumina,150,25000000"</span> &gt;&gt; sample_info.csv
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Sample_002,Illumina,150,27000000"</span> &gt;&gt; sample_info.csv
<span class="hljs-built_in">echo</span> <span class="hljs-string">"Sample_003,PacBio,15000,500000"</span> &gt;&gt; sample_info.csv

<span class="hljs-comment"># 2. 查看文件内容的不同方法</span>
cat sample_info.csv          <span class="hljs-comment"># 显示全部内容</span>
head -2 sample_info.csv      <span class="hljs-comment"># 显示前2行</span>
tail -1 sample_info.csv      <span class="hljs-comment"># 显示最后1行</span>
less sample_info.csv         <span class="hljs-comment"># 分页查看（按q退出）</span>

<span class="hljs-comment"># 3. 文件复制和移动</span>
cp sample_info.csv data/sample_info_backup.csv
mv sample_info.csv data/

<span class="hljs-comment"># 4. 文件权限管理</span>
chmod 644 data/sample_info.csv    <span class="hljs-comment"># 设置文件权限</span>
chmod +x scripts/                 <span class="hljs-comment"># 给目录添加执行权限</span>
ls -l data/sample_info.csv        <span class="hljs-comment"># 查看权限</span>
</div></code></pre>
<h4 id="113-%E6%96%87%E6%9C%AC%E5%A4%84%E7%90%86%E5%91%BD%E4%BB%A4%E5%AE%9E%E8%B7%B5">1.1.3 文本处理命令实践</h4>
<p><strong>实验目标：</strong> 学会使用grep、awk、sed等工具处理生物信息学数据</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 准备测试数据</span>
cat &gt; data/gene_expression.txt &lt;&lt; <span class="hljs-string">'EOF'</span>
Gene_ID	Sample1	Sample2	Sample3	P_value
ENSG00000223972	5.2	4.8	5.1	0.001
ENSG00000227232	12.5	11.8	12.3	0.002
ENSG00000278267	0.5	0.3	0.4	0.15
ENSG00000243485	8.9	9.2	8.7	0.003
ENSG00000284332	15.2	14.8	15.5	0.0001
EOF

<span class="hljs-comment"># 1. grep命令实践</span>
<span class="hljs-comment"># 查找显著差异表达基因（P值小于0.01）</span>
grep -E <span class="hljs-string">"0\.(000|00[1-9])"</span> data/gene_expression.txt
<span class="hljs-comment"># 解释：-E使用扩展正则表达式，匹配P值模式</span>

<span class="hljs-comment"># 统计显著基因数量</span>
grep -c -E <span class="hljs-string">"0\.(000|00[1-9])"</span> data/gene_expression.txt

<span class="hljs-comment"># 查找高表达基因（任一样本表达量&gt;10）</span>
grep -E <span class="hljs-string">"1[0-9]\."</span> data/gene_expression.txt

<span class="hljs-comment"># 2. awk命令实践</span>
<span class="hljs-comment"># 计算每个基因的平均表达量</span>
awk <span class="hljs-string">'NR&gt;1 {avg=($2+$3+$4)/3; print $1, avg}'</span> data/gene_expression.txt

<span class="hljs-comment"># 筛选高表达且显著的基因</span>
awk <span class="hljs-string">'NR&gt;1 &amp;&amp; $5&lt;0.01 &amp;&amp; ($2&gt;10 || $3&gt;10 || $4&gt;10) {print $1, $5}'</span> data/gene_expression.txt

<span class="hljs-comment"># 统计各个P值范围的基因数量</span>
awk <span class="hljs-string">'NR&gt;1 {
    if($5&lt;0.001) count1++; 
    else if($5&lt;0.01) count2++; 
    else count3++
} 
END {
    print "P&lt;0.001:", count1, "genes"; 
    print "0.001&lt;=P&lt;0.01:", count2, "genes"; 
    print "P&gt;=0.01:", count3, "genes"
}'</span> data/gene_expression.txt

<span class="hljs-comment"># 3. sed命令实践</span>
<span class="hljs-comment"># 将制表符替换为逗号（转换为CSV格式）</span>
sed <span class="hljs-string">'s/\t/,/g'</span> data/gene_expression.txt &gt; data/gene_expression.csv

<span class="hljs-comment"># 删除头文件行</span>
sed <span class="hljs-string">'1d'</span> data/gene_expression.txt &gt; data/gene_expression_no_header.txt

<span class="hljs-comment"># 将ENSG替换为Gene</span>
sed <span class="hljs-string">'s/ENSG/Gene/g'</span> data/gene_expression.txt &gt; data/gene_expression_modified.txt
</div></code></pre>
<h4 id="114-%E7%AE%A1%E9%81%93%E5%92%8C%E9%87%8D%E5%AE%9A%E5%90%91%E9%AB%98%E7%BA%A7%E6%93%8D%E4%BD%9C">1.1.4 管道和重定向高级操作</h4>
<p><strong>实验目标：</strong> 掌握复杂数据处理流程</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 复杂管道操作示例</span>
<span class="hljs-comment"># 分析sample_info.csv，统计不同平台的reads数量</span>
cat data/sample_info.csv | 
grep -v <span class="hljs-string">"Sample_ID"</span> |           <span class="hljs-comment"># 去除头行</span>
awk -F<span class="hljs-string">','</span> <span class="hljs-string">'{
    platform[$2] += $4
} 
END {
    for(p in platform) 
        print p, platform[p]
}'</span> |
sort -k2 -nr                    <span class="hljs-comment"># 按第二列数值降序排序</span>

<span class="hljs-comment"># 2. 统计基因表达数据</span>
cat data/gene_expression.txt |
awk <span class="hljs-string">'NR&gt;1 {
    for(i=2; i&lt;=4; i++) {
        if($i &gt; max) max = $i;
        if(min == "" || $i &lt; min) min = $i;
        sum += $i; count++;
    }
} 
END {
    print "Max expression:", max;
    print "Min expression:", min;
    print "Average expression:", sum/count
}'</span>

<span class="hljs-comment"># 3. 日志文件分析模拟</span>
cat &gt; logs/analysis.log &lt;&lt; <span class="hljs-string">'EOF'</span>
2024-01-15 10:30:01 INFO Starting quality control
2024-01-15 10:35:12 INFO FastQC analysis completed
2024-01-15 10:40:23 WARNING Low quality reads detected
2024-01-15 10:45:34 INFO Trimming completed
2024-01-15 10:50:45 ERROR Alignment failed <span class="hljs-keyword">for</span> sample_003
2024-01-15 10:55:56 INFO Retrying alignment with different parameters
2024-01-15 11:00:07 INFO Alignment completed successfully
EOF

<span class="hljs-comment"># 分析日志：统计不同级别的消息</span>
grep -o -E <span class="hljs-string">"(INFO|WARNING|ERROR)"</span> logs/analysis.log | 
sort | 
uniq -c | 
awk <span class="hljs-string">'{print $2": "$1}'</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86%E7%94%9F%E7%89%A9%E4%BF%A1%E6%81%AF%E5%AD%A6%E5%88%86%E6%9E%90%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE%E5%AE%9E%E8%B7%B5">第二部分：生物信息学分析环境配置实践</h2>
<h3 id="21-conda%E7%8E%AF%E5%A2%83%E7%AE%A1%E7%90%86%E5%AE%9E%E8%B7%B5">2.1 Conda环境管理实践</h3>
<h4 id="211-conda%E5%AE%89%E8%A3%85%E5%92%8C%E9%85%8D%E7%BD%AE">2.1.1 Conda安装和配置</h4>
<p><strong>实验目标：</strong> 安装并配置Conda包管理器</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 下载并安装Miniconda（如果未安装）</span>
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b -p <span class="hljs-variable">$HOME</span>/miniconda3

<span class="hljs-comment"># 2. 初始化conda环境</span>
<span class="hljs-variable">$HOME</span>/miniconda3/bin/conda init bash
<span class="hljs-built_in">source</span> ~/.bashrc

<span class="hljs-comment"># 3. 验证安装</span>
conda --version
<span class="hljs-comment"># 预期输出：conda 23.x.x</span>

<span class="hljs-comment"># 4. 配置conda channels</span>
conda config --add channels defaults
conda config --add channels bioconda
conda config --add channels conda-forge
conda config --<span class="hljs-built_in">set</span> channel_priority strict

<span class="hljs-comment"># 5. 查看配置</span>
conda config --show channels
</div></code></pre>
<h4 id="212-%E5%88%9B%E5%BB%BA%E4%B8%93%E7%94%A8%E5%88%86%E6%9E%90%E7%8E%AF%E5%A2%83">2.1.2 创建专用分析环境</h4>
<p><strong>实验目标：</strong> 为NGS分析创建独立的conda环境</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 创建NGS分析环境</span>
conda create -n ngs_analysis python=3.9 -y

<span class="hljs-comment"># 2. 激活环境</span>
conda activate ngs_analysis

<span class="hljs-comment"># 3. 验证环境</span>
<span class="hljs-built_in">which</span> python
python --version

<span class="hljs-comment"># 4. 安装基础生物信息学工具</span>
conda install -c bioconda fastqc trimmomatic bwa samtools bcftools -y

<span class="hljs-comment"># 5. 验证工具安装</span>
fastqc --version        <span class="hljs-comment"># 预期：FastQC v0.x.x</span>
trimmomatic -version    <span class="hljs-comment"># 预期：0.39</span>
bwa                     <span class="hljs-comment"># 显示帮助信息</span>
samtools --version      <span class="hljs-comment"># 预期：samtools x.x.x</span>

<span class="hljs-comment"># 6. 创建环境信息记录</span>
conda list &gt; ngs_analysis_packages.txt
conda env <span class="hljs-built_in">export</span> &gt; ngs_analysis_environment.yml
</div></code></pre>
<h4 id="213-%E7%8E%AF%E5%A2%83%E7%AE%A1%E7%90%86%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5">2.1.3 环境管理最佳实践</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 列出所有环境</span>
conda env list

<span class="hljs-comment"># 2. 创建特定版本的环境</span>
conda create -n old_tools python=3.7 bwa=0.7.15 samtools=1.9 -y

<span class="hljs-comment"># 3. 克隆环境</span>
conda create --<span class="hljs-built_in">clone</span> ngs_analysis --name ngs_analysis_backup

<span class="hljs-comment"># 4. 从文件创建环境</span>
conda env create -f ngs_analysis_environment.yml

<span class="hljs-comment"># 5. 环境清理</span>
conda clean --all

<span class="hljs-comment"># 6. 删除不需要的环境</span>
<span class="hljs-comment"># conda env remove --name old_tools  # 谨慎使用</span>
</div></code></pre>
<h3 id="22-%E8%BD%AF%E4%BB%B6%E5%AE%89%E8%A3%85%E9%AA%8C%E8%AF%81%E5%AE%9E%E8%B7%B5">2.2 软件安装验证实践</h3>
<h4 id="221-%E5%B7%A5%E5%85%B7%E5%8A%9F%E8%83%BD%E6%B5%8B%E8%AF%95">2.2.1 工具功能测试</h4>
<p><strong>实验目标：</strong> 验证安装的生物信息学工具功能正常</p>
<pre class="hljs"><code><div><span class="hljs-comment"># 确保在ngs_analysis环境中</span>
conda activate ngs_analysis

<span class="hljs-comment"># 1. 创建测试数据目录</span>
mkdir -p test_data
<span class="hljs-built_in">cd</span> test_data

<span class="hljs-comment"># 2. 创建模拟FASTQ数据进行测试</span>
cat &gt; test_sample.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@SEQ_ID_1
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@SEQ_ID_2
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII<span class="hljs-comment">###</span>
@SEQ_ID_3
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
EOF

<span class="hljs-comment"># 3. 测试FastQC</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"测试FastQC..."</span>
fastqc test_sample.fastq -o .
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"test_sample_fastqc.html"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ FastQC测试成功"</span>
<span class="hljs-keyword">else</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✗ FastQC测试失败"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 4. 测试Trimmomatic</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"测试Trimmomatic..."</span>
trimmomatic SE test_sample.fastq test_sample_trimmed.fastq MINLEN:20
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"test_sample_trimmed.fastq"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ Trimmomatic测试成功"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"原始序列数: <span class="hljs-variable">$(grep -c "^@" test_sample.fastq)</span>"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"过滤后序列数: <span class="hljs-variable">$(grep -c "^@" test_sample_trimmed.fastq)</span>"</span>
<span class="hljs-keyword">else</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✗ Trimmomatic测试失败"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 5. 创建简单参考序列测试BWA</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"&gt;chr1"</span> &gt; reference.fasta
<span class="hljs-built_in">echo</span> <span class="hljs-string">"GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG"</span> &gt;&gt; reference.fasta

<span class="hljs-comment"># 建立BWA索引</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"测试BWA索引建立..."</span>
bwa index reference.fasta
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"reference.fasta.bwt"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ BWA索引建立成功"</span>
<span class="hljs-keyword">else</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✗ BWA索引建立失败"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 6. 测试SAMtools</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"测试SAMtools..."</span>
samtools faidx reference.fasta
<span class="hljs-keyword">if</span> [ -f <span class="hljs-string">"reference.fasta.fai"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ SAMtools测试成功"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"参考序列信息:"</span>
    cat reference.fasta.fai
<span class="hljs-keyword">else</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✗ SAMtools测试失败"</span>
<span class="hljs-keyword">fi</span>

<span class="hljs-comment"># 清理测试文件</span>
<span class="hljs-built_in">cd</span> ..
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%89%E9%83%A8%E5%88%86%E6%B5%8B%E5%BA%8F%E6%95%B0%E6%8D%AE%E6%A0%BC%E5%BC%8F%E8%AF%A6%E8%A7%A3%E4%B8%8E%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C">第三部分：测序数据格式详解与实践操作</h2>
<h3 id="31-fastq%E6%A0%BC%E5%BC%8F%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90">3.1 FASTQ格式深度解析</h3>
<h4 id="311-fastq%E6%A0%BC%E5%BC%8F%E7%BB%93%E6%9E%84%E5%AE%9E%E8%B7%B5">3.1.1 FASTQ格式结构实践</h4>
<p><strong>实验目标：</strong> 深入理解FASTQ格式的各个组成部分</p>
<pre class="hljs"><code><div>mkdir -p data_formats
<span class="hljs-built_in">cd</span> data_formats

<span class="hljs-comment"># 1. 创建标准FASTQ示例</span>
cat &gt; illumina_example.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@HWUSI-EAS100R:6:73:941:1973<span class="hljs-comment">#0/1</span>
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
!<span class="hljs-string">''</span>*((((***++<span class="hljs-string">""</span>%%<span class="hljs-string">''</span>++<span class="hljs-string">"++)()...+()''*(((***+"</span>))<span class="hljs-string">""</span>*(((***++<span class="hljs-string">""</span><span class="hljs-string">""</span>*))(***++<span class="hljs-string">""</span>
@HWUSI-EAS100R:6:73:941:1974<span class="hljs-comment">#0/1</span>
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII<span class="hljs-comment">###</span>
@HWUSI-EAS100R:6:73:941:1975<span class="hljs-comment">#0/1</span>
NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN
+
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
EOF

<span class="hljs-comment"># 2. 解析FASTQ头部信息</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== FASTQ头部信息解析 ==="</span>
grep <span class="hljs-string">"^@"</span> illumina_example.fastq | head -1 | 
awk -F<span class="hljs-string">':'</span> <span class="hljs-string">'{
    print "仪器ID:", $1; 
    print "流通池编号:", $2; 
    print "Lane编号:", $3; 
    print "Tile编号:", $4; 
    print "X坐标:", $5; 
    print "Y坐标:", substr($6,1,index($6,"#")-1)
}'</span>

<span class="hljs-comment"># 3. 质量值解码实践</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 质量值解码示例 ==="</span>
quality_line=<span class="hljs-string">"!''*((((***++\"\"%%''++'++)()...+()''"</span>

<span class="hljs-comment"># 创建质量值解码脚本</span>
cat &gt; decode_quality.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
import sys

def decode_phred33(quality_string):
    <span class="hljs-string">""</span><span class="hljs-string">"解码Phred+33质量值"</span><span class="hljs-string">""</span>
    qualities = []
    <span class="hljs-keyword">for</span> char <span class="hljs-keyword">in</span> quality_string:
        q_score = ord(char) - 33
        error_prob = 10**(-q_score/10)
        accuracy = 1 - error_prob
        qualities.append({
            <span class="hljs-string">'char'</span>: char,
            <span class="hljs-string">'ascii'</span>: ord(char),
            <span class="hljs-string">'q_score'</span>: q_score,
            <span class="hljs-string">'error_prob'</span>: f<span class="hljs-string">"{error_prob:.6f}"</span>,
            <span class="hljs-string">'accuracy'</span>: f<span class="hljs-string">"{accuracy:.6f}"</span>
        })
    <span class="hljs-built_in">return</span> qualities

<span class="hljs-comment"># 测试质量字符串</span>
test_quality = <span class="hljs-string">"!''*((((***++\"\"%%''++'++)()...+()''"</span>
<span class="hljs-built_in">print</span>(<span class="hljs-string">"位置\t字符\tASCII\tQ值\t错误概率\t准确率"</span>)
<span class="hljs-built_in">print</span>(<span class="hljs-string">"-"</span> * 60)

<span class="hljs-keyword">for</span> i, q <span class="hljs-keyword">in</span> enumerate(decode_phred33(test_quality[:10])):  <span class="hljs-comment"># 只显示前10个</span>
    <span class="hljs-built_in">print</span>(f<span class="hljs-string">"{i+1}\t{q['char']}\t{q['ascii']}\t{q['q_score']}\t{q['error_prob']}\t{q['accuracy']}"</span>)
EOF

python3 decode_quality.py

<span class="hljs-comment"># 4. FASTQ统计信息计算</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== FASTQ文件统计信息 ==="</span>
cat &gt; fastq_stats.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
fastq_file=<span class="hljs-variable">$1</span>

<span class="hljs-keyword">if</span> [ ! -f <span class="hljs-string">"<span class="hljs-variable">$fastq_file</span>"</span> ]; <span class="hljs-keyword">then</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"文件不存在: <span class="hljs-variable">$fastq_file</span>"</span>
    <span class="hljs-built_in">exit</span> 1
<span class="hljs-keyword">fi</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"文件: <span class="hljs-variable">$fastq_file</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"序列总数: <span class="hljs-variable">$(grep -c "^@" $fastq_file)</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"总碱基数: <span class="hljs-variable">$(grep -v "^[@+]" $fastq_file | tr -d '\n' | wc -c)</span>"</span>

<span class="hljs-comment"># 计算平均序列长度</span>
total_length=$(grep -v <span class="hljs-string">"^[@+]"</span> <span class="hljs-variable">$fastq_file</span> | awk <span class="hljs-string">'{sum += length($0)} END {print sum}'</span>)
seq_count=$(grep -c <span class="hljs-string">"^@"</span> <span class="hljs-variable">$fastq_file</span>)
<span class="hljs-built_in">echo</span> <span class="hljs-string">"平均序列长度: <span class="hljs-variable">$(echo "scale=2; $total_length / $seq_count" | bc)</span>"</span>

<span class="hljs-comment"># GC含量计算</span>
gc_content=$(grep -v <span class="hljs-string">"^[@+]"</span> <span class="hljs-variable">$fastq_file</span> | tr -d <span class="hljs-string">'\n'</span> | 
             awk <span class="hljs-string">'{
                 gsub(/[^GCgc]/, "", $0); 
                 gc_count = length($0)
             } END {
                 print gc_count
             }'</span>)
<span class="hljs-built_in">echo</span> <span class="hljs-string">"GC含量: <span class="hljs-variable">$(echo "scale=2; $gc_content * 100 / $total_length" | bc)</span>%"</span>

<span class="hljs-comment"># N含量统计</span>
n_count=$(grep -v <span class="hljs-string">"^[@+]"</span> <span class="hljs-variable">$fastq_file</span> | tr -d <span class="hljs-string">'\n'</span> | tr -<span class="hljs-built_in">cd</span> <span class="hljs-string">'N'</span> | wc -c)
<span class="hljs-built_in">echo</span> <span class="hljs-string">"N碱基数: <span class="hljs-variable">$n_count</span>"</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"N含量: <span class="hljs-variable">$(echo "scale=4; $n_count * 100 / $total_length" | bc)</span>%"</span>
EOF

chmod +x fastq_stats.sh
./fastq_stats.sh illumina_example.fastq
</div></code></pre>
<h4 id="312-%E4%B8%8D%E5%90%8C%E5%B9%B3%E5%8F%B0fastq%E5%B7%AE%E5%BC%82%E5%88%86%E6%9E%90">3.1.2 不同平台FASTQ差异分析</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 1. 创建不同平台的FASTQ示例</span>
<span class="hljs-comment"># Illumina格式</span>
cat &gt; illumina_reads.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@HWUSI-EAS100R:6:73:941:1973<span class="hljs-comment">#0/1</span>
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII<span class="hljs-comment">###</span>
@HWUSI-EAS100R:6:73:941:1974<span class="hljs-comment">#0/1</span>
ACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGTACGT
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
EOF

<span class="hljs-comment"># PacBio格式（长读长）</span>
cat &gt; pacbio_reads.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@m54238_180901_142851/4194304/ccs
GATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATGATTTGGGGTTCAAAGCAGTATCGATCAAATAGTAAATGATGATGATGATGATGATGATGATGATGATGATG
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH</div></code></pre>

</body>
</html>
