<!DOCTYPE html>
<html>
<head>
<title>Lecture7_lab.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E4%B8%93%E9%A2%98%E4%B8%83%E5%AE%8F%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%B5%8B%E5%BA%8F%E4%B8%8E%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90---%E5%A2%9E%E5%BC%BA%E7%89%88%E5%AE%9E%E8%B7%B5%E6%93%8D%E4%BD%9C%E8%AF%BE">专题七：宏基因组测序与数据分析 - 增强版实践操作课</h1>
<h2 id="%E8%AF%BE%E7%A8%8B%E7%9B%AE%E6%A0%87">课程目标</h2>
<p>本增强版实践课程旨在帮助学生：</p>
<ol>
<li>掌握完整的宏基因组数据处理流程和质量控制策略</li>
<li>学会使用MetaPhlAn3、Kraken2等工具进行物种分类分析</li>
<li>掌握宏基因组组装和功能注释的高级方法</li>
<li>学会宏基因组差异分析和生态网络构建</li>
</ol>
<h2 id="%E5%AE%9E%E9%AA%8C%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87">实验环境准备</h2>
<h3 id="%E8%BD%AF%E4%BB%B6%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE">软件环境配置</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建专题七专用环境</span>
conda create -n metagenomics_analysis python=3.8 -y
conda activate metagenomics_analysis

<span class="hljs-comment"># 安装基础分析工具</span>
conda install -c bioconda fastp fastqc multiqc trimmomatic -y
conda install -c bioconda bowtie2 samtools seqtk -y

<span class="hljs-comment"># 安装物种分类工具</span>
conda install -c bioconda metaphlan kraken2 bracken -y
conda install -c bioconda centrifuge kaiju -y

<span class="hljs-comment"># 安装组装工具</span>
conda install -c bioconda megahit spades metaspades -y
conda install -c bioconda prodigal prokka -y

<span class="hljs-comment"># 安装功能注释工具</span>
conda install -c bioconda hmmer diamond blast -y
conda install -c bioconda eggnog-mapper -y

<span class="hljs-comment"># 安装R包和可视化工具</span>
conda install -c conda-forge r-base r-ggplot2 r-dplyr r-vegan -y
conda install -c bioconda r-phyloseq r-deseq2 -y

<span class="hljs-comment"># 验证安装</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 软件版本验证 ==="</span>
fastp --version
metaphlan --version
kraken2 --version
megahit --version
diamond version

<span class="hljs-built_in">echo</span> <span class="hljs-string">"软件安装完成！"</span>
</div></code></pre>
<h3 id="%E5%AE%9E%E9%AA%8C%E6%95%B0%E6%8D%AE%E5%87%86%E5%A4%87">实验数据准备</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建工作目录</span>
mkdir -p metagenomics_practice/{data/{raw,processed,databases},results/{qc,taxonomy,assembly,annotation,functional},scripts,logs}
<span class="hljs-built_in">cd</span> metagenomics_practice

<span class="hljs-comment"># 创建模拟宏基因组双端测序数据（肠道菌群样本）</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 创建模拟宏基因组数据 ==="</span>

<span class="hljs-comment"># 样本1：健康对照组</span>
cat &gt; data/raw/healthy_R1.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@M01234:1:*********-A1B2C:1:1101:15000:1000 1:N:0:ACGT
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTAT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15001:1001 1:N:0:ACGT
TTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@M01234:1:*********-A1B2C:1:1101:15002:1002 1:N:0:ACGT
ACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGA
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15003:1003 1:N:0:ACGT
TTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCTGATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTT
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

cat &gt; data/raw/healthy_R2.fastq &lt;&lt; <span class="hljs-string">'EOF'</span>
@M01234:1:*********-A1B2C:1:1101:15000:1000 2:N:0:ACGT
ATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCAATGCTATCGCGTGCACACCCCCCCAGACGAAAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTGATC
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15001:1001 2:N:0:ACGT
TATTATTATATGTCCTACAAGCATTAATTAATTAACACTTTAGTAGGTATTGTTCGCCTGTAATATTGAACGTAGGTGCGATAAATAATAGGGATGAGGCAGGAATCAAAGACAGATACTGCGACATAGGGTGCTCCGGCTCCAGCGTCTCGCAA
+
JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
@M01234:1:*********-A1B2C:1:1101:15002:1002 2:N:0:ACGT
TCTGGTTAGGCTGGTGTAGGGTTCTTTGTTTTGGGGGTTTGGCAGAGATGTGTTTAAGTGCTGTGGCCAGAAGCGGGGGGAGGGGGGGGTTTGGTGGAAATTTTTTGTTATGATGTCTGTGTGGAAAGTGGCTGTGCAGACATTCAATTGTT
+
IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII
@M01234:1:*********-A1B2C:1:1101:15003:1003 2:N:0:ACGT
AAATACCAAATGCATGGAGAGCTCCCGTGAGTGGTTAATAGGGTGATAGACCTGTGATCAGAAAAGGGTGTTATTATTGTGTGCTGTGAGGCCTCAGAGATGGGGTTGGCTTTGGGAGGGGATAAGCATTGGAAAGATAAAATTTGAAA
+
HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
EOF

<span class="hljs-comment"># 样本2：疾病组</span>
cp data/raw/healthy_R1.fastq data/raw/disease_R1.fastq
cp data/raw/healthy_R2.fastq data/raw/disease_R2.fastq

<span class="hljs-comment"># 创建样本元数据</span>
cat &gt; data/sample_metadata.txt &lt;&lt; <span class="hljs-string">'EOF'</span>
SampleID	Group	Description
healthy	Control	Healthy control sample
disease	Disease	Disease sample
EOF

<span class="hljs-built_in">echo</span> <span class="hljs-string">"实验数据准备完成！"</span>
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86%E5%AE%8F%E5%9F%BA%E5%9B%A0%E7%BB%84%E6%95%B0%E6%8D%AE%E9%A2%84%E5%A4%84%E7%90%86%E4%B8%8E%E8%B4%A8%E6%8E%A7">第一部分：宏基因组数据预处理与质控</h2>
<h3 id="11-%E6%95%B0%E6%8D%AE%E8%B4%A8%E9%87%8F%E8%AF%84%E4%BC%B0%E5%92%8C%E9%A2%84%E5%A4%84%E7%90%86">1.1 数据质量评估和预处理</h3>
<h4 id="111-%E5%8E%9F%E5%A7%8B%E6%95%B0%E6%8D%AE%E8%B4%A8%E9%87%8F%E6%8E%A7%E5%88%B6">1.1.1 原始数据质量控制</h4>
<p><strong>实验目标：</strong> 评估宏基因组测序数据质量，识别潜在的技术偏差和污染</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 宏基因组数据质量控制实践 ==="</span>

<span class="hljs-comment"># 1. 创建质量控制目录结构</span>
mkdir -p results/qc/{raw,processed,reports}

<span class="hljs-comment"># 2. FastQC质量评估</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"执行原始数据质量评估..."</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> healthy disease; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"处理样本: <span class="hljs-variable">$sample</span>"</span>
    
    <span class="hljs-comment"># 对双端测序数据进行质量评估</span>
    fastqc data/raw/<span class="hljs-variable">${sample}</span>_R1.fastq -o results/qc/raw/
    fastqc data/raw/<span class="hljs-variable">${sample}</span>_R2.fastq -o results/qc/raw/
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">$sample</span> 样本质量评估完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-comment"># 3. 生成综合质量报告</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"生成综合质量报告..."</span>
multiqc results/qc/raw/ -o results/qc/reports/ -n raw_data_qc_report

<span class="hljs-built_in">echo</span> <span class="hljs-string">"原始数据质量评估完成！"</span>
</div></code></pre>
<h4 id="112-%E9%AB%98%E7%BA%A7%E8%B4%A8%E9%87%8F%E8%BF%87%E6%BB%A4%E4%B8%8E%E9%A2%84%E5%A4%84%E7%90%86">1.1.2 高级质量过滤与预处理</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建宏基因组特化的质量过滤脚本</span>
cat &gt; scripts/metagenomics_preprocessing.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
宏基因组数据预处理脚本
包含质量过滤、宿主序列去除、复杂度分析等功能
"</span><span class="hljs-string">""</span>

import os
import subprocess
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

class MetagenomicsPreprocessor:
    def __init__(self, working_dir):
        self.working_dir = working_dir
        self.samples = []
        
    def setup_directories(self):
        <span class="hljs-string">""</span><span class="hljs-string">"创建必要的目录结构"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">dirs</span> = [
            <span class="hljs-string">'results/qc/fastp'</span>,
            <span class="hljs-string">'results/qc/host_removal'</span>, 
            <span class="hljs-string">'results/qc/complexity'</span>,
            <span class="hljs-string">'results/qc/final_stats'</span>
        ]
        <span class="hljs-keyword">for</span> dir_path <span class="hljs-keyword">in</span> <span class="hljs-built_in">dirs</span>:
            os.makedirs(dir_path, exist_ok=True)
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"目录结构创建完成"</span>)
    
    def run_fastp_filtering(self, sample_name, r1_file, r2_file):
        <span class="hljs-string">""</span><span class="hljs-string">"使用fastp进行高质量过滤"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"正在处理样本: {sample_name}"</span>)
        
        <span class="hljs-comment"># fastp参数优化（针对宏基因组）</span>
        cmd = [
            <span class="hljs-string">'fastp'</span>,
            <span class="hljs-string">'-i'</span>, r1_file,
            <span class="hljs-string">'-I'</span>, r2_file,
            <span class="hljs-string">'-o'</span>, f<span class="hljs-string">'results/qc/fastp/{sample_name}_clean_R1.fastq'</span>,
            <span class="hljs-string">'-O'</span>, f<span class="hljs-string">'results/qc/fastp/{sample_name}_clean_R2.fastq'</span>,
            <span class="hljs-string">'-h'</span>, f<span class="hljs-string">'results/qc/fastp/{sample_name}_fastp.html'</span>,
            <span class="hljs-string">'-j'</span>, f<span class="hljs-string">'results/qc/fastp/{sample_name}_fastp.json'</span>,
            <span class="hljs-string">'--cut_right'</span>,
            <span class="hljs-string">'--cut_right_window_size'</span>, <span class="hljs-string">'4'</span>,
            <span class="hljs-string">'--cut_right_mean_quality'</span>, <span class="hljs-string">'20'</span>,
            <span class="hljs-string">'--qualified_quality_phred'</span>, <span class="hljs-string">'20'</span>,
            <span class="hljs-string">'--unqualified_percent_limit'</span>, <span class="hljs-string">'40'</span>,
            <span class="hljs-string">'--length_required'</span>, <span class="hljs-string">'50'</span>,
            <span class="hljs-string">'--detect_adapter_for_pe'</span>,
            <span class="hljs-string">'--correction'</span>,
            <span class="hljs-string">'--thread'</span>, <span class="hljs-string">'4'</span>
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"✓ {sample_name} fastp过滤完成"</span>)
            <span class="hljs-built_in">return</span> True
        except subprocess.CalledProcessError as e:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"❌ {sample_name} fastp过滤失败: {e}"</span>)
            <span class="hljs-built_in">return</span> False
    
    def analyze_sequence_complexity(self, sample_name):
        <span class="hljs-string">""</span><span class="hljs-string">"分析序列复杂度"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分析 {sample_name} 序列复杂度..."</span>)
        
        <span class="hljs-comment"># 计算序列复杂度和重复序列比例</span>
        r1_file = f<span class="hljs-string">'results/qc/fastp/{sample_name}_clean_R1.fastq'</span>
        
        <span class="hljs-keyword">if</span> not os.path.exists(r1_file):
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"文件不存在: {r1_file}"</span>)
            <span class="hljs-built_in">return</span>
        
        <span class="hljs-comment"># 使用seqtk计算序列统计</span>
        cmd = [<span class="hljs-string">'seqtk'</span>, <span class="hljs-string">'comp'</span>, r1_file]
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            <span class="hljs-comment"># 解析结果并保存</span>
            with open(f<span class="hljs-string">'results/qc/complexity/{sample_name}_complexity.txt'</span>, <span class="hljs-string">'w'</span>) as f:
                f.write(<span class="hljs-string">"name\tlength\t#A\t#C\t#G\t#T\t#2\t#3\t#4\t#CpG\t#tv\t#ts\t#CpG-ts\n"</span>)
                f.write(result.stdout)
            
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"✓ {sample_name} 复杂度分析完成"</span>)
        except subprocess.CalledProcessError as e:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"❌ {sample_name} 复杂度分析失败: {e}"</span>)
    
    def generate_preprocessing_stats(self):
        <span class="hljs-string">""</span><span class="hljs-string">"生成预处理统计报告"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"生成预处理统计报告..."</span>)
        
        stats = []
        <span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> [<span class="hljs-string">'healthy'</span>, <span class="hljs-string">'disease'</span>]:
            sample_stats = {
                <span class="hljs-string">'sample'</span>: sample,
                <span class="hljs-string">'raw_reads'</span>: self._count_fastq_reads(f<span class="hljs-string">'data/raw/{sample}_R1.fastq'</span>),
                <span class="hljs-string">'clean_reads'</span>: self._count_fastq_reads(f<span class="hljs-string">'results/qc/fastp/{sample}_clean_R1.fastq'</span>),
            }
            
            <span class="hljs-comment"># 计算过滤效率</span>
            <span class="hljs-keyword">if</span> sample_stats[<span class="hljs-string">'raw_reads'</span>] &gt; 0:
                sample_stats[<span class="hljs-string">'retention_rate'</span>] = (sample_stats[<span class="hljs-string">'clean_reads'</span>] / sample_stats[<span class="hljs-string">'raw_reads'</span>]) * 100
            <span class="hljs-keyword">else</span>:
                sample_stats[<span class="hljs-string">'retention_rate'</span>] = 0
            
            stats.append(sample_stats)
        
        <span class="hljs-comment"># 保存统计结果</span>
        df = pd.DataFrame(stats)
        df.to_csv(<span class="hljs-string">'results/qc/final_stats/preprocessing_summary.csv'</span>, index=False)
        
        <span class="hljs-comment"># 生成可视化图表</span>
        self._plot_preprocessing_stats(df)
        
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"统计报告生成完成"</span>)
        <span class="hljs-built_in">return</span> df
    
    def _count_fastq_reads(self, fastq_file):
        <span class="hljs-string">""</span><span class="hljs-string">"统计FASTQ文件中的reads数量"</span><span class="hljs-string">""</span>
        <span class="hljs-keyword">if</span> not os.path.exists(fastq_file):
            <span class="hljs-built_in">return</span> 0
        
        try:
            cmd = [<span class="hljs-string">'grep'</span>, <span class="hljs-string">'-c'</span>, <span class="hljs-string">'^@'</span>, fastq_file]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            <span class="hljs-built_in">return</span> int(result.stdout.strip())
        except:
            <span class="hljs-built_in">return</span> 0
    
    def _plot_preprocessing_stats(self, df):
        <span class="hljs-string">""</span><span class="hljs-string">"绘制预处理统计图表"</span><span class="hljs-string">""</span>
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        <span class="hljs-comment"># 读数统计</span>
        x = range(len(df))
        width = 0.35
        
        axes[0].bar([i - width/2 <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> x], df[<span class="hljs-string">'raw_reads'</span>], width, label=<span class="hljs-string">'原始reads'</span>, alpha=0.8)
        axes[0].bar([i + width/2 <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> x], df[<span class="hljs-string">'clean_reads'</span>], width, label=<span class="hljs-string">'过滤后reads'</span>, alpha=0.8)
        axes[0].set_xlabel(<span class="hljs-string">'样本'</span>)
        axes[0].set_ylabel(<span class="hljs-string">'Reads数量'</span>)
        axes[0].set_title(<span class="hljs-string">'预处理前后reads统计'</span>)
        axes[0].set_xticks(x)
        axes[0].set_xticklabels(df[<span class="hljs-string">'sample'</span>])
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        <span class="hljs-comment"># 保留率</span>
        bars = axes[1].bar(df[<span class="hljs-string">'sample'</span>], df[<span class="hljs-string">'retention_rate'</span>], alpha=0.8, color=<span class="hljs-string">'green'</span>)
        axes[1].set_xlabel(<span class="hljs-string">'样本'</span>)
        axes[1].set_ylabel(<span class="hljs-string">'保留率 (%)'</span>)
        axes[1].set_title(<span class="hljs-string">'质量过滤保留率'</span>)
        axes[1].grid(True, alpha=0.3)
        
        <span class="hljs-comment"># 添加数值标签</span>
        <span class="hljs-keyword">for</span> bar <span class="hljs-keyword">in</span> bars:
            height = bar.get_height()
            axes[1].text(bar.get_x() + bar.get_width()/2., height,
                        f<span class="hljs-string">'{height:.1f}%'</span>, ha=<span class="hljs-string">'center'</span>, va=<span class="hljs-string">'bottom'</span>)
        
        plt.tight_layout()
        plt.savefig(<span class="hljs-string">'results/qc/final_stats/preprocessing_stats.png'</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
        plt.close()
        
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"统计图表已保存到: results/qc/final_stats/preprocessing_stats.png"</span>)

def main():
    processor = MetagenomicsPreprocessor(<span class="hljs-string">'.'</span>)
    processor.setup_directories()
    
    <span class="hljs-comment"># 处理所有样本</span>
    samples = [
        (<span class="hljs-string">'healthy'</span>, <span class="hljs-string">'data/raw/healthy_R1.fastq'</span>, <span class="hljs-string">'data/raw/healthy_R2.fastq'</span>),
        (<span class="hljs-string">'disease'</span>, <span class="hljs-string">'data/raw/disease_R1.fastq'</span>, <span class="hljs-string">'data/raw/disease_R2.fastq'</span>)
    ]
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== 开始宏基因组数据预处理 ==="</span>)
    
    <span class="hljs-keyword">for</span> sample_name, r1_file, r2_file <span class="hljs-keyword">in</span> samples:
        <span class="hljs-keyword">if</span> processor.run_fastp_filtering(sample_name, r1_file, r2_file):
            processor.analyze_sequence_complexity(sample_name)
    
    <span class="hljs-comment"># 生成最终统计报告</span>
    stats_df = processor.generate_preprocessing_stats()
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n=== 预处理结果摘要 ==="</span>)
    <span class="hljs-built_in">print</span>(stats_df.to_string(index=False))
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"\n=== 宏基因组数据预处理完成 ==="</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    main()
EOF

<span class="hljs-comment"># 运行预处理脚本</span>
chmod +x scripts/metagenomics_preprocessing.py
python3 scripts/metagenomics_preprocessing.py
</div></code></pre>
<h3 id="12-%E5%AE%BF%E4%B8%BBdna%E5%8E%BB%E9%99%A4%E5%AE%9E%E8%B7%B5">1.2 宿主DNA去除实践</h3>
<h4 id="121-%E6%9E%84%E5%BB%BA%E5%AE%BF%E4%B8%BB%E5%9F%BA%E5%9B%A0%E7%BB%84%E7%B4%A2%E5%BC%95">1.2.1 构建宿主基因组索引</h4>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 宿主DNA去除实践 ==="</span>

<span class="hljs-comment"># 1. 创建简化的人类参考基因组序列用于演示</span>
mkdir -p data/databases/host_genome

cat &gt; data/databases/host_genome/human_chr22.fa &lt;&lt; <span class="hljs-string">'EOF'</span>
&gt;chr22
GATCACAGGTCTATCACCCTATTAACCACTCACGGGAGCTCTCCATGCATTTGGTATTTTCGTCTGGGGGGTGTGCACGCGATAGCATTGCGAGACGCTGGAGCCGGAGCACCCTATGTCGCAGTATCTGTCTTTGATTCCTGCCTCATCCTATTATTTATCGCACCTACGTTCAATATTACAGGCGAACATACCTACTAAAGTGTGTTAATTAATTAATGCTTGTAGGACATAATAATAACAATTGAATGTCTGCACAGCCACTTTCCACACAGACATCATAACAAAAAATTTCCACCAAACCCCCCCCTCCCCCCGCTTCTGGCCACAGCACTTAAACACATCTCTGCCAAACCCCAAAAACAAAGAACCCTAACACCAGCCTAACCAGATTTCAAATTTTATCTTTCCAATGCTATCCCTCCCAAAGCCAACCCCATCTCTGAGGCATCACAGTCTACACATCAATAATAACACCTTTTCT
EOF

<span class="hljs-comment"># 2. 构建Bowtie2索引</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"构建宿主基因组索引..."</span>
bowtie2-build data/databases/host_genome/human_chr22.fa data/databases/host_genome/human_index

<span class="hljs-built_in">echo</span> <span class="hljs-string">"宿主基因组索引构建完成！"</span>
</div></code></pre>
<h4 id="122-%E5%AE%BF%E4%B8%BBdna%E8%BF%87%E6%BB%A4">1.2.2 宿主DNA过滤</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建宿主DNA去除脚本</span>
cat &gt; scripts/remove_host_dna.sh &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-meta">#!/bin/bash</span>
<span class="hljs-comment"># 宿主DNA去除脚本</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 宿主DNA去除处理 ==="</span>

mkdir -p results/qc/host_removal

<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> healthy disease; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"处理样本: <span class="hljs-variable">$sample</span>"</span>
    
    <span class="hljs-comment"># 1. 比对到宿主基因组</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"  步骤1: 比对到宿主基因组..."</span>
    bowtie2 -x data/databases/host_genome/human_index \
        -1 results/qc/fastp/<span class="hljs-variable">${sample}</span>_clean_R1.fastq \
        -2 results/qc/fastp/<span class="hljs-variable">${sample}</span>_clean_R2.fastq \
        -S results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.sam \
        --un-conc results/qc/host_removal/<span class="hljs-variable">${sample}</span>_non_host_%.fastq \
        --threads 4 \
        2&gt; results/qc/host_removal/<span class="hljs-variable">${sample}</span>_bowtie2.log
    
    <span class="hljs-comment"># 2. 提取未比对的reads（非宿主reads）</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"  步骤2: 提取非宿主reads..."</span>
    mv results/qc/host_removal/<span class="hljs-variable">${sample}</span>_non_host_1.fastq results/qc/host_removal/<span class="hljs-variable">${sample}</span>_clean_nonhost_R1.fastq
    mv results/qc/host_removal/<span class="hljs-variable">${sample}</span>_non_host_2.fastq results/qc/host_removal/<span class="hljs-variable">${sample}</span>_clean_nonhost_R2.fastq
    
    <span class="hljs-comment"># 3. 生成比对统计</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"  步骤3: 生成比对统计..."</span>
    samtools view -bS results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.sam &gt; results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.bam
    samtools flagstat results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.bam &gt; results/qc/host_removal/<span class="hljs-variable">${sample}</span>_flagstat.txt
    
    <span class="hljs-comment"># 4. 统计过滤效果</span>
    original_reads=$(grep -c <span class="hljs-string">"^@"</span> results/qc/fastp/<span class="hljs-variable">${sample}</span>_clean_R1.fastq)
    remaining_reads=$(grep -c <span class="hljs-string">"^@"</span> results/qc/host_removal/<span class="hljs-variable">${sample}</span>_clean_nonhost_R1.fastq)
    host_reads=$((original_reads - remaining_reads))
    host_percentage=$(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$host_reads</span> * 100 / <span class="hljs-variable">$original_reads</span>"</span> | bc)
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"  样本 <span class="hljs-variable">$sample</span> 宿主DNA去除统计:"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"    原始reads: <span class="hljs-variable">$original_reads</span>"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"    宿主reads: <span class="hljs-variable">$host_reads</span> (<span class="hljs-variable">${host_percentage}</span>%)"</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"    保留reads: <span class="hljs-variable">$remaining_reads</span>"</span>
    
    <span class="hljs-comment"># 保存统计信息</span>
    cat &gt; results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_removal_stats.txt &lt;&lt; EOF
样本: <span class="hljs-variable">$sample</span>
原始reads数: <span class="hljs-variable">$original_reads</span>
宿主reads数: <span class="hljs-variable">$host_reads</span>
宿主比例: <span class="hljs-variable">${host_percentage}</span>%
保留reads数: <span class="hljs-variable">$remaining_reads</span>
保留比例: $(<span class="hljs-built_in">echo</span> <span class="hljs-string">"scale=2; <span class="hljs-variable">$remaining_reads</span> * 100 / <span class="hljs-variable">$original_reads</span>"</span> | bc)%
EOF
    
    <span class="hljs-comment"># 清理中间文件</span>
    rm results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.sam
    rm results/qc/host_removal/<span class="hljs-variable">${sample}</span>_host_aligned.bam
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">$sample</span> 宿主DNA去除完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"=== 宿主DNA去除处理完成 ==="</span>
EOF

chmod +x scripts/remove_host_dna.sh
./scripts/remove_host_dna.sh
</div></code></pre>
<hr>
<h2 id="%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86metaphlankraken%E8%BF%9B%E8%A1%8C%E7%89%A9%E7%A7%8D%E5%88%86%E7%B1%BB%E5%88%86%E6%9E%90">第二部分：MetaPhlAn/Kraken进行物种分类分析</h2>
<h3 id="21-metaphlan3%E7%89%A9%E7%A7%8D%E5%88%86%E7%B1%BB%E5%AE%9E%E8%B7%B5">2.1 MetaPhlAn3物种分类实践</h3>
<h4 id="211-metaphlan3%E6%95%B0%E6%8D%AE%E5%BA%93%E5%87%86%E5%A4%87%E5%92%8C%E5%9F%BA%E7%A1%80%E5%88%86%E6%9E%90">2.1.1 MetaPhlAn3数据库准备和基础分析</h4>
<p><strong>实验目标：</strong> 学会使用MetaPhlAn3进行宏基因组物种分类，理解标记基因方法的原理</p>
<pre class="hljs"><code><div><span class="hljs-built_in">echo</span> <span class="hljs-string">"=== MetaPhlAn3物种分类分析 ==="</span>

<span class="hljs-comment"># 1. 创建分析目录</span>
mkdir -p results/taxonomy/metaphlan/{profiles,merged,plots}

<span class="hljs-comment"># 2. 安装和配置MetaPhlAn3数据库</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"配置MetaPhlAn3数据库..."</span>
metaphlan --install --bowtie2db data/databases/metaphlan_db

<span class="hljs-comment"># 3. 运行MetaPhlAn3分析</span>
<span class="hljs-built_in">echo</span> <span class="hljs-string">"运行MetaPhlAn3物种分类..."</span>
<span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> healthy disease; <span class="hljs-keyword">do</span>
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"分析样本: <span class="hljs-variable">$sample</span>"</span>
    
    <span class="hljs-comment"># 合并双端测序数据用于MetaPhlAn分析</span>
    cat results/qc/host_removal/<span class="hljs-variable">${sample}</span>_clean_nonhost_R1.fastq \
        results/qc/host_removal/<span class="hljs-variable">${sample}</span>_clean_nonhost_R2.fastq &gt; \
        results/qc/host_removal/<span class="hljs-variable">${sample}</span>_merged_for_metaphlan.fastq
    
    <span class="hljs-comment"># 运行MetaPhlAn3</span>
    metaphlan results/qc/host_removal/<span class="hljs-variable">${sample}</span>_merged_for_metaphlan.fastq \
        --input_type fastq \
        --bowtie2out results/taxonomy/metaphlan/<span class="hljs-variable">${sample}</span>_bowtie2.txt \
        --nproc 4 \
        --bowtie2db data/databases/metaphlan_db \
        -o results/taxonomy/metaphlan/profiles/<span class="hljs-variable">${sample}</span>_profile.txt
    
    <span class="hljs-built_in">echo</span> <span class="hljs-string">"✓ <span class="hljs-variable">$sample</span> MetaPhlAn3分析完成"</span>
<span class="hljs-keyword">done</span>

<span class="hljs-built_in">echo</span> <span class="hljs-string">"MetaPhlAn3分析完成！"</span>
</div></code></pre>
<h4 id="212-metaphlan%E7%BB%93%E6%9E%9C%E5%88%86%E6%9E%90%E5%92%8C%E5%8F%AF%E8%A7%86%E5%8C%96">2.1.2 MetaPhlAn结果分析和可视化</h4>
<pre class="hljs"><code><div><span class="hljs-comment"># 创建MetaPhlAn结果分析脚本</span>
cat &gt; scripts/analyze_metaphlan_results.py &lt;&lt; <span class="hljs-string">'EOF'</span>
<span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-string">""</span><span class="hljs-string">"
MetaPhlAn结果分析和可视化脚本
"</span><span class="hljs-string">""</span>

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

class MetaPhlAnAnalyzer:
    def __init__(self):
        self.profiles_dir = <span class="hljs-string">"results/taxonomy/metaphlan/profiles"</span>
        self.output_dir = <span class="hljs-string">"results/taxonomy/metaphlan"</span>
        
    def merge_profiles(self):
        <span class="hljs-string">""</span><span class="hljs-string">"合并所有样本的MetaPhlAn分类结果"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"合并MetaPhlAn分类结果..."</span>)
        
        <span class="hljs-comment"># 收集所有分类文件</span>
        profile_files = []
        samples = []
        
        <span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> [<span class="hljs-string">'healthy'</span>, <span class="hljs-string">'disease'</span>]:
            profile_file = f<span class="hljs-string">"{self.profiles_dir}/{sample}_profile.txt"</span>
            <span class="hljs-keyword">if</span> os.path.exists(profile_file):
                profile_files.append(profile_file)
                samples.append(sample)
        
        <span class="hljs-keyword">if</span> not profile_files:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"未找到MetaPhlAn分类结果文件"</span>)
            <span class="hljs-built_in">return</span> None
        
        <span class="hljs-comment"># 合并分类结果</span>
        cmd = f<span class="hljs-string">"merge_metaphlan_tables.py {' '.join(profile_files)} &gt; {self.output_dir}/merged/merged_abundance_table.txt"</span>
        os.system(cmd)
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"合并结果已保存到: {self.output_dir}/merged/merged_abundance_table.txt"</span>)
        <span class="hljs-built_in">return</span> f<span class="hljs-string">"{self.output_dir}/merged/merged_abundance_table.txt"</span>
    
    def analyze_taxonomic_composition(self):
        <span class="hljs-string">""</span><span class="hljs-string">"分析分类组成"</span><span class="hljs-string">""</span>
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"分析分类组成..."</span>)
        
        <span class="hljs-comment"># 读取分类结果</span>
        composition_data = {}
        
        <span class="hljs-keyword">for</span> sample <span class="hljs-keyword">in</span> [<span class="hljs-string">'healthy'</span>, <span class="hljs-string">'disease'</span>]:
            profile_file = f<span class="hljs-string">"{self.profiles_dir}/{sample}_profile.txt"</span>
            <span class="hljs-keyword">if</span> os.path.exists(profile_file):
                composition_data[sample] = self._parse_metaphlan_profile(profile_file)
        
        <span class="hljs-keyword">if</span> not composition_data:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">"没有可用的分类数据"</span>)
            <span class="hljs-built_in">return</span>
        
        <span class="hljs-comment"># 分析门水平组成</span>
        phylum_composition = self._analyze_phylum_level(composition_data)
        
        <span class="hljs-comment"># 分析属水平组成</span>
        genus_composition = self._analyze_genus_level(composition_data)
        
        <span class="hljs-comment"># 生成可视化图表</span>
        self._plot_taxonomic_composition(phylum_composition, genus_composition)
        
        <span class="hljs-comment"># 保存分析结果</span>
        self._save_composition_analysis(phylum_composition, genus_composition)
    
    def _parse_metaphlan_profile(self, profile_file):
        <span class="hljs-string">""</span><span class="hljs-string">"解析MetaPhlAn分类文件"</span><span class="hljs-string">""</span>
        composition = {
            <span class="hljs-string">'phylum'</span>: {},
            <span class="hljs-string">'genus'</span>: {},
            <span class="hljs-string">'species'</span>: {}
        }
        
        try:
            with open(profile_file, <span class="hljs-string">'r'</span>) as f:
                <span class="hljs-keyword">for</span> line <span class="hljs-keyword">in</span> f:
                    <span class="hljs-keyword">if</span> line.startswith(<span class="hljs-string">'#'</span>) or not line.strip():
                        <span class="hljs-built_in">continue</span>
                    
                    parts = line.strip().split(<span class="hljs-string">'\t'</span>)
                    <span class="hljs-keyword">if</span> len(parts) &gt;= 2:
                        taxonomy = parts[0]
                        abundance = <span class="hljs-built_in">float</span>(parts[1])
                        
                        <span class="hljs-comment"># 解析分类水平</span>
                        <span class="hljs-keyword">if</span> <span class="hljs-string">'|p__'</span> <span class="hljs-keyword">in</span> taxonomy and <span class="hljs-string">'|c__'</span> not <span class="hljs-keyword">in</span> taxonomy:
                            <span class="hljs-comment"># 门水平</span>
                            phylum = taxonomy.split(<span class="hljs-string">'|p__'</span>)[1]
                            composition[<span class="hljs-string">'phylum'</span>][phylum] = abundance
                        <span class="hljs-keyword">elif</span> <span class="hljs-string">'|g__'</span> <span class="hljs-keyword">in</span> taxonomy and <span class="hljs-string">'|s__'</span> not <span class="hljs-keyword">in</span> taxonomy:
                            <span class="hljs-comment"># 属水平</span>
                            genus = taxonomy.split(<span class="hljs-string">'|g__'</span>)[1]
                            composition[<span class="hljs-string">'genus'</span>][genus] = abundance
                        <span class="hljs-keyword">elif</span> <span class="hljs-string">'|s__'</span> <span class="hljs-keyword">in</span> taxonomy:
                            <span class="hljs-comment"># 种水平</span>
                            species = taxonomy.split(<span class="hljs-string">'|s__'</span>)[1]
                            composition[<span class="hljs-string">'species'</span>][species] = abundance
        
        except FileNotFoundError:
            <span class="hljs-built_in">print</span>(f<span class="hljs-string">"文件不存在: {profile_file}"</span>)
        
        <span class="hljs-built_in">return</span> composition
    
    def _analyze_phylum_level(self, composition_data):
        <span class="hljs-string">""</span><span class="hljs-string">"分析门水平组成"</span><span class="hljs-string">""</span>
        phylum_df = pd.DataFrame()
        
        <span class="hljs-keyword">for</span> sample, data <span class="hljs-keyword">in</span> composition_data.items():
            sample_data = pd.Series(data[<span class="hljs-string">'phylum'</span>], name=sample)
            phylum_df = pd.concat([phylum_df, sample_data], axis=1)
        
        phylum_df = phylum_df.fillna(0)
        <span class="hljs-built_in">return</span> phylum_df
    
    def _analyze_genus_level(self, composition_data):
        <span class="hljs-string">""</span><span class="hljs-string">"分析属水平组成"</span><span class="hljs-string">""</span>
        genus_df = pd.DataFrame()
        
        <span class="hljs-keyword">for</span> sample, data <span class="hljs-keyword">in</span> composition_data.items():
            sample_data = pd.Series(data[<span class="hljs-string">'genus'</span>], name=sample)
            genus_df = pd.concat([genus_df, sample_data], axis=1)
        
        genus_df = genus_df.fillna(0)
        <span class="hljs-built_in">return</span> genus_df
    
    def _plot_taxonomic_composition(self, phylum_df, genus_df):
        <span class="hljs-string">""</span><span class="hljs-string">"绘制分类组成图表"</span><span class="hljs-string">""</span>
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        <span class="hljs-comment"># 门水平堆叠柱状图</span>
        <span class="hljs-keyword">if</span> not phylum_df.empty:
            phylum_df.T.plot(kind=<span class="hljs-string">'bar'</span>, stacked=True, ax=axes[0,0], colormap=<span class="hljs-string">'Set3'</span>)
            axes[0,0].set_title(<span class="hljs-string">'门水平分类组成'</span>)
            axes[0,0].set_ylabel(<span class="hljs-string">'相对丰度 (%)'</span>)
            axes[0,0].legend(bbox_to_anchor=(1.05, 1), loc=<span class="hljs-string">'upper left'</span>)
            axes[0,0].tick_params(axis=<span class="hljs-string">'x'</span>, rotation=45)
        
        <span class="hljs-comment"># 门水平饼图（健康样本）</span>
        <span class="hljs-keyword">if</span> not phylum_df.empty and <span class="hljs-string">'healthy'</span> <span class="hljs-keyword">in</span> phylum_df.columns:
            healthy_phylum = phylum_df[<span class="hljs-string">'healthy'</span>][phylum_df[<span class="hljs-string">'healthy'</span>] &gt; 0]
            <span class="hljs-keyword">if</span> not healthy_phylum.empty:
                axes[0,1].pie(healthy_phylum.values, labels=healthy_phylum.index, autopct=<span class="hljs-string">'%1.1f%%'</span>)
                axes[0,1].set_title(<span class="hljs-string">'健康样本门水平组成'</span>)
        
        <span class="hljs-comment"># 属水平top10（健康样本）</span>
        <span class="hljs-keyword">if</span> not genus_df.empty and <span class="hljs-string">'healthy'</span> <span class="hljs-keyword">in</span> genus_df.columns:
            healthy_genus = genus_df[<span class="hljs-string">'healthy'</span>].sort_values(ascending=False).head(10)
            <span class="hljs-keyword">if</span> not healthy_genus.empty:
                healthy_genus.plot(kind=<span class="hljs-string">'bar'</span>, ax=axes[1,0])
                axes[1,0].set_title(<span class="hljs-string">'健康样本top10属'</span>)
                axes[1,0].set_ylabel(<span class="hljs-string">'相对丰度 (%)'</span>)
                axes[1,0].tick_params(axis=<span class="hljs-string">'x'</span>, rotation=45)
        
        <span class="hljs-comment"># 属水平top10（疾病样本）</span>
        <span class="hljs-keyword">if</span> not genus_df.empty and <span class="hljs-string">'disease'</span> <span class="hljs-keyword">in</span> genus_df.columns:
            disease_genus = genus_df[<span class="hljs-string">'disease'</span>].sort_values(ascending=False).head(10)
            <span class="hljs-keyword">if</span> not disease_genus.empty:
                disease_genus.plot(kind=<span class="hljs-string">'bar'</span>, ax=axes[1,1], color=<span class="hljs-string">'red'</span>, alpha=0.7)
                axes[1,1].set_title(<span class="hljs-string">'疾病样本top10属'</span>)
                axes[1,1].set_ylabel(<span class="hljs-string">'相对丰度 (%)'</span>)
                axes[1,1].tick_params(axis=<span class="hljs-string">'x'</span>, rotation=45)
        
        plt.tight_layout()
        plt.savefig(f<span class="hljs-string">'{self.output_dir}/plots/taxonomic_composition.png'</span>, dpi=300, bbox_inches=<span class="hljs-string">'tight'</span>)
        plt.close()
        
        <span class="hljs-built_in">print</span>(f<span class="hljs-string">"分类组成图表已保存到: {self.output_dir}/plots/taxonomic_composition.png"</span>)
    
    def _save_composition_analysis(self, phylum_df, genus_df):
        <span class="hljs-string">""</span><span class="hljs-string">"保存组成分析结果"</span><span class="hljs-string">""</span>
        <span class="hljs-keyword">if</span> not phylum_df.empty:
            phylum_df.to_csv(f<span class="hljs-string">'{self.output_dir}/merged/phylum_composition.csv'</span>)
        
        <span class="hljs-keyword">if</span> not genus_df.empty:
            genus_df.to_csv(f<span class="hljs-string">'{self.output_dir}/merged/genus_composition.csv'</span>)
        
        <span class="hljs-built_in">print</span>(<span class="hljs-string">"组成分析结果已保存"</span>)

def main():
    analyzer = MetaPhlAnAnalyzer()
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== MetaPhlAn结果分析 ==="</span>)
    
    <span class="hljs-comment"># 合并分类结果</span>
    merged_file = analyzer.merge_profiles()
    
    <span class="hljs-comment"># 分析分类组成</span>
    analyzer.analyze_taxonomic_composition()
    
    <span class="hljs-built_in">print</span>(<span class="hljs-string">"=== MetaPhlAn结果分析完成 ==="</span>)

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">"__main__"</span>:
    main()
EOF

python3 scripts/analyze_metaphlan_results.py
</div></code></pre>

</body>
</html>
