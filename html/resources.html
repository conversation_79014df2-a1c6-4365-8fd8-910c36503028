<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习资源 - NGS高通量测序技术课程平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: #667eea;
        }

        /* 主要内容 */
        .main-content {
            padding: 3rem 0;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card h2 {
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-card h2 i {
            color: #667eea;
            font-size: 1.2rem;
        }

        /* 讲义下载区域 */
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .download-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .download-item:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
        }

        .download-item i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .download-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .download-item p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .download-item .file-size {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
            padding: 0.2rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6de8, #6a4495);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            border: 2px solid #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .btn-tertiary {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            border: 2px solid #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-tertiary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .resource-list {
            list-style: none;
            margin-top: 1.5rem;
        }

        .resource-list li {
            background: rgba(102, 126, 234, 0.1);
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .resource-list li strong {
            color: #667eea;
        }

        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .resource-category {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .resource-category h4 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .resource-category ul {
            list-style: none;
            padding-left: 0;
        }

        .resource-category li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .resource-category li::before {
            content: '🔗';
            position: absolute;
            left: 0;
        }

        .resource-category a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .resource-category a:hover {
            text-decoration: underline;
        }

        /* 统计信息 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }

        .stat-item i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .stat-item .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin-bottom: 2rem;
            color: white;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .content-card {
                padding: 1.5rem;
            }

            .download-grid,
            .resource-grid,
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-dna"></i>
                    NGS课程平台
                </a>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="index.html#courses"><i class="fas fa-book"></i> 课程</a></li>
                    <li><a href="about.html"><i class="fas fa-info-circle"></i> 关于</a></li>
                    <li><a href="syllabus.html"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                    <li><a href="resources.html" class="active"><i class="fas fa-download"></i> 资源</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html">首页</a> / 学习资源
            </div>

            <!-- 页面头部 -->
            <div class="page-header">
                <h1><i class="fas fa-download"></i> 学习资源中心</h1>
                <p>丰富的学习资料，助力你的NGS技术学习之旅</p>
            </div>

            <!-- PDF讲义下载 -->
            <div class="content-card">
                <h2><i class="fas fa-file-pdf"></i> 课程讲义下载</h2>
                <p>所有专题的PDF格式讲义，支持离线学习和打印。</p>
                
                <div class="download-grid">
                    <div class="download-item">
                        <i class="fas fa-rocket"></i>
                        <h4>专题一讲义</h4>
                        <p>高通量测序技术概论</p>
                        <div class="file-size">1.7MB</div>
                        <a href="note_pdf/Lecture1_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                
                    </div>
                    <div class="download-item">
                        <i class="fas fa-shield-alt"></i>
                        <h4>专题二讲义</h4>
                        <p>数据质量控制与预处理</p>
                        <div class="file-size">1.1MB</div>
                        <a href="note_pdf/Lecture2_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-dna"></i>
                        <h4>专题三讲义</h4>
                        <p>基因组测序数据分析</p>
                        <div class="file-size">2.1MB</div>
                        <a href="note_pdf/Lecture3_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-project-diagram"></i>
                        <h4>专题四讲义</h4>
                        <p>转录组测序数据分析</p>
                        <div class="file-size">2.0MB</div>
                        <a href="note_pdf/Lecture4_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-fingerprint"></i>
                        <h4>专题五讲义</h4>
                        <p>表观基因组测序分析</p>
                        <div class="file-size">1.8MB</div>
                        <a href="note_pdf/Lecture5_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-atom"></i>
                        <h4>专题六讲义</h4>
                        <p>单细胞测序技术分析</p>
                        <div class="file-size">1.1MB</div>
                        <a href="note_pdf/Lecture6_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-bacteria"></i>
                        <h4>专题七讲义</h4>
                        <p>宏基因组测序分析</p>
                        <div class="file-size">1.9MB</div>
                        <a href="note_pdf/Lecture7_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                    <div class="download-item">
                        <i class="fas fa-leaf"></i>
                        <h4>专题八讲义</h4>
                        <p>植物保护中的应用</p>
                        <div class="file-size">1.7MB</div>
                        <a href="note_pdf/Lecture8_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        
                    </div>
                </div>
            </div>

            <!-- 在线资源 -->
            <div class="content-card">
                <h2><i class="fas fa-globe"></i> 在线学习资源</h2>
                <div class="resource-grid">
                    <div class="resource-category">
                        <h4><i class="fas fa-database"></i> 公共数据库</h4>
                        <ul>
                            <li><a href="https://www.ncbi.nlm.nih.gov/sra" target="_blank">NCBI SRA</a> - 测序数据存档</li>
                            <li><a href="https://www.ebi.ac.uk/ena" target="_blank">ENA</a> - 欧洲核酸数据库</li>
                            <li><a href="https://www.ncbi.nlm.nih.gov/geo/" target="_blank">GEO</a> - 基因表达数据库</li>
                            <li><a href="https://www.ncbi.nlm.nih.gov/genbank/" target="_blank">GenBank</a> - 基因序列数据库</li>
                            <li><a href="https://www.ensembl.org/" target="_blank">Ensembl</a> - 基因组浏览器</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-laptop-code"></i> 分析平台</h4>
                        <ul>
                            <li><a href="https://bioconductor.org/" target="_blank">Bioconductor</a> - R生物信息学包</li>
                            <li><a href="https://galaxy.genome.au/" target="_blank">Galaxy</a> - 在线分析平台</li>
                            <li><a href="https://www.docker.com/" target="_blank">Docker</a> - 容器化平台</li>
                            <li><a href="https://conda.io/" target="_blank">Conda</a> - 包管理工具</li>
                            <li><a href="https://jupyter.org/" target="_blank">Jupyter</a> - 交互式笔记本</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-tools"></i> 分析工具</h4>
                        <ul>
                            <li><a href="https://github.com/lh3/bwa" target="_blank">BWA</a> - 序列比对工具</li>
                            <li><a href="https://gatk.broadinstitute.org/" target="_blank">GATK</a> - 变异检测工具包</li>
                            <li><a href="https://bioconductor.org/packages/DESeq2/" target="_blank">DESeq2</a> - 差异表达分析</li>
                            <li><a href="https://satijalab.org/seurat/" target="_blank">Seurat</a> - 单细胞分析</li>
                            <li><a href="https://scanpy.readthedocs.io/" target="_blank">Scanpy</a> - 单细胞Python工具</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-chart-line"></i> 可视化工具</h4>
                        <ul>
                            <li><a href="http://software.broadinstitute.org/software/igv/" target="_blank">IGV</a> - 基因组浏览器</li>
                            <li><a href="https://www.rstudio.com/" target="_blank">RStudio</a> - R开发环境</li>
                            <li><a href="https://cytoscape.org/" target="_blank">Cytoscape</a> - 网络可视化</li>
                            <li><a href="https://plotly.com/" target="_blank">Plotly</a> - 交互式图表</li>
                            <li><a href="https://matplotlib.org/" target="_blank">Matplotlib</a> - Python绘图库</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 学习指南 -->
            <div class="content-card">
                <h2><i class="fas fa-book-open"></i> 学习指南与教程</h2>
                <ul class="resource-list">
                    <li>
                        <strong>Linux基础：</strong> 
                        掌握命令行操作是生物信息学分析的基础。推荐学习bash shell、文件操作、文本处理等基本技能。
                    </li>
                    <li>
                        <strong>R语言编程：</strong> 
                        R是生物统计分析的主要语言。建议从基础语法开始，逐步学习数据处理、统计分析和可视化。
                    </li>
                    <li>
                        <strong>Python编程：</strong> 
                        Python在生物信息学流程开发中应用广泛。重点学习BioPython、pandas、numpy等库。
                    </li>
                    <li>
                        <strong>数据格式：</strong> 
                        熟悉FASTQ、SAM/BAM、VCF、GTF/GFF等常用生物信息学数据格式的结构和处理方法。
                    </li>
                    <li>
                        <strong>流程管理：</strong> 
                        学习使用Snakemake、Nextflow等工具构建可重现的分析流程。
                    </li>
                </ul>
            </div>

            <!-- 参考书籍 -->
            <div class="content-card">
                <h2><i class="fas fa-book"></i> 推荐参考书籍</h2>
                <div class="resource-grid">
                    <div class="resource-category">
                        <h4><i class="fas fa-graduation-cap"></i> 入门教材</h4>
                        <ul>
                            <li>《Bioinformatics Data Skills》 - Vince Buffalo</li>
                            <li>《Computational Genome Analysis》 - Richard C. Deonier</li>
                            <li>《生物信息学基础教程》 - 陈铭</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-microscope"></i> 专业进阶</h4>
                        <ul>
                            <li>《RNA-seq Data Analysis》 - Eija Korpelainen</li>
                            <li>《Single Cell Methods》 - Methods in Molecular Biology</li>
                            <li>《Plant Pathogen Genomics》 - Methods and Protocols</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="content-card">
                <h2><i class="fas fa-compass"></i> 快速导航</h2>
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.html" class="btn-primary">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <a href="about.html" class="btn-primary">
                        <i class="fas fa-info-circle"></i> 课程介绍
                    </a>
                    <a href="syllabus.html" class="btn-primary">
                        <i class="fas fa-graduation-cap"></i> 教学大纲
                    </a>
                    <a href="index.html#courses" class="btn-primary">
                        <i class="fas fa-book"></i> 开始学习
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 下载项悬停效果
        const downloadItems = document.querySelectorAll('.download-item');
        downloadItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习资源 - NGS高通量测序技术课程平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: #667eea;
        }

        /* 主要内容 */
        .main-content {
            padding: 3rem 0;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card h2 {
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-card h2 i {
            color: #667eea;
            font-size: 1.2rem;
        }

        /* 讲义下载区域 */
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .download-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .download-item:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
        }

        .download-item i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .download-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .download-item p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .download-item .file-size {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
            padding: 0.2rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6de8, #6a4495);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            border: 2px solid #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .btn-tertiary {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            border: 2px solid #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-tertiary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .resource-list {
            list-style: none;
            margin-top: 1.5rem;
        }

        .resource-list li {
            background: rgba(102, 126, 234, 0.1);
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .resource-list li strong {
            color: #667eea;
        }

        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .resource-category {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .resource-category h4 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .resource-category ul {
            list-style: none;
            padding-left: 0;
        }

        .resource-category li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .resource-category li::before {
            content: '🔗';
            position: absolute;
            left: 0;
        }

        .resource-category a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .resource-category a:hover {
            text-decoration: underline;
        }

        /* 统计信息 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }

        .stat-item i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .stat-item .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin-bottom: 2rem;
            color: white;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .content-card {
                padding: 1.5rem;
            }

            .download-grid,
            .resource-grid,
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-dna"></i>
                    NGS课程平台
                </a>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="index.html#courses"><i class="fas fa-book"></i> 课程</a></li>
                    <li><a href="about.html"><i class="fas fa-info-circle"></i> 关于</a></li>
                    <li><a href="syllabus.html"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                    <li><a href="resources.html" class="active"><i class="fas fa-download"></i> 资源</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html">首页</a> / 学习资源
            </div>

            <!-- 页面头部 -->
            <div class="page-header">
                <h1><i class="fas fa-download"></i> 学习资源中心</h1>
                <p>丰富的学习资料，助力你的NGS技术学习之旅</p>
            </div>

            <!-- 资源统计 -->
            <div class="content-card">
                <h2><i class="fas fa-chart-bar"></i> 资源概览</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <i class="fas fa-file-pdf"></i>
                        <div class="number">8</div>
                        <div>PDF讲义</div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-presentation"></i>
                        <div class="number">8</div>
                        <div>交互课件</div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-tools"></i>
                        <div class="number">20+</div>
                        <div>分析工具</div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-database"></i>
                        <div class="number">10+</div>
                        <div>数据库资源</div>
                    </div>
                </div>
            </div>

            <!-- PDF讲义下载 -->
            <div class="content-card">
                <h2><i class="fas fa-file-pdf"></i> 课程讲义下载</h2>
                <p>所有专题的PDF格式讲义，支持离线学习和打印。</p>
                
                <div class="download-grid">
                    <div class="download-item">
                        <i class="fas fa-rocket"></i>
                        <h4>专题一讲义</h4>
                        <p>高通量测序技术概论</p>
                        <div class="file-size">1.7MB</div>
                        <a href="note_pdf/Lecture1_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture1_NGS_Technology_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                        <a href="lab_html/Lecture1_lab.html" class="btn-tertiary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-shield-alt"></i>
                        <h4>专题二讲义</h4>
                        <p>数据质量控制与预处理</p>
                        <div class="file-size">1.1MB</div>
                        <a href="note_pdf/Lecture2_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture2_QC_Preprocessing_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                        <a href="lab_html/Lecture2_lab.html" class="btn-tertiary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-dna"></i>
                        <h4>专题三讲义</h4>
                        <p>基因组测序数据分析</p>
                        <div class="file-size">2.1MB</div>
                        <a href="note_pdf/Lecture3_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture3_DNA_seq_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                        <a href="lab_html/Lecture3_lab.html" class="btn-tertiary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-project-diagram"></i>
                        <h4>专题四讲义</h4>
                        <p>转录组测序数据分析</p>
                        <div class="file-size">2.0MB</div>
                        <a href="note_pdf/Lecture4_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture4_RNA_seq_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                        <a href="lab_html/Lecture4_lab.html" class="btn-tertiary" target="_blank">
                            <i class="fas fa-flask"></i> 实践指导
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-atom"></i>
                        <h4>专题六讲义</h4>
                        <p>单细胞测序技术分析</p>
                        <div class="file-size">1.1MB</div>
                        <a href="note_pdf/Lecture6_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture6_SingleCell_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-bacteria"></i>
                        <h4>专题七讲义</h4>
                        <p>宏基因组测序分析</p>
                        <div class="file-size">1.9MB</div>
                        <a href="note_pdf/Lecture7_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture7_Metagenomics_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                    </div>
                    <div class="download-item">
                        <i class="fas fa-leaf"></i>
                        <h4>专题八讲义</h4>
                        <p>植物保护中的应用</p>
                        <div class="file-size">1.7MB</div>
                        <a href="note_pdf/Lecture8_note.pdf" class="btn-primary" target="_blank">
                            <i class="fas fa-download"></i> 下载PDF
                        </a>
                        <a href="Lecture8_Plant_Protection_Interactive_Slides.html" class="btn-secondary">
                            <i class="fas fa-play"></i> 交互课件
                        </a>
                    </div>
                </div>
            </div>

            <!-- 在线资源 -->
            <div class="content-card">
                <h2><i class="fas fa-globe"></i> 在线学习资源</h2>
                <div class="resource-grid">
                    <div class="resource-category">
                        <h4><i class="fas fa-database"></i> 公共数据库</h4>
                        <ul>
                            <li><a href="https://www.ncbi.nlm.nih.gov/sra" target="_blank">NCBI SRA</a> - 测序数据存档</li>
                            <li><a href="https://www.ebi.ac.uk/ena" target="_blank">ENA</a> - 欧洲核酸数据库</li>
                            <li><a href="https://www.ncbi.nlm.nih.gov/geo/" target="_blank">GEO</a> - 基因表达数据库</li>
                            <li><a href="https://www.ncbi.nlm.nih.gov/genbank/" target="_blank">GenBank</a> - 基因序列数据库</li>
                            <li><a href="https://www.ensembl.org/" target="_blank">Ensembl</a> - 基因组浏览器</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-laptop-code"></i> 分析平台</h4>
                        <ul>
                            <li><a href="https://bioconductor.org/" target="_blank">Bioconductor</a> - R生物信息学包</li>
                            <li><a href="https://galaxy.genome.au/" target="_blank">Galaxy</a> - 在线分析平台</li>
                            <li><a href="https://www.docker.com/" target="_blank">Docker</a> - 容器化平台</li>
                            <li><a href="https://conda.io/" target="_blank">Conda</a> - 包管理工具</li>
                            <li><a href="https://jupyter.org/" target="_blank">Jupyter</a> - 交互式笔记本</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-tools"></i> 分析工具</h4>
                        <ul>
                            <li><a href="https://github.com/lh3/bwa" target="_blank">BWA</a> - 序列比对工具</li>
                            <li><a href="https://gatk.broadinstitute.org/" target="_blank">GATK</a> - 变异检测工具包</li>
                            <li><a href="https://bioconductor.org/packages/DESeq2/" target="_blank">DESeq2</a> - 差异表达分析</li>
                            <li><a href="https://satijalab.org/seurat/" target="_blank">Seurat</a> - 单细胞分析</li>
                            <li><a href="https://scanpy.readthedocs.io/" target="_blank">Scanpy</a> - 单细胞Python工具</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-chart-line"></i> 可视化工具</h4>
                        <ul>
                            <li><a href="http://software.broadinstitute.org/software/igv/" target="_blank">IGV</a> - 基因组浏览器</li>
                            <li><a href="https://www.rstudio.com/" target="_blank">RStudio</a> - R开发环境</li>
                            <li><a href="https://cytoscape.org/" target="_blank">Cytoscape</a> - 网络可视化</li>
                            <li><a href="https://plotly.com/" target="_blank">Plotly</a> - 交互式图表</li>
                            <li><a href="https://matplotlib.org/" target="_blank">Matplotlib</a> - Python绘图库</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 学习指南 -->
            <div class="content-card">
                <h2><i class="fas fa-book-open"></i> 学习指南与教程</h2>
                <ul class="resource-list">
                    <li>
                        <strong>Linux基础：</strong> 
                        掌握命令行操作是生物信息学分析的基础。推荐学习bash shell、文件操作、文本处理等基本技能。
                    </li>
                    <li>
                        <strong>R语言编程：</strong> 
                        R是生物统计分析的主要语言。建议从基础语法开始，逐步学习数据处理、统计分析和可视化。
                    </li>
                    <li>
                        <strong>Python编程：</strong> 
                        Python在生物信息学流程开发中应用广泛。重点学习BioPython、pandas、numpy等库。
                    </li>
                    <li>
                        <strong>数据格式：</strong> 
                        熟悉FASTQ、SAM/BAM、VCF、GTF/GFF等常用生物信息学数据格式的结构和处理方法。
                    </li>
                    <li>
                        <strong>流程管理：</strong> 
                        学习使用Snakemake、Nextflow等工具构建可重现的分析流程。
                    </li>
                </ul>
            </div>

            <!-- 参考书籍 -->
            <div class="content-card">
                <h2><i class="fas fa-book"></i> 推荐参考书籍</h2>
                <div class="resource-grid">
                    <div class="resource-category">
                        <h4><i class="fas fa-graduation-cap"></i> 入门教材</h4>
                        <ul>
                            <li>《Bioinformatics Data Skills》 - Vince Buffalo</li>
                            <li>《Computational Genome Analysis》 - Richard C. Deonier</li>
                            <li>《生物信息学基础教程》 - 陈铭</li>
                        </ul>
                    </div>
                    <div class="resource-category">
                        <h4><i class="fas fa-microscope"></i> 专业进阶</h4>
                        <ul>
                            <li>《RNA-seq Data Analysis》 - Eija Korpelainen</li>
                            <li>《Single Cell Methods》 - Methods in Molecular Biology</li>
                            <li>《Plant Pathogen Genomics》 - Methods and Protocols</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="content-card">
                <h2><i class="fas fa-compass"></i> 快速导航</h2>
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.html" class="btn-primary">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <a href="about.html" class="btn-primary">
                        <i class="fas fa-info-circle"></i> 课程介绍
                    </a>
                    <a href="syllabus.html" class="btn-primary">
                        <i class="fas fa-graduation-cap"></i> 教学大纲
                    </a>
                    <a href="index.html#courses" class="btn-primary">
                        <i class="fas fa-book"></i> 开始学习
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 下载项悬停效果
        const downloadItems = document.querySelectorAll('.download-item');
        downloadItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html> 