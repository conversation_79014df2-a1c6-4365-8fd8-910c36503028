<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题一：高通量测序技术原理与发展 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px; /* 为两个导航栏留出空间 */
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #764ba2;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        h3 {
            color: #667eea;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #764ba2;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .tech-box {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            border-left: 5px solid #764ba2;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .comparison-table {
            margin: 20px 0;
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .comparison-table tr:hover {
            background: #e3f2fd;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .timeline {
            position: relative;
            margin: 30px 0;
        }

        .timeline-item {
            display: flex;
            margin: 20px 0;
            align-items: center;
        }

        .timeline-year {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            min-width: 80px;
            text-align: center;
            margin-right: 20px;
        }

        .timeline-content {
            flex: 1;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .platform-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .platform-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        }

        .platform-card h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .tech-specs {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
        }

        .tech-specs ul {
            list-style: none;
            padding: 0;
        }

        .tech-specs li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            color: #2c3e50;
            font-weight: 500;
        }

        .tech-specs li:last-child {
            border-bottom: none;
        }

        .tech-specs strong {
            color: #667eea;
            font-weight: 600;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 技术示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .slide-counter {
            position: fixed;
            top: 70px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: 600;
            color: #667eea;
            z-index: 1000;
            border: 2px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .slide-menu {
            position: fixed;
            top: 70px;
            left: 20px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(255,255,255,0.95);
            border: 2px solid #667eea;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 18px;
            color: #667eea;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-btn:hover {
            background: #667eea;
            color: white;
        }

        .menu-dropdown {
            display: none;
            position: absolute;
            top: 50px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            min-width: 300px;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.show {
            display: block;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
            color: #2c3e50;
        }

        .menu-item:hover {
            background: #f0f0f0;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        /* 主导航栏样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 1rem 20px;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* 专题导航栏 */
        .topic-navbar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 10px 20px;
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            z-index: 999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .topic-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-nav {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 6px 10px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85em;
            white-space: nowrap;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .controls {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            color: #667eea;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        ul, ol {
            padding-left: 25px;
            margin: 15px 0;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
        }

        strong {
            color: #667eea;
            font-weight: 600;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 5px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .tech-box {
            background: #e8f5e8;
            border-left: 5px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .tech-box h4 {
            color: #2E7D32 !important;
            margin-bottom: 15px;
        }

        .tech-box ul li {
            color: #333 !important;
        }

        .comparison-table {
            margin: 20px 0;
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #FFFFFF !important;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            color: #333 !important;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .flowchart {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .flowchart-step {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-weight: 600;
            flex: 1;
            min-width: 150px;
            position: relative;
        }

        .flowchart-step:not(:last-child)::after {
            content: "→";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #667eea;
        }

        /* 原理图样式 */
        .principle-diagram {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .dna-sequence {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .sequencing-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .step-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .molecule-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            border: 2px solid #667eea;
        }

        .base {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin: 0 5px;
            font-size: 1.2em;
        }

        .base-A { background: #e74c3c; }
        .base-T { background: #3498db; }
        .base-G { background: #f39c12; }
        .base-C { background: #27ae60; }

        .bond {
            width: 20px;
            height: 3px;
            background: #667eea;
            margin: 0 2px;
        }

        .flowcell-diagram {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #667eea;
            position: relative;
        }

        .flowcell-lane {
            height: 60px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border: 1px solid #667eea;
        }

        .cluster {
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
            100% { opacity: 0.6; transform: scale(1); }
        }

        .nanopore-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #667eea;
        }

        .membrane {
            width: 300px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            position: relative;
            margin: 20px 0;
        }

        .pore {
            width: 30px;
            height: 30px;
            background: #f39c12;
            border-radius: 50%;
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border: 3px solid #e67e22;
        }

        .dna-strand {
            width: 200px;
            height: 4px;
            background: #e74c3c;
            position: relative;
            margin: 10px 0;
            border-radius: 2px;
        }

        .current-signal {
            width: 100%;
            height: 60px;
            background: #2c3e50;
            border-radius: 5px;
            position: relative;
            margin: 20px 0;
            overflow: hidden;
        }

        .signal-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                #3498db 25%,
                #e74c3c 50%,
                #f39c12 75%,
                transparent 100%);
            animation: wave 3s infinite;
        }

        @keyframes wave {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .cost-chart {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #667eea;
            height: 300px;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 50px;
            left: 50px;
            width: calc(100% - 100px);
            height: calc(100% - 100px);
            border-left: 3px solid #2c3e50;
            border-bottom: 3px solid #2c3e50;
        }

        .cost-curve {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(231, 76, 60, 0.3) 0%,
                rgba(241, 196, 15, 0.3) 30%,
                rgba(46, 204, 113, 0.3) 60%,
                rgba(52, 152, 219, 0.3) 100%);
            clip-path: polygon(0% 5%, 10% 8%, 30% 15%, 50% 25%, 70% 40%, 90% 70%, 100% 95%, 100% 100%, 0% 100%);
        }

        .chart-labels {
            position: absolute;
            bottom: 10px;
            left: 50px;
            right: 50px;
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #2c3e50;
        }

        .y-labels {
            position: absolute;
            left: 10px;
            top: 50px;
            bottom: 50px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 0.9em;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .slide {
                width: 95%;
                padding: 20px;
                height: 90vh;
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.6em;
            }

            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .platform-grid {
                grid-template-columns: 1fr;
            }

            .flowchart {
                flex-direction: column;
            }

            .flowchart-step:not(:last-child)::after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }

            .molecule-diagram {
                flex-wrap: wrap;
            }

            .membrane {
                width: 250px;
            }

            .cost-chart {
                height: 250px;
            }
        }

        .tech-box strong {
            color: #2E7D32 !important;
        }

        .graphic-abstract {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border: 2px solid #e3f2fd;
        }

        .graphic-abstract svg {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="navbar">
        <div class="nav-content">
            <a href="index.html" class="logo">
                <i class="fas fa-dna"></i>
                NGS课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture1_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">16</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <div class="controls">
        <button class="control-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
    </div>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题一：高通量测序技术原理与发展",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>了解测序技术从第一代到第三代的发展历程及其关键技术突破，掌握主流高通量测序平台的核心原理、技术特点及优缺点，理解不同测序技术的适用场景，熟悉高通量测序实验设计的基本要素。</p>
                    </div>

                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">📊 高通量测序技术发展图形摘要</h3>
                        <svg width="100%" height="500" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="dnaGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="25%" style="stop-color:#3498db" />
                                    <stop offset="50%" style="stop-color:#f39c12" />
                                    <stop offset="75%" style="stop-color:#27ae60" />
                                    <stop offset="100%" style="stop-color:#e74c3c" />
                                </linearGradient>
                                <linearGradient id="techGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#667eea" />
                                    <stop offset="100%" style="stop-color:#764ba2" />
                                </linearGradient>
                                <!-- 箭头标记定义 -->
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="500" fill="url(#bgGradient)" rx="15"/>
                            
                            <!-- 核心技术突破 - 左上角 -->
                            <g transform="translate(30, 30)">
                                <rect x="0" y="0" width="180" height="90" fill="url(#techGradient)" rx="10" opacity="0.9"/>
                                <text x="90" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">核心技术突破</text>
                                <text x="15" y="40" fill="white" font-size="10">• 并行化 (Parallelization)</text>
                                <text x="15" y="55" fill="white" font-size="10">• 微型化 (Miniaturization)</text>
                                <text x="15" y="70" fill="white" font-size="10">• 自动化 (Automation)</text>
                                <text x="15" y="85" fill="white" font-size="10">• 信号放大技术</text>
                            </g>
                            
                            <!-- 应用领域 - 右上角 -->
                            <g transform="translate(790, 30)">
                                <rect x="0" y="0" width="180" height="90" fill="url(#techGradient)" rx="10" opacity="0.9"/>
                                <text x="90" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">主要应用领域</text>
                                <text x="15" y="40" fill="white" font-size="10">• 基因组学 & 转录组学</text>
                                <text x="15" y="55" fill="white" font-size="10">• 精准医学 & 个性化治疗</text>
                                <text x="15" y="70" fill="white" font-size="10">• 农业育种 & 环境监测</text>
                                <text x="15" y="85" fill="white" font-size="10">• 法医鉴定 & 食品安全</text>
                            </g>
                            
                            <!-- 成本下降曲线 - 中上部 -->
                            <g transform="translate(250, 30)">
                                <rect x="0" y="0" width="500" height="120" fill="white" rx="10" opacity="0.9" stroke="#667eea" stroke-width="2"/>
                                <text x="250" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">测序成本革命</text>
                                
                                <!-- 成本曲线 - 修正为下降趋势 -->
                                <path d="M30,40 Q150,45 250,70 Q350,90 470,100" stroke="#e74c3c" stroke-width="4" fill="none"/>
                                <circle cx="30" cy="40" r="4" fill="#e74c3c"/>
                                <circle cx="250" cy="70" r="4" fill="#e74c3c"/>
                                <circle cx="470" cy="100" r="4" fill="#e74c3c"/>
                                
                                <text x="30" y="55" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">$30亿</text>
                                <text x="250" y="85" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">$1000</text>
                                <text x="470" y="115" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">$100</text>
                                
                                <text x="80" y="115" text-anchor="middle" fill="#2c3e50" font-size="10">2003年</text>
                                <text x="250" y="115" text-anchor="middle" fill="#2c3e50" font-size="10">2014年</text>
                                <text x="420" y="115" text-anchor="middle" fill="#2c3e50" font-size="10">2024年</text>
                            </g>
                            
                            <!-- 时间轴 - 下部 -->
                            <line x1="80" y1="450" x2="920" y2="450" stroke="#667eea" stroke-width="4"/>
                            
                            <!-- 第一代测序 (1977) - 左下 -->
                            <g transform="translate(150, 350)">
                                <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                <text x="0" y="6" text-anchor="middle" fill="white" font-size="14" font-weight="bold">1st</text>
                                <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">1977年</text>
                                <text x="0" y="75" text-anchor="middle" fill="#2c3e50" font-size="11">Sanger测序</text>
                                
                                <!-- DNA双螺旋 - 调整位置 -->
                                <g transform="translate(-20, -100)">
                                    <path d="M0,0 Q20,15 40,0 Q20,-15 0,0" stroke="#e74c3c" stroke-width="3" fill="none"/>
                                    <path d="M0,15 Q20,30 40,15 Q20,0 0,15" stroke="#3498db" stroke-width="3" fill="none"/>
                                    <circle cx="8" cy="8" r="3" fill="#e74c3c"/>
                                    <circle cx="32" cy="8" r="3" fill="#3498db"/>
                                    <text x="20" y="50" text-anchor="middle" fill="#2c3e50" font-size="9">双螺旋结构</text>
                                </g>
                            </g>
                            
                            <!-- 第二代测序 (2005) - 中下 -->
                            <g transform="translate(400, 300)">
                                <circle cx="0" cy="0" r="40" fill="#3498db" opacity="0.9"/>
                                <text x="0" y="6" text-anchor="middle" fill="white" font-size="16" font-weight="bold">2nd</text>
                                <text x="0" y="110" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">2005年</text>
                                <text x="0" y="125" text-anchor="middle" fill="#2c3e50" font-size="11">NGS革命</text>
                                
                                <!-- 并行化示意 - 调整位置 -->
                                <g transform="translate(-35, -80)">
                                    <rect x="0" y="0" width="10" height="10" fill="#667eea" rx="2"/>
                                    <rect x="15" y="0" width="10" height="10" fill="#667eea" rx="2"/>
                                    <rect x="30" y="0" width="10" height="10" fill="#667eea" rx="2"/>
                                    <rect x="45" y="0" width="10" height="10" fill="#667eea" rx="2"/>
                                    <rect x="60" y="0" width="10" height="10" fill="#667eea" rx="2"/>
                                    
                                    <rect x="0" y="15" width="10" height="10" fill="#764ba2" rx="2"/>
                                    <rect x="15" y="15" width="10" height="10" fill="#764ba2" rx="2"/>
                                    <rect x="30" y="15" width="10" height="10" fill="#764ba2" rx="2"/>
                                    <rect x="45" y="15" width="10" height="10" fill="#764ba2" rx="2"/>
                                    <rect x="60" y="15" width="10" height="10" fill="#764ba2" rx="2"/>
                                    
                                    <text x="35" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">大规模并行</text>
                                </g>
                                
                                <!-- 通量箭头 -->
                                <path d="M-70,50 L70,50" stroke="#f39c12" stroke-width="4" marker-end="url(#arrowhead)"/>
                                <text x="0" y="70" text-anchor="middle" fill="#f39c12" font-size="12" font-weight="bold">通量↑ 成本↓</text>
                            </g>
                            
                            <!-- 第三代测序 (2011) - 右下 -->
                            <g transform="translate(750, 280)">
                                <circle cx="0" cy="0" r="45" fill="#27ae60" opacity="0.9"/>
                                <text x="0" y="6" text-anchor="middle" fill="white" font-size="18" font-weight="bold">3rd</text>
                                <text x="0" y="130" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">2011年</text>
                                <text x="0" y="145" text-anchor="middle" fill="#2c3e50" font-size="11">长读长时代</text>
                                
                                <!-- 长读长示意 - 调整位置 -->
                                <g transform="translate(-50, -100)">
                                    <rect x="0" y="0" width="100" height="5" fill="url(#dnaGradient)" rx="2"/>
                                    <rect x="0" y="10" width="80" height="5" fill="url(#dnaGradient)" rx="2"/>
                                    <rect x="0" y="20" width="90" height="5" fill="url(#dnaGradient)" rx="2"/>
                                    <text x="50" y="40" text-anchor="middle" fill="#2c3e50" font-size="9">超长读长</text>
                                </g>
                                
                                <!-- 实时检测 - 调整位置 -->
                                <g transform="translate(0, -60)">
                                    <circle cx="0" cy="0" r="10" fill="#f39c12" opacity="0.6">
                                        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                                    </circle>
                                    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="9">实时检测</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 - 调整路径避免重叠 -->
                            <path d="M180,350 Q250,320 360,300" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="8,4" opacity="0.7"/>
                            <path d="M440,300 Q550,290 700,280" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="8,4" opacity="0.7"/>
                            
                            <!-- 数据量增长 - 底部中央 -->
                            <g transform="translate(500, 480)">
                                <text x="0" y="0" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">数据量增长趋势</text>
                                <text x="-120" y="15" text-anchor="middle" fill="#2c3e50" font-size="11">KB级 (1977)</text>
                                <text x="0" y="15" text-anchor="middle" fill="#2c3e50" font-size="11">→ GB级 (2005)</text>
                                <text x="120" y="15" text-anchor="middle" fill="#2c3e50" font-size="11">→ TB级 (2011+)</text>
                            </g>
                        </svg>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>高通量测序技术发展历史与演进</li>
                                <li>第一代、第二代、第三代测序技术原理</li>
                                <li>主流测序平台详解与比较</li>
                                <li>技术特点、优缺点及适用场景</li>
                                <li>测序实验设计与关键考量因素</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>理解测序技术发展的技术突破点</li>
                                <li>掌握不同测序平台的工作原理</li>
                                <li>学会根据研究需求选择合适平台</li>
                                <li>具备测序实验设计能力</li>
                                <li>了解测序技术对生命科学的影响</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>高通量测序技术通过并行化、微型化、自动化等技术突破，实现了从低通量到高通量的革命性飞跃，推动了生命科学研究范式的根本转变。
                    </div>
                `
            },
            {
                title: "测序技术发展历程",
                subtitle: "第一部分：高通量测序技术的发展历史与技术演进",
                content: `
                    <h2>从第一代到第三代测序技术的演进</h2>

                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-year">1976-1977</div>
                            <div class="timeline-content">
                                <h4>第一代测序技术诞生</h4>
                                <p><strong>Maxam-Gilbert法</strong>：化学降解法，基于特定化学处理使DNA在特定碱基处断裂</p>
                                <p><strong>Sanger法</strong>：双脱氧链终止法，利用ddNTPs终止DNA链延伸</p>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-year">1990-2003</div>
                            <div class="timeline-content">
                                <h4>人类基因组计划(HGP) - 测序技术发展的催化剂</h4>
                                <p><strong>项目规模：</strong>国际合作，美国、英国、法国、德国、日本、中国等6个国家参与</p>
                                <p><strong>技术推动：</strong>推动自动化Sanger测序技术发展，催化高通量测序技术需求</p>
                                <p><strong>成本与时间：</strong>约30亿美元，耗时13年，比原计划提前2年完成</p>
                                <p><strong>技术竞争：</strong>公共HGP vs Celera公司，分层鸟枪法 vs 全基因组鸟枪法</p>
                                <p><strong>重要成果：</strong>3.2Gb基因组，~20,000个基因，99.99%准确率</p>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-year">2005</div>
                            <div class="timeline-content">
                                <h4>第二代测序技术(NGS)元年</h4>
                                <p><strong>454测序</strong>：首个商业化NGS技术，焦磷酸测序原理</p>
                                <p>读长：400-700bp，通量大幅提升</p>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-year">2007-2010</div>
                            <div class="timeline-content">
                                <h4>NGS技术快速发展</h4>
                                <p><strong>Illumina/Solexa</strong>：可逆终止测序技术</p>
                                <p><strong>Ion Torrent</strong>：半导体测序技术</p>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-year">2011-至今</div>
                            <div class="timeline-content">
                                <h4>第三代测序技术(TGS)</h4>
                                <p><strong>PacBio SMRT</strong>：单分子实时测序</p>
                                <p><strong>Oxford Nanopore</strong>：纳米孔测序技术</p>
                            </div>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>🌟 人类基因组项目的深远影响</h3>
                        <div class="two-column">
                            <div>
                                <h4>技术推动</h4>
                                <ul>
                                    <li>自动化测序技术成熟化</li>
                                    <li>催化NGS技术发展需求</li>
                                    <li>生物信息学学科建立</li>
                                    <li>大规模测序中心建设</li>
                                </ul>
                            </div>
                            <div>
                                <h4>科学影响</h4>
                                <ul>
                                    <li>建立人类遗传变异图谱</li>
                                    <li>推动个性化医学发展</li>
                                    <li>促进疾病基因发现</li>
                                    <li>催生功能基因组学</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="tech-box">
                        <h4>🚀 技术突破的关键驱动因素</h4>
                        <ul>
                            <li><strong>并行化(Parallelization)：</strong>从单个反应到数百万个同时反应</li>
                            <li><strong>微型化(Miniaturization)：</strong>反应体积从微升降至纳升级别</li>
                            <li><strong>自动化(Automation)：</strong>减少人工操作，提高效率和稳定性</li>
                            <li><strong>信号放大技术：</strong>桥式PCR、乳液PCR等原位扩增技术</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "第一代测序技术：Sanger法",
                subtitle: "双脱氧链终止法的原理与技术特点",
                content: `
                    <h2>Sanger测序技术详解</h2>

                    <div class="tech-box">
                        <h4>🧬 双脱氧链终止法核心原理</h4>
                        <p><strong>关键组件：</strong></p>
                        <ul>
                            <li><strong>DNA模板：</strong>待测序的单链DNA</li>
                            <li><strong>引物：</strong>与模板互补的短DNA片段</li>
                            <li><strong>DNA聚合酶：</strong>催化DNA合成</li>
                            <li><strong>dNTPs：</strong>四种正常脱氧核苷酸</li>
                            <li><strong>ddNTPs：</strong>四种双脱氧核苷酸(缺少3'-OH基团)</li>
                        </ul>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">DNA模板<br>引物结合</div>
                        <div class="flowchart-step">聚合酶<br>链延伸</div>
                        <div class="flowchart-step">ddNTP掺入<br>链终止</div>
                        <div class="flowchart-step">毛细管电泳<br>分离</div>
                        <div class="flowchart-step">荧光检测<br>序列读取</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术优势</h3>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>准确率：</strong> >99.99% (QV40+)</li>
                                    <li><strong>读长：</strong> 800-1000 bp</li>
                                    <li><strong>错误类型：</strong> 主要为替换错误</li>
                                    <li><strong>可靠性：</strong> 技术成熟，结果稳定</li>
                                    <li><strong>应用：</strong> 验证测序的金标准</li>
                                </ul>
                            </div>
                        </div>
                        <div>
                            <h3>技术局限</h3>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>通量：</strong> 极低，单次单样本</li>
                                    <li><strong>成本：</strong> 每碱基成本高</li>
                                    <li><strong>速度：</strong> 耗时较长</li>
                                    <li><strong>规模：</strong> 不适合大规模项目</li>
                                    <li><strong>自动化：</strong> 自动化程度有限</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="principle-diagram">
                        <h4>🧬 Sanger测序原理图解</h4>

                        <div class="sequencing-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>DNA模板与引物结合</strong>
                                <div class="dna-sequence">
                                    5'-ATCGATCGATCG...-3' (模板链)<br>
                                    3'-TAGCTAGCTAG-5' (引物)
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>DNA聚合酶延伸反应</strong>
                                <div class="molecule-diagram">
                                    <div class="base base-A">A</div>
                                    <div class="bond"></div>
                                    <div class="base base-T">T</div>
                                    <div class="bond"></div>
                                    <div class="base base-C">C</div>
                                    <div class="bond"></div>
                                    <div class="base base-G">G</div>
                                    <div style="margin: 0 10px; font-weight: bold;">+ ddNTP</div>
                                    <div class="base" style="background: #8e44ad;">dd</div>
                                </div>
                                <p style="text-align: center; margin-top: 10px;">
                                    <small>dNTPs (正常) + ddNTPs (终止子，荧光标记)</small>
                                </p>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>链终止与产物分离</strong>
                                <div class="dna-sequence">
                                    ddA终止: 3'-TAGCTAGCTAGC-<span style="color: #e74c3c;">ddA</span>-5'<br>
                                    ddC终止: 3'-TAGCTAGCTAGCG-<span style="color: #27ae60;">ddC</span>-5'<br>
                                    ddG终止: 3'-TAGCTAGCTAGCGA-<span style="color: #f39c12;">ddG</span>-5'<br>
                                    ddT终止: 3'-TAGCTAGCTAGCGAT-<span style="color: #3498db;">ddT</span>-5'
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <strong>毛细管电泳分离与荧光检测</strong>
                                <div style="text-align: center; margin: 15px 0;">
                                    <div style="display: inline-block; margin: 0 10px;">
                                        <div class="base base-A" style="width: 20px; height: 20px; font-size: 0.8em;">A</div>
                                        <div style="font-size: 0.8em; margin-top: 5px;">红色</div>
                                    </div>
                                    <div style="display: inline-block; margin: 0 10px;">
                                        <div class="base base-T" style="width: 20px; height: 20px; font-size: 0.8em;">T</div>
                                        <div style="font-size: 0.8em; margin-top: 5px;">蓝色</div>
                                    </div>
                                    <div style="display: inline-block; margin: 0 10px;">
                                        <div class="base base-G" style="width: 20px; height: 20px; font-size: 0.8em;">G</div>
                                        <div style="font-size: 0.8em; margin-top: 5px;">橙色</div>
                                    </div>
                                    <div style="display: inline-block; margin: 0 10px;">
                                        <div class="base base-C" style="width: 20px; height: 20px; font-size: 0.8em;">C</div>
                                        <div style="font-size: 0.8em; margin-top: 5px;">绿色</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# Sanger测序化学反应
DNA聚合酶反应:
Template + Primer + dNTPs + ddNTPs → 终止产物

关键特点:
- ddNTPs缺少3'-OH基团 → 无法继续延伸
- 荧光标记区分四种碱基
- 毛细管电泳按长度分离
- 激光激发检测荧光信号
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 应用局限：</strong>Sanger测序虽然准确性极高，但由于通量低、成本高，主要用于小片段验证、基因克隆确认、PCR产物测序等小规模应用。
                    </div>
                `
            },
            {
                title: "第二代测序技术：NGS原理",
                subtitle: "边合成边测序与大规模并行化技术",
                content: `
                    <h2>NGS核心技术：边合成边测序(SBS)</h2>

                    <div class="highlight-box">
                        <h3>技术革命的核心突破</h3>
                        <p>NGS技术通过<strong>大规模并行化</strong>实现了通量的指数级提升：从Sanger的单个反应提升到数百万至数十亿个同时反应。</p>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">DNA片段化<br>接头连接</div>
                        <div class="flowchart-step">固相扩增<br>簇形成</div>
                        <div class="flowchart-step">测序反应<br>信号检测</div>
                        <div class="flowchart-step">图像处理<br>碱基识别</div>
                        <div class="flowchart-step">序列输出<br>质量评估</div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔬 固相扩增技术</h4>
                            <p><strong>桥式PCR (Illumina)：</strong></p>
                            <ul>
                                <li>DNA片段随机附着在Flowcell表面</li>
                                <li>通过桥式PCR原位扩增</li>
                                <li>形成包含数百万拷贝的DNA簇</li>
                                <li>簇密度：~1000个簇/mm²</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>🧪 乳液PCR技术</h4>
                            <p><strong>emPCR (454/Ion Torrent)：</strong></p>
                            <ul>
                                <li>单个DNA分子与磁珠结合</li>
                                <li>包裹在油包水微滴中</li>
                                <li>PCR扩增形成单克隆磁珠</li>
                                <li>每个磁珠含~100万个拷贝</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>📊 信号检测方式</h4>
                            <p><strong>多种检测原理：</strong></p>
                            <ul>
                                <li><strong>光学检测：</strong>荧光信号(Illumina)</li>
                                <li><strong>化学检测：</strong>pH变化(Ion Torrent)</li>
                                <li><strong>生物发光：</strong>焦磷酸反应(454)</li>
                                <li><strong>电学检测：</strong>电流变化(Nanopore)</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ 技术优势</h4>
                            <p><strong>NGS核心优势：</strong></p>
                            <ul>
                                <li><strong>高通量：</strong>数百万reads/run</li>
                                <li><strong>低成本：</strong>单位成本大幅降低</li>
                                <li><strong>高准确性：</strong>QV30+ (99.9%+)</li>
                                <li><strong>自动化：</strong>全流程自动化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>技术特征</th>
                                <th>第一代(Sanger)</th>
                                <th>第二代(NGS)</th>
                                <th>提升倍数</th>
                            </tr>
                            <tr>
                                <td><strong>通量(reads/run)</strong></td>
                                <td>1</td>
                                <td>10⁶ - 10⁹</td>
                                <td>10⁶ - 10⁹倍</td>
                            </tr>
                            <tr>
                                <td><strong>成本($/Mb)</strong></td>
                                <td>~$5,000</td>
                                <td>~$0.01</td>
                                <td>50万倍降低</td>
                            </tr>
                            <tr>
                                <td><strong>时间(天)</strong></td>
                                <td>1-2天</td>
                                <td>1-3天</td>
                                <td>相当</td>
                            </tr>
                            <tr>
                                <td><strong>读长(bp)</strong></td>
                                <td>800-1000</td>
                                <td>50-300</td>
                                <td>2-20倍降低</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-box">
                        <strong>💡 技术权衡：</strong>NGS通过牺牲单次读长来换取巨大的通量提升和成本降低，这种权衡使得大规模基因组学研究成为可能。
                    </div>
                `
            },
            {
                title: "第三代测序技术：长读长革命",
                subtitle: "单分子实时测序与纳米孔测序技术",
                content: `
                    <h2>第三代测序技术(TGS)的突破性创新</h2>

                    <div class="highlight-box">
                        <h3>技术革命：从短读长到长读长</h3>
                        <p>第三代测序技术通过<strong>单分子测序</strong>和<strong>实时检测</strong>，实现了超长读长(10kb-1Mb+)，解决了NGS在基因组组装和结构变异检测方面的局限性。</p>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔬 PacBio SMRT技术</h4>
                            <p><strong>单分子实时测序原理：</strong></p>
                            <ul>
                                <li><strong>ZMW孔：</strong>零模波导孔，直径<激发光波长</li>
                                <li><strong>固定聚合酶：</strong>DNA聚合酶固定在孔底部</li>
                                <li><strong>荧光dNTP：</strong>磷酸连接荧光基团</li>
                                <li><strong>实时检测：</strong>掺入时产生荧光脉冲</li>
                                <li><strong>连续合成：</strong>荧光基团随PPi释放</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ Oxford Nanopore技术</h4>
                            <p><strong>纳米孔测序原理：</strong></p>
                            <ul>
                                <li><strong>蛋白纳米孔：</strong>嵌入脂质双分子层</li>
                                <li><strong>马达蛋白：</strong>控制DNA通过速度</li>
                                <li><strong>电流检测：</strong>不同碱基产生特征电流</li>
                                <li><strong>实时分析：</strong>边测序边分析</li>
                                <li><strong>直接RNA：</strong>可直接测序RNA分子</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>📊 HiFi Reads技术</h4>
                            <p><strong>环形一致性测序：</strong></p>
                            <ul>
                                <li><strong>SMRTbell文库：</strong>环形DNA模板</li>
                                <li><strong>多轮测序：</strong>同一分子多次读取</li>
                                <li><strong>一致性序列：</strong>多次读取生成共识</li>
                                <li><strong>高准确性：</strong>>99.9% (QV30+)</li>
                                <li><strong>长读长：</strong>平均15-25kb</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>🎯 技术优势</h4>
                            <p><strong>长读长测序优势：</strong></p>
                            <ul>
                                <li><strong>基因组组装：</strong>跨越重复序列</li>
                                <li><strong>结构变异：</strong>检测大型SV</li>
                                <li><strong>单倍型定相：</strong>长距离连锁信息</li>
                                <li><strong>全长转录本：</strong>完整异构体序列</li>
                                <li><strong>表观修饰：</strong>直接检测甲基化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>技术特征</th>
                                <th>PacBio SMRT</th>
                                <th>Oxford Nanopore</th>
                                <th>技术优势</th>
                            </tr>
                            <tr>
                                <td><strong>读长范围</strong></td>
                                <td>HiFi: 10-25kb<br>CLR: 20kb+</td>
                                <td>平均>10kb<br>最长>4Mb</td>
                                <td>超长读长</td>
                            </tr>
                            <tr>
                                <td><strong>原始准确率</strong></td>
                                <td>CLR: ~85-95%<br>HiFi: >99.9%</td>
                                <td>Raw: ~90-99%</td>
                                <td>HiFi高精度</td>
                            </tr>
                            <tr>
                                <td><strong>主要错误类型</strong></td>
                                <td>随机Indel</td>
                                <td>Indel + 替换</td>
                                <td>可通过算法校正</td>
                            </tr>
                            <tr>
                                <td><strong>特殊功能</strong></td>
                                <td>动力学检测修饰</td>
                                <td>直接RNA测序</td>
                                <td>多维度信息</td>
                            </tr>
                        </table>
                    </div>

                    <div class="principle-diagram">
                        <h4>🔌 Oxford Nanopore测序原理</h4>

                        <div class="nanopore-diagram">
                            <div style="text-align: center; margin-bottom: 20px;">
                                <strong>纳米孔测序装置</strong>
                            </div>

                            <div class="membrane">
                                <div class="pore"></div>
                            </div>
                            <div style="text-align: center; margin: 10px 0;">
                                <small>脂质双分子层膜 + 蛋白纳米孔</small>
                            </div>

                            <div class="dna-strand"></div>
                            <div style="text-align: center; margin: 10px 0;">
                                <small>DNA分子通过纳米孔</small>
                            </div>

                            <div class="current-signal">
                                <div class="signal-wave"></div>
                            </div>
                            <div style="text-align: center; margin: 10px 0;">
                                <small>电流信号变化检测不同碱基</small>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>电压驱动</strong>
                                <p>在膜两侧施加电压，驱动DNA分子通过纳米孔</p>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>马达蛋白控制</strong>
                                <p>马达蛋白控制DNA通过速度，确保稳定的信号检测</p>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>电流信号检测</strong>
                                <p>不同碱基通过时产生特征性电流变化，实时识别碱基序列</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-box">
                        <h4>🚀 第三代测序的应用突破</h4>
                        <ul>
                            <li><strong>参考基因组构建：</strong>T2T人类基因组、复杂植物基因组</li>
                            <li><strong>结构变异研究：</strong>大片段插入、缺失、倒位、易位</li>
                            <li><strong>复杂区域解析：</strong>着丝粒、端粒、高重复序列</li>
                            <li><strong>全长转录组：</strong>无需拼接的完整异构体分析</li>
                            <li><strong>表观基因组：</strong>原生DNA甲基化检测</li>
                            <li><strong>宏基因组学：</strong>完整微生物基因组组装</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "Illumina测序平台详解",
                subtitle: "第三部分：主流测序平台技术原理与性能",
                content: `
                    <h2>Illumina：可逆终止测序技术</h2>

                    <div class="tech-box">
                        <h4>🔬 可逆终止SBS技术原理</h4>
                        <p><strong>核心技术组件：</strong></p>
                        <ul>
                            <li><strong>可逆终止子：</strong>3'-OH被保护基团阻断</li>
                            <li><strong>荧光标记：</strong>四种不同颜色荧光染料</li>
                            <li><strong>化学切割：</strong>去除终止基团和荧光</li>
                            <li><strong>循环反应：</strong>掺入→成像→切割→洗涤</li>
                        </ul>
                    </div>

                    <div class="principle-diagram">
                        <h4>🔬 Illumina Flowcell结构与桥式PCR</h4>

                        <div class="flowcell-diagram">
                            <div style="text-align: center; margin-bottom: 15px;">
                                <strong>Flowcell表面 (8个Lane)</strong>
                            </div>
                            <div class="flowcell-lane">
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                            </div>
                            <div class="flowcell-lane">
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                                <div class="cluster"></div>
                            </div>
                            <div style="text-align: center; margin-top: 15px;">
                                <small>每个点代表一个DNA簇 (~1000个拷贝)</small>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>桥式PCR扩增</strong>
                                <p>单个DNA分子通过接头与Flowcell表面结合，形成"桥"状结构进行PCR扩增</p>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>簇形成</strong>
                                <p>每个原始分子扩增成包含~1000个相同序列的DNA簇</p>
                            </div>
                        </div>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">荧光dNTP<br>掺入</div>
                        <div class="flowchart-step">激光激发<br>荧光成像</div>
                        <div class="flowchart-step">化学切割<br>去保护</div>
                        <div class="flowchart-step">洗涤<br>准备下轮</div>
                        <div class="flowchart-step">循环重复<br>序列延伸</div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🖥️ 桌面级平台</h4>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>iSeq 100：</strong>1.2Gb, 4M reads</li>
                                    <li><strong>MiniSeq：</strong>7.5Gb, 25M reads</li>
                                    <li><strong>MiSeq：</strong>15Gb, 25M reads, 2×300bp</li>
                                </ul>
                            </div>
                            <p><strong>应用：</strong>小基因组、靶向测序、16S扩增子</p>
                        </div>

                        <div class="platform-card">
                            <h4>🏭 生产级平台</h4>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>NextSeq 1000/2000：</strong>120Gb, 400M reads</li>
                                    <li><strong>NovaSeq 6000：</strong>6Tb, 20B reads</li>
                                    <li><strong>NovaSeq X Plus：</strong>16Tb, 52B reads</li>
                                </ul>
                            </div>
                            <p><strong>应用：</strong>WGS、大规模转录组、群体基因组学</p>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ 技术特点</h4>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>准确率：</strong>>99.9% (QV30+)</li>
                                    <li><strong>错误类型：</strong>主要为替换错误</li>
                                    <li><strong>GC偏好：</strong>相对均一覆盖</li>
                                    <li><strong>双端测序：</strong>提供配对信息</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>📊 成本效益</h4>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>单位成本：</strong>$0.01-0.1/Mb</li>
                                    <li><strong>通量范围：</strong>Gb-Tb级别</li>
                                    <li><strong>运行时间：</strong>几小时-几天</li>
                                    <li><strong>市场份额：</strong>>70%</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# Illumina测序化学反应示例
循环 1: 掺入 A-荧光-终止子 → 激发绿色荧光 → 记录位置
       化学切割去除荧光和终止基团 → 恢复3'-OH

循环 2: 掺入 T-荧光-终止子 → 激发红色荧光 → 记录位置
       化学切割去除荧光和终止基团 → 恢复3'-OH

循环 3: 掺入 G-荧光-终止子 → 激发蓝色荧光 → 记录位置
       ...

最终序列: ATGCATGC...
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>平台型号</th>
                                <th>最大输出</th>
                                <th>最大读长</th>
                                <th>运行时间</th>
                                <th>主要应用</th>
                            </tr>
                            <tr>
                                <td><strong>MiSeq</strong></td>
                                <td>15 Gb</td>
                                <td>2×300 bp</td>
                                <td>4-56小时</td>
                                <td>小基因组、靶向测序</td>
                            </tr>
                            <tr>
                                <td><strong>NextSeq 2000</strong></td>
                                <td>120 Gb</td>
                                <td>2×150 bp</td>
                                <td>12-48小时</td>
                                <td>外显子组、转录组</td>
                            </tr>
                            <tr>
                                <td><strong>NovaSeq 6000</strong></td>
                                <td>6 Tb</td>
                                <td>2×150 bp</td>
                                <td>13-44小时</td>
                                <td>WGS、大规模项目</td>
                            </tr>
                            <tr>
                                <td><strong>NovaSeq X Plus</strong></td>
                                <td>16 Tb</td>
                                <td>2×150 bp</td>
                                <td>16-42小时</td>
                                <td>超大规模基因组学</td>
                            </tr>
                        </table>
                    </div>
                `
            },
            {
                title: "其他主流测序平台",
                subtitle: "Ion Torrent、PacBio、Oxford Nanopore技术详解",
                content: `
                    <h2>多元化测序技术生态</h2>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>⚡ Ion Torrent半导体测序</h4>
                            <p><strong>技术原理：</strong></p>
                            <ul>
                                <li><strong>pH检测：</strong>DNA聚合释放H+离子</li>
                                <li><strong>ISFET传感器：</strong>离子敏感场效应管</li>
                                <li><strong>无光学系统：</strong>纯电子检测</li>
                                <li><strong>同聚物挑战：</strong>连续相同碱基难检测</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>读长：</strong>200-600bp</li>
                                    <li><strong>准确率：</strong>~99% (QV20)</li>
                                    <li><strong>运行时间：</strong>2-7小时</li>
                                    <li><strong>优势：</strong>快速、成本低</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔬 PacBio Sequel系列</h4>
                            <p><strong>SMRT技术升级：</strong></p>
                            <ul>
                                <li><strong>Sequel IIe：</strong>8M ZMW孔</li>
                                <li><strong>Revio：</strong>25M ZMW孔，4倍通量提升</li>
                                <li><strong>HiFi模式：</strong>高精度长读长</li>
                                <li><strong>CLR模式：</strong>超长读长(>100kb)</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>HiFi读长：</strong>10-25kb, >99.9%准确率</li>
                                    <li><strong>CLR读长：</strong>平均20kb+</li>
                                    <li><strong>通量：</strong>15-30Gb/SMRT Cell</li>
                                    <li><strong>运行时间：</strong>30分钟-30小时</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🧬 Oxford Nanopore系列</h4>
                            <p><strong>便携式到高通量：</strong></p>
                            <ul>
                                <li><strong>MinION：</strong>便携式，USB供电</li>
                                <li><strong>GridION：</strong>5个MinION并行</li>
                                <li><strong>PromethION：</strong>48个流通池，超高通量</li>
                                <li><strong>Flongle：</strong>低成本入门级</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>读长：</strong>平均10-50kb，最长>4Mb</li>
                                    <li><strong>准确率：</strong>95-99% (持续改进)</li>
                                    <li><strong>实时分析：</strong>边测序边分析</li>
                                    <li><strong>直接RNA：</strong>无需逆转录</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🌟 新兴测序平台</h4>
                            <p><strong>技术创新：</strong></p>
                            <ul>
                                <li><strong>MGI/BGI：</strong>DNBSEQ技术，DNA纳米球</li>
                                <li><strong>Element Bio：</strong>AVITI平台，表面化学</li>
                                <li><strong>Singular Genomics：</strong>G4平台</li>
                                <li><strong>Ultima Genomics：</strong>$100基因组目标</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>成本优势：</strong>进一步降低测序成本</li>
                                    <li><strong>技术差异化：</strong>独特技术路线</li>
                                    <li><strong>市场竞争：</strong>打破垄断格局</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# Ion Torrent pH检测原理
DNA聚合反应：
dNTP + (DNA)n → (DNA)n+1 + PPi + H+

pH变化检测：
正常pH: 7.4
掺入A: pH ↓ (检测到H+释放)
掺入T: pH ↓ (检测到H+释放)
掺入G: pH ↓ (检测到H+释放)
掺入C: pH ↓ (检测到H+释放)

同聚物问题：
AAAA → 4个H+同时释放 → 信号强度4倍 → 长度判断困难
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 平台选择考虑：</strong>不同测序平台各有优势，选择时需要综合考虑项目需求、预算、时间、准确率要求等多个因素。
                    </div>
                `
            },
            {
                title: "主流测序平台介绍",
                subtitle: "Illumina、Ion Torrent、PacBio、Nanopore等主流平台详解",
                content: `
                    <h2>当前主流测序平台生态系统</h2>

                    <div class="highlight-box">
                        <h3>🌟 测序平台市场格局</h3>
                        <p>当前测序市场由几大主要厂商主导，每个平台都有其独特的技术优势和应用场景，形成了多元化的技术生态系统。</p>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔬 Illumina平台系列</h4>
                            <p><strong>市场地位：</strong>全球市场份额>70%</p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>桌面级：</strong>iSeq 100, MiniSeq, MiSeq</li>
                                    <li><strong>中等通量：</strong>NextSeq 1000/2000</li>
                                    <li><strong>高通量：</strong>NovaSeq 6000, NovaSeq X</li>
                                    <li><strong>技术特点：</strong>可逆终止SBS技术</li>
                                </ul>
                            </div>
                            <p><strong>主要应用：</strong>WGS、WES、RNA-seq、表观基因组学</p>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ Thermo Fisher (Ion Torrent)</h4>
                            <p><strong>技术特色：</strong>半导体测序技术</p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>个人级：</strong>Ion PGM</li>
                                    <li><strong>生产级：</strong>Ion Proton, Ion S5</li>
                                    <li><strong>技术优势：</strong>快速、无光学系统</li>
                                    <li><strong>技术挑战：</strong>同聚物错误</li>
                                </ul>
                            </div>
                            <p><strong>主要应用：</strong>靶向测序、快速诊断、病原检测</p>
                        </div>

                        <div class="platform-card">
                            <h4>🧬 Pacific Biosciences (PacBio)</h4>
                            <p><strong>技术特色：</strong>单分子实时测序(SMRT)</p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>当前平台：</strong>Sequel IIe, Revio</li>
                                    <li><strong>技术优势：</strong>长读长、高精度HiFi</li>
                                    <li><strong>读长范围：</strong>HiFi 10-25kb, CLR >100kb</li>
                                    <li><strong>特殊功能：</strong>表观修饰检测</li>
                                </ul>
                            </div>
                            <p><strong>主要应用：</strong>基因组组装、结构变异、全长转录本</p>
                        </div>

                        <div class="platform-card">
                            <h4>🔌 Oxford Nanopore Technologies</h4>
                            <p><strong>技术特色：</strong>纳米孔测序技术</p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>便携式：</strong>MinION, Flongle</li>
                                    <li><strong>高通量：</strong>GridION, PromethION</li>
                                    <li><strong>技术优势：</strong>超长读长、实时分析</li>
                                    <li><strong>特殊功能：</strong>直接RNA测序</li>
                                </ul>
                            </div>
                            <p><strong>主要应用：</strong>实时监测、现场检测、复杂基因组</p>
                        </div>

                        <div class="platform-card">
                            <h4>🌟 新兴平台</h4>
                            <p><strong>技术创新：</strong>挑战现有格局</p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>MGI/BGI：</strong>DNBSEQ技术</li>
                                    <li><strong>Element Biosciences：</strong>AVITI平台</li>
                                    <li><strong>Singular Genomics：</strong>G4平台</li>
                                    <li><strong>Ultima Genomics：</strong>$100基因组目标</li>
                                </ul>
                            </div>
                            <p><strong>市场影响：</strong>成本竞争、技术多样化</p>
                        </div>

                        <div class="platform-card">
                            <h4>📊 平台选择策略</h4>
                            <p><strong>关键考虑因素：</strong></p>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>研究目标：</strong>基因组组装 vs 变异检测</li>
                                    <li><strong>样本类型：</strong>DNA vs RNA vs 表观修饰</li>
                                    <li><strong>预算限制：</strong>设备成本 vs 运行成本</li>
                                    <li><strong>时间要求：</strong>快速结果 vs 高精度</li>
                                    <li><strong>数据量需求：</strong>单样本 vs 大规模项目</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>平台类型</th>
                                <th>代表产品</th>
                                <th>核心技术</th>
                                <th>主要优势</th>
                                <th>典型应用</th>
                            </tr>
                            <tr>
                                <td><strong>短读长NGS</strong></td>
                                <td>Illumina NovaSeq</td>
                                <td>可逆终止SBS</td>
                                <td>高通量、低成本、高精度</td>
                                <td>WGS、WES、RNA-seq</td>
                            </tr>
                            <tr>
                                <td><strong>半导体测序</strong></td>
                                <td>Ion Torrent S5</td>
                                <td>pH检测</td>
                                <td>快速、无光学系统</td>
                                <td>靶向测序、快速诊断</td>
                            </tr>
                            <tr>
                                <td><strong>长读长SMRT</strong></td>
                                <td>PacBio Revio</td>
                                <td>单分子实时测序</td>
                                <td>长读长、高精度HiFi</td>
                                <td>基因组组装、结构变异</td>
                            </tr>
                            <tr>
                                <td><strong>纳米孔测序</strong></td>
                                <td>ONT PromethION</td>
                                <td>纳米孔电流检测</td>
                                <td>超长读长、实时分析</td>
                                <td>实时监测、复杂结构</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术发展趋势</h3>
                            <div class="tech-box">
                                <h4>🚀 技术创新方向</h4>
                                <ul>
                                    <li><strong>成本降低：</strong>向$100基因组迈进</li>
                                    <li><strong>速度提升：</strong>实时分析能力增强</li>
                                    <li><strong>精度改进：</strong>长读长技术准确率提升</li>
                                    <li><strong>通量增加：</strong>单次运行数据量增长</li>
                                    <li><strong>便携化：</strong>现场检测设备发展</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>应用领域拓展</h3>
                            <div class="tech-box">
                                <h4>🌍 新兴应用</h4>
                                <ul>
                                    <li><strong>临床诊断：</strong>精准医学、液体活检</li>
                                    <li><strong>农业育种：</strong>作物基因组改良</li>
                                    <li><strong>环境监测：</strong>微生物组分析</li>
                                    <li><strong>食品安全：</strong>病原体检测</li>
                                    <li><strong>法医鉴定：</strong>个体识别</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 平台选择建议：</strong>没有"最好"的测序平台，只有"最适合"的平台。选择时需要综合考虑研究目标、样本特性、预算限制、时间要求等多个因素。
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 技术更新快速：</strong>测序技术发展迅速，新平台和技术不断涌现，需要持续关注技术发展动态，及时更新知识体系。
                    </div>
                `
            },
            {
                title: "测序平台技术特点对比",
                subtitle: "第四部分：各测序平台的技术特点、优缺点及适用场景",
                content: `
                    <h2>全面技术参数对比分析</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>技术特征</th>
                                <th>Illumina</th>
                                <th>Ion Torrent</th>
                                <th>PacBio</th>
                                <th>Oxford Nanopore</th>
                            </tr>
                            <tr>
                                <td><strong>测序原理</strong></td>
                                <td>可逆终止SBS</td>
                                <td>半导体pH检测</td>
                                <td>单分子实时SMRT</td>
                                <td>纳米孔电流检测</td>
                            </tr>
                            <tr>
                                <td><strong>读长范围</strong></td>
                                <td>50-300bp</td>
                                <td>200-600bp</td>
                                <td>10kb-100kb+</td>
                                <td>1kb-4Mb+</td>
                            </tr>
                            <tr>
                                <td><strong>准确率</strong></td>
                                <td>>99.9% (QV30+)</td>
                                <td>~99% (QV20)</td>
                                <td>HiFi: >99.9%<br>CLR: ~85%</td>
                                <td>95-99%</td>
                            </tr>
                            <tr>
                                <td><strong>主要错误类型</strong></td>
                                <td>替换错误</td>
                                <td>Indel(同聚物)</td>
                                <td>随机Indel</td>
                                <td>Indel + 替换</td>
                            </tr>
                            <tr>
                                <td><strong>通量范围</strong></td>
                                <td>Gb - Tb</td>
                                <td>Mb - Gb</td>
                                <td>Gb - 数十Gb</td>
                                <td>Gb - 数十Gb</td>
                            </tr>
                            <tr>
                                <td><strong>运行时间</strong></td>
                                <td>几小时-几天</td>
                                <td>2-7小时</td>
                                <td>30分钟-30小时</td>
                                <td>几分钟-几天</td>
                            </tr>
                            <tr>
                                <td><strong>单位成本</strong></td>
                                <td>$0.01-0.1/Mb</td>
                                <td>$0.1-1/Mb</td>
                                <td>$0.1-1/Mb</td>
                                <td>$0.1-1/Mb</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>应用场景优势分析</h3>
                            <div class="platform-card">
                                <h4>🎯 Illumina优势应用</h4>
                                <ul>
                                    <li><strong>全基因组测序：</strong>高通量、低成本</li>
                                    <li><strong>外显子组测序：</strong>高准确率</li>
                                    <li><strong>转录组测序：</strong>定量精确</li>
                                    <li><strong>表观基因组：</strong>ChIP-seq、ATAC-seq</li>
                                    <li><strong>群体基因组学：</strong>大规模样本</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>⚡ Ion Torrent优势应用</h4>
                                <ul>
                                    <li><strong>快速诊断：</strong>短运行时间</li>
                                    <li><strong>靶向测序：</strong>小panel快速检测</li>
                                    <li><strong>病原检测：</strong>快速鉴定</li>
                                    <li><strong>法医学：</strong>降解样本分析</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <div class="platform-card">
                                <h4>🔬 PacBio优势应用</h4>
                                <ul>
                                    <li><strong>基因组组装：</strong>de novo组装</li>
                                    <li><strong>结构变异：</strong>大型SV检测</li>
                                    <li><strong>全长转录本：</strong>异构体分析</li>
                                    <li><strong>表观修饰：</strong>甲基化检测</li>
                                    <li><strong>复杂基因组：</strong>高重复区域</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>🧬 Nanopore优势应用</h4>
                                <ul>
                                    <li><strong>实时分析：</strong>边测序边分析</li>
                                    <li><strong>便携检测：</strong>现场快速检测</li>
                                    <li><strong>直接RNA：</strong>RNA修饰检测</li>
                                    <li><strong>超长读长：</strong>复杂结构解析</li>
                                    <li><strong>宏基因组：</strong>微生物组装</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>混合测序策略(Hybrid Sequencing)</h3>
                        <p><strong>短读长 + 长读长组合：</strong>发挥各技术优势，互补不足</p>
                        <ul>
                            <li><strong>Illumina + PacBio：</strong>高准确率 + 长读长组装</li>
                            <li><strong>Illumina + Nanopore：</strong>成本效益 + 结构变异</li>
                            <li><strong>多平台验证：</strong>提高结果可靠性</li>
                        </ul>
                    </div>

                    <div class="tech-box">
                        <h4>🚀 技术发展趋势</h4>
                        <ul>
                            <li><strong>成本持续下降：</strong>向$100基因组目标迈进</li>
                            <li><strong>准确率提升：</strong>长读长技术准确率不断改善</li>
                            <li><strong>通量增加：</strong>单次运行数据量持续增长</li>
                            <li><strong>速度加快：</strong>实时分析能力增强</li>
                            <li><strong>应用拓展：</strong>从研究走向临床诊断</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "测序实验设计与考虑因素",
                subtitle: "第五部分：测序实验设计的关键要素与最佳实践",
                content: `
                    <h2>科学的实验设计是成功的关键</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">研究目标<br>明确化</div>
                        <div class="flowchart-step">平台选择<br>技术评估</div>
                        <div class="flowchart-step">样本设计<br>质控标准</div>
                        <div class="flowchart-step">文库构建<br>测序参数</div>
                        <div class="flowchart-step">数据分析<br>结果验证</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>实验设计核心要素</h3>
                            <div class="tech-box">
                                <h4>🎯 研究目标明确化</h4>
                                <ul>
                                    <li><strong>基因组组装：</strong>选择长读长技术</li>
                                    <li><strong>变异检测：</strong>考虑覆盖深度需求</li>
                                    <li><strong>表达定量：</strong>评估技术重复需求</li>
                                    <li><strong>功能注释：</strong>确定分析流程</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>📊 测序深度确定</h4>
                                <ul>
                                    <li><strong>WGS：</strong>30-50X (变异检测)</li>
                                    <li><strong>WES：</strong>100-150X (外显子区域)</li>
                                    <li><strong>RNA-seq：</strong>20-50M reads/样本</li>
                                    <li><strong>ChIP-seq：</strong>20-40M reads/样本</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>样本质量控制</h3>
                            <div class="tech-box">
                                <h4>🧪 DNA质量要求</h4>
                                <ul>
                                    <li><strong>浓度：</strong>≥10ng/μL (短读长)</li>
                                    <li><strong>纯度：</strong>A260/280 = 1.8-2.0</li>
                                    <li><strong>完整性：</strong>DIN ≥ 7 (长读长需求更高)</li>
                                    <li><strong>无污染：</strong>无蛋白质、RNA污染</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>🔬 RNA质量要求</h4>
                                <ul>
                                    <li><strong>RIN值：</strong>≥7 (完整性指标)</li>
                                    <li><strong>浓度：</strong>≥50ng/μL</li>
                                    <li><strong>无DNA污染：</strong>DNase处理</li>
                                    <li><strong>保存条件：</strong>-80°C长期保存</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>📚 文库构建策略</h4>
                            <p><strong>DNA文库类型：</strong></p>
                            <ul>
                                <li><strong>PCR-free：</strong>减少PCR偏好性</li>
                                <li><strong>PCR-based：</strong>适合低起始量</li>
                                <li><strong>Mate-pair：</strong>大插入片段</li>
                                <li><strong>Long-range：</strong>长距离连接信息</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>🧬 RNA文库策略</h4>
                            <p><strong>RNA文库类型：</strong></p>
                            <ul>
                                <li><strong>Poly-A选择：</strong>mRNA富集</li>
                                <li><strong>rRNA去除：</strong>总RNA分析</li>
                                <li><strong>链特异性：</strong>保留链信息</li>
                                <li><strong>小RNA：</strong>miRNA、siRNA</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>⚖️ 成本预算规划</h4>
                            <p><strong>成本组成：</strong></p>
                            <ul>
                                <li><strong>样本制备：</strong>20-30%</li>
                                <li><strong>测序费用：</strong>40-50%</li>
                                <li><strong>数据分析：</strong>20-30%</li>
                                <li><strong>验证实验：</strong>10-20%</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>⏱️ 时间安排</h4>
                            <p><strong>项目周期：</strong></p>
                            <ul>
                                <li><strong>样本准备：</strong>1-2周</li>
                                <li><strong>文库构建：</strong>1-3天</li>
                                <li><strong>测序运行：</strong>1-3天</li>
                                <li><strong>数据分析：</strong>1-4周</li>
                            </ul>
                        </div>
                    </div>

                    <div class="code-block">
# 测序深度计算示例
人类基因组大小: 3.2 Gb
目标覆盖深度: 30X
所需数据量: 3.2 Gb × 30 = 96 Gb

考虑质量过滤损失(~10%):
实际需求: 96 Gb ÷ 0.9 = 107 Gb

Illumina NovaSeq 6000 S4流通池: 1.5 Tb
可测序样本数: 1500 Gb ÷ 107 Gb ≈ 14个样本
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 实验设计要点：</strong>充分的前期规划可以避免后期问题，包括样本量计算、对照设置、批次效应控制等都需要仔细考虑。
                    </div>
                `
            },
            {
                title: "测序成本革命与技术驱动",
                subtitle: "测序成本下降的技术因素与市场影响",
                content: `
                    <h2>测序成本的指数级下降</h2>

                    <div class="highlight-box">
                        <h3>摩尔定律 vs 测序成本定律</h3>
                        <p>测序成本的下降速度<strong>远超摩尔定律</strong>：从2001年到2021年，测序成本下降了<strong>100万倍</strong>，而同期计算机性能仅提升了约1000倍。</p>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>年份</th>
                                <th>里程碑事件</th>
                                <th>基因组成本</th>
                                <th>技术平台</th>
                                <th>成本下降因子</th>
                            </tr>
                            <tr>
                                <td><strong>2003</strong></td>
                                <td>人类基因组计划完成</td>
                                <td>$30亿</td>
                                <td>Sanger测序</td>
                                <td>基准</td>
                            </tr>
                            <tr>
                                <td><strong>2007</strong></td>
                                <td>个人基因组项目</td>
                                <td>$100万</td>
                                <td>454 + Sanger</td>
                                <td>3,000倍</td>
                            </tr>
                            <tr>
                                <td><strong>2014</strong></td>
                                <td>$1000基因组实现</td>
                                <td>$1,000</td>
                                <td>Illumina HiSeq</td>
                                <td>300万倍</td>
                            </tr>
                            <tr>
                                <td><strong>2022</strong></td>
                                <td>$100基因组接近</td>
                                <td>$200-600</td>
                                <td>NovaSeq 6000</td>
                                <td>500万倍</td>
                            </tr>
                            <tr>
                                <td><strong>2024</strong></td>
                                <td>$100基因组目标</td>
                                <td>$100</td>
                                <td>Ultima/Element</td>
                                <td>3000万倍</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>成本下降的技术驱动因素</h3>
                            <div class="tech-box">
                                <h4>🔬 技术创新</h4>
                                <ul>
                                    <li><strong>并行化程度：</strong>从1个反应到数十亿个</li>
                                    <li><strong>试剂优化：</strong>酶活性提升、化学稳定性</li>
                                    <li><strong>光学系统：</strong>检测精度和速度提升</li>
                                    <li><strong>芯片技术：</strong>密度增加、制造成本降低</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>🏭 规模经济</h4>
                                <ul>
                                    <li><strong>产量提升：</strong>大规模生产降低单位成本</li>
                                    <li><strong>供应链优化：</strong>原材料成本控制</li>
                                    <li><strong>自动化：</strong>减少人工成本</li>
                                    <li><strong>标准化：</strong>通用试剂和耗材</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>市场竞争与技术突破</h3>
                            <div class="tech-box">
                                <h4>💰 商业模式创新</h4>
                                <ul>
                                    <li><strong>仪器租赁：</strong>降低初始投资门槛</li>
                                    <li><strong>服务外包：</strong>专业测序服务公司</li>
                                    <li><strong>云计算：</strong>数据分析成本分摊</li>
                                    <li><strong>开源工具：</strong>降低软件成本</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>🌍 应用领域扩展</h4>
                                <ul>
                                    <li><strong>临床诊断：</strong>大规模医疗应用</li>
                                    <li><strong>农业育种：</strong>作物基因组学</li>
                                    <li><strong>环境监测：</strong>微生物组分析</li>
                                    <li><strong>法医鉴定：</strong>个体识别应用</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# 成本下降计算示例
2003年人类基因组成本: $3,000,000,000
2024年预期成本: $100
总下降倍数: 30,000,000倍

年均下降率: (100/3,000,000,000)^(1/21) - 1 = -58.7%
相当于每年成本减少约60%

对比摩尔定律(18个月翻倍):
21年计算性能提升: 2^(21*12/18) = 16,384倍
测序成本下降: 30,000,000倍 (1,831倍于摩尔定律)
                    </div>

                    <div class="principle-diagram">
                        <h4>📈 测序成本下降趋势图</h4>

                        <div class="cost-chart">
                            <div class="y-labels">
                                <div>$10B</div>
                                <div>$1B</div>
                                <div>$100M</div>
                                <div>$10M</div>
                                <div>$1M</div>
                                <div>$100K</div>
                                <div>$10K</div>
                                <div>$1K</div>
                                <div>$100</div>
                            </div>
                            <div class="chart-line">
                                <div class="cost-curve"></div>
                            </div>
                            <div class="chart-labels">
                                <div>2003</div>
                                <div>2007</div>
                                <div>2010</div>
                                <div>2014</div>
                                <div>2018</div>
                                <div>2022</div>
                                <div>2024</div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <div style="display: inline-block; margin: 0 15px;">
                                <div style="width: 20px; height: 10px; background: rgba(231, 76, 60, 0.6); display: inline-block; margin-right: 5px;"></div>
                                <small>Sanger时代</small>
                            </div>
                            <div style="display: inline-block; margin: 0 15px;">
                                <div style="width: 20px; height: 10px; background: rgba(241, 196, 15, 0.6); display: inline-block; margin-right: 5px;"></div>
                                <small>NGS早期</small>
                            </div>
                            <div style="display: inline-block; margin: 0 15px;">
                                <div style="width: 20px; height: 10px; background: rgba(46, 204, 113, 0.6); display: inline-block; margin-right: 5px;"></div>
                                <small>NGS成熟期</small>
                            </div>
                            <div style="display: inline-block; margin: 0 15px;">
                                <div style="width: 20px; height: 10px; background: rgba(52, 152, 219, 0.6); display: inline-block; margin-right: 5px;"></div>
                                <small>竞争时代</small>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 未来展望：</strong>随着新技术平台的出现和竞争加剧，测序成本有望继续下降，$10基因组可能在2030年前实现。
                    </div>
                `
            },
            {
                title: "测序数据格式与质量评估",
                subtitle: "FASTQ格式详解与测序质量控制",
                content: `
                    <h2>FASTQ格式：测序数据的标准载体</h2>

                    <div class="tech-box">
                        <h4>📄 FASTQ格式结构</h4>
                        <p>FASTQ格式包含四行信息，每个序列读段(read)占用四行：</p>
                        <ul>
                            <li><strong>第1行：</strong>序列标识符，以@开头</li>
                            <li><strong>第2行：</strong>DNA/RNA序列</li>
                            <li><strong>第3行：</strong>分隔符，通常为+</li>
                            <li><strong>第4行：</strong>质量值，与序列等长</li>
                        </ul>
                    </div>

                    <div class="principle-diagram">
                        <h4>📄 FASTQ格式可视化解析</h4>

                        <div class="sequencing-step">
                            <div class="step-number">@</div>
                            <div class="step-content">
                                <strong>序列标识符行</strong>
                                <div class="dna-sequence" style="background: #e8f5e8;">
                                    @SRR001666.1 071112_SLXA-EAS1_s_7:5:1:817:345 length=36
                                </div>
                                <small>包含：运行ID、仪器信息、坐标位置、序列长度</small>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">DNA</div>
                            <div class="step-content">
                                <strong>DNA序列行</strong>
                                <div class="molecule-diagram">
                                    <div class="base base-G">G</div>
                                    <div class="base base-G">G</div>
                                    <div class="base base-G">G</div>
                                    <div class="base base-T">T</div>
                                    <div class="base base-G">G</div>
                                    <div class="base base-A">A</div>
                                    <div class="base base-T">T</div>
                                    <div class="base base-G">G</div>
                                    <div style="margin: 0 5px;">...</div>
                                </div>
                                <small>实际测序得到的碱基序列</small>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">+</div>
                            <div class="step-content">
                                <strong>分隔符行</strong>
                                <div class="dna-sequence" style="background: #f0f0f0;">
                                    +SRR001666.1 071112_SLXA-EAS1_s_7:5:1:817:345 length=36
                                </div>
                                <small>可选重复标识符，通常只有"+"</small>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">Q</div>
                            <div class="step-content">
                                <strong>质量值行</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="font-family: monospace; font-size: 1.2em; margin-right: 20px;">
                                        I I I I I I I I 9 I G 9 I C
                                    </div>
                                    <div style="font-size: 0.9em;">
                                        Q40 Q40 Q40 Q40 Q24 Q40 Q38 Q24 Q40 Q34
                                    </div>
                                </div>
                                <small>ASCII字符编码的质量值 (Phred+33)</small>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# 质量值解码计算
ASCII字符 → Phred质量值 → 错误概率 → 准确率

I (ASCII 73) → Q40 → 10^(-4.0) → 99.99%
9 (ASCII 57) → Q24 → 10^(-2.4) → 99.60%
C (ASCII 67) → Q34 → 10^(-3.4) → 99.96%

公式: Phred = -10 × log10(错误概率)
     准确率 = 1 - 错误概率
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>质量值(Phred)</th>
                                <th>错误概率</th>
                                <th>准确率</th>
                                <th>ASCII字符(+33)</th>
                                <th>质量等级</th>
                            </tr>
                            <tr>
                                <td><strong>10</strong></td>
                                <td>1/10</td>
                                <td>90%</td>
                                <td>+</td>
                                <td>低质量</td>
                            </tr>
                            <tr>
                                <td><strong>20</strong></td>
                                <td>1/100</td>
                                <td>99%</td>
                                <td>5</td>
                                <td>中等质量</td>
                            </tr>
                            <tr>
                                <td><strong>30</strong></td>
                                <td>1/1,000</td>
                                <td>99.9%</td>
                                <td>?</td>
                                <td>高质量</td>
                            </tr>
                            <tr>
                                <td><strong>40</strong></td>
                                <td>1/10,000</td>
                                <td>99.99%</td>
                                <td>I</td>
                                <td>极高质量</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>质量控制指标</h3>
                            <div class="platform-card">
                                <h4>📊 序列质量指标</h4>
                                <ul>
                                    <li><strong>平均质量值：</strong>整体序列质量</li>
                                    <li><strong>质量分布：</strong>位置特异性质量</li>
                                    <li><strong>N含量：</strong>未确定碱基比例</li>
                                    <li><strong>GC含量：</strong>碱基组成偏好</li>
                                    <li><strong>序列长度：</strong>读长分布统计</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>🔍 常见质量问题</h4>
                                <ul>
                                    <li><strong>3'端质量下降：</strong>测序化学限制</li>
                                    <li><strong>接头污染：</strong>文库构建问题</li>
                                    <li><strong>重复序列：</strong>PCR重复或光学重复</li>
                                    <li><strong>GC偏好：</strong>聚合酶偏好性</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>质量控制工具</h3>
                            <div class="platform-card">
                                <h4>🛠️ 主要QC工具</h4>
                                <ul>
                                    <li><strong>FastQC：</strong>最流行的质量评估工具</li>
                                    <li><strong>MultiQC：</strong>多样本质量报告整合</li>
                                    <li><strong>fastp：</strong>快速质量控制和过滤</li>
                                    <li><strong>Trimmomatic：</strong>接头去除和质量修剪</li>
                                    <li><strong>cutadapt：</strong>接头序列去除</li>
                                </ul>
                            </div>

                            <div class="platform-card">
                                <h4>✂️ 数据预处理</h4>
                                <ul>
                                    <li><strong>质量修剪：</strong>去除低质量碱基</li>
                                    <li><strong>接头去除：</strong>清除文库构建接头</li>
                                    <li><strong>长度过滤：</strong>保留合适长度序列</li>
                                    <li><strong>重复去除：</strong>PCR重复序列处理</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 质量控制要点：</strong>严格的质量控制是后续分析成功的基础，低质量数据会严重影响变异检测、基因组组装等下游分析的准确性。
                    </div>
                `
            },
            {
                title: "测序错误模式与校正策略",
                subtitle: "不同测序平台的错误特征与生物信息学校正方法",
                content: `
                    <h2>测序错误的系统性分析</h2>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔬 Illumina错误模式</h4>
                            <p><strong>主要错误类型：</strong></p>
                            <ul>
                                <li><strong>替换错误：</strong>占总错误的>95%</li>
                                <li><strong>质量衰减：</strong>3'端质量下降</li>
                                <li><strong>GC偏好：</strong>极端GC区域覆盖不均</li>
                                <li><strong>光学重复：</strong>簇识别错误</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>错误率：</strong>0.1-1% (QV20-30)</li>
                                    <li><strong>错误分布：</strong>随机分布</li>
                                    <li><strong>校正方法：</strong>质量过滤、共识序列</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ Ion Torrent错误模式</h4>
                            <p><strong>同聚物错误：</strong></p>
                            <ul>
                                <li><strong>插入/缺失：</strong>占总错误的>80%</li>
                                <li><strong>同聚物长度：</strong>连续相同碱基难准确测定</li>
                                <li><strong>信号饱和：</strong>长同聚物信号重叠</li>
                                <li><strong>载流子效应：</strong>相邻孔信号干扰</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>错误率：</strong>1-2% (QV17-20)</li>
                                    <li><strong>错误热点：</strong>同聚物区域</li>
                                    <li><strong>校正方法：</strong>流空间算法、Torrent Suite</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🧬 PacBio错误模式</h4>
                            <p><strong>随机错误：</strong></p>
                            <ul>
                                <li><strong>插入/缺失：</strong>主要错误类型</li>
                                <li><strong>随机分布：</strong>无系统性偏好</li>
                                <li><strong>HiFi校正：</strong>多轮测序共识</li>
                                <li><strong>动力学信息：</strong>聚合酶停顿检测修饰</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>CLR错误率：</strong>10-15%</li>
                                    <li><strong>HiFi错误率：</strong><0.1% (QV30+)</li>
                                    <li><strong>校正方法：</strong>环形共识、Arrow算法</li>
                                </ul>
                            </div>
                        </div>

                        <div class="platform-card">
                            <h4>🔌 Nanopore错误模式</h4>
                            <p><strong>复杂错误：</strong></p>
                            <ul>
                                <li><strong>插入/缺失：</strong>主要错误类型</li>
                                <li><strong>系统性偏好：</strong>特定k-mer模式</li>
                                <li><strong>基线漂移：</strong>电流信号变化</li>
                                <li><strong>马达蛋白：</strong>DNA通过速度不均</li>
                            </ul>
                            <div class="tech-specs">
                                <ul>
                                    <li><strong>错误率：</strong>5-15% (持续改进)</li>
                                    <li><strong>错误模式：</strong>上下文相关</li>
                                    <li><strong>校正方法：</strong>Medaka、Flye算法</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# 错误校正算法示例 - PacBio HiFi
原始CLR reads (10-15%错误率):
Read1: ATCGATCGATCG...
Read2: ATCGATCGATCG...
Read3: ATCGATCGATCG...
...
Read15: ATCGATCGATCG...

多序列比对和共识:
Position:  123456789012
Read1:     ATCGATCGATCG
Read2:     ATCGATCGATCG
Read3:     ATCGATCGATCG
Consensus: ATCGATCGATCG (QV40+)

# 错误校正效果
输入: 15个CLR reads, 每个85%准确率
输出: 1个HiFi read, >99.9%准确率
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>生物信息学校正策略</h3>
                            <div class="tech-box">
                                <h4>🔧 短读长校正</h4>
                                <ul>
                                    <li><strong>k-mer频率：</strong>基于k-mer出现频率校正</li>
                                    <li><strong>重叠校正：</strong>利用序列重叠信息</li>
                                    <li><strong>质量加权：</strong>结合质量值进行校正</li>
                                    <li><strong>参考基因组：</strong>基于已知序列校正</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>📏 长读长校正</h4>
                                <ul>
                                    <li><strong>自校正：</strong>长读长间重叠校正</li>
                                    <li><strong>混合校正：</strong>短读长辅助校正</li>
                                    <li><strong>共识算法：</strong>多轮测序共识</li>
                                    <li><strong>图算法：</strong>基于图结构的校正</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>校正工具与算法</h3>
                            <div class="tech-box">
                                <h4>🛠️ 主要校正工具</h4>
                                <ul>
                                    <li><strong>Quorum：</strong>k-mer频率校正</li>
                                    <li><strong>BFC：</strong>Bloom filter校正</li>
                                    <li><strong>Canu：</strong>长读长自校正</li>
                                    <li><strong>Racon：</strong>共识序列生成</li>
                                    <li><strong>Pilon：</strong>参考基因组校正</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>📈 校正效果评估</h4>
                                <ul>
                                    <li><strong>准确率提升：</strong>校正前后错误率对比</li>
                                    <li><strong>覆盖度保持：</strong>校正过程中数据损失</li>
                                    <li><strong>计算成本：</strong>时间和内存消耗</li>
                                    <li><strong>下游影响：</strong>对组装和变异检测的影响</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 校正策略选择：</strong>不同测序平台需要针对性的错误校正策略，了解错误模式有助于选择合适的校正方法和参数。
                    </div>
                `
            },
            {
                title: "文库构建技术详解",
                subtitle: "DNA和RNA文库构建的关键步骤与技术要点",
                content: `
                    <h2>文库构建：测序成功的关键步骤</h2>

                    <div class="principle-diagram">
                        <h4>🧪 DNA文库构建详细流程</h4>

                        <div class="sequencing-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>DNA质量检测</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="margin-right: 20px;">
                                        <div style="width: 60px; height: 8px; background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c); border-radius: 4px;"></div>
                                        <small>完整性检测</small>
                                    </div>
                                    <div>
                                        <strong>要求：</strong>浓度≥10ng/μL, DIN≥7, A260/280=1.8-2.0
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>DNA片段化</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="margin-right: 20px;">
                                        <div style="width: 80px; height: 4px; background: #3498db; margin: 2px 0;"></div>
                                        <div style="width: 60px; height: 4px; background: #3498db; margin: 2px 0;"></div>
                                        <div style="width: 70px; height: 4px; background: #3498db; margin: 2px 0;"></div>
                                        <small>片段化结果</small>
                                    </div>
                                    <div>
                                        <strong>方法：</strong>超声波、转座酶、限制性内切酶
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>末端修复与接头连接</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="margin-right: 20px;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 10px; height: 4px; background: #e74c3c;"></div>
                                            <div style="width: 60px; height: 4px; background: #3498db;"></div>
                                            <div style="width: 10px; height: 4px; background: #27ae60;"></div>
                                        </div>
                                        <small>接头-插入片段-接头</small>
                                    </div>
                                    <div>
                                        <strong>功能：</strong>平端修复、A尾添加、Y型接头连接
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <strong>PCR扩增与纯化</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="margin-right: 20px;">
                                        <div style="font-size: 1.5em;">1→2→4→8→16</div>
                                        <small>指数扩增</small>
                                    </div>
                                    <div>
                                        <strong>参数：</strong>8-15个循环，避免过度扩增
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="sequencing-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <strong>质量验证与定量</strong>
                                <div style="display: flex; align-items: center; margin: 10px 0;">
                                    <div style="margin-right: 20px;">
                                        <div style="width: 50px; height: 30px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 5px; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8em;">
                                            qPCR
                                        </div>
                                        <small>精确定量</small>
                                    </div>
                                    <div>
                                        <strong>检测：</strong>浓度、大小分布、接头二聚体
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">样本制备<br>质量检测</div>
                        <div class="flowchart-step">DNA片段化<br>大小选择</div>
                        <div class="flowchart-step">末端修复<br>接头连接</div>
                        <div class="flowchart-step">PCR扩增<br>纯化富集</div>
                        <div class="flowchart-step">质量验证<br>定量上机</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>DNA文库构建策略</h3>
                            <div class="platform-card">
                                <h4>🧬 片段化方法</h4>
                                <ul>
                                    <li><strong>物理片段化：</strong>超声波打断、雾化</li>
                                    <li><strong>酶切片段化：</strong>限制性内切酶、转座酶</li>
                                    <li><strong>化学片段化：</strong>DNase I处理</li>
                                    <li><strong>机械剪切：</strong>针头剪切、微流控</li>
                                </ul>
                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>目标大小：</strong>150-800bp (Illumina)</li>
                                        <li><strong>分布特征：</strong>正态分布最佳</li>
                                        <li><strong>质量要求：</strong>避免过度降解</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>🔗 接头连接技术</h4>
                                <ul>
                                    <li><strong>Y型接头：</strong>标准Illumina接头</li>
                                    <li><strong>条形码接头：</strong>样本多重化标记</li>
                                    <li><strong>UMI接头：</strong>分子标记去重</li>
                                    <li><strong>定向接头：</strong>保持链方向信息</li>
                                </ul>
                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>连接效率：</strong>>90%</li>
                                        <li><strong>接头二聚体：</strong><5%</li>
                                        <li><strong>插入片段：</strong>目标大小分布</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3>RNA文库构建策略</h3>
                            <div class="platform-card">
                                <h4>🧪 RNA富集方法</h4>
                                <ul>
                                    <li><strong>Poly-A选择：</strong>mRNA特异性富集</li>
                                    <li><strong>rRNA去除：</strong>RiboMinus、RiboZero</li>
                                    <li><strong>总RNA：</strong>包含所有RNA类型</li>
                                    <li><strong>小RNA：</strong>miRNA、siRNA富集</li>
                                </ul>
                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>mRNA比例：</strong>1-5% (总RNA)</li>
                                        <li><strong>rRNA去除率：</strong>>95%</li>
                                        <li><strong>完整性要求：</strong>RIN ≥ 7</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="platform-card">
                                <h4>🔄 逆转录策略</h4>
                                <ul>
                                    <li><strong>随机引物：</strong>全转录本覆盖</li>
                                    <li><strong>Oligo-dT：</strong>3'端特异性</li>
                                    <li><strong>链特异性：</strong>保留链方向信息</li>
                                    <li><strong>UMI标记：</strong>定量准确性</li>
                                </ul>
                                <div class="tech-specs">
                                    <ul>
                                        <li><strong>逆转录效率：</strong>>80%</li>
                                        <li><strong>5'偏好性：</strong>最小化</li>
                                        <li><strong>GC偏好性：</strong>均一覆盖</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# 文库构建质量评估
# 1. 片段大小分布检测
Bioanalyzer/TapeStation分析:
- 主峰位置: 300-400bp (目标)
- 峰形: 正态分布
- 接头二聚体: <5% (120-140bp)

# 2. 文库浓度定量
qPCR定量 (更准确):
- 有效浓度: 2-10 nM
- 扩增效率: 90-110%
- 特异性: 单一熔解峰

# 3. 测序质量预测
文库质量 → 测序质量:
- 高质量文库: Q30 > 85%
- 中等质量: Q30 = 70-85%
- 低质量文库: Q30 < 70%
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>⚡ PCR-free文库</h4>
                            <p><strong>优势：</strong></p>
                            <ul>
                                <li>无PCR偏好性</li>
                                <li>GC覆盖均一</li>
                                <li>重复序列保真</li>
                                <li>定量更准确</li>
                            </ul>
                            <p><strong>要求：</strong>高质量、高浓度DNA (>1μg)</p>
                        </div>

                        <div class="platform-card">
                            <h4>🔄 PCR扩增文库</h4>
                            <p><strong>优势：</strong></p>
                            <ul>
                                <li>起始量要求低</li>
                                <li>适合降解样本</li>
                                <li>成本相对较低</li>
                                <li>操作相对简单</li>
                            </ul>
                            <p><strong>注意：</strong>控制PCR循环数 (8-15 cycles)</p>
                        </div>

                        <div class="platform-card">
                            <h4>🏷️ 多重化策略</h4>
                            <p><strong>条形码设计：</strong></p>
                            <ul>
                                <li>汉明距离 ≥ 3</li>
                                <li>避免同聚物</li>
                                <li>GC含量平衡</li>
                                <li>长度一致性</li>
                            </ul>
                            <p><strong>样本数：</strong>96-384个样本/lane</p>
                        </div>

                        <div class="platform-card">
                            <h4>🎯 特殊应用文库</h4>
                            <p><strong>专用文库类型：</strong></p>
                            <ul>
                                <li><strong>ChIP-seq：</strong>蛋白-DNA相互作用</li>
                                <li><strong>ATAC-seq：</strong>染色质可及性</li>
                                <li><strong>Hi-C：</strong>染色体三维结构</li>
                                <li><strong>单细胞：</strong>细胞特异性分析</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 文库构建要点：</strong>文库质量直接影响测序结果，每个步骤都需要严格的质量控制，特别是片段大小分布和接头连接效率。
                    </div>
                `
            },
            {
                title: "测序数据存储与管理",
                subtitle: "大数据时代的测序数据存储策略与管理最佳实践",
                content: `
                    <h2>测序数据：从TB到PB的存储挑战</h2>

                    <div class="highlight-box">
                        <h3>数据量爆炸式增长</h3>
                        <p>一个人类全基因组测序(30X)产生约<strong>100GB</strong>原始数据，大型项目可产生<strong>PB级</strong>数据量，对存储和管理提出巨大挑战。</p>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>测序类型</th>
                                <th>原始数据量</th>
                                <th>分析数据量</th>
                                <th>存储周期</th>
                                <th>访问频率</th>
                            </tr>
                            <tr>
                                <td><strong>WGS (30X)</strong></td>
                                <td>100-150 GB</td>
                                <td>50-100 GB</td>
                                <td>长期</td>
                                <td>低</td>
                            </tr>
                            <tr>
                                <td><strong>WES</strong></td>
                                <td>8-15 GB</td>
                                <td>5-10 GB</td>
                                <td>长期</td>
                                <td>中等</td>
                            </tr>
                            <tr>
                                <td><strong>RNA-seq</strong></td>
                                <td>5-20 GB</td>
                                <td>2-10 GB</td>
                                <td>中期</td>
                                <td>高</td>
                            </tr>
                            <tr>
                                <td><strong>单细胞RNA-seq</strong></td>
                                <td>50-500 GB</td>
                                <td>20-200 GB</td>
                                <td>长期</td>
                                <td>高</td>
                            </tr>
                            <tr>
                                <td><strong>长读长WGS</strong></td>
                                <td>200-1000 GB</td>
                                <td>100-500 GB</td>
                                <td>长期</td>
                                <td>低</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>存储架构设计</h3>
                            <div class="tech-box">
                                <h4>🏗️ 分层存储策略</h4>
                                <ul>
                                    <li><strong>热存储：</strong>SSD，频繁访问数据</li>
                                    <li><strong>温存储：</strong>HDD，定期访问数据</li>
                                    <li><strong>冷存储：</strong>磁带/云存储，归档数据</li>
                                    <li><strong>自动迁移：</strong>基于访问模式自动分层</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>☁️ 云存储方案</h4>
                                <ul>
                                    <li><strong>AWS S3：</strong>多种存储类别</li>
                                    <li><strong>Google Cloud：</strong>Nearline/Coldline</li>
                                    <li><strong>Azure Blob：</strong>热/冷/归档层</li>
                                    <li><strong>混合云：</strong>本地+云端结合</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h3>数据压缩技术</h3>
                            <div class="tech-box">
                                <h4>🗜️ 压缩算法比较</h4>
                                <ul>
                                    <li><strong>gzip：</strong>通用，压缩比3-5倍</li>
                                    <li><strong>bzip2：</strong>高压缩比，速度慢</li>
                                    <li><strong>lz4：</strong>高速压缩，压缩比中等</li>
                                    <li><strong>专用压缩：</strong>CRAM、BCF格式</li>
                                </ul>
                            </div>

                            <div class="tech-box">
                                <h4>📁 文件格式优化</h4>
                                <ul>
                                    <li><strong>CRAM：</strong>参考基因组压缩</li>
                                    <li><strong>BCF：</strong>二进制VCF格式</li>
                                    <li><strong>HDF5：</strong>科学数据格式</li>
                                    <li><strong>Parquet：</strong>列式存储格式</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="code-block">
# 数据生命周期管理示例
# 1. 原始数据 (FASTQ)
生成后: 立即备份到冷存储
保留期: 2-5年 (法规要求)
访问: 极少，仅用于重新分析

# 2. 比对数据 (BAM/CRAM)
生成后: 热存储 (1个月) → 温存储 (1年) → 冷存储
压缩: BAM → CRAM (50-80%空间节省)
索引: 保持快速随机访问

# 3. 变异数据 (VCF/BCF)
生成后: 长期热存储 (频繁查询)
压缩: VCF → BCF + tabix索引
备份: 多地点备份

# 4. 分析结果
报告: 长期保存
中间文件: 定期清理 (6个月)
日志文件: 短期保存 (1个月)
                    </div>

                    <div class="platform-grid">
                        <div class="platform-card">
                            <h4>🔐 数据安全策略</h4>
                            <ul>
                                <li><strong>加密存储：</strong>静态数据加密</li>
                                <li><strong>传输加密：</strong>HTTPS/SFTP</li>
                                <li><strong>访问控制：</strong>基于角色的权限</li>
                                <li><strong>审计日志：</strong>操作记录追踪</li>
                                <li><strong>备份验证：</strong>定期完整性检查</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>📊 元数据管理</h4>
                            <ul>
                                <li><strong>样本信息：</strong>来源、处理记录</li>
                                <li><strong>实验参数：</strong>测序平台、参数</li>
                                <li><strong>分析流程：</strong>软件版本、参数</li>
                                <li><strong>质量指标：</strong>QC结果记录</li>
                                <li><strong>关联关系：</strong>数据间关联</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>⚡ 性能优化</h4>
                            <ul>
                                <li><strong>并行I/O：</strong>多线程读写</li>
                                <li><strong>缓存策略：</strong>热点数据缓存</li>
                                <li><strong>网络优化：</strong>带宽和延迟优化</li>
                                <li><strong>存储优化：</strong>RAID配置</li>
                                <li><strong>预取策略：</strong>预测性数据加载</li>
                            </ul>
                        </div>

                        <div class="platform-card">
                            <h4>💰 成本控制</h4>
                            <ul>
                                <li><strong>生命周期策略：</strong>自动分层迁移</li>
                                <li><strong>重复数据删除：</strong>节省存储空间</li>
                                <li><strong>压缩优化：</strong>平衡压缩比和速度</li>
                                <li><strong>云成本监控：</strong>使用量跟踪</li>
                                <li><strong>容量规划：</strong>预测存储需求</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 最佳实践：</strong>建立完善的数据管理策略，包括自动化的生命周期管理、多层备份、元数据记录和成本优化，确保数据的长期可用性和可追溯性。
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成菜单
            const menuDropdown = document.getElementById('menuDropdown');
            slides.forEach((slide, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = 'menu-item';
                menuItem.textContent = `${index + 1}. ${slide.title}`;
                menuItem.onclick = () => {
                    goToSlide(index);
                    toggleMenu();
                };
                menuDropdown.appendChild(menuItem);
            });

            // 生成幻灯片
            const container = document.querySelector('.container');
            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = 'slide';
                if (index === 0) slideElement.classList.add('active');

                slideElement.innerHTML = `
                    <h1>${slide.title}</h1>
                    <div class="subtitle">${slide.subtitle}</div>
                    ${slide.content}
                `;

                container.appendChild(slideElement);
            });

            updateNavigation();
            updateProgress();
        }

        // 导航功能
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                currentSlideIndex++;
                showSlide(currentSlideIndex);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                currentSlideIndex--;
                showSlide(currentSlideIndex);
            }
        }

        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                showSlide(currentSlideIndex);
            }
        }

        function showSlide(index) {
            const slides = document.querySelectorAll('.slide');
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });

            updateNavigation();
            updateProgress();
            updateNavbar();
            document.getElementById('currentSlide').textContent = index + 1;
        }

        function updateNavbar() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));

            // 根据当前幻灯片索引高亮对应的导航项
            if (currentSlideIndex >= 0 && currentSlideIndex < navItems.length) {
                navItems[currentSlideIndex]?.classList.add('active');
            }
        }

        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
        }

        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 菜单功能
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('show');
        }

        // 自动播放功能
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay();
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        function resetPresentation() {
            currentSlideIndex = 0;
            showSlide(currentSlideIndex);
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
        }

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    goToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    goToSlide(totalSlides - 1);
                    break;
                case 'Escape':
                    if (document.getElementById('menuDropdown').classList.contains('show')) {
                        toggleMenu();
                    }
                    break;
                case 'f':
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            const menu = document.querySelector('.slide-menu');
            const dropdown = document.getElementById('menuDropdown');
            if (!menu.contains(e.target) && dropdown.classList.contains('show')) {
                toggleMenu();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPresentation);
    </script>
</body>
</html>
