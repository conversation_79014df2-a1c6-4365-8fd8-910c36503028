<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题八：高通量测序技术在植物保护中的应用 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2E8B57;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #228B22;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2E8B57;
            padding-bottom: 10px;
        }

        h3 {
            color: #2E8B57;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #228B22;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #228B22;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #2E8B57;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #228B22;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #228B22;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #2E8B57;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #228B22;
        }

        .highlight-box {
            background: linear-gradient(135deg, #2E8B57, #228B22);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #2E8B57;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #228B22;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(46, 139, 87, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(46, 139, 87, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2E8B57, #228B22);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(46, 139, 87, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(46, 139, 87, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 300px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #228B22;
        }

        .menu-item.current {
            background: #e3f2fd;
            border-left-color: #2E8B57;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture8_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">12</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式" style="position: fixed; top: 30px; right: 100px; background: rgba(46, 139, 87, 0.9); color: white; border: none; padding: 12px 16px; border-radius: 50%; cursor: pointer; font-size: 1.2em; backdrop-filter: blur(10px); transition: all 0.3s ease; z-index: 1000;">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 添加更多样式
        const additionalStyles = `
            .two-column {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin: 20px 0;
            }

            .comparison-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .comparison-table th,
            .comparison-table td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }

            .comparison-table th {
                background: #228B22;
                color: white;
                font-weight: bold;
            }

            .comparison-table tr:nth-child(even) {
                background: #f8f9fa;
            }

            .flowchart {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin: 30px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }

            .flowchart-step {
                background: #228B22;
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                text-align: center;
                flex: 1;
                margin: 0 10px;
                position: relative;
            }

            .flowchart-step::after {
                content: '→';
                position: absolute;
                right: -25px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.5em;
                color: #228B22;
            }

            .flowchart-step:last-child::after {
                display: none;
            }

            .image-placeholder {
                width: 100%;
                height: 200px;
                background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #7f8c8d;
                font-size: 1.1em;
                margin: 20px 0;
            }

            .key-points {
                background: #fff9c4;
                border: 2px solid #fbc02d;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
            }

            .key-points h4 {
                color: #e65100 !important;
                margin-bottom: 15px;
                font-size: 1.2em;
                font-weight: bold;
            }

            .key-points ul li {
                color: #333 !important;
            }

            .tools-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .tool-card {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .tool-card:hover {
                border-color: #228B22;
                transform: translateY(-5px);
                box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            }

            .tool-card h4 {
                color: #1B5E20 !important;
                margin-bottom: 10px;
                font-weight: bold;
            }

            .tool-card p {
                color: #333 !important;
                line-height: 1.6;
            }

            /* 确保所有文本颜色对比度良好 */
            .info-box strong {
                color: #1B5E20 !important;
            }

            .warning-box strong {
                color: #E65100 !important;
            }

            .algorithm-box h3 {
                color: #2E7D32 !important;
            }

            .flowchart-step {
                color: #FFFFFF !important;
                font-weight: bold;
            }

            .comparison-table th {
                color: #FFFFFF !important;
            }

            .comparison-table td {
                color: #333 !important;
            }
        `;

        // 添加样式到页面
        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // 幻灯片数据
        const slides = [
            {
                title: "专题八：高通量测序技术在植物保护中的应用",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🌱 高通量测序技术在植物保护中的应用图形摘要</h3>
                        <svg width="100%" height="520" viewBox="0 0 1000 520" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient8" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="plantGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#27ae60" />
                                    <stop offset="25%" style="stop-color:#2ecc71" />
                                    <stop offset="50%" style="stop-color:#f39c12" />
                                    <stop offset="75%" style="stop-color:#e74c3c" />
                                    <stop offset="100%" style="stop-color:#8e44ad" />
                                </linearGradient>
                                <linearGradient id="protectionGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead8" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="520" fill="url(#bgGradient8)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">高通量测序技术在植物保护中的应用工作流</text>
                            
                            <!-- 植物病原体类型 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">主要植物病原体与研究对象</text>
                                
                                <!-- 真菌病原体 -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">真菌病原体</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">效应子|毒素</text>
                                </g>
                                
                                <!-- 细菌病原体 -->
                                <g transform="translate(190, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">细菌病原体</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">毒力|抗药性</text>
                                </g>
                                
                                <!-- 病毒病原体 -->
                                <g transform="translate(330, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">病毒病原体</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">变异|进化</text>
                                </g>
                                
                                <!-- 卵菌病原体 -->
                                <g transform="translate(470, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#3498db" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">卵菌病原体</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">RXLR效应子</text>
                                </g>
                                
                                <!-- 植物抗性 -->
                                <g transform="translate(610, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#9b59b6" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">植物抗性</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">R基因|QTL</text>
                                </g>
                                
                                <!-- 研究目标 -->
                                <g transform="translate(750, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">研究目标:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">诊断 | 防控 | 育种 | 管理</text>
                                </g>
                            </g>
                            
                            <!-- 测序技术应用 - 中上部 -->
                            <g transform="translate(50, 160)">
                                <!-- 全基因组测序 -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">全基因组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">参考基因组</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">基因注释</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">比较基因组</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">进化分析</text>
                                </g>
                                
                                <!-- 重测序 -->
                                <g transform="translate(130, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">重测序</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">变异检测</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">群体遗传</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">抗药性</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">致病性</text>
                                </g>
                                
                                <!-- 转录组测序 -->
                                <g transform="translate(260, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">转录组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">表达分析</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">互作机制</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">抗性响应</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">调控网络</text>
                                </g>
                                
                                <!-- 扩增子测序 -->
                                <g transform="translate(390, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">扩增子</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">靶向检测</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">分子诊断</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">种群监测</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">快速鉴定</text>
                                </g>
                                
                                <!-- 宏基因组 -->
                                <g transform="translate(520, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">宏基因组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">微生物群落</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">生态功能</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">环境监测</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">生防菌</text>
                                </g>
                                
                                <!-- 表观基因组 -->
                                <g transform="translate(650, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e91e63" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">表观基因组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">DNA甲基化</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">组蛋白修饰</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">基因沉默</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">抗性调控</text>
                                </g>
                                
                                <!-- 单细胞测序 -->
                                <g transform="translate(780, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#34495e" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">单细胞</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">细胞异质性</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">发育轨迹</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">免疫细胞</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">抗性机制</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M110,35 L120,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                                <path d="M240,35 L250,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                                <path d="M370,35 L380,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                                <path d="M500,35 L510,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                                <path d="M630,35 L640,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                                <path d="M760,35 L770,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead8)"/>
                            </g>
                            
                            <!-- 数据分析流程 - 中下部 -->
                            <g transform="translate(50, 270)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">植物保护基因组数据分析核心步骤</text>
                                
                                <!-- 质量控制 -->
                                <g transform="translate(50, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">质量控制</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">FastQC检查</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">宿主去除</text>
                                </g>
                                
                                <!-- 基因组组装 -->
                                <g transform="translate(140, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">基因组组装</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">序列拼接</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">基因预测</text>
                                </g>
                                
                                <!-- 功能注释 -->
                                <g transform="translate(230, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">功能注释</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">效应子预测</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">毒力基因</text>
                                </g>
                                
                                <!-- 比较分析 -->
                                <g transform="translate(320, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">比较分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">同源基因</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">进化分析</text>
                                </g>
                                
                                <!-- 变异检测 -->
                                <g transform="translate(410, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">变异检测</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">SNP/InDel</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">结构变异</text>
                                </g>
                                
                                <!-- 表达分析 -->
                                <g transform="translate(500, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">表达分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">差异表达</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">通路富集</text>
                                </g>
                                
                                <!-- 互作分析 -->
                                <g transform="translate(590, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">互作分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">宿主-病原</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">网络构建</text>
                                </g>
                                
                                <!-- 分子诊断 -->
                                <g transform="translate(680, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">分子诊断</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">标记开发</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">检测体系</text>
                                </g>
                                
                                <!-- 应用转化 -->
                                <g transform="translate(770, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">应用转化</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">精准防控</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">分子育种</text>
                                </g>
                            </g>
                            
                            <!-- 应用领域 - 底部 -->
                            <g transform="translate(50, 390)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">植物保护基因组学应用领域</text>
                                
                                <!-- 病害诊断 -->
                                <g transform="translate(80, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">病害</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">诊断</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 快速检测</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 种群鉴定</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 抗药性检测</text>
                                </g>
                                
                                <!-- 抗性育种 -->
                                <g transform="translate(220, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">抗性</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">育种</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 基因挖掘</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 分子标记</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 基因编辑</text>
                                </g>
                                
                                <!-- 精准防控 -->
                                <g transform="translate(360, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">精准</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">防控</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 靶向药物</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 生物防治</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 综合管理</text>
                                </g>
                                
                                <!-- 流行病学 -->
                                <g transform="translate(500, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">流行</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">病学</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 传播追踪</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 风险评估</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 预警系统</text>
                                </g>
                                
                                <!-- 生态管理 -->
                                <g transform="translate(640, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">生态</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">管理</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 微生物群落</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 生态平衡</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 可持续发展</text>
                                </g>
                                
                                <!-- 药物开发 -->
                                <g transform="translate(780, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e91e63" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">药物</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">开发</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 新靶点发现</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 作用机制</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 抗性管理</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M140,450 Q170,430 200,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M280,450 Q310,430 340,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M420,450 Q450,430 480,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M560,450 Q590,430 620,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M700,450 Q730,430 760,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>掌握高通量测序技术在植物保护中的应用原理和方法，理解病原体基因组特征与致病机制，熟悉分子诊断技术和抗性基因挖掘策略，了解精准防控技术的发展前景。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>植物病原体基因组学基础</li>
                                <li>病原体测序策略与方法</li>
                                <li>比较基因组学分析</li>
                                <li>植物病害分子诊断</li>
                                <li>病害监测与预警系统</li>
                                <li>植物-病原体互作机制</li>
                                <li>抗性基因挖掘与利用</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>理解植物保护中的基因组学应用</li>
                                <li>掌握病原体测序分析方法</li>
                                <li>熟悉分子诊断技术原理</li>
                                <li>了解抗性机制研究策略</li>
                                <li>认识精准防控技术发展</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "植物保护基因组学概述",
                subtitle: "第一部分：基础概念与技术框架",
                content: `
                    <h2>植物保护基因组学的核心概念</h2>

                    <div class="info-box">
                        <strong>💡 定义：</strong>植物保护基因组学是运用基因组学技术研究植物病原体、植物抗性机制、病原体-植物互作关系，为植物病害防控提供分子基础的学科。
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🦠 病原体基因组学</h4>
                            <p><strong>真菌：</strong>致病基因、毒性因子<br>
                            <strong>细菌：</strong>毒力岛、抗药性<br>
                            <strong>病毒：</strong>基因组变异、进化</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌱 植物抗性基因组学</h4>
                            <p><strong>R基因：</strong>抗病基因挖掘<br>
                            <strong>QTL：</strong>数量性状位点<br>
                            <strong>表观遗传：</strong>调控机制</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔬 互作基因组学</h4>
                            <p><strong>转录组：</strong>表达谱分析<br>
                            <strong>蛋白组：</strong>蛋白互作<br>
                            <strong>代谢组：</strong>代谢通路</p>
                        </div>
                        <div class="tool-card">
                            <h4>🎯 应用基因组学</h4>
                            <p><strong>分子诊断：</strong>快速检测<br>
                            <strong>分子育种：</strong>抗性改良<br>
                            <strong>精准防控：</strong>靶向治疗</p>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>技术优势</h3>
                        <p><strong>高通量：</strong>大规模基因组分析 → <strong>高精度：</strong>单核苷酸水平检测 → <strong>高效率：</strong>快速诊断与鉴定 → <strong>高特异性：</strong>精准靶向防控</p>
                    </div>
                `
            },
            {
                title: "植物病原体基因组学",
                subtitle: "第二部分：病原体基因组特征与分析",
                content: `
                    <h2>主要植物病原体类型</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>病原体类型</th>
                                <th>基因组特征</th>
                                <th>致病机制</th>
                                <th>研究重点</th>
                            </tr>
                            <tr>
                                <td><strong>真菌病原体</strong></td>
                                <td>10-200 Mb，复杂基因组</td>
                                <td>酶解细胞壁、毒素分泌</td>
                                <td>效应子、次生代谢</td>
                            </tr>
                            <tr>
                                <td><strong>细菌病原体</strong></td>
                                <td>1-10 Mb，质粒携带</td>
                                <td>III型分泌系统</td>
                                <td>毒力基因、抗药性</td>
                            </tr>
                            <tr>
                                <td><strong>病毒病原体</strong></td>
                                <td>3-300 kb，RNA/DNA</td>
                                <td>劫持宿主机制</td>
                                <td>基因组变异、进化</td>
                            </tr>
                            <tr>
                                <td><strong>卵菌病原体</strong></td>
                                <td>20-250 Mb，二倍体</td>
                                <td>RXLR效应子</td>
                                <td>效应子进化</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>致病基因特征</h3>
                            <ul>
                                <li><strong>效应子基因：</strong>抑制植物免疫</li>
                                <li><strong>毒力基因：</strong>侵染与定殖</li>
                                <li><strong>分泌蛋白：</strong>胞外酶系统</li>
                                <li><strong>次生代谢：</strong>毒素合成途径</li>
                            </ul>
                        </div>
                        <div>
                            <h3>基因组进化特点</h3>
                            <ul>
                                <li><strong>水平基因转移：</strong>毒力基因获得</li>
                                <li><strong>基因重复：</strong>效应子扩张</li>
                                <li><strong>转座元件：</strong>基因组重排</li>
                                <li><strong>选择压力：</strong>抗性基因进化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>效应子预测方法</h3>
                        <div class="code-block">
# 真菌效应子预测
SignalP: 信号肽预测
EffectorP: 效应子概率评分
LOCALIZER: 亚细胞定位

# 细菌效应子预测
T3SS: III型分泌信号
T4SS: IV型分泌信号
Hrp box: 调控序列识别
                        </div>
                    </div>
                `
            },
            {
                title: "病原体测序策略",
                subtitle: "测序技术选择与实验设计",
                content: `
                    <h2>测序策略选择</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">样本采集<br>与处理</div>
                        <div class="flowchart-step">DNA/RNA<br>提取</div>
                        <div class="flowchart-step">测序平台<br>选择</div>
                        <div class="flowchart-step">数据分析<br>与注释</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🧬 全基因组测序</h4>
                            <p><strong>目标：</strong>完整基因组组装<br>
                            <strong>策略：</strong>长短读长结合<br>
                            <strong>应用：</strong>参考基因组构建</p>
                        </div>
                        <div class="tool-card">
                            <h4>📊 重测序分析</h4>
                            <p><strong>目标：</strong>变异检测分析<br>
                            <strong>策略：</strong>高深度测序<br>
                            <strong>应用：</strong>群体遗传学研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔬 转录组测序</h4>
                            <p><strong>目标：</strong>基因表达分析<br>
                            <strong>策略：</strong>RNA-seq<br>
                            <strong>应用：</strong>致病机制研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>🎯 靶向测序</h4>
                            <p><strong>目标：</strong>特定基因分析<br>
                            <strong>策略：</strong>扩增子测序<br>
                            <strong>应用：</strong>抗药性检测</p>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🔬 实验设计要点</h4>
                        <ul>
                            <li><strong>样本选择：</strong>代表性菌株、不同致病型</li>
                            <li><strong>时间点：</strong>侵染过程的关键阶段</li>
                            <li><strong>对照设置：</strong>非致病菌株、处理对照</li>
                            <li><strong>重复设计：</strong>生物学重复、技术重复</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 技术挑战：</strong>宿主DNA污染、低生物量样本、复杂基因组结构等问题需要特殊的实验设计和分析策略。
                    </div>
                `
            },
            {
                title: "比较基因组学分析",
                subtitle: "病原体进化与致病性比较",
                content: `
                    <h2>比较基因组学方法</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">基因组<br>比对</div>
                        <div class="flowchart-step">同源基因<br>识别</div>
                        <div class="flowchart-step">进化分析<br>构建</div>
                        <div class="flowchart-step">功能<br>注释</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>分析类型</th>
                                <th>主要工具</th>
                                <th>分析内容</th>
                                <th>应用价值</th>
                            </tr>
                            <tr>
                                <td><strong>全基因组比对</strong></td>
                                <td>MUMmer, MAUVE</td>
                                <td>共线性、重排</td>
                                <td>进化关系分析</td>
                            </tr>
                            <tr>
                                <td><strong>同源基因分析</strong></td>
                                <td>OrthoFinder, BLAST</td>
                                <td>核心基因、特有基因</td>
                                <td>功能基因鉴定</td>
                            </tr>
                            <tr>
                                <td><strong>系统发育分析</strong></td>
                                <td>RAxML, IQ-TREE</td>
                                <td>进化树构建</td>
                                <td>分类地位确定</td>
                            </tr>
                            <tr>
                                <td><strong>选择压力分析</strong></td>
                                <td>PAML, HyPhy</td>
                                <td>dN/dS比值</td>
                                <td>适应性进化</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>核心基因组分析</h3>
                        <div class="code-block">
# 泛基因组分析流程
1. 基因预测与注释
2. 同源基因聚类 (OrthoFinder)
3. 核心基因组识别 (>95%菌株共有)
4. 附属基因组分析 (菌株特有)
5. 功能富集分析

# 致病性相关基因
效应子基因家族扩张
毒力基因水平转移
抗药性基因获得
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>重要发现</h3>
                        <p><strong>基因组可塑性：</strong>病原体基因组高度可变 → <strong>致病性进化：</strong>效应子基因快速进化 → <strong>宿主适应：</strong>特异性基因获得</p>
                    </div>
                `
            },
            {
                title: "功能基因组学研究",
                subtitle: "致病机制的分子基础",
                content: `
                    <h2>功能基因组学方法</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🧬 转录组分析</h4>
                            <p><strong>RNA-seq：</strong>全转录组表达<br>
                            <strong>时间序列：</strong>侵染过程动态<br>
                            <strong>条件比较：</strong>环境响应</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔬 蛋白组分析</h4>
                            <p><strong>质谱技术：</strong>蛋白质鉴定<br>
                            <strong>分泌蛋白组：</strong>效应子筛选<br>
                            <strong>修饰蛋白组：</strong>调控机制</p>
                        </div>
                        <div class="tool-card">
                            <h4>⚗️ 代谢组分析</h4>
                            <p><strong>小分子代谢：</strong>代谢通路<br>
                            <strong>次生代谢：</strong>毒素合成<br>
                            <strong>营养代谢：</strong>生存策略</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧪 功能验证</h4>
                            <p><strong>基因敲除：</strong>功能缺失<br>
                            <strong>互补实验：</strong>功能恢复<br>
                            <strong>异源表达：</strong>功能确认</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>转录调控网络</h3>
                            <ul>
                                <li><strong>转录因子：</strong>调控基因表达</li>
                                <li><strong>信号通路：</strong>环境感知响应</li>
                                <li><strong>表观调控：</strong>染色质修饰</li>
                                <li><strong>小RNA：</strong>转录后调控</li>
                            </ul>
                        </div>
                        <div>
                            <h3>致病性调控</h3>
                            <ul>
                                <li><strong>毒力调控子：</strong>致病基因开关</li>
                                <li><strong>环境信号：</strong>宿主识别机制</li>
                                <li><strong>代谢调控：</strong>营养获取策略</li>
                                <li><strong>应激响应：</strong>防御机制激活</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 研究策略：</strong>结合多组学数据，构建致病性调控网络，识别关键调控节点，为靶向防控提供分子靶标。
                    </div>
                `
            },
            {
                title: "植物病害分子诊断",
                subtitle: "第三部分：基于测序的快速诊断技术",
                content: `
                    <h2>分子诊断技术体系</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">样本采集<br>与处理</div>
                        <div class="flowchart-step">核酸提取<br>与扩增</div>
                        <div class="flowchart-step">测序检测<br>与分析</div>
                        <div class="flowchart-step">结果判读<br>与报告</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>诊断方法</th>
                                <th>技术原理</th>
                                <th>检测时间</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>扩增子测序</strong></td>
                                <td>特异性引物PCR+测序</td>
                                <td>4-6小时</td>
                                <td>已知病原体检测</td>
                            </tr>
                            <tr>
                                <td><strong>宏基因组测序</strong></td>
                                <td>总DNA随机测序</td>
                                <td>12-24小时</td>
                                <td>未知病原体筛查</td>
                            </tr>
                            <tr>
                                <td><strong>纳米孔测序</strong></td>
                                <td>实时单分子测序</td>
                                <td>1-2小时</td>
                                <td>现场快速检测</td>
                            </tr>
                            <tr>
                                <td><strong>数字PCR</strong></td>
                                <td>绝对定量检测</td>
                                <td>2-3小时</td>
                                <td>精确定量分析</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>诊断引物设计</h3>
                        <div class="code-block">
# 特异性引物设计原则
1. 靶标基因选择 (ITS, COI, 16S rRNA)
2. 保守区域识别 (多序列比对)
3. 引物特异性验证 (BLAST搜索)
4. PCR条件优化 (退火温度、循环数)

# 质量控制
阳性对照: 已知阳性样本
阴性对照: 无病原体样本
空白对照: 无模板对照
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 诊断优势</h4>
                        <ul>
                            <li><strong>高特异性：</strong>基因水平精确识别</li>
                            <li><strong>高敏感性：</strong>低浓度病原体检测</li>
                            <li><strong>多重检测：</strong>同时检测多种病原体</li>
                            <li><strong>定量分析：</strong>病原体载量评估</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "病害监测与预警系统",
                subtitle: "大数据驱动的智能监测",
                content: `
                    <h2>智能监测系统架构</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">数据采集<br>传感器网络</div>
                        <div class="flowchart-step">实时检测<br>分子诊断</div>
                        <div class="flowchart-step">数据分析<br>AI算法</div>
                        <div class="flowchart-step">预警发布<br>决策支持</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🌐 物联网监测</h4>
                            <p><strong>环境传感器：</strong>温湿度、光照<br>
                            <strong>图像识别：</strong>病害症状检测<br>
                            <strong>无人机巡检：</strong>大面积监测</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧬 分子检测</h4>
                            <p><strong>便携式设备：</strong>现场快速检测<br>
                            <strong>自动化平台：</strong>高通量筛查<br>
                            <strong>实时PCR：</strong>定量监测</p>
                        </div>
                        <div class="tool-card">
                            <h4>🤖 人工智能</h4>
                            <p><strong>机器学习：</strong>模式识别<br>
                            <strong>深度学习：</strong>图像分析<br>
                            <strong>预测模型：</strong>风险评估</p>
                        </div>
                        <div class="tool-card">
                            <h4>📱 移动应用</h4>
                            <p><strong>实时预警：</strong>推送通知<br>
                            <strong>决策支持：</strong>防控建议<br>
                            <strong>数据共享：</strong>区域协作</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>预测模型构建</h3>
                        <div class="code-block">
# 病害风险预测模型
输入变量:
- 环境因子 (温度、湿度、降雨)
- 病原体检测数据
- 作物生长阶段
- 历史发病记录

机器学习算法:
- 随机森林 (Random Forest)
- 支持向量机 (SVM)
- 神经网络 (Neural Network)
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>系统优势</h3>
                        <p><strong>早期预警：</strong>提前发现病害风险 → <strong>精准防控：</strong>靶向施药策略 → <strong>成本降低：</strong>减少农药使用 → <strong>效果提升：</strong>防控效率优化</p>
                    </div>
                `
            },
            {
                title: "植物-病原体互作机制",
                subtitle: "第四部分：分子互作网络解析",
                content: `
                    <h2>植物免疫系统</h2>

                    <div class="two-column">
                        <div>
                            <h3>先天免疫 (PTI)</h3>
                            <ul>
                                <li><strong>PAMP识别：</strong>病原体相关分子模式</li>
                                <li><strong>PRR受体：</strong>模式识别受体</li>
                                <li><strong>信号转导：</strong>MAPK级联反应</li>
                                <li><strong>防御响应：</strong>胼胝质沉积、ROS爆发</li>
                            </ul>
                        </div>
                        <div>
                            <h3>效应子触发免疫 (ETI)</h3>
                            <ul>
                                <li><strong>R基因识别：</strong>抗病基因产物</li>
                                <li><strong>效应子检测：</strong>直接或间接识别</li>
                                <li><strong>信号放大：</strong>免疫反应增强</li>
                                <li><strong>超敏反应：</strong>程序性细胞死亡</li>
                            </ul>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>互作类型</th>
                                <th>分子机制</th>
                                <th>研究方法</th>
                                <th>应用价值</th>
                            </tr>
                            <tr>
                                <td><strong>蛋白-蛋白互作</strong></td>
                                <td>效应子-靶标结合</td>
                                <td>Y2H, Co-IP, BiFC</td>
                                <td>靶标基因鉴定</td>
                            </tr>
                            <tr>
                                <td><strong>转录调控</strong></td>
                                <td>转录因子调控</td>
                                <td>ChIP-seq, RNA-seq</td>
                                <td>调控网络构建</td>
                            </tr>
                            <tr>
                                <td><strong>表观遗传</strong></td>
                                <td>染色质修饰</td>
                                <td>ATAC-seq, BS-seq</td>
                                <td>记忆机制研究</td>
                            </tr>
                            <tr>
                                <td><strong>代谢互作</strong></td>
                                <td>代谢物交换</td>
                                <td>LC-MS, GC-MS</td>
                                <td>营养竞争分析</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-box">
                        <strong>💡 研究前沿：</strong>单细胞测序技术揭示细胞水平的互作异质性，空间转录组学解析组织水平的互作模式，为理解复杂的植物-病原体互作提供新视角。
                    </div>
                `
            },
            {
                title: "抗性基因挖掘与利用",
                subtitle: "分子育种的基因资源",
                content: `
                    <h2>抗性基因类型与特征</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🛡️ R基因</h4>
                            <p><strong>NBS-LRR：</strong>经典抗病基因<br>
                            <strong>RLP/RLK：</strong>受体类蛋白<br>
                            <strong>特点：</strong>种族特异性抗性</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔧 QTL基因</h4>
                            <p><strong>数量性状：</strong>多基因控制<br>
                            <strong>部分抗性：</strong>减缓病害发展<br>
                            <strong>特点：</strong>持久性抗性</p>
                        </div>
                        <div class="tool-card">
                            <h4>⚡ 诱导抗性</h4>
                            <p><strong>SAR：</strong>系统获得性抗性<br>
                            <strong>ISR：</strong>诱导系统抗性<br>
                            <strong>特点：</strong>广谱抗性</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧬 人工抗性</h4>
                            <p><strong>基因编辑：</strong>CRISPR/Cas9<br>
                            <strong>转基因：</strong>外源抗性基因<br>
                            <strong>特点：</strong>定向改良</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>抗性基因挖掘流程</h3>
                        <div class="code-block">
# GWAS分析流程
1. 群体构建 (自然群体/杂交群体)
2. 表型鉴定 (抗性评价)
3. 基因型检测 (SNP芯片/重测序)
4. 关联分析 (MLM模型)
5. 候选基因验证

# 图位克隆策略
粗定位 → 精细定位 → 候选基因预测 → 功能验证
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 育种应用策略</h4>
                        <ul>
                            <li><strong>基因聚合：</strong>多个抗性基因组合</li>
                            <li><strong>分子标记：</strong>辅助选择育种</li>
                            <li><strong>基因编辑：</strong>精准基因改良</li>
                            <li><strong>抗性管理：</strong>延缓抗性丧失</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 挑战与对策：</strong>病原体快速进化导致抗性丧失，需要持续挖掘新的抗性资源，发展持久性抗性策略。
                    </div>
                `
            },
            {
                title: "生物防治应用",
                subtitle: "微生物农药与生防菌剂",
                content: `
                    <h2>生物防治策略</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">生防菌<br>筛选</div>
                        <div class="flowchart-step">机制<br>解析</div>
                        <div class="flowchart-step">基因组<br>改良</div>
                        <div class="flowchart-step">产品<br>开发</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>生防机制</th>
                                <th>分子基础</th>
                                <th>关键基因</th>
                                <th>应用实例</th>
                            </tr>
                            <tr>
                                <td><strong>抗生作用</strong></td>
                                <td>抗菌化合物合成</td>
                                <td>NRPS, PKS基因簇</td>
                                <td>枯草芽孢杆菌</td>
                            </tr>
                            <tr>
                                <td><strong>竞争作用</strong></td>
                                <td>营养竞争、空间占位</td>
                                <td>铁载体合成基因</td>
                                <td>荧光假单胞菌</td>
                            </tr>
                            <tr>
                                <td><strong>寄生作用</strong></td>
                                <td>细胞壁降解酶</td>
                                <td>几丁质酶、葡聚糖酶</td>
                                <td>木霉菌</td>
                            </tr>
                            <tr>
                                <td><strong>诱导抗性</strong></td>
                                <td>激发子分泌</td>
                                <td>效应子、MAMP</td>
                                <td>根瘤菌</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>基因组改良策略</h3>
                            <ul>
                                <li><strong>代谢工程：</strong>提高抗菌物质产量</li>
                                <li><strong>适应性改良：</strong>增强环境适应性</li>
                                <li><strong>安全性改造：</strong>消除潜在风险基因</li>
                                <li><strong>功能增强：</strong>引入新的防病机制</li>
                            </ul>
                        </div>
                        <div>
                            <h3>产品开发要点</h3>
                            <ul>
                                <li><strong>菌株稳定性：</strong>遗传稳定、功能持久</li>
                                <li><strong>制剂技术：</strong>载体选择、保护剂添加</li>
                                <li><strong>应用技术：</strong>施用方法、时机选择</li>
                                <li><strong>质量控制：</strong>活菌数、纯度检测</li>
                            </ul>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>发展趋势</h3>
                        <p><strong>合成生物学：</strong>人工设计生防菌 → <strong>微生物组工程：</strong>群落功能优化 → <strong>智能递送：</strong>靶向释放系统 → <strong>精准防控：</strong>个性化解决方案</p>
                    </div>
                `
            },
            {
                title: "课程总结",
                subtitle: "植物保护基因组学的现状与展望",
                content: `
                    <div class="highlight-box">
                        <h3>核心知识点</h3>
                        <p>本专题系统介绍了高通量测序技术在植物保护领域的应用，涵盖病原体基因组学、分子诊断、抗性机制研究等关键技术，为现代植物保护提供了强有力的技术支撑。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术成就</h3>
                            <ul>
                                <li><strong>病原体基因组：</strong>致病机制深度解析</li>
                                <li><strong>分子诊断：</strong>快速精准检测技术</li>
                                <li><strong>抗性基因：</strong>大规模资源挖掘</li>
                                <li><strong>互作机制：</strong>分子网络构建</li>
                            </ul>
                        </div>
                        <div>
                            <h3>应用前景</h3>
                            <ul>
                                <li><strong>精准防控：</strong>个性化防治方案</li>
                                <li><strong>智能监测：</strong>预警系统建设</li>
                                <li><strong>绿色防控：</strong>生物防治发展</li>
                                <li><strong>抗性育种：</strong>分子设计育种</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 学习收获</h4>
                        <ul>
                            <li><strong>理论基础：</strong>掌握植物保护基因组学核心概念</li>
                            <li><strong>技术方法：</strong>了解主要测序技术和分析方法</li>
                            <li><strong>应用能力：</strong>具备解决实际问题的技术思路</li>
                            <li><strong>发展视野：</strong>认识学科发展趋势和挑战</li>
                        </ul>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🔬 技术发展</h4>
                            <p><strong>单细胞测序：</strong>细胞水平分析<br>
                            <strong>空间组学：</strong>时空动态解析<br>
                            <strong>多组学整合：</strong>系统生物学</p>
                        </div>
                        <div class="tool-card">
                            <h4>🤖 智能化</h4>
                            <p><strong>人工智能：</strong>模式识别<br>
                            <strong>机器学习：</strong>预测建模<br>
                            <strong>自动化：</strong>高通量分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌱 可持续发展</h4>
                            <p><strong>绿色防控：</strong>环境友好<br>
                            <strong>精准农业：</strong>资源节约<br>
                            <strong>生态平衡：</strong>系统优化</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌍 全球合作</h4>
                            <p><strong>数据共享：</strong>开放科学<br>
                            <strong>标准制定：</strong>技术规范<br>
                            <strong>能力建设：</strong>人才培养</p>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 未来展望：</strong>随着测序技术的不断发展和成本的持续降低，植物保护基因组学将在保障粮食安全、促进可持续农业发展方面发挥越来越重要的作用。
                    </div>

                    <div class="algorithm-box">
                        <h3>推荐学习资源</h3>
                        <div class="code-block">
# 专业期刊
Nature Plants, Plant Cell, MPMI
Phytopathology, Plant Disease

# 数据库资源
NCBI Pathogen Detection
PHI-base: 病原体-宿主互作数据库
PlantGDB: 植物基因组数据库

# 分析工具
Galaxy: 在线分析平台
KBase: 系统生物学平台
                        </div>
                    </div>
                `
            }
        ];

        // 初始化幻灯片
        function initSlides() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            const container = document.querySelector('.container');
            container.innerHTML = '';

            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <div class="slide-header">
                        <h1 class="slide-title">${slide.title}</h1>
                        <p class="slide-subtitle">${slide.subtitle}</p>
                    </div>
                    <div class="slide-content">
                        ${slide.content}
                    </div>
                `;
                container.appendChild(slideElement);
            });

            generateMenu();
            updateProgress();
            updateNavigation();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = '';

            slides.forEach((slide, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = `menu-item ${index === currentSlideIndex ? 'current' : ''}`;
                menuItem.textContent = `${index + 1}. ${slide.title}`;
                menuItem.onclick = () => goToSlide(index);
                menuDropdown.appendChild(menuItem);
            });
        }

        // 导航功能
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                document.querySelectorAll('.slide').forEach(slide => slide.classList.remove('active'));
                document.querySelectorAll('.slide')[index].classList.add('active');

                currentSlideIndex = index;
                updateProgress();
                updateNavigation();
                generateMenu();

                // 关闭菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        }

        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 菜单切换
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                btn.textContent = '自动播放';
                isAutoPlaying = false;
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay();
                    }
                }, 5000);
                btn.textContent = '停止播放';
                isAutoPlaying = true;
            }
        }

        // 重新开始
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    goToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.getElementById('menuDropdown').classList.contains('active')) {
                        document.getElementById('menuDropdown').classList.remove('active');
                    }
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            const menu = document.querySelector('.slide-menu');
            const dropdown = document.getElementById('menuDropdown');
            if (!menu.contains(e.target) && dropdown.classList.contains('active')) {
                dropdown.classList.remove('active');
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', initSlides);
    </script>
</body>
</html>
