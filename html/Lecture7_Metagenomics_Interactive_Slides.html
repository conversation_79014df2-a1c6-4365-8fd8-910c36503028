<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题七：宏基因组测序与数据分析 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #764ba2;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        h3 {
            color: #667eea;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #764ba2;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #16a085, #2c3e50);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        /* 导航栏样式 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #764ba2;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #764ba2;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #764ba2;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #667eea;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #764ba2;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #764ba2;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(102, 126, 234, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(102, 126, 234, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(102, 126, 234, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(102, 126, 234, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 300px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #764ba2;
        }

        .menu-item.current {
            background: #e3f2fd;
            border-left-color: #667eea;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture7_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">12</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 添加更多样式
        const additionalStyles = `
            .two-column {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin: 20px 0;
            }

            .comparison-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .comparison-table th,
            .comparison-table td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }

            .comparison-table th {
                background: #764ba2;
                color: white;
                font-weight: bold;
            }

            .comparison-table tr:nth-child(even) {
                background: #f8f9fa;
            }

            .flowchart {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin: 30px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }

            .flowchart-step {
                background: #764ba2;
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                text-align: center;
                flex: 1;
                margin: 0 10px;
                position: relative;
            }

            .flowchart-step::after {
                content: '→';
                position: absolute;
                right: -25px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.5em;
                color: #764ba2;
            }

            .flowchart-step:last-child::after {
                display: none;
            }

            .image-placeholder {
                width: 100%;
                height: 200px;
                background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #7f8c8d;
                font-size: 1.1em;
                margin: 20px 0;
            }

            .key-points {
                background: #fff9c4;
                border: 2px solid #fbc02d;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
            }

            .key-points h4 {
                color: #e65100 !important;
                margin-bottom: 15px;
                font-size: 1.2em;
                font-weight: bold;
            }

            .key-points ul li {
                color: #333 !important;
            }

            .tools-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .tool-card {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .tool-card:hover {
                border-color: #764ba2;
                transform: translateY(-5px);
                box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            }

            .tool-card h4 {
                color: #4527A0 !important;
                margin-bottom: 10px;
                font-weight: bold;
            }

            .tool-card p {
                color: #333 !important;
                line-height: 1.6;
            }

            /* 确保所有文本颜色对比度良好 */
            .info-box strong {
                color: #1565C0 !important;
            }

            .warning-box strong {
                color: #E65100 !important;
            }

            .algorithm-box h3 {
                color: #2E7D32 !important;
            }

            .flowchart-step {
                color: #FFFFFF !important;
                font-weight: bold;
            }

            .comparison-table th {
                color: #FFFFFF !important;
            }

            .comparison-table td {
                color: #333 !important;
            }
        `;

        // 添加样式到页面
        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // 幻灯片数据
        const slides = [
            {
                title: "专题七：宏基因组测序与数据分析",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🦠 宏基因组测序与数据分析流程图形摘要</h3>
                        <svg width="100%" height="540" viewBox="0 0 1000 540" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient7" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="microbiomeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="25%" style="stop-color:#f39c12" />
                                    <stop offset="50%" style="stop-color:#27ae60" />
                                    <stop offset="75%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#9b59b6" />
                                </linearGradient>
                                <linearGradient id="diversityGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead7" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="540" fill="url(#bgGradient7)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">宏基因组测序技术与微生物群落分析工作流</text>
                            
                            <!-- 微生物栖息地 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">主要微生物栖息地与研究对象</text>
                                
                                <!-- 人体微生物组 -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">人体微生物组</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">肠道|皮肤|口腔</text>
                                </g>
                                
                                <!-- 环境微生物组 -->
                                <g transform="translate(190, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">环境微生物组</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">土壤|海洋|大气</text>
                                </g>
                                
                                <!-- 植物微生物组 -->
                                <g transform="translate(330, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">植物微生物组</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">根际|叶际|内生</text>
                                </g>
                                
                                <!-- 极端环境 -->
                                <g transform="translate(470, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#3498db" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">极端环境</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">高温|高盐|深海</text>
                                </g>
                                
                                <!-- 工业环境 -->
                                <g transform="translate(610, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#9b59b6" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">工业环境</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">污水|发酵|反应器</text>
                                </g>
                                
                                <!-- 研究意义 -->
                                <g transform="translate(750, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">研究意义:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">健康 | 生态 | 环境 | 产业</text>
                                </g>
                            </g>
                            
                            <!-- 技术方法 - 中上部 -->
                            <g transform="translate(50, 160)">
                                <!-- 16S扩增子 -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">16S扩增子</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">物种组成</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">标记基因</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">系统发育</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">成本低</text>
                                </g>
                                
                                <!-- 鸟枪法测序 -->
                                <g transform="translate(130, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">鸟枪法测序</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">功能分析</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">全基因组</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">新基因发现</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">信息全面</text>
                                </g>
                                
                                <!-- 宏转录组 -->
                                <g transform="translate(260, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">宏转录组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">基因表达</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">RNA测序</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">活性分析</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">功能验证</text>
                                </g>
                                
                                <!-- 宏蛋白组 -->
                                <g transform="translate(390, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">宏蛋白组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">蛋白表达</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">质谱分析</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">功能蛋白</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">代谢途径</text>
                                </g>
                                
                                <!-- 宏代谢组 -->
                                <g transform="translate(520, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">宏代谢组</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">代谢产物</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">小分子</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">生化功能</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">宿主互作</text>
                                </g>
                                
                                <!-- 单细胞测序 -->
                                <g transform="translate(650, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e91e63" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">单细胞测序</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">细胞异质性</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">稀有种群</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">功能分化</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">进化动态</text>
                                </g>
                                
                                <!-- 多组学整合 -->
                                <g transform="translate(780, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#34495e" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">多组学整合</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">系统生物学</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">网络分析</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">机制解析</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">预测模型</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M110,35 L120,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                                <path d="M240,35 L250,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                                <path d="M370,35 L380,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                                <path d="M500,35 L510,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                                <path d="M630,35 L640,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                                <path d="M760,35 L770,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead7)"/>
                            </g>
                            
                            <!-- 数据分析流程 - 中下部 -->
                            <g transform="translate(50, 270)">
                                <rect x="0" y="0" width="900" height="100" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">宏基因组数据分析核心步骤</text>
                                
                                <!-- 质量控制 -->
                                <g transform="translate(50, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">质量控制</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">FastQC检查</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">宿主去除</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">接头修剪</text>
                                </g>
                                
                                <!-- 序列处理 -->
                                <g transform="translate(140, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">序列处理</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">去重合并</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">长度过滤</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">嵌合体检测</text>
                                </g>
                                
                                <!-- 物种注释 -->
                                <g transform="translate(230, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">物种注释</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">分类学分析</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">OTU聚类</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">ASV分析</text>
                                </g>
                                
                                <!-- 功能注释 -->
                                <g transform="translate(320, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">功能注释</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">基因预测</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">KEGG/COG</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">代谢途径</text>
                                </g>
                                
                                <!-- 多样性分析 -->
                                <g transform="translate(410, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">多样性分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">α/β多样性</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">稀释曲线</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">群落结构</text>
                                </g>
                                
                                <!-- 差异分析 -->
                                <g transform="translate(500, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">差异分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">LEfSe/DESeq2</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">标志物种</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">功能差异</text>
                                </g>
                                
                                <!-- 网络分析 -->
                                <g transform="translate(590, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">网络分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">共现网络</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">关键物种</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">生态位</text>
                                </g>
                                
                                <!-- 机器学习 -->
                                <g transform="translate(680, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">机器学习</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">分类预测</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">特征选择</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">模式识别</text>
                                </g>
                                
                                <!-- 可视化 -->
                                <g transform="translate(770, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">数据可视化</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">PCoA/NMDS</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">热图/桑基图</text>
                                    <text x="0" y="54" fill="#2c3e50" font-size="9">交互式图表</text>
                                </g>
                            </g>
                            
                            <!-- 应用领域 - 底部 -->
                            <g transform="translate(50, 410)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">宏基因组学应用领域</text>
                                
                                <!-- 人类健康 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">人类</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">健康</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 疾病诊断</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 个性化医疗</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 药物开发</text>
                                </g>
                                
                                <!-- 农业生产 -->
                                <g transform="translate(180, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">农业</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">生产</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 土壤改良</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 植物保护</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 生物肥料</text>
                                </g>
                                
                                <!-- 环境监测 -->
                                <g transform="translate(310, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">环境</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">监测</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 污染评估</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 生态修复</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 气候变化</text>
                                </g>
                                
                                <!-- 工业应用 -->
                                <g transform="translate(440, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">工业</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">应用</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 生物制造</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 废物处理</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 能源生产</text>
                                </g>
                                
                                <!-- 食品安全 -->
                                <g transform="translate(570, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">食品</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">安全</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 病原检测</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 质量控制</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 发酵优化</text>
                                </g>
                                
                                <!-- 基础研究 -->
                                <g transform="translate(700, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e91e63" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">基础</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">研究</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 进化生物学</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 生态学</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 系统生物学</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M110,450 Q140,430 170,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M240,450 Q270,430 300,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M370,450 Q400,430 430,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M500,450 Q530,430 560,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M630,450 Q660,430 690,450" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解宏基因组学的基本概念和研究意义，掌握主要技术方法和实验设计原则，熟悉数据分析流程和关键算法，了解在不同领域的应用前景。</p>
                    </div>
                `
            },
            {
                title: "宏基因组学概述",
                subtitle: "第一部分：基础概念与原理",
                content: `
                    <h2>什么是宏基因组学？</h2>

                    <div class="info-box">
                        <strong>💡 定义：</strong>宏基因组学是直接从环境样本中提取所有微生物的遗传物质，进行高通量测序和分析，研究微生物群落结构、功能和生态的学科。
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>研究方法</th>
                                <th>研究对象</th>
                                <th>优势</th>
                                <th>局限性</th>
                            </tr>
                            <tr>
                                <td><strong>传统微生物学</strong></td>
                                <td>可培养微生物</td>
                                <td>研究深入、功能明确</td>
                                <td>仅覆盖<1%的微生物</td>
                            </tr>
                            <tr>
                                <td><strong>宏基因组学</strong></td>
                                <td>环境中所有微生物</td>
                                <td>全面、无偏好性</td>
                                <td>数据复杂、分析困难</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🎯 核心优势</h4>
                        <ul>
                            <li><strong>全面性：</strong>研究所有微生物，包括不可培养种类</li>
                            <li><strong>原位性：</strong>保持微生物在自然环境中的状态</li>
                            <li><strong>功能性：</strong>同时获得物种组成和功能信息</li>
                            <li><strong>系统性：</strong>研究微生物群落整体特征</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "微生物组的主要栖息地",
                subtitle: "微生物的多样化生存环境",
                content: `
                    <h2>主要研究环境</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🧑‍🦱 人体微生物组</h4>
                            <p><strong>肠道：</strong>最复杂的微生物社区<br>
                            <strong>皮肤：</strong>第一道生物防线<br>
                            <strong>口腔：</strong>疾病相关微生物</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌱 植物微生物组</h4>
                            <p><strong>根际：</strong>营养循环关键区域<br>
                            <strong>叶际：</strong>植物健康守护者<br>
                            <strong>内生菌：</strong>共生关系建立</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌍 环境微生物组</h4>
                            <p><strong>土壤：</strong>生物地球化学循环<br>
                            <strong>海洋：</strong>全球碳氧循环<br>
                            <strong>极端环境：</strong>生命极限探索</p>
                        </div>
                        <div class="tool-card">
                            <h4>🏭 工业微生物组</h4>
                            <p><strong>污水处理：</strong>环境修复<br>
                            <strong>发酵工业：</strong>产品生产<br>
                            <strong>生物反应器：</strong>过程优化</p>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>微生物组功能</h3>
                        <p><strong>营养循环：</strong>碳、氮、磷、硫循环 → <strong>宿主健康：</strong>免疫调节、代谢支持 → <strong>环境净化：</strong>污染物降解、生态修复</p>
                    </div>
                `
            },
            {
                title: "宏基因组研究方法",
                subtitle: "技术路线与方法选择",
                content: `
                    <h2>主要研究方法</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">样本采集<br>与保存</div>
                        <div class="flowchart-step">DNA提取<br>与质控</div>
                        <div class="flowchart-step">测序策略<br>选择</div>
                        <div class="flowchart-step">数据分析<br>与解读</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>方法类型</th>
                                <th>技术特点</th>
                                <th>适用场景</th>
                                <th>主要优势</th>
                            </tr>
                            <tr>
                                <td><strong>扩增子测序</strong></td>
                                <td>16S/18S/ITS标记基因</td>
                                <td>群落结构分析</td>
                                <td>成本低、标准化</td>
                            </tr>
                            <tr>
                                <td><strong>鸟枪法测序</strong></td>
                                <td>全基因组随机测序</td>
                                <td>功能分析、新基因发现</td>
                                <td>信息全面、功能预测</td>
                            </tr>
                            <tr>
                                <td><strong>宏转录组</strong></td>
                                <td>RNA测序分析</td>
                                <td>基因表达、活性分析</td>
                                <td>反映实时活动状态</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🔬 方法选择原则</h4>
                        <ul>
                            <li><strong>研究目标：</strong>物种组成 vs 功能分析</li>
                            <li><strong>样本特性：</strong>复杂度、宿主DNA含量</li>
                            <li><strong>预算考虑：</strong>成本效益平衡</li>
                            <li><strong>技术要求：</strong>分析能力、设备条件</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "实验设计与样本处理",
                subtitle: "第二部分：实验设计策略",
                content: `
                    <h2>样本采集策略</h2>

                    <div class="two-column">
                        <div>
                            <h3>采样设计要素</h3>
                            <ul>
                                <li><strong>空间代表性：</strong>采样点分布、梯度设计</li>
                                <li><strong>时间动态性：</strong>采样频率、季节变化</li>
                                <li><strong>样本量：</strong>统计功效、重复数量</li>
                                <li><strong>对照设置：</strong>阴性对照、阳性对照</li>
                            </ul>
                        </div>
                        <div>
                            <h3>样本保存方法</h3>
                            <ul>
                                <li><strong>低温冷冻：</strong>-80°C长期保存</li>
                                <li><strong>化学固定：</strong>RNAlater等稳定剂</li>
                                <li><strong>快速处理：</strong>减少降解时间</li>
                                <li><strong>标准化：</strong>统一操作流程</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>DNA提取质量控制</h3>
                        <div class="code-block">
# 质量评估指标
浓度检测：Qubit荧光定量 (ng/μL)
纯度评估：A260/A280 > 1.8, A260/A230 > 2.0
完整性检查：琼脂糖凝胶电泳
抑制物检测：qPCR抑制测试

# 质量标准
DNA浓度：≥ 10 ng/μL
总量：≥ 1 μg (用于建库)
片段大小：主要 > 10 kb
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 常见问题：</strong>宿主DNA污染、提取偏好性、抑制物残留、DNA降解等问题会严重影响后续分析结果的准确性。
                    </div>
                `
            },
            {
                title: "测序策略与平台选择",
                subtitle: "技术平台比较与选择",
                content: `
                    <h2>测序平台特点</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>📊 Illumina平台</h4>
                            <p><strong>读长：</strong>150-300 bp<br>
                            <strong>精度：</strong>>99.9%<br>
                            <strong>通量：</strong>极高<br>
                            <strong>成本：</strong>最低</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧬 PacBio SMRT</h4>
                            <p><strong>读长：</strong>10-100 kb<br>
                            <strong>精度：</strong>99.9% (HiFi)<br>
                            <strong>通量：</strong>中等<br>
                            <strong>成本：</strong>较高</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔬 Oxford Nanopore</h4>
                            <p><strong>读长：</strong>1-2 Mb<br>
                            <strong>精度：</strong>95-99%<br>
                            <strong>通量：</strong>可调<br>
                            <strong>成本：</strong>中等</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔄 混合策略</h4>
                            <p><strong>组合：</strong>短+长读长<br>
                            <strong>精度：</strong>最高<br>
                            <strong>通量：</strong>优化<br>
                            <strong>成本：</strong>平衡</p>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>应用场景</th>
                                <th>推荐平台</th>
                                <th>测序深度</th>
                                <th>主要考虑因素</th>
                            </tr>
                            <tr>
                                <td>群落结构分析</td>
                                <td>Illumina</td>
                                <td>1-10 Gb</td>
                                <td>成本效益、标准化</td>
                            </tr>
                            <tr>
                                <td>基因组组装</td>
                                <td>PacBio + Illumina</td>
                                <td>50-100 Gb</td>
                                <td>组装质量、连续性</td>
                            </tr>
                            <tr>
                                <td>功能基因挖掘</td>
                                <td>Illumina</td>
                                <td>10-50 Gb</td>
                                <td>覆盖度、注释准确性</td>
                            </tr>
                        </table>
                    </div>
                `
            },
            {
                title: "数据质量控制与预处理",
                subtitle: "第三部分：数据预处理流程",
                content: `
                    <h2>质量控制流程</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">原始数据<br>质量评估</div>
                        <div class="flowchart-step">接头去除<br>质量过滤</div>
                        <div class="flowchart-step">宿主序列<br>去除</div>
                        <div class="flowchart-step">污染检测<br>与去除</div>
                    </div>

                    <div class="algorithm-box">
                        <h3>质控参数设置</h3>
                        <div class="code-block">
# Trimmomatic参数示例
ILLUMINACLIP:adapters.fa:2:30:10
LEADING:3 TRAILING:3
SLIDINGWINDOW:4:15
MINLEN:36

# FastQC质量指标
Per base sequence quality > Q20
Per sequence quality scores > Q30
Sequence length distribution
Adapter content < 5%
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>质控工具</h3>
                            <ul>
                                <li><strong>FastQC：</strong>质量评估报告</li>
                                <li><strong>Trimmomatic：</strong>接头去除、质量过滤</li>
                                <li><strong>Cutadapt：</strong>接头序列修剪</li>
                                <li><strong>BBDuk：</strong>污染序列过滤</li>
                            </ul>
                        </div>
                        <div>
                            <h3>宿主去除策略</h3>
                            <ul>
                                <li><strong>BWA比对：</strong>比对到宿主基因组</li>
                                <li><strong>Bowtie2：</strong>快速宿主序列识别</li>
                                <li><strong>KneadData：</strong>整合质控流程</li>
                                <li><strong>DeconSeq：</strong>污染序列检测</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 质控要点：</strong>严格的质量控制是后续分析成功的基础。需要根据样本类型和研究目标调整参数，确保数据质量与分析需求的平衡。
                    </div>
                `
            },
            {
                title: "序列拼接与基因组分箱",
                subtitle: "第四部分：从序列片段到基因组草图",
                content: `
                    <h2>宏基因组拼接策略</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🧩 MEGAHIT</h4>
                            <p><strong>算法：</strong>简洁De Bruijn图<br>
                            <strong>优势：</strong>内存效率高、速度快<br>
                            <strong>适用：</strong>大规模数据集</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔧 MetaSPAdes</h4>
                            <p><strong>算法：</strong>多k-mer策略<br>
                            <strong>优势：</strong>质量高、处理复杂区域<br>
                            <strong>适用：</strong>高质量组装需求</p>
                        </div>
                        <div class="tool-card">
                            <h4>📊 IDBA-UD</h4>
                            <p><strong>算法：</strong>不均匀深度优化<br>
                            <strong>优势：</strong>处理丰度差异<br>
                            <strong>适用：</strong>复杂群落</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔄 混合拼接</h4>
                            <p><strong>策略：</strong>多工具结合<br>
                            <strong>优势：</strong>互补优化<br>
                            <strong>适用：</strong>高质量需求</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>拼接质量评估</h3>
                        <div class="code-block">
# 基本统计指标
总长度：所有contigs的总碱基数
Contig数量：拼接产生的序列片段数
N50：50%总长度对应的contig长度
最大长度：最长contig的长度

# 质量评估工具
QUAST：拼接质量统计
BUSCO：基因完整性评估
CheckM：基因组完整性检查
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>分箱(Binning)原理</h3>
                        <p><strong>序列组成：</strong>GC含量、k-mer频率 → <strong>覆盖度模式：</strong>单样本深度、多样本共变 → <strong>系统发育：</strong>标记基因、进化关系</p>
                    </div>
                `
            },
            {
                title: "基因组分箱工具与策略",
                subtitle: "MAGs构建与质量评估",
                content: `
                    <h2>主要分箱工具</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>工具名称</th>
                                <th>核心算法</th>
                                <th>主要特征</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>MetaBAT2</strong></td>
                                <td>四核苷酸频率+覆盖度</td>
                                <td>速度快、效果好</td>
                                <td>标准分箱流程</td>
                            </tr>
                            <tr>
                                <td><strong>MaxBin2</strong></td>
                                <td>EM算法+标记基因</td>
                                <td>利用单拷贝基因</td>
                                <td>高质量MAGs</td>
                            </tr>
                            <tr>
                                <td><strong>CONCOCT</strong></td>
                                <td>高斯混合模型</td>
                                <td>多样本共变优化</td>
                                <td>时间序列数据</td>
                            </tr>
                            <tr>
                                <td><strong>DAS Tool</strong></td>
                                <td>多工具整合</td>
                                <td>结果优化组合</td>
                                <td>最终质量提升</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>MAGs质量标准</h3>
                            <ul>
                                <li><strong>高质量：</strong>完整性>90%，污染度<5%</li>
                                <li><strong>中等质量：</strong>完整性≥50%，污染度<10%</li>
                                <li><strong>低质量：</strong>不满足中等质量标准</li>
                                <li><strong>附加要求：</strong>rRNA基因、tRNA基因</li>
                            </ul>
                        </div>
                        <div>
                            <h3>质量评估工具</h3>
                            <ul>
                                <li><strong>CheckM：</strong>基于标记基因的评估</li>
                                <li><strong>BUSCO：</strong>单拷贝直系同源基因</li>
                                <li><strong>GTDB-Tk：</strong>分类学注释</li>
                                <li><strong>QUAST：</strong>组装统计指标</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 分箱优化策略</h4>
                        <ul>
                            <li><strong>多工具组合：</strong>使用多种分箱工具并整合结果</li>
                            <li><strong>参数优化：</strong>根据数据特点调整参数</li>
                            <li><strong>手工精修：</strong>基于可视化结果手动优化</li>
                            <li><strong>质量筛选：</strong>只保留高质量和中等质量MAGs</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "物种组成分析",
                subtitle: "第五部分：微生物群落结构解析",
                content: `
                    <h2>物种分类方法</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">序列数据<br>输入</div>
                        <div class="flowchart-step">分类学<br>注释</div>
                        <div class="flowchart-step">丰度<br>计算</div>
                        <div class="flowchart-step">群落结构<br>分析</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🔍 Kraken2</h4>
                            <p><strong>方法：</strong>k-mer匹配<br>
                            <strong>速度：</strong>极快<br>
                            <strong>精度：</strong>高<br>
                            <strong>数据库：</strong>可定制</p>
                        </div>
                        <div class="tool-card">
                            <h4>📈 MetaPhlAn</h4>
                            <p><strong>方法：</strong>标记基因<br>
                            <strong>速度：</strong>快<br>
                            <strong>精度：</strong>高<br>
                            <strong>输出：</strong>相对丰度</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧬 Centrifuge</h4>
                            <p><strong>方法：</strong>压缩索引<br>
                            <strong>内存：</strong>低<br>
                            <strong>精度：</strong>好<br>
                            <strong>特点：</strong>内存高效</p>
                        </div>
                        <div class="tool-card">
                            <h4>🎯 DIAMOND</h4>
                            <p><strong>方法：</strong>蛋白比对<br>
                            <strong>速度：</strong>快<br>
                            <strong>敏感性：</strong>高<br>
                            <strong>适用：</strong>功能注释</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>丰度计算方法</h3>
                        <div class="code-block">
# 相对丰度计算
相对丰度 = (物种reads数 / 总reads数) × 100%

# 标准化方法
TPM (Transcripts Per Million)
RPKM (Reads Per Kilobase Million)
CPM (Counts Per Million)

# 稀释曲线分析
rarefaction_curve(sample_data, depth_range)
                        </div>
                    </div>
                `
            },
            {
                title: "功能注释与代谢分析",
                subtitle: "微生物群落功能潜力解析",
                content: `
                    <h2>功能注释流程</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">基因预测<br>ORF识别</div>
                        <div class="flowchart-step">功能数据库<br>比对</div>
                        <div class="flowchart-step">通路重建<br>代谢分析</div>
                        <div class="flowchart-step">功能丰度<br>统计</div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>数据库</th>
                                <th>功能类型</th>
                                <th>特点</th>
                                <th>应用场景</th>
                            </tr>
                            <tr>
                                <td><strong>KEGG</strong></td>
                                <td>代谢通路</td>
                                <td>通路完整、可视化好</td>
                                <td>代谢功能分析</td>
                            </tr>
                            <tr>
                                <td><strong>COG/eggNOG</strong></td>
                                <td>直系同源基因</td>
                                <td>进化关系清晰</td>
                                <td>比较基因组学</td>
                            </tr>
                            <tr>
                                <td><strong>Pfam</strong></td>
                                <td>蛋白质家族</td>
                                <td>结构域注释</td>
                                <td>蛋白质功能预测</td>
                            </tr>
                            <tr>
                                <td><strong>CAZy</strong></td>
                                <td>碳水化合物酶</td>
                                <td>专业化数据库</td>
                                <td>碳循环研究</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>基因预测工具</h3>
                            <ul>
                                <li><strong>Prodigal：</strong>原核生物基因预测</li>
                                <li><strong>MetaGeneMark：</strong>宏基因组优化</li>
                                <li><strong>FragGeneScan：</strong>短片段基因预测</li>
                                <li><strong>AUGUSTUS：</strong>真核生物基因预测</li>
                            </ul>
                        </div>
                        <div>
                            <h3>功能分析工具</h3>
                            <ul>
                                <li><strong>HUMAnN：</strong>代谢通路分析</li>
                                <li><strong>MEGAN：</strong>分类与功能整合</li>
                                <li><strong>STAMP：</strong>统计分析</li>
                                <li><strong>PICRUSt：</strong>功能预测</li>
                            </ul>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>关键代谢通路</h3>
                        <p><strong>碳循环：</strong>光合作用、呼吸作用、甲烷代谢 → <strong>氮循环：</strong>固氮、硝化、反硝化 → <strong>硫循环：</strong>硫化、硫氧化、硫还原</p>
                    </div>
                `
            },
            {
                title: "微生物群落多样性分析",
                subtitle: "第六部分：生态学指标与统计分析",
                content: `
                    <h2>多样性指标体系</h2>

                    <div class="two-column">
                        <div>
                            <h3>α多样性（群落内）</h3>
                            <ul>
                                <li><strong>丰富度：</strong>Observed OTUs, Chao1</li>
                                <li><strong>均匀度：</strong>Shannon, Simpson指数</li>
                                <li><strong>系统发育：</strong>Faith's PD, MNTD</li>
                                <li><strong>稀释曲线：</strong>测序深度充分性</li>
                            </ul>
                        </div>
                        <div>
                            <h3>β多样性（群落间）</h3>
                            <ul>
                                <li><strong>距离矩阵：</strong>Bray-Curtis, Jaccard</li>
                                <li><strong>系统发育：</strong>UniFrac距离</li>
                                <li><strong>降维分析：</strong>PCoA, NMDS, t-SNE</li>
                                <li><strong>聚类分析：</strong>层次聚类、k-means</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>多样性计算公式</h3>
                        <div class="code-block">
# Shannon多样性指数
H' = -Σ(pi × ln(pi))
其中pi为第i个物种的相对丰度

# Simpson多样性指数
D = 1 - Σ(pi²)

# Bray-Curtis距离
BC = Σ|xi - yi| / Σ(xi + yi)
                        </div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>📊 QIIME2</h4>
                            <p><strong>功能：</strong>全流程分析平台<br>
                            <strong>特点：</strong>标准化、可重现<br>
                            <strong>适用：</strong>扩增子数据</p>
                        </div>
                        <div class="tool-card">
                            <h4>📈 R/phyloseq</h4>
                            <p><strong>功能：</strong>统计分析包<br>
                            <strong>特点：</strong>灵活、可定制<br>
                            <strong>适用：</strong>高级统计分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>🔬 mothur</h4>
                            <p><strong>功能：</strong>微生物生态分析<br>
                            <strong>特点：</strong>功能全面<br>
                            <strong>适用：</strong>传统OTU分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>🧬 MicrobiomeAnalyst</h4>
                            <p><strong>功能：</strong>在线分析平台<br>
                            <strong>特点：</strong>易用、可视化<br>
                            <strong>适用：</strong>快速分析</p>
                        </div>
                    </div>
                `
            },
            {
                title: "比较分析与统计检验",
                subtitle: "组间差异与关联分析",
                content: `
                    <h2>统计分析方法</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>分析类型</th>
                                <th>统计方法</th>
                                <th>适用条件</th>
                                <th>R包/工具</th>
                            </tr>
                            <tr>
                                <td><strong>组间比较</strong></td>
                                <td>PERMANOVA, ANOSIM</td>
                                <td>多组样本比较</td>
                                <td>vegan, QIIME2</td>
                            </tr>
                            <tr>
                                <td><strong>差异物种</strong></td>
                                <td>LEfSe, DESeq2</td>
                                <td>生物标志物发现</td>
                                <td>LEfSe, DESeq2</td>
                            </tr>
                            <tr>
                                <td><strong>关联分析</strong></td>
                                <td>Spearman, Pearson</td>
                                <td>环境因子关联</td>
                                <td>corrplot, Hmisc</td>
                            </tr>
                            <tr>
                                <td><strong>网络分析</strong></td>
                                <td>SparCC, SPIEC-EASI</td>
                                <td>微生物互作</td>
                                <td>SpiecEasi, igraph</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>LEfSe分析流程</h3>
                        <div class="code-block">
# LEfSe (Linear discriminant analysis Effect Size)
1. Kruskal-Wallis检验 (α=0.05)
2. Wilcoxon秩和检验 (α=0.05)
3. 线性判别分析 (LDA>2.0)
4. 生物标志物识别

# 解释阈值
LDA score > 2.0: 显著差异
p-value < 0.05: 统计显著
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>多重检验校正</h3>
                            <ul>
                                <li><strong>Bonferroni：</strong>保守校正方法</li>
                                <li><strong>FDR (BH)：</strong>控制假发现率</li>
                                <li><strong>q-value：</strong>贝叶斯FDR</li>
                                <li><strong>permutation：</strong>置换检验</li>
                            </ul>
                        </div>
                        <div>
                            <h3>效应量评估</h3>
                            <ul>
                                <li><strong>Cohen's d：</strong>标准化效应量</li>
                                <li><strong>R²：</strong>解释方差比例</li>
                                <li><strong>LDA score：</strong>判别分析得分</li>
                                <li><strong>fold change：</strong>倍数变化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 注意事项：</strong>微生物组数据具有稀疏性、组成性和过度离散等特点，需要选择合适的统计方法和进行适当的数据转换。
                    </div>
                `
            },
            {
                title: "数据可视化与结果解读",
                subtitle: "图表制作与生物学意义阐释",
                content: `
                    <h2>主要可视化类型</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>📊 物种组成图</h4>
                            <p><strong>类型：</strong>堆叠柱状图、饼图<br>
                            <strong>用途：</strong>展示群落结构<br>
                            <strong>工具：</strong>ggplot2, plotly</p>
                        </div>
                        <div class="tool-card">
                            <h4>🗺️ 排序图</h4>
                            <p><strong>类型：</strong>PCoA, NMDS, PCA<br>
                            <strong>用途：</strong>样本关系可视化<br>
                            <strong>工具：</strong>phyloseq, vegan</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌡️ 热图</h4>
                            <p><strong>类型：</strong>物种丰度、功能热图<br>
                            <strong>用途：</strong>模式识别<br>
                            <strong>工具：</strong>pheatmap, ComplexHeatmap</p>
                        </div>
                        <div class="tool-card">
                            <h4>🕸️ 网络图</h4>
                            <p><strong>类型：</strong>共现网络、功能网络<br>
                            <strong>用途：</strong>互作关系展示<br>
                            <strong>工具：</strong>Cytoscape, igraph</p>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎨 可视化最佳实践</h4>
                        <ul>
                            <li><strong>颜色选择：</strong>色盲友好、对比度适当</li>
                            <li><strong>图例说明：</strong>清晰标注、单位明确</li>
                            <li><strong>数据标准化：</strong>相对丰度、Z-score转换</li>
                            <li><strong>统计标注：</strong>显著性标记、置信区间</li>
                        </ul>
                    </div>

                    <div class="algorithm-box">
                        <h3>R代码示例</h3>
                        <div class="code-block">
# 物种组成柱状图
library(ggplot2)
ggplot(data, aes(x=Sample, y=Abundance, fill=Genus)) +
  geom_bar(stat="identity") +
  theme_classic() +
  labs(title="Microbial Community Composition")

# PCoA排序图
library(phyloseq)
ordinate(physeq, method="PCoA", distance="bray")
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 解读要点：</strong>结合生物学背景解释数据模式，关注统计显著性与生物学意义的平衡，考虑技术偏差和混杂因素的影响。
                    </div>
                `
            },
            {
                title: "应用案例与前沿发展",
                subtitle: "第七部分：实际应用与技术趋势",
                content: `
                    <h2>典型应用案例</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>🏥 人体健康</h4>
                            <p><strong>肠道菌群：</strong>疾病诊断标志物<br>
                            <strong>个性化医疗：</strong>精准治疗方案<br>
                            <strong>药物开发：</strong>微生物靶向治疗</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌾 农业应用</h4>
                            <p><strong>土壤健康：</strong>微生物指标评估<br>
                            <strong>植物保护：</strong>生物防治菌剂<br>
                            <strong>作物育种：</strong>微生物辅助选择</p>
                        </div>
                        <div class="tool-card">
                            <h4>🌍 环境监测</h4>
                            <p><strong>污染评估：</strong>生物指示物种<br>
                            <strong>生态修复：</strong>功能菌群筛选<br>
                            <strong>气候变化：</strong>碳氮循环研究</p>
                        </div>
                        <div class="tool-card">
                            <h4>🏭 工业生物技术</h4>
                            <p><strong>生物制造：</strong>新酶发现<br>
                            <strong>废物处理：</strong>微生物降解<br>
                            <strong>能源生产：</strong>生物燃料</p>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>技术发展趋势</h3>
                        <p><strong>长读长测序：</strong>完整基因组组装 → <strong>单细胞技术：</strong>个体水平分析 → <strong>多组学整合：</strong>系统生物学 → <strong>人工智能：</strong>模式识别与预测</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>新兴技术</h3>
                            <ul>
                                <li><strong>Hi-C测序：</strong>空间结构分析</li>
                                <li><strong>CRISPR筛选：</strong>功能基因鉴定</li>
                                <li><strong>合成生物学：</strong>人工微生物组</li>
                                <li><strong>机器学习：</strong>预测模型构建</li>
                            </ul>
                        </div>
                        <div>
                            <h3>挑战与机遇</h3>
                            <ul>
                                <li><strong>标准化：</strong>方法学统一</li>
                                <li><strong>数据整合：</strong>多中心协作</li>
                                <li><strong>因果推断：</strong>机制解析</li>
                                <li><strong>临床转化：</strong>应用落地</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "课程总结",
                subtitle: "宏基因组学核心要点回顾",
                content: `
                    <div class="highlight-box">
                        <h3>核心知识点</h3>
                        <p>本专题系统介绍了宏基因组学的基本概念、技术方法、分析流程和应用前景，为深入理解微生物群落提供了完整的知识框架。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术要点</h3>
                            <ul>
                                <li><strong>实验设计：</strong>采样策略、质量控制</li>
                                <li><strong>测序技术：</strong>平台选择、深度设计</li>
                                <li><strong>数据处理：</strong>质控、拼接、分箱</li>
                                <li><strong>生物信息学：</strong>注释、分类、统计</li>
                            </ul>
                        </div>
                        <div>
                            <h3>分析方法</h3>
                            <ul>
                                <li><strong>物种组成：</strong>分类学注释、丰度计算</li>
                                <li><strong>功能分析：</strong>基因预测、通路重建</li>
                                <li><strong>多样性：</strong>α/β多样性、统计检验</li>
                                <li><strong>可视化：</strong>图表制作、结果解读</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 学习收获</h4>
                        <ul>
                            <li><strong>理论基础：</strong>掌握宏基因组学核心概念和原理</li>
                            <li><strong>技术方法：</strong>了解主要技术路线和工具选择</li>
                            <li><strong>分析技能：</strong>具备基本的数据分析和解读能力</li>
                            <li><strong>应用视野：</strong>认识在不同领域的应用潜力</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong>💡 未来展望：</strong>宏基因组学作为微生物生态学研究的重要工具，将在精准医学、可持续农业、环境保护等领域发挥越来越重要的作用。
                    </div>

                    <div class="algorithm-box">
                        <h3>推荐学习资源</h3>
                        <div class="code-block">
# 在线课程
Coursera: Microbiome Analysis
edX: Introduction to Metagenomics

# 分析平台
QIIME2: https://qiime2.org/
MG-RAST: https://www.mg-rast.org/

# 数据库
NCBI SRA: 测序数据
IMG/M: 宏基因组数据库
                        </div>
                    </div>
                `
            }
        ];

        // 初始化幻灯片
        function initSlides() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            const container = document.querySelector('.container');
            container.innerHTML = '';

            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <div class="slide-header">
                        <h1 class="slide-title">${slide.title}</h1>
                        <p class="slide-subtitle">${slide.subtitle}</p>
                    </div>
                    <div class="slide-content">
                        ${slide.content}
                    </div>
                `;
                container.appendChild(slideElement);
            });

            generateMenu();
            updateProgress();
            updateNavigation();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = '';

            slides.forEach((slide, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = `menu-item ${index === currentSlideIndex ? 'current' : ''}`;
                menuItem.textContent = `${index + 1}. ${slide.title}`;
                menuItem.onclick = () => goToSlide(index);
                menuDropdown.appendChild(menuItem);
            });
        }

        // 导航功能
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                document.querySelectorAll('.slide').forEach(slide => slide.classList.remove('active'));
                document.querySelectorAll('.slide')[index].classList.add('active');

                currentSlideIndex = index;
                updateProgress();
                updateNavigation();
                generateMenu();

                // 关闭菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        }

        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 菜单切换
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                btn.textContent = '自动播放';
                isAutoPlaying = false;
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay();
                    }
                }, 5000);
                btn.textContent = '停止播放';
                isAutoPlaying = true;
            }
        }

        // 重新开始
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    goToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.getElementById('menuDropdown').classList.contains('active')) {
                        document.getElementById('menuDropdown').classList.remove('active');
                    }
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            const menu = document.querySelector('.slide-menu');
            const dropdown = document.getElementById('menuDropdown');
            if (!menu.contains(e.target) && dropdown.classList.contains('active')) {
                dropdown.classList.remove('active');
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', initSlides);
    </script>
</body>
</html>
