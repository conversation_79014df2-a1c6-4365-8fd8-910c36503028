<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题三：基因组测序(DNA-seq)数据分析 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #3498db;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #3498db;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #3498db;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #3498db;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #e74c3c;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #3498db;
        }

        .highlight-box {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .code-comment {
            color: #95a5a6;
            font-style: italic;
        }

        .code-keyword {
            color: #3498db;
            font-weight: bold;
        }

        .code-string {
            color: #e74c3c;
        }

        .code-function {
            color: #f39c12;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(52, 152, 219, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(52, 152, 219, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(52, 152, 219, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(52, 152, 219, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 300px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #3498db;
        }

        .menu-item.current {
            background: #e3f2fd;
            border-left-color: #2196f3;
            font-weight: bold;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .flowchart {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .flowchart-step {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            position: relative;
        }

        .flowchart-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #3498db;
        }

        .flowchart-step:last-child::after {
            display: none;
        }

        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1em;
            margin: 20px 0;
        }

        .key-points {
            background: #fff9c4;
            border: 2px solid #fbc02d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .key-points h4 {
            color: #e65100 !important;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .key-points ul li {
            color: #333 !important;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .algorithm-box h3 {
            color: #2E7D32 !important;
        }

        .comparison-table th {
            color: #FFFFFF !important;
        }

        .comparison-table td {
            color: #333 !important;
        }

        .flowchart-step {
            color: #FFFFFF !important;
            font-weight: bold;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tool-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tool-card:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .tool-card h4 {
            color: #1976D2 !important;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .tool-card p {
            color: #333 !important;
            line-height: 1.6;
        }

        .fullscreen-btn {
            position: fixed;
            top: 30px;
            right: 100px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fullscreen-btn:hover {
            background: rgba(52, 152, 219, 1);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture3_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">16</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题三：基因组测序(DNA-seq)数据分析",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🧬 基因组测序数据分析流程图形摘要</h3>
                        <svg width="100%" height="500" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="dnaGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="25%" style="stop-color:#3498db" />
                                    <stop offset="50%" style="stop-color:#f39c12" />
                                    <stop offset="75%" style="stop-color:#27ae60" />
                                    <stop offset="100%" style="stop-color:#e74c3c" />
                                </linearGradient>
                                <linearGradient id="analysisGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="500" fill="url(#bgGradient3)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">基因组测序数据分析工作流</text>
                            
                            <!-- 测序类型对比 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">基因组测序策略选择</text>
                                
                                <!-- WGS -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">全基因组测序</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">(WGS)</text>
                                </g>
                                
                                <!-- WES -->
                                <g transform="translate(200, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">外显子测序</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">(WES)</text>
                                </g>
                                
                                <!-- 靶向测序 -->
                                <g transform="translate(350, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">靶向测序</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">(Targeted)</text>
                                </g>
                                
                                <!-- 深度要求 -->
                                <g transform="translate(500, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">测序深度要求:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">SNP: 15-30X | Indel: 20-40X | SV: 30-50X</text>
                                </g>
                            </g>
                            
                            <!-- 主要分析流程 - 中部 -->
                            <g transform="translate(50, 160)">
                                <!-- 原始数据 -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="120" height="60" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="60" y="25" text-anchor="middle" fill="white" font-size="12" font-weight="bold">原始数据</text>
                                    <text x="60" y="40" text-anchor="middle" fill="white" font-size="10">FASTQ格式</text>
                                    <text x="60" y="52" text-anchor="middle" fill="white" font-size="9">质量控制</text>
                                </g>
                                
                                <!-- 参考基因组比对 -->
                                <g transform="translate(180, 0)">
                                    <rect x="0" y="0" width="120" height="60" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="60" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">参考比对</text>
                                    <text x="60" y="35" text-anchor="middle" fill="white" font-size="10">BWA-MEM</text>
                                    <text x="60" y="47" text-anchor="middle" fill="white" font-size="9">Bowtie2</text>
                                    <text x="60" y="57" text-anchor="middle" fill="white" font-size="9">Minimap2</text>
                                </g>
                                
                                <!-- 变异检测 -->
                                <g transform="translate(360, 0)">
                                    <rect x="0" y="0" width="120" height="60" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="60" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">变异检测</text>
                                    <text x="60" y="35" text-anchor="middle" fill="white" font-size="10">GATK</text>
                                    <text x="60" y="47" text-anchor="middle" fill="white" font-size="9">FreeBayes</text>
                                    <text x="60" y="57" text-anchor="middle" fill="white" font-size="9">VarScan</text>
                                </g>
                                
                                <!-- 变异注释 -->
                                <g transform="translate(540, 0)">
                                    <rect x="0" y="0" width="120" height="60" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="60" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">变异注释</text>
                                    <text x="60" y="35" text-anchor="middle" fill="white" font-size="10">ANNOVAR</text>
                                    <text x="60" y="47" text-anchor="middle" fill="white" font-size="9">VEP</text>
                                    <text x="60" y="57" text-anchor="middle" fill="white" font-size="9">SnpEff</text>
                                </g>
                                
                                <!-- 功能分析 -->
                                <g transform="translate(720, 0)">
                                    <rect x="0" y="0" width="120" height="60" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="60" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">功能分析</text>
                                    <text x="60" y="35" text-anchor="middle" fill="white" font-size="10">通路分析</text>
                                    <text x="60" y="47" text-anchor="middle" fill="white" font-size="9">疾病关联</text>
                                    <text x="60" y="57" text-anchor="middle" fill="white" font-size="9">药物基因组</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M120,30 L170,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead3)"/>
                                <path d="M300,30 L350,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead3)"/>
                                <path d="M480,30 L530,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead3)"/>
                                <path d="M660,30 L710,30" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead3)"/>
                            </g>
                            
                            <!-- 变异类型检测 - 下部 -->
                            <g transform="translate(50, 280)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">变异类型与检测方法</text>
                                
                                <!-- SNP检测 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">SNP</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">单核苷酸</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 点突变</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 高频变异</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 易检测</text>
                                </g>
                                
                                <!-- Indel检测 -->
                                <g transform="translate(200, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Indel</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">插入缺失</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 小片段</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 移码突变</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 局部重比对</text>
                                </g>
                                
                                <!-- CNV检测 -->
                                <g transform="translate(350, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">CNV</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">拷贝数变异</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 大片段</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 深度分析</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 统计检验</text>
                                </g>
                                
                                <!-- SV检测 -->
                                <g transform="translate(500, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">SV</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">结构变异</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 重排</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 倒位</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 配对末端</text>
                                </g>
                                
                                <!-- 基因组组装 -->
                                <g transform="translate(650, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">组装</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="9">从头组装</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 重叠图</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 字符串图</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 长读长</text>
                                </g>
                            </g>
                            
                            <!-- 算法核心概念 - 底部 -->
                            <g transform="translate(50, 420)">
                                <rect x="0" y="0" width="800" height="60" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">核心算法概念</text>
                                
                                <text x="50" y="40" fill="#2c3e50" font-size="11" font-weight="bold">比对算法:</text>
                                <text x="50" y="52" fill="#2c3e50" font-size="10">FM-index • 种子扩展 • 动态规划</text>
                                
                                <text x="300" y="40" fill="#2c3e50" font-size="11" font-weight="bold">变异检测:</text>
                                <text x="300" y="52" fill="#2c3e50" font-size="10">贝叶斯模型 • 机器学习 • 统计检验</text>
                                
                                <text x="550" y="40" fill="#2c3e50" font-size="11" font-weight="bold">质量控制:</text>
                                <text x="550" y="52" fill="#2c3e50" font-size="10">MAPQ评分 • 深度过滤 • 链偏好性</text>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M120,340 Q150,320 180,340" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M270,340 Q300,320 330,340" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M420,340 Q450,320 480,340" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M570,340 Q600,320 630,340" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解基因组测序的基本原理、实验设计考量、核心生物信息学分析流程（比对、变异检测、注释、组装）及其背后的算法思想与应用场景。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>基因组测序实验设计与应用场景</li>
                                <li>参考基因组比对算法原理</li>
                                <li>变异检测方法学</li>
                                <li>变异注释与功能预测</li>
                                <li>基因组组装策略与算法</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>掌握基因组测序的核心概念</li>
                                <li>理解主流分析算法的原理</li>
                                <li>学会选择合适的分析工具</li>
                                <li>具备解读分析结果的能力</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "基因组测序实验设计",
                subtitle: "第一部分：实验设计与应用场景",
                content: `
                    <h2>基因组测序类型与应用</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>测序类型</th>
                                <th>应用场景</th>
                                <th>优势</th>
                                <th>局限性</th>
                            </tr>
                            <tr>
                                <td><strong>全基因组测序(WGS)</strong></td>
                                <td>重测序、从头测序</td>
                                <td>全面覆盖、检测所有变异类型</td>
                                <td>成本高、数据量大</td>
                            </tr>
                            <tr>
                                <td><strong>外显子组测序(WES)</strong></td>
                                <td>孟德尔遗传病、肿瘤研究</td>
                                <td>成本效益高、聚焦编码区</td>
                                <td>无法检测非编码区变异</td>
                            </tr>
                            <tr>
                                <td><strong>靶向测序</strong></td>
                                <td>已知基因突变筛查</td>
                                <td>成本最低、深度高</td>
                                <td>覆盖范围有限</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🎯 关键考虑因素</h4>
                        <ul>
                            <li><strong>研究目标：</strong>发现新变异 vs 已知变异检测</li>
                            <li><strong>样本类型：</strong>DNA质量、起始量、保存条件</li>
                            <li><strong>预算限制：</strong>测序成本与分析成本的平衡</li>
                            <li><strong>时间要求：</strong>紧急诊断 vs 科研项目</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "测序深度与覆盖度设计",
                subtitle: "实验设计的核心参数",
                content: `
                    <h2>测序深度计算与选择</h2>

                    <div class="algorithm-box">
                        <h3>测序深度计算公式</h3>
                        <div class="code-block">
测序深度 = (读段数量 × 读段长度) / 基因组大小

例如：人类基因组(3Gb)
- 30X深度需要：30 × 3Gb = 90Gb数据
- 150bp读段需要：90Gb / 150bp = 6亿条读段
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>不同应用的深度要求</h3>
                            <ul>
                                <li><strong>SNP检测：</strong> 15-30X</li>
                                <li><strong>Indel检测：</strong> 20-40X</li>
                                <li><strong>结构变异：</strong> 30-50X</li>
                                <li><strong>CNV检测：</strong> 0.1-1X (低深度)</li>
                                <li><strong>从头组装：</strong> 50-100X</li>
                            </ul>
                        </div>
                        <div>
                            <h3>覆盖度影响因素</h3>
                            <ul>
                                <li>GC含量偏好性</li>
                                <li>重复序列区域</li>
                                <li>文库制备偏差</li>
                                <li>测序平台特性</li>
                                <li>样本DNA质量</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 注意：</strong>理论深度与有效深度存在差异。由于测序偏差和质量过滤，实际可用于分析的深度通常为理论深度的70-90%。
                    </div>
                `
            },
            {
                title: "参考基因组比对概述",
                subtitle: "第二部分：参考基因组比对算法原理",
                content: `
                    <h2>比对算法的核心挑战</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">原始读段<br>(FASTQ)</div>
                        <div class="flowchart-step">质量控制<br>预处理</div>
                        <div class="flowchart-step">参考基因组<br>比对</div>
                        <div class="flowchart-step">比对结果<br>(SAM/BAM)</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>技术挑战</h3>
                            <ul>
                                <li><strong>规模挑战：</strong>人类基因组3Gb，数亿条读段</li>
                                <li><strong>精度要求：</strong>允许少量错配和gap</li>
                                <li><strong>重复序列：</strong>45%的人类基因组为重复序列</li>
                                <li><strong>多重比对：</strong>一条读段可能比对到多个位置</li>
                            </ul>
                        </div>
                        <div>
                            <h3>算法策略</h3>
                            <ul>
                                <li><strong>索引构建：</strong>预处理参考基因组</li>
                                <li><strong>种子查找：</strong>快速定位候选区域</li>
                                <li><strong>精确比对：</strong>动态规划算法</li>
                                <li><strong>结果评分：</strong>比对质量评估</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 关键概念：</strong>现代比对算法都采用"种子-扩展"策略，先通过快速的种子匹配找到候选位置，再进行精确的局部比对。
                    </div>
                `
            },
            {
                title: "BWA-MEM算法原理",
                subtitle: "最广泛使用的比对算法",
                content: `
                    <h2>BWA-MEM核心算法</h2>

                    <div class="algorithm-box">
                        <h3>算法流程</h3>
                        <ol>
                            <li><strong>FM-index构建：</strong>基于BWT的压缩索引</li>
                            <li><strong>MEM查找：</strong>寻找最大精确匹配作为种子</li>
                            <li><strong>种子链化：</strong>将共线性种子连接成链</li>
                            <li><strong>局部扩展：</strong>Smith-Waterman算法填补空隙</li>
                            <li><strong>比对评分：</strong>选择最佳比对位置</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>BWA-MEM</h4>
                            <p>适用于70bp-1Mbp读段，支持长读段和配对末端数据</p>
                        </div>
                        <div class="tool-card">
                            <h4>Bowtie2</h4>
                            <p>基于FM-index，适用于短读段，速度快内存低</p>
                        </div>
                        <div class="tool-card">
                            <h4>Minimap2</h4>
                            <p>专为长读段设计，支持PacBio和ONT数据</p>
                        </div>
                        <div class="tool-card">
                            <h4>HISAT2</h4>
                            <p>专为RNA-seq设计，支持剪接比对</p>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🔧 BWA-MEM关键参数</h4>
                        <ul>
                            <li><strong>-k：</strong>最小种子长度(默认19)</li>
                            <li><strong>-w：</strong>带宽(默认100)</li>
                            <li><strong>-A/-B：</strong>匹配/错配得分(1/-4)</li>
                            <li><strong>-O/-E：</strong>gap开启/延伸罚分(6/1)</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "比对质量评估",
                subtitle: "SAM/BAM格式与质量指标",
                content: `
                    <h2>SAM格式详解</h2>

                    <div class="code-block">
@HD	VN:1.6	SO:coordinate
@SQ	SN:chr1	LN:248956422
read1	99	chr1	1001	60	100M	=	1201	300	ATCGATCG...	IIIIIIII...
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>字段</th>
                                <th>含义</th>
                                <th>示例</th>
                            </tr>
                            <tr>
                                <td>QNAME</td>
                                <td>读段名称</td>
                                <td>read1</td>
                            </tr>
                            <tr>
                                <td>FLAG</td>
                                <td>比对标志</td>
                                <td>99 (配对、正向)</td>
                            </tr>
                            <tr>
                                <td>RNAME</td>
                                <td>参考序列名</td>
                                <td>chr1</td>
                            </tr>
                            <tr>
                                <td>POS</td>
                                <td>比对起始位置</td>
                                <td>1001</td>
                            </tr>
                            <tr>
                                <td>MAPQ</td>
                                <td>映射质量</td>
                                <td>60</td>
                            </tr>
                            <tr>
                                <td>CIGAR</td>
                                <td>比对描述</td>
                                <td>100M (100个匹配)</td>
                            </tr>
                        </table>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>质量指标</h3>
                            <ul>
                                <li><strong>比对率：</strong>成功比对的读段比例</li>
                                <li><strong>唯一比对率：</strong>唯一比对的读段比例</li>
                                <li><strong>插入片段大小：</strong>配对末端距离分布</li>
                                <li><strong>覆盖深度：</strong>每个位点的读段数</li>
                            </ul>
                        </div>
                        <div>
                            <h3>MAPQ质量分数</h3>
                            <ul>
                                <li><strong>60：</strong>错误概率 < 0.0001%</li>
                                <li><strong>30：</strong>错误概率 < 0.1%</li>
                                <li><strong>20：</strong>错误概率 < 1%</li>
                                <li><strong>0：</strong>多重比对或低质量</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "变异检测概述",
                subtitle: "第三部分：变异检测方法学",
                content: `
                    <h2>变异类型与检测策略</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>SNP/Indel</h4>
                            <p>单核苷酸变异和小插入缺失<br>检测方法：统计模型、贝叶斯推断</p>
                        </div>
                        <div class="tool-card">
                            <h4>结构变异(SV)</h4>
                            <p>大片段插入、删除、重排<br>检测方法：多种信号整合</p>
                        </div>
                        <div class="tool-card">
                            <h4>拷贝数变异(CNV)</h4>
                            <p>基因组片段拷贝数改变<br>检测方法：读段深度分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>复杂重排</h4>
                            <p>染色体易位、倒位等<br>检测方法：配对末端分析</p>
                        </div>
                    </div>

                    <div class="flowchart">
                        <div class="flowchart-step">BAM文件<br>输入</div>
                        <div class="flowchart-step">变异检测<br>算法</div>
                        <div class="flowchart-step">质量过滤<br>VQSR</div>
                        <div class="flowchart-step">VCF文件<br>输出</div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>变异检测的准确性取决于测序深度、读段质量、算法选择和参数设置。不同类型的变异需要不同的检测策略。
                    </div>
                `
            },
            {
                title: "GATK最佳实践流程",
                subtitle: "SNP/Indel检测的金标准",
                content: `
                    <h2>GATK HaplotypeCaller工作原理</h2>

                    <div class="algorithm-box">
                        <h3>算法核心步骤</h3>
                        <ol>
                            <li><strong>活跃区域识别：</strong>寻找可能存在变异的区域</li>
                            <li><strong>局部重组装：</strong>在活跃区域进行de novo组装</li>
                            <li><strong>单倍型构建：</strong>构建可能的单倍型序列</li>
                            <li><strong>似然计算：</strong>计算每个单倍型的似然值</li>
                            <li><strong>基因型推断：</strong>确定最可能的基因型</li>
                        </ol>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>GATK流程优势</h3>
                            <ul>
                                <li>处理复杂区域能力强</li>
                                <li>准确处理Indel周围的SNP</li>
                                <li>提供详细的质量评分</li>
                                <li>支持多样本联合检测</li>
                                <li>广泛的社区验证</li>
                            </ul>
                        </div>
                        <div>
                            <h3>关键质量指标</h3>
                            <ul>
                                <li><strong>QUAL：</strong>变异质量分数</li>
                                <li><strong>DP：</strong>总读段深度</li>
                                <li><strong>AD：</strong>等位基因深度</li>
                                <li><strong>GQ：</strong>基因型质量</li>
                                <li><strong>PL：</strong>基因型似然值</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 注意：</strong>GATK需要高质量的输入数据和适当的预处理步骤（去重、碱基质量重校正、Indel重比对）才能获得最佳结果。
                    </div>
                `
            },
            {
                title: "结构变异检测",
                subtitle: "大片段变异的检测策略",
                content: `
                    <h2>结构变异检测信号</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>读段深度(RD)</h4>
                            <p>检测拷贝数变异<br>删除→深度降低<br>重复→深度增加</p>
                        </div>
                        <div class="tool-card">
                            <h4>配对末端(PEM)</h4>
                            <p>检测大插入删除<br>异常插入片段大小<br>方向异常</p>
                        </div>
                        <div class="tool-card">
                            <h4>分裂读段(SR)</h4>
                            <p>精确断点定位<br>读段跨越断点<br>提供碱基级精度</p>
                        </div>
                        <div class="tool-card">
                            <h4>从头组装(AS)</h4>
                            <p>复杂重排检测<br>局部组装<br>发现新序列</p>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>SV类型</th>
                                <th>主要检测信号</th>
                                <th>常用工具</th>
                                <th>检测精度</th>
                            </tr>
                            <tr>
                                <td>删除(DEL)</td>
                                <td>RD + PEM + SR</td>
                                <td>Delly, Lumpy, Manta</td>
                                <td>高</td>
                            </tr>
                            <tr>
                                <td>插入(INS)</td>
                                <td>PEM + SR + AS</td>
                                <td>Manta, GRIDSS</td>
                                <td>中等</td>
                            </tr>
                            <tr>
                                <td>倒位(INV)</td>
                                <td>PEM + SR</td>
                                <td>Delly, BreakDancer</td>
                                <td>中等</td>
                            </tr>
                            <tr>
                                <td>易位(TRA)</td>
                                <td>PEM + SR</td>
                                <td>Delly, GRIDSS</td>
                                <td>低</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🔍 长读长测序的优势</h4>
                        <ul>
                            <li>直接跨越重复序列和复杂区域</li>
                            <li>提供更准确的断点信息</li>
                            <li>检测短读长无法发现的变异</li>
                            <li>简化复杂重排的解析</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "变异注释概述",
                subtitle: "第四部分：变异注释与功能预测",
                content: `
                    <h2>变异注释的层次结构</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">位置注释<br>基因/外显子</div>
                        <div class="flowchart-step">功能注释<br>蛋白质影响</div>
                        <div class="flowchart-step">频率注释<br>人群数据库</div>
                        <div class="flowchart-step">临床注释<br>致病性评估</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>基本注释类型</h3>
                            <ul>
                                <li><strong>基因间区：</strong>位于基因外部</li>
                                <li><strong>内含子区：</strong>基因内非编码区</li>
                                <li><strong>外显子区：</strong>编码序列区域</li>
                                <li><strong>UTR区：</strong>非翻译区</li>
                                <li><strong>剪接位点：</strong>外显子-内含子边界</li>
                                <li><strong>调控区域：</strong>启动子、增强子</li>
                            </ul>
                        </div>
                        <div>
                            <h3>功能后果分类</h3>
                            <ul>
                                <li><strong>同义突变：</strong>不改变氨基酸</li>
                                <li><strong>错义突变：</strong>氨基酸改变</li>
                                <li><strong>无义突变：</strong>产生终止密码子</li>
                                <li><strong>移码突变：</strong>改变读码框</li>
                                <li><strong>剪接变异：</strong>影响mRNA剪接</li>
                                <li><strong>起始/终止变异：</strong>影响翻译</li>
                            </ul>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>影响程度分级</h3>
                        <p><strong>HIGH：</strong>移码、无义、剪接位点 → <strong>MODERATE：</strong>错义 → <strong>LOW：</strong>同义 → <strong>MODIFIER：</strong>非编码区</p>
                    </div>
                `
            },
            {
                title: "变异注释工具",
                subtitle: "主流注释工具比较",
                content: `
                    <h2>注释工具特点对比</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>ANNOVAR</h4>
                            <p><strong>优势：</strong>数据库丰富、可定制性强<br>
                            <strong>特点：</strong>支持多种格式、灵活配置<br>
                            <strong>适用：</strong>研究项目、深度注释</p>
                        </div>
                        <div class="tool-card">
                            <h4>SnpEff</h4>
                            <p><strong>优势：</strong>速度快、标准化输出<br>
                            <strong>特点：</strong>预编译数据库、SO标准<br>
                            <strong>适用：</strong>流程化分析、快速注释</p>
                        </div>
                        <div class="tool-card">
                            <h4>VEP</h4>
                            <p><strong>优势：</strong>Ensembl集成、功能全面<br>
                            <strong>特点：</strong>在线/本地、插件丰富<br>
                            <strong>适用：</strong>临床分析、标准化流程</p>
                        </div>
                        <div class="tool-card">
                            <h4>OpenCRAVAT</h4>
                            <p><strong>优势：</strong>模块化、可视化强<br>
                            <strong>特点：</strong>Web界面、交互分析<br>
                            <strong>适用：</strong>临床解读、教学演示</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>注释流程示例</h3>
                        <div class="code-block">
# ANNOVAR注释示例
table_annovar.pl input.vcf humandb/ \\
  -buildver hg38 \\
  -out output \\
  -remove \\
  -protocol refGene,gnomAD_genome,clinvar_20210501 \\
  -operation g,f,f \\
  -nastring . \\
  -vcfinput
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 选择建议：</strong>对于临床应用推荐VEP，研究项目推荐ANNOVAR，快速分析推荐SnpEff，教学演示推荐OpenCRAVAT。
                    </div>
                `
            },
            {
                title: "功能预测方法",
                subtitle: "变异致病性评估",
                content: `
                    <h2>功能预测算法原理</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>SIFT</h4>
                            <p><strong>原理：</strong>序列保守性分析<br>
                            <strong>输出：</strong>0-1分数，<0.05有害<br>
                            <strong>特点：</strong>基于进化保守性</p>
                        </div>
                        <div class="tool-card">
                            <h4>PolyPhen-2</h4>
                            <p><strong>原理：</strong>结构+保守性+功能域<br>
                            <strong>输出：</strong>Benign/Possibly/Probably damaging<br>
                            <strong>特点：</strong>整合多种特征</p>
                        </div>
                        <div class="tool-card">
                            <h4>CADD</h4>
                            <p><strong>原理：</strong>整合63种注释特征<br>
                            <strong>输出：</strong>Phred-scaled分数<br>
                            <strong>特点：</strong>全基因组范围评分</p>
                        </div>
                        <div class="tool-card">
                            <h4>REVEL</h4>
                            <p><strong>原理：</strong>元预测器，整合13种工具<br>
                            <strong>输出：</strong>0-1分数<br>
                            <strong>特点：</strong>专为罕见变异设计</p>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>预测方法</th>
                                <th>核心特征</th>
                                <th>适用范围</th>
                                <th>准确性</th>
                            </tr>
                            <tr>
                                <td>保守性分析</td>
                                <td>进化保守性</td>
                                <td>所有变异</td>
                                <td>中等</td>
                            </tr>
                            <tr>
                                <td>结构预测</td>
                                <td>蛋白质结构影响</td>
                                <td>已知结构蛋白</td>
                                <td>高</td>
                            </tr>
                            <tr>
                                <td>机器学习</td>
                                <td>多特征整合</td>
                                <td>训练集覆盖范围</td>
                                <td>较高</td>
                            </tr>
                            <tr>
                                <td>元预测器</td>
                                <td>多工具整合</td>
                                <td>广泛</td>
                                <td>最高</td>
                            </tr>
                        </table>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 重要提醒：</strong>功能预测工具只能提供参考，不能替代实验验证。临床解读需要结合患者表型、家系分析和文献证据。
                    </div>
                `
            },
            {
                title: "变异优先级排序",
                subtitle: "从海量变异中筛选候选",
                content: `
                    <h2>变异筛选漏斗策略</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">原始变异<br>数百万个</div>
                        <div class="flowchart-step">质量过滤<br>数十万个</div>
                        <div class="flowchart-step">频率过滤<br>数万个</div>
                        <div class="flowchart-step">功能过滤<br>数千个</div>
                        <div class="flowchart-step">候选变异<br>数十个</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>过滤策略</h3>
                            <ul>
                                <li><strong>质量过滤：</strong>QUAL, DP, GQ阈值</li>
                                <li><strong>频率过滤：</strong>gnomAD < 1% 或 0.1%</li>
                                <li><strong>功能过滤：</strong>HIGH/MODERATE影响</li>
                                <li><strong>基因过滤：</strong>疾病相关基因</li>
                                <li><strong>遗传模式：</strong>符合家系分离</li>
                            </ul>
                        </div>
                        <div>
                            <h3>优先级评分</h3>
                            <ul>
                                <li><strong>变异类型：</strong>LoF > 错义 > 同义</li>
                                <li><strong>功能预测：</strong>CADD, REVEL分数</li>
                                <li><strong>基因重要性：</strong>pLI, LOEUF分数</li>
                                <li><strong>临床证据：</strong>ClinVar分类</li>
                                <li><strong>表型匹配：</strong>HPO术语匹配</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 关键数据库资源</h4>
                        <ul>
                            <li><strong>gnomAD：</strong>人群频率数据，评估变异稀有度</li>
                            <li><strong>ClinVar：</strong>临床意义分类，已知致病变异</li>
                            <li><strong>OMIM：</strong>基因-疾病关联，表型描述</li>
                            <li><strong>HGMD：</strong>已发表的致病变异数据库</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "基因组组装概述",
                subtitle: "第五部分：基因组组装策略与算法",
                content: `
                    <h2>基因组组装的基本概念</h2>

                    <div class="two-column">
                        <div>
                            <h3>组装类型</h3>
                            <ul>
                                <li><strong>从头组装：</strong>不依赖参考基因组</li>
                                <li><strong>参考辅助：</strong>利用近缘物种参考</li>
                                <li><strong>重测序组装：</strong>改进现有组装</li>
                                <li><strong>单倍型组装：</strong>分离同源染色体</li>
                            </ul>
                        </div>
                        <div>
                            <h3>质量指标</h3>
                            <ul>
                                <li><strong>N50/NG50：</strong>连续性指标</li>
                                <li><strong>L50/LG50：</strong>片段数量指标</li>
                                <li><strong>BUSCO：</strong>基因完整性评估</li>
                                <li><strong>覆盖度：</strong>基因组覆盖程度</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>N50计算示例</h3>
                        <div class="code-block">
Contigs长度: [1000, 800, 600, 400, 200] bp
总长度: 3000 bp
排序后累加:
- 1000 bp (累计1000, 33%)
- 800 bp (累计1800, 60%) ← 超过50%
N50 = 800 bp
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>组装挑战</h3>
                        <p><strong>重复序列：</strong>基因组45%为重复序列 → <strong>杂合性：</strong>二倍体复杂性 → <strong>测序错误：</strong>干扰组装 → <strong>覆盖不均：</strong>影响连续性</p>
                    </div>
                `
            },
            {
                title: "短读长组装算法",
                subtitle: "De Bruijn图方法",
                content: `
                    <h2>De Bruijn图组装原理</h2>

                    <div class="algorithm-box">
                        <h3>算法流程</h3>
                        <ol>
                            <li><strong>k-mer分解：</strong>将读段分解为k长度子串</li>
                            <li><strong>构建DBG：</strong>k-mer作为边，(k-1)-mer作为节点</li>
                            <li><strong>图简化：</strong>去除错误路径和气泡结构</li>
                            <li><strong>路径寻找：</strong>寻找欧拉路径重建序列</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>SPAdes</h4>
                            <p><strong>特点：</strong>多k-mer策略，错误校正<br>
                            <strong>适用：</strong>细菌、宏基因组、单细胞<br>
                            <strong>优势：</strong>功能全面，质量高</p>
                        </div>
                        <div class="tool-card">
                            <h4>Velvet</h4>
                            <p><strong>特点：</strong>经典DBG实现<br>
                            <strong>适用：</strong>小型基因组<br>
                            <strong>优势：</strong>简单易用，教学友好</p>
                        </div>
                        <div class="tool-card">
                            <h4>ABySS</h4>
                            <p><strong>特点：</strong>并行化设计<br>
                            <strong>适用：</strong>大型基因组<br>
                            <strong>优势：</strong>可扩展性强</p>
                        </div>
                        <div class="tool-card">
                            <h4>SOAPdenovo</h4>
                            <p><strong>特点：</strong>内存优化<br>
                            <strong>适用：</strong>植物基因组<br>
                            <strong>优势：</strong>处理复杂基因组</p>
                        </div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>DBG优势</h3>
                            <ul>
                                <li>计算效率高</li>
                                <li>内存占用相对较低</li>
                                <li>对测序错误有容忍度</li>
                                <li>适合短读长数据</li>
                            </ul>
                        </div>
                        <div>
                            <h3>DBG局限</h3>
                            <ul>
                                <li>k值选择困难</li>
                                <li>重复序列处理能力有限</li>
                                <li>信息丢失（k-mer化）</li>
                                <li>图结构复杂化</li>
                            </ul>
                        </div>
                    </div>
                `
            },
            {
                title: "长读长组装算法",
                subtitle: "OLC与String Graph方法",
                content: `
                    <h2>长读长组装的优势与挑战</h2>

                    <div class="highlight-box">
                        <h3>长读长技术革命</h3>
                        <p>PacBio HiFi (>99.9%准确率) 和 Oxford Nanopore (实时测序) 技术能够跨越重复序列，极大简化基因组组装问题。</p>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>Canu</h4>
                            <p><strong>策略：</strong>OLC三阶段流程<br>
                            <strong>特点：</strong>错误校正+修剪+组装<br>
                            <strong>适用：</strong>PacBio CLR, ONT</p>
                        </div>
                        <div class="tool-card">
                            <h4>Flye</h4>
                            <p><strong>策略：</strong>重复图(Repeat Graph)<br>
                            <strong>特点：</strong>快速，处理重复序列强<br>
                            <strong>适用：</strong>ONT, PacBio</p>
                        </div>
                        <div class="tool-card">
                            <h4>Hifiasm</h4>
                            <p><strong>策略：</strong>单倍型感知组装<br>
                            <strong>特点：</strong>高质量单倍型分离<br>
                            <strong>适用：</strong>PacBio HiFi</p>
                        </div>
                        <div class="tool-card">
                            <h4>wtdbg2</h4>
                            <p><strong>策略：</strong>模糊Bruijn图<br>
                            <strong>特点：</strong>极快速度，低内存<br>
                            <strong>适用：</strong>大基因组快速组装</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>OLC算法流程</h3>
                        <ol>
                            <li><strong>错误校正：</strong>利用读段重叠校正错误</li>
                            <li><strong>重叠检测：</strong>寻找读段间的重叠关系</li>
                            <li><strong>布局构建：</strong>确定读段的排列顺序</li>
                            <li><strong>一致性序列：</strong>生成最终的contig序列</li>
                        </ol>
                    </div>

                    <div class="info-box">
                        <strong>💡 混合组装策略：</strong>结合短读长的高精度和长读长的连续性，先用长读长构建骨架，再用短读长进行错误校正。
                    </div>
                `
            },
            {
                title: "组装后处理与改进",
                subtitle: "从Contigs到染色体级别组装",
                content: `
                    <h2>组装质量提升策略</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">初始Contigs<br>N50~100kb</div>
                        <div class="flowchart-step">支架构建<br>N50~1Mb</div>
                        <div class="flowchart-step">间隙填充<br>减少N区域</div>
                        <div class="flowchart-step">错误校正<br>提高准确性</div>
                        <div class="flowchart-step">染色体级别<br>N50~100Mb</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>支架构建</h4>
                            <p><strong>数据：</strong>配对末端、连锁读段、长读长<br>
                            <strong>目标：</strong>确定contigs的顺序和方向<br>
                            <strong>工具：</strong>SSPACE, BESST</p>
                        </div>
                        <div class="tool-card">
                            <h4>间隙填充</h4>
                            <p><strong>数据：</strong>原始读段、长读长<br>
                            <strong>目标：</strong>填补scaffolds中的N区域<br>
                            <strong>工具：</strong>GapFiller, PBJelly</p>
                        </div>
                        <div class="tool-card">
                            <h4>错误校正</h4>
                            <p><strong>数据：</strong>高精度短读长<br>
                            <strong>目标：</strong>纠正单碱基错误<br>
                            <strong>工具：</strong>Pilon, Racon</p>
                        </div>
                        <div class="tool-card">
                            <h4>Hi-C组装</h4>
                            <p><strong>数据：</strong>染色质交联数据<br>
                            <strong>目标：</strong>染色体级别组装<br>
                            <strong>工具：</strong>3D-DNA, SALSA2</p>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>Hi-C技术原理</h3>
                        <p>利用染色质在三维空间中的交互频率信息，同一染色体上的区域交互频率远高于不同染色体，且交互频率随基因组距离增加而衰减。</p>
                    </div>

                    <div class="key-points">
                        <h4>🎯 质量评估工具</h4>
                        <ul>
                            <li><strong>QUAST：</strong>组装统计指标计算</li>
                            <li><strong>BUSCO：</strong>基因完整性评估</li>
                            <li><strong>Merqury：</strong>基于k-mer的质量评估</li>
                            <li><strong>Inspector：</strong>组装错误检测</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "实际应用案例",
                subtitle: "基因组测序在不同领域的应用",
                content: `
                    <h2>基因组测序的广泛应用</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>临床诊断</h4>
                            <p><strong>应用：</strong>罕见病诊断、肿瘤基因检测<br>
                            <strong>特点：</strong>高准确性、快速报告<br>
                            <strong>挑战：</strong>变异解读、临床意义评估</p>
                        </div>
                        <div class="tool-card">
                            <h4>精准医疗</h4>
                            <p><strong>应用：</strong>个体化用药、疾病风险评估<br>
                            <strong>特点：</strong>基于基因型的治疗选择<br>
                            <strong>挑战：</strong>药物基因组学数据库</p>
                        </div>
                        <div class="tool-card">
                            <h4>农业育种</h4>
                            <p><strong>应用：</strong>作物改良、抗性基因发现<br>
                            <strong>特点：</strong>加速育种进程<br>
                            <strong>挑战：</strong>复杂性状的遗传基础</p>
                        </div>
                        <div class="tool-card">
                            <h4>进化研究</h4>
                            <p><strong>应用：</strong>物种进化、群体遗传学<br>
                            <strong>特点：</strong>大规模比较基因组学<br>
                            <strong>挑战：</strong>古DNA分析、数据整合</p>
                        </div>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>应用领域</th>
                                <th>主要目标</th>
                                <th>技术要求</th>
                                <th>分析重点</th>
                            </tr>
                            <tr>
                                <td>临床诊断</td>
                                <td>致病变异发现</td>
                                <td>高准确性、标准化</td>
                                <td>变异注释、致病性评估</td>
                            </tr>
                            <tr>
                                <td>肿瘤研究</td>
                                <td>体细胞突变检测</td>
                                <td>高敏感性、低频变异</td>
                                <td>驱动基因、耐药机制</td>
                            </tr>
                            <tr>
                                <td>群体遗传学</td>
                                <td>遗传多样性分析</td>
                                <td>大样本、成本控制</td>
                                <td>等位基因频率、连锁不平衡</td>
                            </tr>
                            <tr>
                                <td>新物种基因组</td>
                                <td>参考基因组构建</td>
                                <td>高连续性、完整性</td>
                                <td>基因组组装、基因注释</td>
                            </tr>
                        </table>
                    </div>

                    <div class="info-box">
                        <strong>💡 发展趋势：</strong>测序成本持续下降，分析算法不断优化，应用场景日益扩大，正在从科研工具转变为常规临床检测手段。
                    </div>
                `
            },
            {
                title: "技术发展趋势",
                subtitle: "基因组测序技术的未来方向",
                content: `
                    <h2>新兴技术与发展方向</h2>

                    <div class="two-column">
                        <div>
                            <h3>测序技术发展</h3>
                            <ul>
                                <li><strong>超长读长：</strong>ONT读长可达2Mb+</li>
                                <li><strong>实时测序：</strong>边测序边分析</li>
                                <li><strong>便携式设备：</strong>现场快速检测</li>
                                <li><strong>单分子测序：</strong>无需PCR扩增</li>
                                <li><strong>多组学整合：</strong>表观、转录、蛋白</li>
                            </ul>
                        </div>
                        <div>
                            <h3>分析方法创新</h3>
                            <ul>
                                <li><strong>AI算法：</strong>深度学习在变异检测中的应用</li>
                                <li><strong>云计算：</strong>大规模并行分析</li>
                                <li><strong>图基因组：</strong>超越线性参考基因组</li>
                                <li><strong>单倍型分析：</strong>相位信息的利用</li>
                                <li><strong>结构变异：</strong>复杂重排的精确检测</li>
                            </ul>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>图基因组(Graph Genome)概念</h3>
                        <p>传统线性参考基因组无法充分代表人群的遗传多样性。图基因组将多个个体的基因组信息整合为图结构，更好地表示群体变异。</p>
                    </div>

                    <div class="algorithm-box">
                        <h3>未来挑战</h3>
                        <ul>
                            <li><strong>数据存储：</strong>PB级数据的存储和管理</li>
                            <li><strong>计算资源：</strong>实时分析的计算需求</li>
                            <li><strong>标准化：</strong>跨平台、跨实验室的一致性</li>
                            <li><strong>隐私保护：</strong>基因组数据的安全共享</li>
                            <li><strong>临床转化：</strong>从研究到临床应用的转化</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 伦理考量：</strong>基因组数据涉及个人隐私和遗传信息，需要建立完善的伦理规范和法律框架。
                    </div>
                `
            },
            {
                title: "课程总结",
                subtitle: "基因组测序数据分析核心要点",
                content: `
                    <h2>核心知识点回顾</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">实验设计<br>深度、覆盖度</div>
                        <div class="flowchart-step">序列比对<br>BWA-MEM</div>
                        <div class="flowchart-step">变异检测<br>GATK</div>
                        <div class="flowchart-step">变异注释<br>功能预测</div>
                        <div class="flowchart-step">基因组组装<br>算法策略</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>理论掌握</h3>
                            <ul>
                                <li>✅ 理解不同测序策略的适用场景</li>
                                <li>✅ 掌握比对算法的核心原理</li>
                                <li>✅ 了解变异检测的统计基础</li>
                                <li>✅ 熟悉注释工具的选择标准</li>
                                <li>✅ 理解组装算法的优缺点</li>
                            </ul>
                        </div>
                        <div>
                            <h3>实践能力</h3>
                            <ul>
                                <li>🔧 能够设计合理的测序实验</li>
                                <li>🔧 会选择适当的分析工具</li>
                                <li>🔧 能解读分析结果的质量</li>
                                <li>🔧 具备变异优先级排序能力</li>
                                <li>🔧 了解组装质量评估方法</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 关键技能总结</h4>
                        <ul>
                            <li><strong>算法理解：</strong>掌握BWA-MEM、GATK HaplotypeCaller等核心算法原理</li>
                            <li><strong>工具选择：</strong>根据数据类型和研究目标选择合适的分析工具</li>
                            <li><strong>质量控制：</strong>理解各个分析步骤的质量评估指标</li>
                            <li><strong>结果解读：</strong>能够正确解读SAM/BAM、VCF等格式文件</li>
                            <li><strong>问题诊断：</strong>识别和解决分析过程中的常见问题</li>
                        </ul>
                    </div>

                    <div class="highlight-box">
                        <h3>后续学习建议</h3>
                        <p><strong>实践操作：</strong>通过实验课程加深理解 → <strong>前沿跟踪：</strong>关注新技术和新方法 → <strong>项目应用：</strong>参与实际研究项目 → <strong>持续学习：</strong>保持对领域发展的敏感度</p>
                    </div>

                    <div class="info-box">
                        <strong>💡 学习资源推荐：</strong><br>
                        • 官方文档：GATK、BWA等工具的官方教程<br>
                        • 在线课程：Coursera、edX的生物信息学课程<br>
                        • 学术期刊：Nature Methods、Bioinformatics等<br>
                        • 开源项目：GitHub上的分析流程和工具
                    </div>
                `
            },
            {
                title: "组装后处理与改进",
                subtitle: "从Contigs到染色体级别组装",
                content: `
                    <h2>组装质量提升策略</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">初始Contigs<br>N50~100kb</div>
                        <div class="flowchart-step">支架构建<br>N50~1Mb</div>
                        <div class="flowchart-step">间隙填充<br>减少N区域</div>
                        <div class="flowchart-step">错误校正<br>提高准确性</div>
                        <div class="flowchart-step">染色体级别<br>N50~100Mb</div>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>支架构建</h4>
                            <p><strong>数据：</strong>配对末端、连锁读段、长读长<br>
                            <strong>目标：</strong>确定contigs的顺序和方向<br>
                            <strong>工具：</strong>SSPACE, BESST</p>
                        </div>
                        <div class="tool-card">
                            <h4>间隙填充</h4>
                            <p><strong>数据：</strong>原始读段、长读长<br>
                            <strong>目标：</strong>填补scaffolds中的N区域<br>
                            <strong>工具：</strong>GapFiller, PBJelly</p>
                        </div>
                        <div class="tool-card">
                            <h4>错误校正</h4>
                            <p><strong>数据：</strong>高精度短读长<br>
                            <strong>目标：</strong>纠正单碱基错误<br>
                            <strong>工具：</strong>Pilon, Racon</p>
                        </div>
                        <div class="tool-card">
                            <h4>Hi-C组装</h4>
                            <p><strong>数据：</strong>染色质交联数据<br>
                            <strong>目标：</strong>染色体级别组装<br>
                            <strong>工具：</strong>3D-DNA, SALSA2</p>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>Hi-C技术原理</h3>
                        <p>利用染色质在三维空间中的交互频率信息，同一染色体上的区域交互频率远高于不同染色体，且交互频率随基因组距离增加而衰减。</p>
                    </div>

                    <div class="key-points">
                        <h4>🎯 质量评估工具</h4>
                        <ul>
                            <li><strong>QUAST：</strong>组装统计指标计算</li>
                            <li><strong>BUSCO：</strong>基因完整性评估</li>
                            <li><strong>Merqury：</strong>基于k-mer的质量评估</li>
                            <li><strong>Inspector：</strong>组装错误检测</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "课程总结",
                subtitle: "基因组测序数据分析核心要点",
                content: `
                    <h2>核心知识点回顾</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">实验设计<br>深度、覆盖度</div>
                        <div class="flowchart-step">序列比对<br>BWA-MEM</div>
                        <div class="flowchart-step">变异检测<br>GATK</div>
                        <div class="flowchart-step">变异注释<br>功能预测</div>
                        <div class="flowchart-step">基因组组装<br>算法策略</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>理论掌握</h3>
                            <ul>
                                <li>✅ 理解不同测序策略的适用场景</li>
                                <li>✅ 掌握比对算法的核心原理</li>
                                <li>✅ 了解变异检测的统计基础</li>
                                <li>✅ 熟悉注释工具的选择标准</li>
                                <li>✅ 理解组装算法的优缺点</li>
                            </ul>
                        </div>
                        <div>
                            <h3>实践能力</h3>
                            <ul>
                                <li>🔧 能够设计合理的测序实验</li>
                                <li>🔧 会选择适当的分析工具</li>
                                <li>🔧 能解读分析结果的质量</li>
                                <li>🔧 具备变异优先级排序能力</li>
                                <li>🔧 了解组装质量评估方法</li>
                            </ul>
                        </div>
                    </div>

                    <div class="key-points">
                        <h4>🎯 关键技能总结</h4>
                        <ul>
                            <li><strong>算法理解：</strong>掌握BWA-MEM、GATK HaplotypeCaller等核心算法原理</li>
                            <li><strong>工具选择：</strong>根据数据类型和研究目标选择合适的分析工具</li>
                            <li><strong>质量控制：</strong>理解各个分析步骤的质量评估指标</li>
                            <li><strong>结果解读：</strong>能够正确解读SAM/BAM、VCF等格式文件</li>
                            <li><strong>问题诊断：</strong>识别和解决分析过程中的常见问题</li>
                        </ul>
                    </div>

                    <div class="highlight-box">
                        <h3>后续学习建议</h3>
                        <p><strong>实践操作：</strong>通过实验课程加深理解 → <strong>前沿跟踪：</strong>关注新技术和新方法 → <strong>项目应用：</strong>参与实际研究项目 → <strong>持续学习：</strong>保持对领域发展的敏感度</p>
                    </div>

                    <div class="info-box">
                        <strong>💡 学习资源推荐：</strong><br>
                        • 官方文档：GATK、BWA等工具的官方教程<br>
                        • 在线课程：Coursera、edX的生物信息学课程<br>
                        • 学术期刊：Nature Methods、Bioinformatics等<br>
                        • 开源项目：GitHub上的分析流程和工具
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成幻灯片HTML
            const container = document.querySelector('.container');
            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <div class="slide-header">
                        <h1 class="slide-title">${slide.title}</h1>
                        <p class="slide-subtitle">${slide.subtitle}</p>
                    </div>
                    <div class="slide-content">
                        ${slide.content}
                    </div>
                `;
                container.appendChild(slideElement);
            });

            // 生成菜单
            generateMenu();
            updateNavigation();
            updateProgress();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = slides.map((slide, index) => `
                <div class="menu-item ${index === 0 ? 'current' : ''}" onclick="goToSlide(${index})">
                    ${index + 1}. ${slide.title}
                </div>
            `).join('');
        }

        // 切换菜单显示
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                // 隐藏当前幻灯片
                document.querySelectorAll('.slide')[currentSlideIndex].classList.remove('active');

                // 显示目标幻灯片
                currentSlideIndex = index;
                document.querySelectorAll('.slide')[currentSlideIndex].classList.add('active');

                // 更新界面
                updateNavigation();
                updateProgress();
                updateMenu();

                // 隐藏菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        // 更新导航按钮状态
        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            
            // 更新主题导航栏的active状态
            document.querySelectorAll('.topic-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据当前幻灯片索引设置active状态
            if (currentSlideIndex >= 0 && currentSlideIndex <= 2) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(0)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 3 && currentSlideIndex <= 5) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(3)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 6 && currentSlideIndex <= 9) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(6)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 10 && currentSlideIndex <= 12) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(10)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 13 && currentSlideIndex <= 14) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(13)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 15) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(15)"]')?.classList.add('active');
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新菜单当前项
        function updateMenu() {
            document.querySelectorAll('.menu-item').forEach((item, index) => {
                item.classList.toggle('current', index === currentSlideIndex);
            });
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay(); // 到达最后一页时停止自动播放
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        // 重新开始演示
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    goToSlide(0);
                    break;
                case 'End':
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    document.getElementById('menuDropdown').classList.remove('active');
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.slide-menu')) {
                document.getElementById('menuDropdown').classList.remove('active');
            }
        });

        // 初始化
        initPresentation();
    </script>
</body>
</html>
