<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于课程 - NGS高通量测序技术课程平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: #667eea;
        }

        /* 主要内容 */
        .main-content {
            padding: 3rem 0;
        }

        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card h2 {
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-card h2 i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }

        .feature-item h4 {
            color: #667eea;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }

        .stat-item i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .stat-item .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .resource-list {
            list-style: none;
            margin-top: 1.5rem;
        }

        .resource-list li {
            background: rgba(102, 126, 234, 0.1);
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .resource-list li strong {
            color: #667eea;
        }

        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6de8, #6a4495);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        /* 讲义下载区域 */
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .download-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .download-item:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
        }

        .download-item i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .download-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .download-item p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .content-card {
                padding: 1.5rem;
            }

            .feature-grid,
            .stats-grid,
            .download-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin-bottom: 2rem;
            color: white;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-dna"></i>
                    NGS课程平台
                </a>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="index.html#courses"><i class="fas fa-book"></i> 课程</a></li>
                    <li><a href="about.html" class="active"><i class="fas fa-info-circle"></i> 关于</a></li>
                    <li><a href="syllabus.html"><i class="fas fa-graduation-cap"></i> 大纲</a></li>
                    <li><a href="resources.html"><i class="fas fa-download"></i> 资源</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.html">首页</a> / 关于课程
            </div>

            <!-- 页面头部 -->
            <div class="page-header">
                <h1><i class="fas fa-microscope"></i> 关于NGS高通量测序技术课程</h1>
                <p>深入了解现代生物信息学的核心技术课程体系</p>
            </div>

            <!-- 内容区域 -->
            <div class="content-grid">
                <!-- 课程概述 -->
                <div class="content-card">
                    <h2><i class="fas fa-info-circle"></i> 课程概述</h2>
                    <p>NGS高通量测序技术课程是专为生物信息学专业硕士研究生设计的核心课程。本课程旨在使学生全面掌握高通量测序的基本原理、主流技术平台及数据分析方法，能够独立设计实验方案并执行常见类型的高通量测序数据分析流程。</p>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <div class="number">32</div>
                            <div>总学时</div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-flask"></i>
                            <div class="number">8</div>
                            <div>专题模块</div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-laptop-code"></i>
                            <div class="number">16</div>
                            <div>实践学时</div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <div class="number">100+</div>
                            <div>受益学生</div>
                        </div>
                    </div>
                </div>

                <!-- 课程特色 -->
                <div class="content-card">
                    <h2><i class="fas fa-star"></i> 课程特色</h2>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <h4><i class="fas fa-microscope"></i> 理论与实践并重</h4>
                            <p>16学时理论课程配合16学时实践操作，确保学生既掌握理论基础，又具备实际操作能力。</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-laptop-code"></i> 真实数据分析</h4>
                            <p>使用来自实际科研项目的测序数据，让学生体验完整的生物信息学分析流程。</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-seedling"></i> 前沿技术应用</h4>
                            <p>涵盖单细胞测序、表观基因组学等最新技术，以及在植物保护中的创新应用。</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-tools"></i> 丰富工具实践</h4>
                            <p>系统学习主流生物信息学分析工具，包括BWA、GATK、DESeq2、Seurat等。</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-project-diagram"></i> 项目导向学习</h4>
                            <p>通过完整的项目案例，培养学生独立解决生物信息学问题的能力。</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-users-cog"></i> 互动式教学</h4>
                            <p>采用交互式幻灯片、小组讨论、案例分析等多种教学方式。</p>
                        </div>
                    </div>
                </div>

                <!-- 技术支持 -->
                <div class="content-card">
                    <h2><i class="fas fa-tools"></i> 技术支持与资源</h2>
                    <ul class="resource-list">
                        <li>
                            <strong>公共数据库：</strong> 
                            <a href="https://www.ncbi.nlm.nih.gov/sra" target="_blank">NCBI SRA</a>、
                            <a href="https://www.ebi.ac.uk/ena" target="_blank">ENA</a>、
                            <a href="https://www.ncbi.nlm.nih.gov/geo/" target="_blank">GEO</a>
                        </li>
                        <li>
                            <strong>分析平台：</strong> 
                            <a href="https://bioconductor.org/" target="_blank">Bioconductor</a>、
                            <a href="https://galaxy.genome.au/" target="_blank">Galaxy</a>、
                            <a href="https://www.docker.com/" target="_blank">Docker容器</a>
                        </li>
                        <li>
                            <strong>编程环境：</strong> 
                            Linux操作系统、R统计软件、Python编程语言、Jupyter Notebook
                        </li>
                        <li>
                            <strong>可视化工具：</strong> 
                            <a href="http://software.broadinstitute.org/software/igv/" target="_blank">IGV</a>、
                            <a href="https://www.rstudio.com/" target="_blank">RStudio</a>、
                            <a href="https://cytoscape.org/" target="_blank">Cytoscape</a>
                        </li>
                    </ul>
                </div>

                <!-- 联系信息 -->
                <div class="content-card">
                    <h2><i class="fas fa-envelope"></i> 联系我们</h2>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <h4><i class="fas fa-university"></i> 开课单位</h4>
                            <p>生物信息学教研室<br>植物保护学院</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-envelope"></i> 联系邮箱</h4>
                            <p><EMAIL><br><EMAIL></p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-clock"></i> 答疑时间</h4>
                            <p>周四<br>14:30 - 17:00</p>
                        </div>
                        <div class="feature-item">
                            <h4><i class="fas fa-map-marker-alt"></i> 上课地点</h4>
                            <p>生物信息学实验室<br>16教105室</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="content-card">
                <h2><i class="fas fa-compass"></i> 快速导航</h2>
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.html" class="btn-primary">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    <a href="syllabus.html" class="btn-primary">
                        <i class="fas fa-graduation-cap"></i> 查看教学大纲
                    </a>
                    <a href="index.html#courses" class="btn-primary">
                        <i class="fas fa-book"></i> 开始学习
                    </a>
                    <a href="resources.html" class="btn-primary">
                        <i class="fas fa-download"></i> 学习资源
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 下载项悬停效果
        const downloadItems = document.querySelectorAll('.download-item');
        downloadItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html> 