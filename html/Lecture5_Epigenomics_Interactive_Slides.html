<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题五：表观基因组测序数据分析 - 交互式课件</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-top: 120px;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
            font-weight: 700;
        }

        h2 {
            color: #3498db;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        h4 {
            color: #3498db;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-style: italic;
        }

        /* 主导航栏样式 */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .main-nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .main-nav-brand {
            display: flex;
            align-items: center;
            color: #2c3e50;
            text-decoration: none;
            font-size: 1.3em;
            font-weight: 700;
        }

        .main-nav-brand i {
            margin-right: 8px;
            color: #3498db;
        }

        .main-nav-links {
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .main-nav-link {
            color: #2c3e50;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .main-nav-link:hover {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* 主题导航栏样式 */
        .topic-navbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            padding: 10px 20px;
            z-index: 999;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .topic-navbar-brand {
            color: white;
            font-size: 1.2em;
            font-weight: bold;
            text-decoration: none;
        }

        .topic-navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .topic-nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .topic-nav-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .topic-nav-item.active {
            background: rgba(255,255,255,0.3);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #8e44ad;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.3em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .slide-content {
            line-height: 1.8;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .slide-content h2 {
            color: #8e44ad;
            font-size: 1.8em;
            margin: 30px 0 20px 0;
            border-left: 5px solid #8e44ad;
            padding-left: 15px;
        }

        .slide-content h3 {
            color: #3498db;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 10px 0;
            position: relative;
        }

        .slide-content li::marker {
            color: #8e44ad;
        }

        .highlight-box {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight-box h3 {
            color: #FFFFFF !important;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .highlight-box p {
            color: #FFFFFF !important;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-block::before {
            content: "💻 代码示例";
            display: block;
            color: #81C784 !important;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 0.85em;
        }

        .code-comment {
            color: #95a5a6;
            font-style: italic;
        }

        .code-keyword {
            color: #3498db;
            font-weight: bold;
        }

        .code-string {
            color: #e74c3c;
        }

        .code-function {
            color: #f39c12;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(142, 68, 173, 0.9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(142, 68, 173, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:disabled {
            background: rgba(149, 165, 166, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #8e44ad, #3498db);
            width: 0%;
            transition: width 0.5s ease;
        }

        .slide-menu {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }

        .menu-btn {
            background: rgba(142, 68, 173, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .menu-btn:hover {
            background: rgba(142, 68, 173, 1);
            transform: scale(1.1);
        }

        .menu-dropdown {
            position: absolute;
            top: 60px;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 10px;
            min-width: 350px;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .menu-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
            font-size: 0.9em;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #8e44ad;
        }

        .menu-item.current {
            background: #f3e5f5;
            border-left-color: #8e44ad;
            font-weight: bold;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #8e44ad;
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .flowchart {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .flowchart-step {
            background: #8e44ad;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
            position: relative;
        }

        .flowchart-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #8e44ad;
        }

        .flowchart-step:last-child::after {
            display: none;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .tool-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tool-card:hover {
            border-color: #8e44ad;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .tool-card h4 {
            color: #8e44ad;
            margin-bottom: 10px;
        }

        .key-points {
            background: #fff9c4;
            border: 2px solid #fbc02d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .key-points h4 {
            color: #e65100 !important;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .key-points ul li {
            color: #333 !important;
        }

        .info-box strong {
            color: #1565C0 !important;
        }

        .warning-box strong {
            color: #E65100 !important;
        }

        .algorithm-box h3 {
            color: #2E7D32 !important;
        }

        .comparison-table th {
            color: #FFFFFF !important;
        }

        .comparison-table td {
            color: #333 !important;
        }

        .flowchart-step {
            color: #FFFFFF !important;
            font-weight: bold;
        }

        .tool-card h4 {
            color: #7B1FA2 !important;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .tool-card p {
            color: #333 !important;
            line-height: 1.6;
        }

        .fullscreen-btn {
            position: fixed;
            top: 30px;
            right: 100px;
            background: rgba(142, 68, 173, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fullscreen-btn:hover {
            background: rgba(142, 68, 173, 1);
            transform: scale(1.1);
        }

        .epigenetic-highlight {
            background: linear-gradient(135deg, #8e44ad, #3498db);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .modification-box {
            background: #e8f4fd;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .modification-box h4 {
            color: #2980b9 !important;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .chromatin-state {
            background: #f0f8ff;
            border-left: 5px solid #4169e1;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="main-navbar">
        <div class="main-nav-container">
            <a href="index.html" class="main-nav-brand">
                <i class="fas fa-dna"></i>
                NGS高通量测序技术课程平台
            </a>
            <div class="main-nav-links">
                <a href="index.html" class="main-nav-link">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="index.html#lectures" class="main-nav-link">
                    <i class="fas fa-book-open"></i>
                    课程
                </a>
                <a href="lab_html/Lecture5_lab.html" class="main-nav-link" target="_blank">
                    <i class="fas fa-flask"></i>
                    实践指导
                </a>
                <a href="about.html" class="main-nav-link">
                    <i class="fas fa-info-circle"></i>
                    关于
                </a>
                <a href="syllabus.html" class="main-nav-link">
                    <i class="fas fa-list-alt"></i>
                    大纲
                </a>
                <a href="resources.html" class="main-nav-link">
                    <i class="fas fa-download"></i>
                    资源
                </a>
            </div>
        </div>
    </nav>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">15</span>
    </div>

    <div class="slide-menu">
        <button class="menu-btn" onclick="toggleMenu()">☰</button>
        <div class="menu-dropdown" id="menuDropdown">
            <!-- 菜单项将通过JavaScript动态生成 -->
        </div>
    </div>

    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏模式">⛶</button>

    <div class="container">
        <!-- 幻灯片内容将通过JavaScript动态生成 -->
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
        <button class="nav-btn" onclick="resetPresentation()">重新开始</button>
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;

        // 幻灯片数据
        const slides = [
            {
                title: "专题五：表观基因组测序数据分析",
                subtitle: "理论课程 - 高通量测序原理与数据分析",
                content: `
                    <div class="graphic-abstract">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🧬 表观基因组测序数据分析流程图形摘要</h3>
                        <svg width="100%" height="520" viewBox="0 0 1000 520" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变 -->
                            <defs>
                                <linearGradient id="bgGradient5" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.1" />
                                    <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="epigeneticGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#e74c3c" />
                                    <stop offset="20%" style="stop-color:#f39c12" />
                                    <stop offset="40%" style="stop-color:#27ae60" />
                                    <stop offset="60%" style="stop-color:#3498db" />
                                    <stop offset="80%" style="stop-color:#9b59b6" />
                                    <stop offset="100%" style="stop-color:#e91e63" />
                                </linearGradient>
                                <linearGradient id="chromatinGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3498db" />
                                    <stop offset="100%" style="stop-color:#2980b9" />
                                </linearGradient>
                                <!-- 箭头标记 -->
                                <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                                </marker>
                            </defs>
                            
                            <!-- 背景 -->
                            <rect width="1000" height="520" fill="url(#bgGradient5)" rx="15"/>
                            
                            <!-- 标题区域 -->
                            <text x="500" y="30" text-anchor="middle" fill="#667eea" font-size="18" font-weight="bold">表观基因组测序技术与数据分析工作流</text>
                            
                            <!-- 表观遗传修饰类型 - 顶部 -->
                            <g transform="translate(50, 50)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">主要表观遗传修饰类型</text>
                                
                                <!-- DNA甲基化 -->
                                <g transform="translate(50, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#e74c3c" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">DNA甲基化</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">CpG位点</text>
                                </g>
                                
                                <!-- 组蛋白修饰 -->
                                <g transform="translate(190, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#f39c12" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">组蛋白修饰</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">H3K4me3等</text>
                                </g>
                                
                                <!-- 染色质可及性 -->
                                <g transform="translate(330, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#27ae60" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">染色质可及性</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">开放区域</text>
                                </g>
                                
                                <!-- 转录因子结合 -->
                                <g transform="translate(470, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#3498db" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">转录因子结合</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">蛋白-DNA</text>
                                </g>
                                
                                <!-- 三维结构 -->
                                <g transform="translate(610, 35)">
                                    <rect x="0" y="0" width="120" height="35" fill="#9b59b6" rx="5" opacity="0.9"/>
                                    <text x="60" y="15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">三维结构</text>
                                    <text x="60" y="28" text-anchor="middle" fill="white" font-size="9">Hi-C/4C</text>
                                </g>
                                
                                <!-- 调控意义 -->
                                <g transform="translate(750, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">调控意义:</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">基因表达 | 细胞分化 | 疾病机制</text>
                                </g>
                            </g>
                            
                            <!-- 主要技术平台 - 中部 -->
                            <g transform="translate(50, 160)">
                                <!-- ChIP-seq -->
                                <g transform="translate(0, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e74c3c" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">ChIP-seq</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">组蛋白修饰</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">转录因子</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">免疫沉淀</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">峰检测</text>
                                </g>
                                
                                <!-- ATAC-seq -->
                                <g transform="translate(130, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#f39c12" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">ATAC-seq</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">染色质可及性</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">转座酶</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">开放区域</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">调控元件</text>
                                </g>
                                
                                <!-- WGBS -->
                                <g transform="translate(260, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#27ae60" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">WGBS</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">DNA甲基化</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">亚硫酸盐</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">全基因组</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">CpG分析</text>
                                </g>
                                
                                <!-- Hi-C -->
                                <g transform="translate(390, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#3498db" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Hi-C</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">三维结构</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">染色质交联</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">空间接触</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">TAD分析</text>
                                </g>
                                
                                <!-- CUT&Tag -->
                                <g transform="translate(520, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#9b59b6" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">CUT&Tag</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">低细胞数</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">蛋白A-Tn5</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">高特异性</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">低背景</text>
                                </g>
                                
                                <!-- scATAC-seq -->
                                <g transform="translate(650, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#e91e63" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">scATAC-seq</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">单细胞</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">细胞异质性</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">发育轨迹</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">调控网络</text>
                                </g>
                                
                                <!-- 多组学整合 -->
                                <g transform="translate(780, 0)">
                                    <rect x="0" y="0" width="110" height="70" fill="#34495e" rx="8" opacity="0.9"/>
                                    <text x="55" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">多组学整合</text>
                                    <text x="55" y="32" text-anchor="middle" fill="white" font-size="10">数据融合</text>
                                    <text x="55" y="44" text-anchor="middle" fill="white" font-size="9">调控网络</text>
                                    <text x="55" y="56" text-anchor="middle" fill="white" font-size="9">功能注释</text>
                                    <text x="55" y="68" text-anchor="middle" fill="white" font-size="9">机制解析</text>
                                </g>
                                
                                <!-- 流程箭头 -->
                                <path d="M110,35 L120,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                                <path d="M240,35 L250,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                                <path d="M370,35 L380,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                                <path d="M500,35 L510,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                                <path d="M630,35 L640,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                                <path d="M760,35 L770,35" stroke="#667eea" stroke-width="3" marker-end="url(#arrowhead5)"/>
                            </g>
                            
                            <!-- 数据分析流程 - 中下部 -->
                            <g transform="translate(50, 270)">
                                <rect x="0" y="0" width="900" height="80" fill="white" rx="10" opacity="0.95" stroke="#667eea" stroke-width="2"/>
                                <text x="450" y="25" text-anchor="middle" fill="#667eea" font-size="14" font-weight="bold">表观基因组数据分析核心步骤</text>
                                
                                <!-- 质量控制 -->
                                <g transform="translate(50, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">质量控制</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">FastQC检查</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">接头去除</text>
                                </g>
                                
                                <!-- 序列比对 -->
                                <g transform="translate(150, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">序列比对</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">BWA/Bowtie2</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">去重处理</text>
                                </g>
                                
                                <!-- 峰检测 -->
                                <g transform="translate(250, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">峰检测</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">MACS2/HOMER</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">信号富集</text>
                                </g>
                                
                                <!-- 注释分析 -->
                                <g transform="translate(350, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">注释分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">基因组特征</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">功能富集</text>
                                </g>
                                
                                <!-- 差异分析 -->
                                <g transform="translate(450, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">差异分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">DiffBind</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">条件比较</text>
                                </g>
                                
                                <!-- 基序分析 -->
                                <g transform="translate(550, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">基序分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">MEME/HOMER</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">转录因子</text>
                                </g>
                                
                                <!-- 可视化 -->
                                <g transform="translate(650, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">数据可视化</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">IGV/deepTools</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">热图/轨迹</text>
                                </g>
                                
                                <!-- 整合分析 -->
                                <g transform="translate(750, 35)">
                                    <text x="0" y="15" fill="#2c3e50" font-size="12" font-weight="bold">整合分析</text>
                                    <text x="0" y="30" fill="#2c3e50" font-size="10">多组学</text>
                                    <text x="0" y="42" fill="#2c3e50" font-size="9">调控网络</text>
                                </g>
                            </g>
                            
                            <!-- 应用领域 - 底部 -->
                            <g transform="translate(50, 390)">
                                <text x="400" y="20" text-anchor="middle" fill="#667eea" font-size="16" font-weight="bold">表观基因组学应用领域</text>
                                
                                <!-- 发育生物学 -->
                                <g transform="translate(50, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">发育</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">生物学</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 细胞分化</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 胚胎发育</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 谱系决定</text>
                                </g>
                                
                                <!-- 疾病研究 -->
                                <g transform="translate(200, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#f39c12" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">疾病</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">研究</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 癌症机制</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 神经疾病</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 代谢疾病</text>
                                </g>
                                
                                <!-- 药物开发 -->
                                <g transform="translate(350, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#27ae60" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">药物</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">开发</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 表观药物</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 靶点发现</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 耐药机制</text>
                                </g>
                                
                                <!-- 环境响应 -->
                                <g transform="translate(500, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#3498db" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">环境</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">响应</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 应激反应</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 营养代谢</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 衰老过程</text>
                                </g>
                                
                                <!-- 进化研究 -->
                                <g transform="translate(650, 40)">
                                    <circle cx="0" cy="0" r="30" fill="#9b59b6" opacity="0.9"/>
                                    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="11" font-weight="bold">进化</text>
                                    <text x="0" y="8" text-anchor="middle" fill="white" font-size="11" font-weight="bold">研究</text>
                                    <text x="0" y="50" text-anchor="middle" fill="#2c3e50" font-size="10">• 物种比较</text>
                                    <text x="0" y="65" text-anchor="middle" fill="#2c3e50" font-size="10">• 适应性进化</text>
                                    <text x="0" y="80" text-anchor="middle" fill="#2c3e50" font-size="10">• 表观遗传</text>
                                </g>
                            </g>
                            
                            <!-- 连接线 -->
                            <path d="M120,430 Q150,410 180,430" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M270,430 Q300,410 330,430" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M420,430 Q450,410 480,430" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                            <path d="M570,430 Q600,410 630,430" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>
                        </svg>
                    </div>

                    <div class="highlight-box">
                        <h3>专题目标</h3>
                        <p>理解表观基因组学的基本概念、主要表观遗传修饰类型、高通量检测技术原理及其数据分析方法，掌握ChIP-seq、ATAC-seq、WGBS等核心技术的应用。</p>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>主要内容</h3>
                            <ul>
                                <li>表观基因组学基础概念与修饰类型</li>
                                <li>ChIP-seq实验原理与峰检测算法</li>
                                <li>ATAC-seq技术原理与数据分析</li>
                                <li>全基因组甲基化测序技术</li>
                                <li>表观修饰与基因表达调控关系</li>
                                <li>多组学数据整合分析方法</li>
                            </ul>
                        </div>
                        <div>
                            <h3>学习目标</h3>
                            <ul>
                                <li>掌握表观遗传修饰的分子机制</li>
                                <li>理解主流表观基因组技术原理</li>
                                <li>学会选择合适的分析工具和方法</li>
                                <li>具备解读表观基因组数据的能力</li>
                                <li>了解表观基因组学在疾病研究中的应用</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 核心概念：</strong>表观基因组学研究不依赖于DNA序列改变的基因表达调控机制，是连接基因型与表型的重要桥梁。
                    </div>
                `
            },
            {
                title: "表观基因组学概述",
                subtitle: "第一部分：表观基因组学基础知识",
                content: `
                    <h2>表观遗传学的核心特征</h2>

                    <div class="key-points">
                        <h4>🧬 表观遗传学四大特征</h4>
                        <ul>
                            <li><strong>可遗传性：</strong>表观遗传标记在细胞分裂中可传递给子代细胞</li>
                            <li><strong>可逆性：</strong>受特定酶系统调控，可动态改变</li>
                            <li><strong>环境响应性：</strong>受发育信号、环境因素影响</li>
                            <li><strong>细胞特异性：</strong>不同细胞类型具有独特的表观基因组谱</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>表观基因组学研究内容</h3>
                            <ul>
                                <li><span class="epigenetic-highlight">DNA甲基化</span>：CpG位点的胞嘧啶甲基化</li>
                                <li><span class="epigenetic-highlight">组蛋白修饰</span>：乙酰化、甲基化等化学修饰</li>
                                <li><span class="epigenetic-highlight">染色质可及性</span>：DNA对调控因子的可接触性</li>
                                <li><span class="epigenetic-highlight">非编码RNA调控</span>：lncRNA、miRNA等</li>
                                <li><span class="epigenetic-highlight">三维基因组结构</span>：染色质空间组织</li>
                            </ul>
                        </div>
                        <div>
                            <h3>表观修饰的动态调控</h3>
                            <ul>
                                <li><strong>写入器(Writers)：</strong>添加修饰的酶</li>
                                <li><strong>擦除器(Erasers)：</strong>去除修饰的酶</li>
                                <li><strong>阅读器(Readers)：</strong>识别修饰的蛋白</li>
                            </ul>
                        </div>
                    </div>

                    <div class="modification-box">
                        <h4>表观修饰的生物学意义</h4>
                        <p><strong>发育调控：</strong>细胞分化和谱系决定 → <strong>疾病机制：</strong>癌症等复杂疾病的驱动因素 → <strong>治疗靶点：</strong>表观遗传药物开发</p>
                    </div>

                    <div class="info-box">
                        <strong>💡 研究价值：</strong>表观基因组学为理解基因表达调控、细胞分化、疾病发生和环境适应提供了全新视角。
                    </div>
                `
            },
            {
                title: "主要表观遗传修饰类型",
                subtitle: "DNA甲基化与组蛋白修饰详解",
                content: `
                    <h2>DNA甲基化</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>甲基化类型</th>
                                <th>位点</th>
                                <th>分布特征</th>
                                <th>功能意义</th>
                            </tr>
                            <tr>
                                <td><strong>CpG甲基化</strong></td>
                                <td>CpG二核苷酸</td>
                                <td>CpG岛、启动子、基因体</td>
                                <td>基因表达调控</td>
                            </tr>
                            <tr>
                                <td><strong>非CpG甲基化</strong></td>
                                <td>CHG、CHH</td>
                                <td>神经元、胚胎干细胞</td>
                                <td>特殊调控功能</td>
                            </tr>
                            <tr>
                                <td><strong>5hmC</strong></td>
                                <td>5-羟甲基胞嘧啶</td>
                                <td>基因体、增强子</td>
                                <td>去甲基化中间体</td>
                            </tr>
                        </table>
                    </div>

                    <h2>组蛋白修饰</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>H3K4me3</h4>
                            <p><strong>位置：</strong>活跃基因启动子<br>
                            <strong>功能：</strong>转录起始标志<br>
                            <strong>效应：</strong>激活转录</p>
                        </div>
                        <div class="tool-card">
                            <h4>H3K27ac</h4>
                            <p><strong>位置：</strong>活跃启动子和增强子<br>
                            <strong>功能：</strong>激活标记<br>
                            <strong>效应：</strong>促进转录</p>
                        </div>
                        <div class="tool-card">
                            <h4>H3K27me3</h4>
                            <p><strong>位置：</strong>抑制基因区域<br>
                            <strong>功能：</strong>Polycomb标记<br>
                            <strong>效应：</strong>基因沉默</p>
                        </div>
                        <div class="tool-card">
                            <h4>H3K36me3</h4>
                            <p><strong>位置：</strong>活跃基因体<br>
                            <strong>功能：</strong>转录延伸标记<br>
                            <strong>效应：</strong>RNA加工调控</p>
                        </div>
                    </div>

                    <div class="chromatin-state">
                        <h4>染色质状态组合模式</h4>
                        <ul>
                            <li><strong>活跃启动子：</strong>H3K4me3 + H3K27ac + 低甲基化 + 开放染色质</li>
                            <li><strong>两价启动子：</strong>H3K4me3 + H3K27me3（干细胞中常见）</li>
                            <li><strong>活跃增强子：</strong>H3K4me1 + H3K27ac + 开放染色质</li>
                            <li><strong>异染色质：</strong>H3K9me3 + 高甲基化 + 致密染色质</li>
                        </ul>
                    </div>
                `
            },
            {
                title: "ChIP-seq实验原理",
                subtitle: "第二部分：ChIP-seq实验原理与分析流程",
                content: `
                    <h2>染色质免疫沉淀基本步骤</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">细胞交联<br>甲醛处理</div>
                        <div class="flowchart-step">染色质片段化<br>超声破碎</div>
                        <div class="flowchart-step">免疫沉淀<br>特异性抗体</div>
                        <div class="flowchart-step">DNA纯化<br>解交联</div>
                        <div class="flowchart-step">文库构建<br>高通量测序</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>关键实验要素</h3>
                            <ul>
                                <li><strong>抗体选择：</strong>特异性、亲和力、ChIP验证</li>
                                <li><strong>交联条件：</strong>甲醛浓度、时间控制</li>
                                <li><strong>片段化：</strong>200-600bp理想范围</li>
                                <li><strong>洗涤条件：</strong>去除非特异性结合</li>
                                <li><strong>对照设计：</strong>Input DNA、IgG对照</li>
                            </ul>
                        </div>
                        <div>
                            <h3>技术变体</h3>
                            <ul>
                                <li><strong>Native ChIP：</strong>无交联，适合组蛋白</li>
                                <li><strong>ChIP-exo：</strong>高分辨率，单碱基精度</li>
                                <li><strong>CUT&RUN：</strong>低背景、低起始量</li>
                                <li><strong>CUT&Tag：</strong>转座酶标记技术</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>实验设计考虑因素</h3>
                        <ul>
                            <li><strong>生物学重复：</strong>至少2-3个独立重复</li>
                            <li><strong>测序深度：</strong>转录因子20-50M reads，组蛋白修饰50-100M reads</li>
                            <li><strong>片段大小：</strong>100-500bp范围，影响分辨率</li>
                            <li><strong>质量控制：</strong>抗体验证、交联效率、富集评估</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 常见挑战：</strong>低信噪比、PCR偏好性、批次效应、片段化偏好性等需要在实验设计和数据分析中予以考虑。
                    </div>
                `
            },
            {
                title: "ChIP-seq数据分析流程",
                subtitle: "从原始数据到峰检测的完整流程",
                content: `
                    <h2>标准分析流程</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">质量控制<br>FastQC</div>
                        <div class="flowchart-step">序列比对<br>Bowtie2/BWA</div>
                        <div class="flowchart-step">数据过滤<br>去重复</div>
                        <div class="flowchart-step">峰检测<br>MACS2</div>
                        <div class="flowchart-step">下游分析<br>注释/功能</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>数据质控指标</h3>
                            <ul>
                                <li><strong>FRiP Score：</strong>峰区域内reads比例</li>
                                <li><strong>富集倍数：</strong>相对于背景的信号强度</li>
                                <li><strong>片段长度分布：</strong>反映片段化质量</li>
                                <li><strong>重复性评估：</strong>生物学重复相关性</li>
                                <li><strong>信噪比：</strong>IP vs Input信号对比</li>
                            </ul>
                        </div>
                        <div>
                            <h3>峰检测工具</h3>
                            <ul>
                                <li><strong>MACS2：</strong>最广泛使用，适合窄峰</li>
                                <li><strong>SICER/EPIC2：</strong>专门检测宽峰</li>
                                <li><strong>HOMER：</strong>集成motif分析功能</li>
                                <li><strong>SEACR：</strong>适合CUT&RUN数据</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>MACS2算法原理</h3>
                        <ol>
                            <li><strong>局部背景建模：</strong>动态泊松分布模型</li>
                            <li><strong>峰位移模型：</strong>估计片段长度，精确定位</li>
                            <li><strong>显著性检验：</strong>基于泊松分布计算p-value</li>
                            <li><strong>FDR控制：</strong>Benjamini-Hochberg多重检验校正</li>
                        </ol>
                    </div>

                    <div class="code-block">
# MACS2基本用法
macs2 callpeak -t ChIP.bam -c Input.bam \\
    -f BAM -g hs -n sample_name \\
    --outdir results/ -q 0.05

# 宽峰检测
macs2 callpeak -t ChIP.bam -c Input.bam \\
    -f BAM -g hs -n sample_name \\
    --broad --broad-cutoff 0.1
                    </div>

                    <div class="info-box">
                        <strong>💡 工具选择：</strong>转录因子选择MACS2默认模式，宽泛组蛋白修饰选择--broad模式或SICER。
                    </div>
                `
            },
            {
                title: "ATAC-seq技术原理",
                subtitle: "第三部分：ATAC-seq技术原理与应用",
                content: `
                    <h2>转座酶介导的标记技术</h2>

                    <div class="key-points">
                        <h4>🧬 ATAC-seq核心原理</h4>
                        <ul>
                            <li><strong>超活性Tn5转座酶：</strong>预装载测序接头的转座复合物</li>
                            <li><strong>开放染色质优先标记：</strong>Tn5优先进入染色质可及区域</li>
                            <li><strong>同时片段化和标记：</strong>一步完成fragmentation和tagging</li>
                            <li><strong>核小体定位信息：</strong>片段长度分布反映染色质结构</li>
                        </ul>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>技术</th>
                                <th>原理</th>
                                <th>优势</th>
                                <th>局限性</th>
                            </tr>
                            <tr>
                                <td><strong>ATAC-seq</strong></td>
                                <td>Tn5转座酶标记</td>
                                <td>快速、低起始量、高信噪比</td>
                                <td>线粒体污染、Tn5偏好性</td>
                            </tr>
                            <tr>
                                <td><strong>DNase-seq</strong></td>
                                <td>DNase I酶切</td>
                                <td>成熟技术、特异性高</td>
                                <td>步骤繁琐、酶切优化困难</td>
                            </tr>
                            <tr>
                                <td><strong>FAIRE-seq</strong></td>
                                <td>甲醛交联富集</td>
                                <td>无需酶处理</td>
                                <td>分辨率较低、背景较高</td>
                            </tr>
                        </table>
                    </div>

                    <div class="algorithm-box">
                        <h3>ATAC-seq实验设计要点</h3>
                        <ul>
                            <li><strong>细胞数量：</strong>标准50K-100K，可低至500-5K</li>
                            <li><strong>转座酶浓度：</strong>需要滴定优化，避免过度切割</li>
                            <li><strong>测序深度：</strong>~50M总reads，25-50M有效reads</li>
                            <li><strong>质控指标：</strong>片段大小分布、TSS富集、线粒体污染率</li>
                        </ul>
                    </div>

                    <div class="modification-box">
                        <h4>ATAC-seq质量控制关键指标</h4>
                        <p><strong>片段大小分布：</strong>NFR峰(<100bp) + 核小体周期性峰(~200bp, ~400bp) → <strong>TSS富集：</strong>转录起始位点信号富集 → <strong>线粒体污染：</strong>希望<15-20%</p>
                    </div>
                `
            },
            {
                title: "ATAC-seq数据分析",
                subtitle: "染色质可及性数据的处理与解读",
                content: `
                    <h2>ATAC-seq分析流程</h2>

                    <div class="flowchart">
                        <div class="flowchart-step">质量控制<br>FastQC</div>
                        <div class="flowchart-step">序列比对<br>Bowtie2</div>
                        <div class="flowchart-step">数据过滤<br>去重/移位校正</div>
                        <div class="flowchart-step">峰检测<br>MACS2/HMMRATAC</div>
                        <div class="flowchart-step">足迹分析<br>TF footprinting</div>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>数据预处理特殊步骤</h3>
                            <ul>
                                <li><strong>去除线粒体reads：</strong>过滤chrM比对reads</li>
                                <li><strong>移位校正：</strong>正链+4bp，负链-5bp</li>
                                <li><strong>片段长度过滤：</strong>保留合理范围片段</li>
                                <li><strong>PCR去重：</strong>使用Picard或samtools</li>
                            </ul>
                        </div>
                        <div>
                            <h3>峰检测工具选择</h3>
                            <ul>
                                <li><strong>MACS2：</strong>需调整参数，使用--nomodel</li>
                                <li><strong>HMMRATAC：</strong>专门设计，考虑核小体模式</li>
                                <li><strong>Genrich：</strong>考虑PCR重复等因素</li>
                                <li><strong>F-seq：</strong>基于密度的峰检测</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>转录因子足迹分析原理</h3>
                        <ol>
                            <li><strong>保护效应：</strong>TF结合保护DNA免受Tn5切割</li>
                            <li><strong>足迹识别：</strong>TF结合位点局部信号下降</li>
                            <li><strong>算法工具：</strong>HINT-ATAC、TOBIAS、CENTIPEDE</li>
                            <li><strong>应用价值：</strong>推断TF实际结合活性</li>
                        </ol>
                    </div>

                    <div class="code-block">
# ATAC-seq峰检测示例
macs2 callpeak -t ATAC.bam -f BAMPE \\
    -n ATAC_sample --nomodel \\
    --shift -100 --extsize 200 \\
    -g hs -q 0.05

# 使用HMMRATAC
HMMRATAC -b ATAC.bam -i ATAC.bam.bai \\
    -g genome.info -o ATAC_peaks
                    </div>

                    <div class="info-box">
                        <strong>💡 分析要点：</strong>ATAC-seq不仅提供开放区域信息，还能通过片段长度分析核小体定位，通过足迹分析推断TF结合。
                    </div>
                `
            },
            {
                title: "全基因组甲基化测序原理",
                subtitle: "第四部分：全基因组甲基化测序原理(WGBS/BS-seq)",
                content: `
                    <h2>亚硫酸氢盐转换原理</h2>

                    <div class="algorithm-box">
                        <h3>化学反应机制</h3>
                        <ol>
                            <li><strong>亚硫酸氢盐处理：</strong>NaHSO₃与未甲基化C反应</li>
                            <li><strong>脱氨基反应：</strong>C转化为尿嘧啶(U)</li>
                            <li><strong>5mC保护：</strong>甲基化C不受影响，保持为C</li>
                            <li><strong>PCR扩增：</strong>U读成T，5mC读成C</li>
                        </ol>
                    </div>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>技术</th>
                                <th>覆盖范围</th>
                                <th>分辨率</th>
                                <th>成本</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td><strong>WGBS</strong></td>
                                <td>全基因组</td>
                                <td>单碱基</td>
                                <td>高</td>
                                <td>金标准研究</td>
                            </tr>
                            <tr>
                                <td><strong>RRBS</strong></td>
                                <td>CpG富集区</td>
                                <td>单碱基</td>
                                <td>中等</td>
                                <td>成本效益研究</td>
                            </tr>
                            <tr>
                                <td><strong>靶向BS-seq</strong></td>
                                <td>特定区域</td>
                                <td>单碱基</td>
                                <td>低</td>
                                <td>候选区域验证</td>
                            </tr>
                            <tr>
                                <td><strong>甲基化芯片</strong></td>
                                <td>固定位点</td>
                                <td>单位点</td>
                                <td>最低</td>
                                <td>大样本筛查</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🔬 转换效率评估</h4>
                        <ul>
                            <li><strong>外源对照：</strong>Lambda噬菌体DNA等完全未甲基化DNA</li>
                            <li><strong>内源评估：</strong>非CpG位点甲基化水平应接近0</li>
                            <li><strong>质量标准：</strong>转换效率应>99%</li>
                            <li><strong>技术偏好：</strong>DNA降解、GC偏好、转换偏好</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 技术局限：</strong>标准WGBS无法区分5mC和5hmC，需要oxBS-seq或TAB-seq等改良技术。
                    </div>
                `
            },
            {
                title: "甲基化数据分析流程",
                subtitle: "从亚硫酸氢盐测序到差异甲基化分析",
                content: `
                    <h2>BS-seq数据分析挑战</h2>

                    <div class="key-points">
                        <h4>🎯 分析挑战与解决方案</h4>
                        <ul>
                            <li><strong>序列复杂度降低：</strong>C→T转换导致序列多样性下降</li>
                            <li><strong>非对称比对：</strong>需要专门的三字母比对策略</li>
                            <li><strong>转换效率评估：</strong>必须验证BS转换完整性</li>
                            <li><strong>甲基化水平计算：</strong>基于C/(C+T)比值</li>
                        </ul>
                    </div>

                    <div class="algorithm-box">
                        <h3>三字母比对策略</h3>
                        <ol>
                            <li><strong>读段转换：</strong>将reads中所有C替换为T</li>
                            <li><strong>参考基因组转换：</strong>正链C→T，负链G→A</li>
                            <li><strong>比对执行：</strong>转换后reads比对到转换参考基因组</li>
                            <li><strong>结果恢复：</strong>推断原始链并恢复C/T信息</li>
                        </ol>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>Bismark</h4>
                            <p><strong>特点：</strong>最流行，功能全面<br>
                            <strong>引擎：</strong>Bowtie2/HISAT2<br>
                            <strong>输出：</strong>比对+甲基化提取</p>
                        </div>
                        <div class="tool-card">
                            <h4>BSMAP</h4>
                            <p><strong>特点：</strong>快速，内存效率高<br>
                            <strong>算法：</strong>种子扩展策略<br>
                            <strong>适用：</strong>大规模数据处理</p>
                        </div>
                        <div class="tool-card">
                            <h4>BWA-meth</h4>
                            <p><strong>特点：</strong>基于BWA-mem<br>
                            <strong>优势：</strong>速度快，准确性高<br>
                            <strong>适用：</strong>标准WGBS分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>methylKit</h4>
                            <p><strong>特点：</strong>R包，统计分析<br>
                            <strong>功能：</strong>DMR检测<br>
                            <strong>适用：</strong>差异甲基化分析</p>
                        </div>
                    </div>

                    <div class="code-block">
# Bismark分析流程
# 1. 构建索引
bismark_genome_preparation --path_to_aligner bowtie2 genome/

# 2. 序列比对
bismark --genome genome/ -1 sample_R1.fq -2 sample_R2.fq

# 3. 甲基化提取
bismark_methylation_extractor --paired-end sample.bam
                    </div>
                `
            },
            {
                title: "差异甲基化区域检测",
                subtitle: "DMR分析的统计方法与挑战",
                content: `
                    <h2>差异甲基化分析挑战</h2>

                    <div class="key-points">
                        <h4>🎯 DMR检测的方法学挑战</h4>
                        <ul>
                            <li><strong>比例数据：</strong>甲基化水平是0-1之间的比例，非计数数据</li>
                            <li><strong>覆盖度差异：</strong>不同位点和样本间覆盖度变化很大</li>
                            <li><strong>空间相关性：</strong>邻近CpG位点甲基化状态相关</li>
                            <li><strong>生物学变异：</strong>需要处理重复样本和个体差异</li>
                        </ul>
                    </div>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>BSmooth</h4>
                            <p><strong>方法：</strong>平滑处理+区域检测<br>
                            <strong>特点：</strong>考虑空间相关性<br>
                            <strong>适用：</strong>WGBS数据</p>
                        </div>
                        <div class="tool-card">
                            <h4>DSS</h4>
                            <p><strong>方法：</strong>Beta二项分布回归<br>
                            <strong>特点：</strong>离散度收缩<br>
                            <strong>适用：</strong>多种BS-seq数据</p>
                        </div>
                        <div class="tool-card">
                            <h4>methylKit</h4>
                            <p><strong>方法：</strong>多种统计检验<br>
                            <strong>特点：</strong>R包，易用性好<br>
                            <strong>适用：</strong>标准DMR分析</p>
                        </div>
                        <div class="tool-card">
                            <h4>metilene</h4>
                            <p><strong>方法：</strong>分段回归<br>
                            <strong>特点：</strong>快速，无需预定义区域<br>
                            <strong>适用：</strong>大规模数据</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>甲基化水平计算与质控</h3>
                        <div class="code-block">
# 甲基化水平计算
Methylation Level = C_reads / (C_reads + T_reads)

# 质控标准
- 最小覆盖度: ≥5x (推荐≥10x)
- 转换效率: >99%
- 非CpG甲基化: <5% (哺乳动物)
                        </div>
                    </div>

                    <div class="modification-box">
                        <h4>DMR生物学意义解读</h4>
                        <p><strong>启动子DMR：</strong>可能影响基因表达 → <strong>增强子DMR：</strong>调控远程基因活性 → <strong>基因体DMR：</strong>影响选择性剪接</p>
                    </div>
                `
            },
            {
                title: "表观修饰与基因表达调控",
                subtitle: "第五部分：表观修饰与基因表达调控关系",
                content: `
                    <h2>染色质状态定义</h2>

                    <div class="chromatin-state">
                        <h4>ChromHMM染色质状态模型</h4>
                        <ul>
                            <li><strong>活跃启动子(Active Promoter)：</strong>H3K4me3 + H3K27ac + 开放染色质</li>
                            <li><strong>弱启动子(Weak Promoter)：</strong>H3K4me3 + 低H3K27ac</li>
                            <li><strong>强增强子(Strong Enhancer)：</strong>H3K4me1 + H3K27ac + 开放染色质</li>
                            <li><strong>弱增强子(Weak Enhancer)：</strong>H3K4me1 + 低H3K27ac</li>
                            <li><strong>转录区域(Transcribed)：</strong>H3K36me3 + RNA Pol II</li>
                            <li><strong>异染色质(Heterochromatin)：</strong>H3K9me3 + 高甲基化</li>
                            <li><strong>Polycomb抑制(Polycomb Repressed)：</strong>H3K27me3</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>表观修饰组合效应</h3>
                            <ul>
                                <li><strong>协同激活：</strong>多种激活标记共存</li>
                                <li><strong>竞争性结合：</strong>激活vs抑制标记</li>
                                <li><strong>两价状态：</strong>H3K4me3 + H3K27me3</li>
                                <li><strong>动态平衡：</strong>修饰酶活性调控</li>
                            </ul>
                        </div>
                        <div>
                            <h3>表观-转录相关性分析</h3>
                            <ul>
                                <li><strong>启动子分析：</strong>TSS±2kb区域信号</li>
                                <li><strong>增强子-基因连接：</strong>Hi-C数据整合</li>
                                <li><strong>相关性计算：</strong>Spearman/Pearson</li>
                                <li><strong>因果推断：</strong>时间序列分析</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>多组学数据整合策略</h3>
                        <ol>
                            <li><strong>数据标准化：</strong>不同技术间信号标准化</li>
                            <li><strong>特征工程：</strong>提取表观遗传特征</li>
                            <li><strong>机器学习：</strong>预测基因表达模型</li>
                            <li><strong>网络分析：</strong>构建调控网络</li>
                        </ol>
                    </div>

                    <div class="info-box">
                        <strong>💡 整合分析价值：</strong>单一表观标记与基因表达的相关性有限，多标记组合能更准确预测转录活性。
                    </div>
                `
            },
            {
                title: "表观基因组学在疾病研究中的应用",
                subtitle: "癌症与发育疾病的表观遗传机制",
                content: `
                    <h2>癌症表观基因组特征</h2>

                    <div class="comparison-table">
                        <table>
                            <tr>
                                <th>表观改变类型</th>
                                <th>具体表现</th>
                                <th>生物学后果</th>
                                <th>治疗靶点</th>
                            </tr>
                            <tr>
                                <td><strong>全基因组低甲基化</strong></td>
                                <td>基因间区、重复序列</td>
                                <td>基因组不稳定</td>
                                <td>DNMT抑制剂</td>
                            </tr>
                            <tr>
                                <td><strong>CpG岛高甲基化</strong></td>
                                <td>抑癌基因启动子</td>
                                <td>肿瘤抑制功能丧失</td>
                                <td>去甲基化药物</td>
                            </tr>
                            <tr>
                                <td><strong>组蛋白修饰异常</strong></td>
                                <td>修饰酶突变/异常表达</td>
                                <td>转录程序紊乱</td>
                                <td>HDAC/HMT抑制剂</td>
                            </tr>
                            <tr>
                                <td><strong>染色质重塑缺陷</strong></td>
                                <td>SWI/SNF复合体突变</td>
                                <td>染色质结构异常</td>
                                <td>合成致死策略</td>
                            </tr>
                        </table>
                    </div>

                    <div class="key-points">
                        <h4>🏥 临床应用前景</h4>
                        <ul>
                            <li><strong>生物标志物：</strong>甲基化状态用于癌症分型和预后</li>
                            <li><strong>液体活检：</strong>血液ctDNA甲基化检测</li>
                            <li><strong>药物反应预测：</strong>MGMT甲基化与TMZ敏感性</li>
                            <li><strong>表观治疗：</strong>DNMT和HDAC抑制剂</li>
                        </ul>
                    </div>

                    <div class="modification-box">
                        <h4>环境因素对表观基因组的影响</h4>
                        <p><strong>营养因素：</strong>叶酸、维生素B12影响甲基化 → <strong>毒物暴露：</strong>重金属、污染物诱导表观改变 → <strong>生活方式：</strong>吸烟、压力、运动的表观效应</p>
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ 研究挑战：</strong>表观遗传改变的因果关系、跨代遗传机制、个体差异等仍需深入研究。
                    </div>
                `
            },
            {
                title: "单细胞表观基因组学",
                subtitle: "前沿技术：解析细胞异质性",
                content: `
                    <h2>单细胞表观基因组技术</h2>

                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4>scATAC-seq</h4>
                            <p><strong>检测：</strong>单细胞染色质可及性<br>
                            <strong>应用：</strong>细胞类型鉴定<br>
                            <strong>挑战：</strong>数据稀疏性</p>
                        </div>
                        <div class="tool-card">
                            <h4>scChIP-seq</h4>
                            <p><strong>检测：</strong>单细胞组蛋白修饰<br>
                            <strong>应用：</strong>发育轨迹分析<br>
                            <strong>挑战：</strong>信号微弱</p>
                        </div>
                        <div class="tool-card">
                            <h4>scBS-seq</h4>
                            <p><strong>检测：</strong>单细胞DNA甲基化<br>
                            <strong>应用：</strong>细胞谱系追踪<br>
                            <strong>挑战：</strong>覆盖度低</p>
                        </div>
                        <div class="tool-card">
                            <h4>scNMT-seq</h4>
                            <p><strong>检测：</strong>多模态同时检测<br>
                            <strong>应用：</strong>全面表观分析<br>
                            <strong>优势：</strong>信息整合</p>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>单细胞数据分析挑战</h3>
                        <ul>
                            <li><strong>数据稀疏性：</strong>大量零值和缺失数据</li>
                            <li><strong>技术噪音：</strong>扩增偏差和dropout事件</li>
                            <li><strong>细胞异质性：</strong>真实生物学差异vs技术变异</li>
                            <li><strong>计算复杂性：</strong>高维数据的降维和聚类</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>分析方法发展</h3>
                            <ul>
                                <li><strong>插补方法：</strong>MAGIC、SAVER等</li>
                                <li><strong>降维技术：</strong>t-SNE、UMAP</li>
                                <li><strong>轨迹推断：</strong>Monocle、Slingshot</li>
                                <li><strong>多模态整合：</strong>Seurat、MOFA+</li>
                            </ul>
                        </div>
                        <div>
                            <h3>应用前景</h3>
                            <ul>
                                <li><strong>发育生物学：</strong>细胞分化机制</li>
                                <li><strong>疾病研究：</strong>肿瘤异质性</li>
                                <li><strong>药物筛选：</strong>单细胞药物反应</li>
                                <li><strong>精准医疗：</strong>个体化治疗策略</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-box">
                        <strong>💡 技术趋势：</strong>单细胞多组学技术正朝着更高通量、更低成本、更多模态同时检测的方向发展。
                    </div>
                `
            },
            {
                title: "表观基因组学前沿与展望",
                subtitle: "技术发展趋势与未来方向",
                content: `
                    <h2>前沿研究方向</h2>

                    <div class="key-points">
                        <h4>🚀 技术发展前沿</h4>
                        <ul>
                            <li><strong>时空表观基因组学：</strong>结合空间信息的表观遗传分析</li>
                            <li><strong>表观编辑技术：</strong>CRISPR-dCas9精准表观修饰</li>
                            <li><strong>长读长测序：</strong>解析复杂区域表观状态</li>
                            <li><strong>实时监测：</strong>活细胞表观动态变化</li>
                        </ul>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>计算方法创新</h3>
                            <ul>
                                <li><strong>深度学习：</strong>表观特征自动提取</li>
                                <li><strong>图神经网络：</strong>染色质相互作用建模</li>
                                <li><strong>因果推断：</strong>表观-表型因果关系</li>
                                <li><strong>多尺度建模：</strong>分子到组织水平整合</li>
                            </ul>
                        </div>
                        <div>
                            <h3>临床转化应用</h3>
                            <ul>
                                <li><strong>精准诊断：</strong>表观标志物panel</li>
                                <li><strong>药物开发：</strong>表观靶点筛选</li>
                                <li><strong>治疗监测：</strong>动态表观变化追踪</li>
                                <li><strong>预防医学：</strong>环境暴露风险评估</li>
                            </ul>
                        </div>
                    </div>

                    <div class="algorithm-box">
                        <h3>表观编辑技术原理</h3>
                        <ol>
                            <li><strong>dCas9系统：</strong>失活Cas9保留DNA结合能力</li>
                            <li><strong>效应域融合：</strong>连接表观修饰酶域</li>
                            <li><strong>精准定位：</strong>gRNA引导特定位点</li>
                            <li><strong>可逆调控：</strong>添加或移除表观标记</li>
                        </ol>
                    </div>

                    <div class="modification-box">
                        <h4>跨代表观遗传研究</h4>
                        <p><strong>机制探索：</strong>表观信息如何跨代传递 → <strong>环境影响：</strong>亲代经历对后代的表观效应 → <strong>进化意义：</strong>表观遗传在适应性进化中的作用</p>
                    </div>

                    <div class="highlight-box">
                        <h3>未来展望</h3>
                        <p>表观基因组学正从描述性研究转向功能性和因果性研究，将在精准医疗、药物开发和疾病预防中发挥越来越重要的作用。</p>
                    </div>
                `
            }
        ];

        // 初始化演示文稿
        function initPresentation() {
            totalSlides = slides.length;
            document.getElementById('totalSlides').textContent = totalSlides;

            // 生成幻灯片HTML
            const container = document.querySelector('.container');
            slides.forEach((slide, index) => {
                const slideElement = document.createElement('div');
                slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                slideElement.innerHTML = `
                    <div class="slide-header">
                        <h1 class="slide-title">${slide.title}</h1>
                        <p class="slide-subtitle">${slide.subtitle}</p>
                    </div>
                    <div class="slide-content">
                        ${slide.content}
                    </div>
                `;
                container.appendChild(slideElement);
            });

            // 生成菜单
            generateMenu();
            updateNavigation();
            updateProgress();
        }

        // 生成菜单
        function generateMenu() {
            const menuDropdown = document.getElementById('menuDropdown');
            menuDropdown.innerHTML = slides.map((slide, index) => `
                <div class="menu-item ${index === 0 ? 'current' : ''}" onclick="goToSlide(${index})">
                    ${index + 1}. ${slide.title}
                </div>
            `).join('');
        }

        // 切换菜单显示
        function toggleMenu() {
            const dropdown = document.getElementById('menuDropdown');
            dropdown.classList.toggle('active');
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                // 隐藏当前幻灯片
                document.querySelectorAll('.slide')[currentSlideIndex].classList.remove('active');

                // 显示目标幻灯片
                currentSlideIndex = index;
                document.querySelectorAll('.slide')[currentSlideIndex].classList.add('active');

                // 更新界面
                updateNavigation();
                updateProgress();
                updateMenu();

                // 隐藏菜单
                document.getElementById('menuDropdown').classList.remove('active');
            }
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        // 更新导航按钮状态
        function updateNavigation() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            
            // 更新主题导航栏的active状态
            document.querySelectorAll('.topic-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据当前幻灯片索引设置active状态
            if (currentSlideIndex === 0) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(0)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 1 && currentSlideIndex <= 2) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(1)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 3 && currentSlideIndex <= 5) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(3)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 6 && currentSlideIndex <= 7) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(6)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 8 && currentSlideIndex <= 11) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(8)"]')?.classList.add('active');
            } else if (currentSlideIndex >= 12) {
                document.querySelector('.topic-nav-item[onclick="goToSlide(12)"]')?.classList.add('active');
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新菜单当前项
        function updateMenu() {
            document.querySelectorAll('.menu-item').forEach((item, index) => {
                item.classList.toggle('current', index === currentSlideIndex);
            });
        }

        // 自动播放
        function toggleAutoPlay() {
            const btn = document.getElementById('autoPlayBtn');
            if (isAutoPlaying) {
                clearInterval(autoPlayInterval);
                isAutoPlaying = false;
                btn.textContent = '自动播放';
            } else {
                autoPlayInterval = setInterval(() => {
                    if (currentSlideIndex < totalSlides - 1) {
                        nextSlide();
                    } else {
                        toggleAutoPlay(); // 到达最后一页时停止自动播放
                    }
                }, 5000);
                isAutoPlaying = true;
                btn.textContent = '停止播放';
            }
        }

        // 重新开始演示
        function resetPresentation() {
            if (isAutoPlaying) {
                toggleAutoPlay();
            }
            goToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    goToSlide(0);
                    break;
                case 'End':
                    goToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    document.getElementById('menuDropdown').classList.remove('active');
                    break;
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.slide-menu')) {
                document.getElementById('menuDropdown').classList.remove('active');
            }
        });

        // 初始化
        initPresentation();
    </script>
</body>
</html>
