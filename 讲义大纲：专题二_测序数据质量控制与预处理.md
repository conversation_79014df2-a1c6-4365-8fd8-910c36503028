# 专题二：测序数据质量控制与预处理

## 理论课教学大纲 (2小时)

### 教学目标
- 理解测序错误的来源与类型
- 掌握测序数据质量评估的关键指标
- 了解数据过滤与质控的策略与方法
- 掌握测序深度与覆盖度的计算及其生物学意义

### 第一部分：测序错误来源与类型分析 (30分钟)
1. 测序错误的主要来源
   - 样本制备过程中的错误
     - DNA/RNA降解
     - PCR扩增偏好性
     - 接头二聚体形成
   - 测序过程中的错误
     - 光学检测错误（Illumina）
     - 信号衰减（Illumina、Ion Torrent）
     - 同聚物区域错误（Ion Torrent、PacBio）
     - 碱基调用算法局限性
   - 数据处理过程中的错误
     - 碱基质量分数计算偏差
     - 数据转换与格式化错误

2. 不同测序平台的错误特征
   - Illumina平台错误模式
     - 替换错误为主（~0.1%）
     - 读长末端质量下降
     - GC含量偏好性
   - Ion Torrent平台错误模式
     - 插入/缺失错误为主
     - 同聚物区域错误率高
   - PacBio平台错误模式
     - 随机插入/缺失错误
     - 单程读长vs环形一致序列
   - Oxford Nanopore平台错误模式
     - 较高的错误率（5-15%）
     - 同聚物和修饰碱基识别挑战

3. 系统性错误与随机错误的区别
   - 系统性错误的特征与识别方法
   - 随机错误的统计特性
   - 错误累积效应分析

### 第二部分：测序数据质量评估指标与方法 (30分钟)
1. 序列质量分数体系
   - Phred质量分数定义与计算
   - 不同编码方式（Phred+33、Phred+64）
   - 质量分数与错误概率的关系
   - 平均质量分数的意义与局限性

2. 基本质量统计指标
   - 每个位置的平均质量分数
   - 序列长度分布
   - GC含量分布
   - N含量统计
   - 每个位置的碱基组成

3. 高级质量评估指标
   - 序列重复率
   - K-mer频率分布
   - 过表示序列分析
   - 接头污染检测
   - 测序偏好性评估

4. 质量评估可视化方法
   - 质量箱线图解读
   - 碱基分布图解读
   - GC含量分布图解读
   - 序列重复率图解读
   - 综合质量报告解读

### 第三部分：数据过滤与质控策略 (25分钟)
1. 质控流程设计原则
   - 研究目标导向的质控策略
   - 数据损失与质量提升的平衡
   - 上下游分析对数据质量的要求
   - 质控参数选择的考虑因素

2. 常见质控策略
   - 低质量序列过滤
     - 质量阈值设定
     - 滑动窗口法
     - 平均质量分数法
   - 序列长度过滤
   - N含量过滤
   - 重复序列处理
   - 污染序列过滤

3. 不同应用场景的质控策略差异
   - 全基因组测序的质控重点
   - 转录组测序的质控重点
   - 表观基因组测序的质控重点
   - 宏基因组测序的质控重点
   - 单细胞测序的质控重点

4. 质控效果评估方法
   - 质控前后数据对比分析
   - 下游分析结果验证
   - 质控参数优化策略

### 第四部分：接头去除和低质量序列过滤原理 (20分钟)
1. 测序接头的类型与特点
   - 测序平台特异性接头
   - 多重测序条形码
   - 接头二聚体形成机制
   - 接头污染的影响

2. 接头识别算法
   - 精确匹配法
   - 模糊匹配法
   - 局部比对算法
   - 接头识别的挑战

3. 接头去除策略
   - 5'端接头去除
   - 3'端接头去除
   - 内部接头识别与处理
   - 双端测序特殊处理

4. 低质量序列过滤算法
   - 硬截断法
   - 滑动窗口法
   - 动态质量阈值法
   - 机器学习方法

### 第五部分：测序深度与覆盖度的计算及意义 (15分钟)
1. 测序深度的定义与计算
   - 测序深度vs测序覆盖度
   - 平均测序深度计算
   - 有效测序深度评估
   - 不同应用所需的测序深度

2. 覆盖度分析方法
   - 基因组覆盖率计算
   - 均匀性评估
   - 覆盖度分布可视化
   - 低覆盖区域分析

3. 测序深度与数据质量的关系
   - 深度对变异检测的影响
   - 深度对组装质量的影响
   - 深度对表达定量的影响
   - 增量测序策略

4. 测序深度与覆盖度的应用指导
   - 实验设计中的深度规划
   - 数据下采样方法
   - 饱和分析
   - 成本效益评估

## 实践操作课教学大纲 (2小时)

### 教学目标
- 掌握FastQC等工具进行测序数据质量评估
- 学习使用Trimmomatic/Cutadapt等工具进行数据过滤与清洗
- 了解MultiQC进行批量质控结果可视化的方法
- 实践数据预处理自动化流程搭建
- 学会进行质控前后数据质量对比分析

### 第一部分：FastQC工具使用与质量报告解读 (30分钟)
1. FastQC工具介绍
   - 软件安装与配置
   - 命令行参数说明
   - 图形界面使用方法
   - 批量处理策略

2. FastQC质量报告详解
   - 基本统计信息解读
   - 每个碱基的质量分数图解读
   - 序列质量分数图解读
   - 碱基含量图解读
   - GC含量分布图解读
   - 每个位置N含量图解读
   - 序列长度分布图解读
   - 序列重复水平图解读
   - 过表示序列分析解读
   - K-mer含量分析解读

3. 实际操作演示
   - 单个FASTQ文件分析
   - 多个文件批量分析
   - 结果导出与保存
   - 常见问题解决方法

### 第二部分：Trimmomatic/Cutadapt等工具进行数据过滤与清洗 (40分钟)
1. Trimmomatic工具使用
   - 软件安装与配置
   - 单端测序数据处理
   - 双端测序数据处理
   - 主要参数详解
     - ILLUMINACLIP（接头去除）
     - SLIDINGWINDOW（滑动窗口质量过滤）
     - LEADING/TRAILING（两端低质量碱基修剪）
     - CROP/HEADCROP（固定长度修剪）
     - MINLEN（最小长度过滤）
     - AVGQUAL（平均质量过滤）
   - 参数优化策略
   - 实际操作演示

2. Cutadapt工具使用
   - 软件安装与配置
   - 基本命令格式
   - 接头识别与去除
     - 5'端接头处理
     - 3'端接头处理
     - 双端接头处理
   - 质量过滤参数
   - 长度过滤参数
   - 输出选项
   - 实际操作演示

3. 其他常用质控工具介绍
   - fastp：一站式FASTQ预处理工具
   - Trim Galore：Cutadapt和FastQC的包装器
   - BBDuk：BBTools套件中的质控工具
   - 工具选择建议

### 第三部分：MultiQC进行批量质控结果可视化 (20分钟)
1. MultiQC工具介绍
   - 软件安装与配置
   - 支持的分析工具类型
   - 命令行参数说明

2. MultiQC使用方法
   - 基本命令格式
   - 结果目录组织
   - 报告配置选项
   - 自定义报告内容

3. MultiQC报告解读
   - 通用统计信息
   - FastQC结果汇总
   - 质控工具结果汇总
   - 样本间比较分析
   - 交互式图表使用

4. 实际操作演示
   - 多样本质控结果汇总
   - 质控前后结果对比
   - 报告导出与分享

### 第四部分：数据预处理自动化流程搭建 (20分钟)
1. 自动化流程设计原则
   - 模块化设计
   - 参数配置化
   - 错误处理机制
   - 日志记录系统

2. Shell脚本实现
   - 基本脚本结构
   - 参数传递方法
   - 循环处理多个样本
   - 条件判断与错误处理
   - 实例脚本演示

3. 工作流管理工具介绍
   - Snakemake基础
   - Nextflow基础
   - 工作流定义语法
   - 参数配置方法
   - 简单工作流示例

4. 并行计算策略
   - GNU Parallel使用
   - 集群提交系统集成
   - 资源分配优化

### 第五部分：质控前后数据质量对比分析 (10分钟)
1. 对比分析方法
   - 关键指标选择
   - 定量比较方法
   - 可视化对比技术

2. 质量提升评估
   - 接头去除效果评估
   - 低质量区域过滤效果
   - 序列长度分布变化
   - 数据损失率计算

3. 实际案例分析
   - 不同类型数据的质控效果
   - 质控参数调整的影响
   - 质控策略优化建议

4. 质控报告撰写指南
   - 关键信息记录
   - 图表选择与解释
   - 方法描述规范
   - 结果解释要点

## 课后作业
1. 使用FastQC分析指定的测序数据集，并撰写质量评估报告
2. 设计并实施一个完整的数据质控流程，包括接头去除和质量过滤
3. 使用MultiQC生成质控前后的对比报告，并分析质控效果
4. 编写一个自动化脚本，能够批量处理多个样本的质控流程

## 参考资料
1. Andrews, S. (2010). FastQC: a quality control tool for high throughput sequence data. Available online at: http://www.bioinformatics.babraham.ac.uk/projects/fastqc
2. Bolger, A. M., Lohse, M., & Usadel, B. (2014). Trimmomatic: a flexible trimmer for Illumina sequence data. Bioinformatics, 30(15), 2114-2120.
3. Martin, M. (2011). Cutadapt removes adapter sequences from high-throughput sequencing reads. EMBnet.journal, 17(1), 10-12.
4. Ewels, P., Magnusson, M., Lundin, S., & Käller, M. (2016). MultiQC: summarize analysis results for multiple tools and samples in a single report. Bioinformatics, 32(19), 3047-3048.
5. Del Fabbro, C., Scalabrin, S., Morgante, M., & Giorgi, F. M. (2013). An extensive evaluation of read trimming effects on Illumina NGS data analysis. PloS one, 8(12), e85024.